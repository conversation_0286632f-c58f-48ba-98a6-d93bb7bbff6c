plugins {
    alias(libs.plugins.android.application)
    // Apply the Google services plugin
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.mdsadrulhasan.gogolaundry'
    compileSdk 35

    defaultConfig {
        applicationId "com.mdsadrulhasan.gogolaundry"
        minSdk 23
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    // Enable BuildConfig feature for custom fields
    buildFeatures {
        buildConfig = true
    }

    buildTypes {
        release {
            // Enable ProGuard for release builds with maximum security
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // Maximum security configurations
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            zipAlignEnabled true
            crunchPngs true

            // Additional anti-reverse engineering measures
            manifestPlaceholders = [
                enableCrashReporting: "false",
                enableAnalytics: "false"
            ]

            // Signing configuration (you should configure this with your keystore)
            // signingConfig signingConfigs.release

            // Build configuration for security
            buildConfigField "boolean", "IS_DEBUG", "false"
            buildConfigField "String", "BUILD_TYPE", '"release"'
        }

        debug {
            // Optional: Enable ProGuard for debug builds too (for testing obfuscation)
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable true

            // Debug build configuration
            buildConfigField "boolean", "IS_DEBUG", "true"
            buildConfigField "String", "BUILD_TYPE", '"debug"'

            // Remove debug suffix to match Firebase configuration
            // applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }

    // Additional security configurations
    packagingOptions {
        // Remove unnecessary files that could contain sensitive information
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/ASL2.0'
        exclude 'META-INF/*.kotlin_module'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    // AndroidX Core
    implementation 'androidx.core:core-ktx:1.12.0'

    // Lifecycle components
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.9.0'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.9.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.9.0'

    // UI components
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.viewpager2:viewpager2:1.1.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Room components
    implementation 'androidx.room:room-runtime:2.6.1'
    annotationProcessor 'androidx.room:room-compiler:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'

    // Retrofit for API calls
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    // OkHttp for networking
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'

    // Gson for JSON parsing
    implementation 'com.google.code.gson:gson:2.10.1'

    // Glide for image loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    // PhotoView for full-screen image viewing with zoom/pan
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    // SweetAlert for beautiful dialogs
    implementation 'com.github.f0ris.sweetalert:library:1.6.2'

    // Shimmer effect for loading states
    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    // Firebase for push notifications
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-messaging:23.4.1'
    implementation 'com.google.firebase:firebase-analytics:21.5.1'
    implementation 'com.google.firebase:firebase-core:21.1.1'

    // Google Maps for location
    implementation 'com.google.android.gms:play-services-maps:18.2.0'
    implementation 'com.google.android.gms:play-services-location:21.1.0'

    // OSMDroid for map functionality
    implementation 'org.osmdroid:osmdroid-android:6.1.17'
    implementation 'org.osmdroid:osmdroid-mapsforge:6.1.17'
    implementation 'org.osmdroid:osmdroid-wms:6.1.17'

    // Location services
    implementation 'com.google.android.gms:play-services-location:21.3.0'
}