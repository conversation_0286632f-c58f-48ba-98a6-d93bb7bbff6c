-- Migration: Add image_url column to notifications table
-- Date: 2025-01-27
-- Description: Add support for images in notifications

-- Add image_url column to notifications table
ALTER TABLE `notifications` 
ADD COLUMN `image_url` VARCHAR(500) NULL DEFAULT NULL AFTER `message`;

-- Add index for better performance when filtering by image presence
ALTER TABLE `notifications` 
ADD INDEX `idx_image_url` (`image_url`);

-- Update existing notifications to have NULL image_url (already default)
-- This is just for documentation purposes as the column will be NULL by default

-- Verify the change
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'your_database_name' 
-- AND TABLE_NAME = 'notifications' 
-- AND COLUMN_NAME = 'image_url';
