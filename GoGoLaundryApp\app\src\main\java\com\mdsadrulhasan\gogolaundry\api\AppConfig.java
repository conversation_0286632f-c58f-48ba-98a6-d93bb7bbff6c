package com.mdsadrulhasan.gogolaundry.api;

import com.google.gson.annotations.SerializedName;

/**
 * App configuration model
 */
public class AppConfig {
    @SerializedName("app_name")
    private String appName;

    @SerializedName("app_version")
    private String appVersion;

    @SerializedName("otp_enabled")
    private Object otpEnabled;

    @SerializedName("otp_length")
    private int otpLength;

    @SerializedName("otp_expiry")
    private int otpExpiry;

    @SerializedName("otp_max_attempts")
    private int otpMaxAttempts;

    @SerializedName("min_password_length")
    private int minPasswordLength;

    @SerializedName("server_time")
    private String serverTime;

    @SerializedName("admin_whatsapp")
    private String adminWhatsapp;

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public boolean isOtpEnabled() {
        if (otpEnabled instanceof Boolean) {
            return (Boolean) otpEnabled;
        } else if (otpEnabled instanceof String) {
            String otpEnabledStr = (String) otpEnabled;
            return otpEnabledStr.equals("1") ||
                    otpEnabledStr.equalsIgnoreCase("true") ||
                    otpEnabledStr.equalsIgnoreCase("yes");
        } else if (otpEnabled instanceof Number) {
            return ((Number) otpEnabled).intValue() == 1;
        }
        return false;
    }

    public Object getOtpEnabled() {
        return otpEnabled;
    }

    public void setOtpEnabled(Object otpEnabled) {
        this.otpEnabled = otpEnabled;
    }

    public int getOtpLength() {
        return otpLength;
    }

    public void setOtpLength(int otpLength) {
        this.otpLength = otpLength;
    }

    public int getOtpExpiry() {
        return otpExpiry;
    }

    public void setOtpExpiry(int otpExpiry) {
        this.otpExpiry = otpExpiry;
    }

    public int getOtpMaxAttempts() {
        return otpMaxAttempts;
    }

    public void setOtpMaxAttempts(int otpMaxAttempts) {
        this.otpMaxAttempts = otpMaxAttempts;
    }

    public int getMinPasswordLength() {
        return minPasswordLength;
    }

    public void setMinPasswordLength(int minPasswordLength) {
        this.minPasswordLength = minPasswordLength;
    }

    public String getServerTime() {
        return serverTime;
    }

    public void setServerTime(String serverTime) {
        this.serverTime = serverTime;
    }

    public String getAdminWhatsapp() {
        return adminWhatsapp != null ? adminWhatsapp : "+1234567890";
    }

    public void setAdminWhatsapp(String adminWhatsapp) {
        this.adminWhatsapp = adminWhatsapp;
    }
}
