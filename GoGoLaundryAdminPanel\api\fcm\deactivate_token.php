<?php
/**
 * FCM Token Deactivation API
 * 
 * This endpoint handles deactivation of FCM tokens (e.g., on logout)
 * 
 * Required parameters:
 * - user_id: User ID
 * - device_id: Device identifier (optional - if not provided, deactivates all user tokens)
 * 
 * Returns:
 * - success: true/false
 * - message: Success/error message
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Include required files
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/FCMService.php';

// Initialize response
$response = [
    'success' => false,
    'message' => 'Unknown error occurred',
    'data' => null
];

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    // If JSON input is empty, try POST data
    if (empty($input)) {
        $input = $_POST;
    }
    
    // Validate required parameters
    if (!isset($input['user_id']) || empty($input['user_id'])) {
        $response['message'] = 'Missing required field: user_id';
        echo json_encode($response);
        exit();
    }
    
    $userId = intval($input['user_id']);
    $deviceId = isset($input['device_id']) ? trim($input['device_id']) : null;
    
    // Validate user exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    
    if ($stmt->rowCount() === 0) {
        $response['message'] = 'User not found';
        echo json_encode($response);
        exit();
    }
    
    // Initialize FCM service
    $fcmService = new FCMService();
    
    if ($deviceId) {
        // Deactivate specific device token
        $result = $fcmService->deactivateUserDeviceToken($pdo, $userId, $deviceId);
        $action = "device token for device $deviceId";
    } else {
        // Deactivate all user tokens
        $result = $fcmService->deactivateAllUserTokens($pdo, $userId);
        $action = "all tokens";
    }
    
    if ($result['success']) {
        $response['success'] = true;
        $response['message'] = 'FCM tokens deactivated successfully';
        $response['data'] = [
            'user_id' => $userId,
            'device_id' => $deviceId,
            'tokens_deactivated' => $result['count'] ?? 1,
            'deactivated_at' => date('Y-m-d H:i:s')
        ];
        
        // Log the deactivation
        error_log("FCM tokens deactivated for user $userId: $action");
    } else {
        $response['message'] = $result['error'] ?? 'Failed to deactivate FCM tokens';
    }
    
} catch (Exception $e) {
    $response['message'] = 'Server error: ' . $e->getMessage();
    error_log('FCM Token Deactivation Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
?>
