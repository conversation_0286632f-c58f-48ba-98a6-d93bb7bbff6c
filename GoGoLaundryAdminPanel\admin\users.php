<?php
/**
 * Admin Users Management
 *
 * This page allows administrators to manage users
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/UserManager.php';

// Initialize user manager
$userManager = new UserManager($pdo);

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$verifiedOnly = isset($_GET['verified']) && $_GET['verified'] === '1';
$perPage = 10;

// Get users with pagination and filtering
$result = $userManager->getAllUsers($page, $perPage, $search, $verifiedOnly);
$users = $result['users'];
$pagination = $result['pagination'];

// Handle user status toggle (verify/unverify)
if (isset($_POST['toggle_verification']) && isset($_POST['user_id']) && is_numeric($_POST['user_id'])) {
    $userId = (int)$_POST['user_id'];
    $currentStatus = isset($_POST['current_status']) ? (int)$_POST['current_status'] : 0;
    $newStatus = $currentStatus ? 0 : 1;

    $result = $userManager->updateUserVerificationStatus($userId, $newStatus);

    if ($result) {
        // Log action
        $adminManager->logAdminAction(
            $adminData['id'],
            'user_verification_update',
            'Updated verification status for user ID: ' . $userId . ' to ' . ($newStatus ? 'verified' : 'unverified'),
            getClientIp()
        );

        $_SESSION['success_message'] = 'User verification status updated successfully.';
    } else {
        $_SESSION['error_message'] = 'Failed to update user verification status.';
    }

    // Redirect to maintain pagination and search
    header('Location: users.php?page=' . $page . '&search=' . urlencode($search) . '&verified=' . ($verifiedOnly ? '1' : '0'));
    exit;
}

// Handle user deletion (soft delete)
if (isset($_POST['delete_user']) && isset($_POST['user_id']) && is_numeric($_POST['user_id'])) {
    $userId = (int)$_POST['user_id'];
    $user = $userManager->getUserById($userId);

    if (!$user) {
        $_SESSION['error_message'] = 'User not found.';
        header('Location: users.php');
        exit;
    }

    $result = $userManager->softDeleteUser($userId);

    if ($result) {
        // Log action
        $adminManager->logAdminAction(
            $adminData['id'],
            'user_delete',
            'Soft deleted user ID: ' . $userId . ' (' . $user['full_name'] . ')',
            getClientIp()
        );

        $_SESSION['success_message'] = 'User deleted successfully. The account will be permanently deleted after 30 days.';
    } else {
        $_SESSION['error_message'] = 'Failed to delete user.';
    }

    // Redirect to maintain pagination and search
    header('Location: users.php?page=' . $page . '&search=' . urlencode($search) . '&verified=' . ($verifiedOnly ? '1' : '0'));
    exit;
}

// Page title and breadcrumbs
$pageTitle = 'Manage Users';
$breadcrumbs = [
    'Manage Users' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Manage Users</h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="reset_password.php" class="btn btn-primary">
            <i class="fas fa-key me-1"></i> Reset User Password
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-8">
                <form action="users.php" method="get" class="d-flex">
                    <input type="text" class="form-control me-2" name="search" placeholder="Search by name, phone, or email..." value="<?php echo htmlspecialchars($search); ?>">
                    <div class="form-check form-switch me-2 d-flex align-items-center">
                        <input class="form-check-input" type="checkbox" id="verified" name="verified" value="1" <?php echo $verifiedOnly ? 'checked' : ''; ?> onchange="this.form.submit()">
                        <label class="form-check-label ms-2" for="verified">Verified Only</label>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if (!empty($search) || $verifiedOnly): ?>
                        <a href="users.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-primary"><?php echo number_format($pagination['total']); ?> Total Users</span>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (count($users) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($user['phone']); ?></td>
                                <td><?php echo htmlspecialchars($user['email'] ?? 'N/A'); ?></td>
                                <td>
                                    <?php if ($user['is_verified']): ?>
                                        <span class="badge bg-success">Verified</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Unverified</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo (new DateTime($user['created_at']))->format('M d, Y'); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-primary dropdown-toggle" id="actionDropdown<?php echo $user['id']; ?>">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu custom-dropdown" id="actionMenu<?php echo $user['id']; ?>">
                                            <li>
                                                <a href="user_details.php?id=<?php echo $user['id']; ?>" class="dropdown-item">
                                                    <i class="fas fa-eye me-2"></i> View Details
                                                </a>
                                            </li>
                                            <li>
                                                <a href="reset_password.php?user_id=<?php echo $user['id']; ?>" class="dropdown-item">
                                                    <i class="fas fa-key me-2"></i> Reset Password
                                                </a>
                                            </li>
                                            <li>
                                                <a href="javascript:void(0);" class="dropdown-item" onclick="document.getElementById('toggleForm<?php echo $user['id']; ?>').submit();">
                                                    <?php if ($user['is_verified']): ?>
                                                        <i class="fas fa-user-times me-2"></i> Mark as Unverified
                                                    <?php else: ?>
                                                        <i class="fas fa-user-check me-2"></i> Mark as Verified
                                                    <?php endif; ?>
                                                </a>
                                                <form id="toggleForm<?php echo $user['id']; ?>" action="users.php" method="post" style="display: none;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <input type="hidden" name="current_status" value="<?php echo $user['is_verified']; ?>">
                                                    <input type="hidden" name="toggle_verification" value="1">
                                                </form>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button type="button" class="dropdown-item text-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#deleteModal"
                                                        data-user-id="<?php echo $user['id']; ?>"
                                                        data-user-name="<?php echo htmlspecialchars($user['full_name']); ?>">
                                                    <i class="fas fa-trash-alt me-2"></i> Delete User
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($pagination['total_pages'] > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&verified=<?php echo $verifiedOnly ? '1' : '0'; ?>">
                                Previous
                            </a>
                        </li>

                        <?php for ($i = max(1, $page - 2); $i <= min($pagination['total_pages'], $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&verified=<?php echo $verifiedOnly ? '1' : '0'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?php echo $page >= $pagination['total_pages'] ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&verified=<?php echo $verifiedOnly ? '1' : '0'; ?>">
                                Next
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No users found matching your criteria.
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm User Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the user <strong id="userName"></strong>?</p>
                <p class="text-warning">This action will soft delete the user. The account will be permanently deleted after 30 days if not restored.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="users.php" method="post">
                    <input type="hidden" name="user_id" id="deleteUserId">
                    <button type="submit" name="delete_user" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add page-specific scripts
$pageScripts = ['js/users.js'];
?>

<?php include 'includes/footer.php'; ?>
