package com.mdsadrulhasan.gogolaundry;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import com.google.gson.Gson;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.OtpResponse;
import com.mdsadrulhasan.gogolaundry.utils.DialogUtils;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ValidationUtils;

import org.json.JSONObject;

import cn.pedant.SweetAlert.SweetAlertDialog;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ForgotPasswordActivity extends AppCompatActivity {

    // Step 1: Phone Input
    private TextInputLayout phoneLayout;
    private TextInputEditText phoneEditText;
    private Button sendOtpButton;

    // Step 2: OTP Verification
    private TextInputLayout otpLayout;
    private TextInputEditText otpEditText;
    private Button verifyOtpButton;
    private TextView otpTimerText;

    // Step 3: New Password
    private TextInputLayout passwordLayout, confirmPasswordLayout;
    private TextInputEditText passwordEditText, confirmPasswordEditText;
    private Button resetPasswordButton;

    // Step views
    private View step1View, step2View, step3View;

    // Common
    private TextView loginText;

    private ApiService apiService;
    private SessionManager sessionManager;
    private CountDownTimer otpTimer;
    private SweetAlertDialog loadingDialog;

    // User input data
    private String phone, otp, newPassword;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_forgot_password);

        // Initialize API service and session manager
        apiService = ApiClient.getApiService(this);
        sessionManager = new SessionManager(this);

        // Initialize views
        initViews();
        setupClickListeners();
    }

    private void initViews() {
        // Step views
        step1View = findViewById(R.id.step1View);
        step2View = findViewById(R.id.step2View);
        step3View = findViewById(R.id.step3View);

        // Step 1: Phone Input
        phoneLayout = findViewById(R.id.phoneLayout);
        phoneEditText = findViewById(R.id.phoneEditText);
        sendOtpButton = findViewById(R.id.sendOtpButton);

        // Step 2: OTP Verification
        otpLayout = findViewById(R.id.otpLayout);
        otpEditText = findViewById(R.id.otpEditText);
        verifyOtpButton = findViewById(R.id.verifyOtpButton);
        otpTimerText = findViewById(R.id.otpTimerText);

        // Step 3: New Password
        passwordLayout = findViewById(R.id.passwordLayout);
        passwordEditText = findViewById(R.id.passwordEditText);
        confirmPasswordLayout = findViewById(R.id.confirmPasswordLayout);
        confirmPasswordEditText = findViewById(R.id.confirmPasswordEditText);
        resetPasswordButton = findViewById(R.id.resetPasswordButton);

        // Common
        loginText = findViewById(R.id.loginText);

        // Initially show step 1
        step1View.setVisibility(View.VISIBLE);
        step2View.setVisibility(View.GONE);
        step3View.setVisibility(View.GONE);
    }

    private void setupClickListeners() {
        // Step 1: Send OTP button
        sendOtpButton.setOnClickListener(v -> {
            if (validatePhoneInput()) {
                // Save input data
                phone = phoneEditText.getText().toString().trim();

                // Format phone number
                phone = ValidationUtils.formatPhone(phone);

                // Send OTP
                sendOtp();
            }
        });

        // Step 2: Verify OTP button
        verifyOtpButton.setOnClickListener(v -> {
            if (validateOtpInput()) {
                // Save input data
                otp = otpEditText.getText().toString().trim();

                // Verify OTP
                verifyOtp();
            }
        });

        // Step 3: Reset Password button
        resetPasswordButton.setOnClickListener(v -> {
            if (validatePasswordInput()) {
                // Save input data
                newPassword = passwordEditText.getText().toString().trim();

                // Reset password
                resetPassword();
            }
        });

        // Login text
        loginText.setOnClickListener(v -> {
            Intent intent = new Intent(ForgotPasswordActivity.this, LoginActivity.class);
            startActivity(intent);
            finish();
        });
    }

    private boolean validatePhoneInput() {
        boolean isValid = true;

        // Validate phone
        String phone = phoneEditText.getText().toString().trim();
        if (!ValidationUtils.isValidPhone(phone)) {
            phoneLayout.setError(getString(R.string.invalid_phone));
            isValid = false;
        } else {
            phoneLayout.setError(null);
        }

        return isValid;
    }

    private boolean validateOtpInput() {
        boolean isValid = true;

        // Get OTP length with default fallback
        int otpLength = 6; // Default OTP length
        if (sessionManager.getConfig() != null) {
            otpLength = sessionManager.getConfig().getOtpLength();
        }

        // Validate OTP
        String otp = otpEditText.getText().toString().trim();
        if (!ValidationUtils.isValidOtp(otp, otpLength)) {
            otpLayout.setError(getString(R.string.invalid_otp));
            isValid = false;
        } else {
            otpLayout.setError(null);
        }

        return isValid;
    }

    private boolean validatePasswordInput() {
        boolean isValid = true;

        // Get min password length with default fallback
        int minPasswordLength = 8; // Default minimum password length
        if (sessionManager.getConfig() != null) {
            minPasswordLength = sessionManager.getConfig().getMinPasswordLength();
        }

        // Validate password
        String password = passwordEditText.getText().toString().trim();
        if (!ValidationUtils.isValidPassword(password, minPasswordLength)) {
            passwordLayout.setError(getString(R.string.invalid_password, minPasswordLength));
            isValid = false;
        } else {
            passwordLayout.setError(null);
        }

        // Validate confirm password
        String confirmPassword = confirmPasswordEditText.getText().toString().trim();
        if (!confirmPassword.equals(password)) {
            confirmPasswordLayout.setError(getString(R.string.passwords_dont_match));
            isValid = false;
        } else {
            confirmPasswordLayout.setError(null);
        }

        return isValid;
    }

    private void sendOtp() {
        // Show loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(this, getString(R.string.sending_otp));
        sendOtpButton.setEnabled(false);

        // Make API call
        apiService.sendRegistrationOtp(phone, "", "reset_password").enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                sendOtpButton.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    try {
                        // Parse response manually
                        Gson gson = new Gson();
                        String jsonString = gson.toJson(response.body());
                        JSONObject jsonResponse = new JSONObject(jsonString);

                        boolean success = jsonResponse.optBoolean("success", false);
                        String message = jsonResponse.optString("message", "");

                        if (success) {
                            // Move to step 2
                            step1View.setVisibility(View.GONE);
                            step2View.setVisibility(View.VISIBLE);

                            // Get expires_in from data object
                            JSONObject dataObject = jsonResponse.optJSONObject("data");
                            int expiresIn = 600; // Default 10 minutes
                            if (dataObject != null) {
                                expiresIn = dataObject.optInt("expires_in", 600);
                            }

                            // Start OTP timer
                            startOtpTimer(expiresIn);

                            // Show success message
                            DialogUtils.showSuccessDialog(ForgotPasswordActivity.this,
                                getString(R.string.success),
                                message);
                        } else {
                            // Show error dialog
                            String errorMessage = message;

                            // Check for specific error messages and provide more user-friendly messages
                            if (errorMessage.contains("not registered")) {
                                errorMessage = getString(R.string.phone_not_registered);
                            } else if (errorMessage.contains("already registered")) {
                                errorMessage = getString(R.string.phone_already_registered);
                            }

                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, errorMessage);
                        }
                    } catch (Exception e) {
                        Log.e("ForgotPassword", "Error parsing OTP response", e);
                        DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.otp_send_failed));
                    }
                } else {
                    // Try to parse error response
                    try {
                        if (response.errorBody() != null) {
                            String errorBody = response.errorBody().string();
                            JSONObject errorJson = new JSONObject(errorBody);
                            String errorMessage = errorJson.optString("message", getString(R.string.otp_send_failed));

                            // Check for specific error messages
                            if (errorMessage.contains("not registered")) {
                                errorMessage = getString(R.string.phone_not_registered);
                            }

                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, errorMessage);
                        } else {
                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.otp_send_failed));
                        }
                    } catch (Exception e) {
                        DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.otp_send_failed));
                    }
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                sendOtpButton.setEnabled(true);

                // Show error dialog
                String errorMessage = t.getMessage();
                if (errorMessage != null && errorMessage.contains("Unable to resolve host")) {
                    errorMessage = getString(R.string.network_error);
                }

                DialogUtils.showErrorDialog(ForgotPasswordActivity.this, errorMessage);
            }
        });
    }

    private void verifyOtp() {
        // Show loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(this, getString(R.string.verifying_otp));
        verifyOtpButton.setEnabled(false);

        // Make API call
        apiService.verifyOtp(phone, otp, "reset_password").enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                verifyOtpButton.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    try {
                        // Parse response manually
                        Gson gson = new Gson();
                        String jsonString = gson.toJson(response.body());
                        JSONObject jsonResponse = new JSONObject(jsonString);

                        boolean success = jsonResponse.optBoolean("success", false);
                        String message = jsonResponse.optString("message", "");

                        if (success) {
                            // Show success message
                            DialogUtils.showSuccessDialog(ForgotPasswordActivity.this,
                                getString(R.string.success),
                                message);

                            // Move to step 3
                            step2View.setVisibility(View.GONE);
                            step3View.setVisibility(View.VISIBLE);
                        } else {
                            // Show error dialog
                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, message);
                        }
                    } catch (Exception e) {
                        Log.e("ForgotPassword", "Error parsing verify OTP response", e);
                        DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.otp_verification_failed));
                    }
                } else {
                    // Try to parse error response
                    try {
                        if (response.errorBody() != null) {
                            String errorBody = response.errorBody().string();
                            JSONObject errorJson = new JSONObject(errorBody);
                            String errorMessage = errorJson.optString("message", getString(R.string.otp_verification_failed));
                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, errorMessage);
                        } else {
                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.otp_verification_failed));
                        }
                    } catch (Exception e) {
                        DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.otp_verification_failed));
                    }
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                verifyOtpButton.setEnabled(true);

                // Show error dialog
                String errorMessage = t.getMessage();
                if (errorMessage != null && errorMessage.contains("Unable to resolve host")) {
                    errorMessage = getString(R.string.network_error);
                }

                DialogUtils.showErrorDialog(ForgotPasswordActivity.this, errorMessage);
            }
        });
    }

    private void resetPassword() {
        // Show loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(this, getString(R.string.resetting_password));
        resetPasswordButton.setEnabled(false);

        // Make API call
        apiService.resetPassword(phone, otp, newPassword).enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                resetPasswordButton.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    try {
                        // Parse response manually
                        Gson gson = new Gson();
                        String jsonString = gson.toJson(response.body());
                        JSONObject jsonResponse = new JSONObject(jsonString);

                        boolean success = jsonResponse.optBoolean("success", false);
                        String message = jsonResponse.optString("message", "");

                        if (success) {
                            // Show success message with action
                            DialogUtils.showSuccessDialog(
                                ForgotPasswordActivity.this,
                                getString(R.string.success),
                                message,
                                getString(R.string.login),
                                sweetAlertDialog -> {
                                    // Navigate to login activity
                                    Intent intent = new Intent(ForgotPasswordActivity.this, LoginActivity.class);
                                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                    startActivity(intent);
                                    finish();
                                    sweetAlertDialog.dismissWithAnimation();
                                });
                        } else {
                            // Show error dialog
                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, message);
                        }
                    } catch (Exception e) {
                        Log.e("ForgotPassword", "Error parsing reset password response", e);
                        DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.password_reset_failed));
                    }
                } else {
                    // Try to parse error response
                    try {
                        if (response.errorBody() != null) {
                            String errorBody = response.errorBody().string();
                            JSONObject errorJson = new JSONObject(errorBody);
                            String errorMessage = errorJson.optString("message", getString(R.string.password_reset_failed));

                            // Check for specific error messages
                            if (errorMessage.contains("Invalid or expired OTP")) {
                                errorMessage = "The OTP you entered has expired or is invalid. Please request a new OTP and try again.";
                            }

                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, errorMessage);
                        } else {
                            DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.password_reset_failed));
                        }
                    } catch (Exception e) {
                        DialogUtils.showErrorDialog(ForgotPasswordActivity.this, getString(R.string.password_reset_failed));
                    }
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                resetPasswordButton.setEnabled(true);

                // Show error dialog
                String errorMessage = t.getMessage();
                if (errorMessage != null && errorMessage.contains("Unable to resolve host")) {
                    errorMessage = getString(R.string.network_error);
                }

                DialogUtils.showErrorDialog(ForgotPasswordActivity.this, errorMessage);
            }
        });
    }

    private void startOtpTimer(int seconds) {
        // Cancel existing timer if any
        if (otpTimer != null) {
            otpTimer.cancel();
        }

        // Create new timer
        otpTimer = new CountDownTimer(seconds * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long minutes = millisUntilFinished / 60000;
                long seconds = (millisUntilFinished % 60000) / 1000;
                otpTimerText.setText(getString(R.string.otp_timer, minutes, seconds));
            }

            @Override
            public void onFinish() {
                otpTimerText.setText(R.string.otp_expired);
                sendOtpButton.setEnabled(true);
                sendOtpButton.setText(R.string.resend_otp);
            }
        }.start();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Cancel timer to prevent memory leaks
        if (otpTimer != null) {
            otpTimer.cancel();
        }

        // Dismiss any active loading dialog
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismissWithAnimation();
        }
    }
}
