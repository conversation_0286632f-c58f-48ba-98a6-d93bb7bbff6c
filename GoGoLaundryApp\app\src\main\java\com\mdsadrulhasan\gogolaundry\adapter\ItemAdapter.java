package com.mdsadrulhasan.gogolaundry.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Item;

import java.util.ArrayList;
import java.util.List;

/**
 * Adapter for displaying items in a RecyclerView
 */
public class ItemAdapter extends RecyclerView.Adapter<ItemAdapter.ItemViewHolder> {

    private List<Item> items;
    private final ItemClickListener listener;

    /**
     * Interface for handling item clicks
     */
    public interface ItemClickListener {
        void onItemClicked(Item item);
        void onAddToCartClicked(Item item, int position);
    }

    /**
     * Constructor
     *
     * @param items List of items
     * @param listener Click listener
     */
    public ItemAdapter(List<Item> items, ItemClickListener listener) {
        this.items = items;
        this.listener = listener;
    }

    /**
     * Update items list
     *
     * @param newItems New list of items
     */
    public void updateItems(List<Item> newItems) {
        if (newItems == null) {
            this.items = new ArrayList<>();
        } else {
            this.items = new ArrayList<>(newItems);
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_laundry_item, parent, false);
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        if (items != null && position < items.size()) {
            Item item = items.get(position);
            holder.bind(item, position);
        }
    }

    @Override
    public int getItemCount() {
        return items != null ? items.size() : 0;
    }

    /**
     * ViewHolder for items
     */
    class ItemViewHolder extends RecyclerView.ViewHolder {

        private final ImageView itemImage;
        private final TextView itemName;
        private final TextView itemDescription;
        private final TextView itemPrice;
        private final Button addToCartButton;
        private final LinearLayout descriptionContainer;
        private final TextView expandCollapseText;

        private boolean isExpanded = false;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            itemImage = itemView.findViewById(R.id.item_image);
            itemName = itemView.findViewById(R.id.item_name);
            itemDescription = itemView.findViewById(R.id.item_description);
            itemPrice = itemView.findViewById(R.id.item_price);
            addToCartButton = itemView.findViewById(R.id.add_to_cart_button);
            descriptionContainer = itemView.findViewById(R.id.description_container);
            expandCollapseText = itemView.findViewById(R.id.expand_collapse_text);
        }

        /**
         * Bind item data to views
         *
         * @param item Item to bind
         * @param position Position in adapter
         */
        public void bind(final Item item, final int position) {
            // Set item name
            itemName.setText(item.getName());

            // Set description with expandable functionality
            setupDescription(item.getDescription());

            // Set price
            itemPrice.setText(item.getFormattedPrice());

            // Load image if available
            if (item.getImageUrl() != null && !item.getImageUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(item.getImageUrl())
                        .apply(new RequestOptions()
                                .placeholder(R.drawable.placeholder_image)
                                .error(R.drawable.placeholder_image))
                        .into(itemImage);
                itemImage.setVisibility(View.VISIBLE);
            } else {
                itemImage.setVisibility(View.GONE);
            }

            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClicked(item);
                }
            });

            // Setup add to cart button with dynamic text
            setupAddToCartButton(item, position);
        }

        /**
         * Setup description with expandable functionality
         *
         * @param description Item description
         */
        private void setupDescription(String description) {
            if (description == null || description.isEmpty()) {
                descriptionContainer.setVisibility(View.GONE);
                return;
            }

            descriptionContainer.setVisibility(View.VISIBLE);
            itemDescription.setText(description);

            // Reset expansion state
            isExpanded = false;
            itemDescription.setMaxLines(3);

            // Check if text needs expansion
            itemDescription.post(() -> {
                if (itemDescription.getLineCount() > 3) {
                    expandCollapseText.setVisibility(View.VISIBLE);
                    expandCollapseText.setText("Show more");
                    setupExpandCollapseListener(description);
                } else {
                    expandCollapseText.setVisibility(View.GONE);
                }
            });
        }

        /**
         * Setup expand/collapse click listener
         *
         * @param fullDescription Full description text
         */
        private void setupExpandCollapseListener(String fullDescription) {
            View.OnClickListener expandCollapseListener = v -> {
                if (isExpanded) {
                    // Collapse
                    itemDescription.setMaxLines(3);
                    expandCollapseText.setText("Show more");
                    isExpanded = false;
                } else {
                    // Expand
                    itemDescription.setMaxLines(Integer.MAX_VALUE);
                    expandCollapseText.setText("Show less");
                    isExpanded = true;
                }
            };

            expandCollapseText.setOnClickListener(expandCollapseListener);
            descriptionContainer.setOnClickListener(expandCollapseListener);
        }

        /**
         * Setup add to cart button with dynamic text and functionality
         *
         * @param item Laundry item
         * @param position Item position
         */
        private void setupAddToCartButton(Item item, int position) {
            // Set button text based on current language or item status
            updateAddToCartButtonText(item);

            // Set click listener
            addToCartButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onAddToCartClicked(item, position);

                    // Optional: Provide visual feedback
                    addToCartButton.setText(itemView.getContext().getString(R.string.add_to_cart_short));

                    // You can add animation or temporary text change here
                    // For example: briefly show "Added!" then revert back
                    addToCartButton.postDelayed(() -> {
                        updateAddToCartButtonText(item);
                    }, 1000);
                }
            });
        }

        /**
         * Update add to cart button text based on item status or language
         *
         * @param item Laundry item
         */
        private void updateAddToCartButtonText(Item item) {
            Context context = itemView.getContext();

            // Check if item is out of stock or inactive
            if (!item.isInStock() || !item.isActive()) {
                addToCartButton.setText(context.getString(R.string.out_of_stock));
                addToCartButton.setEnabled(false);
                addToCartButton.setAlpha(0.6f);
                return;
            }

            // Enable button and reset alpha
            addToCartButton.setEnabled(true);
            addToCartButton.setAlpha(1.0f);

            // Set text based on current language preference
            // The system will automatically use the correct language resource
            addToCartButton.setText(context.getString(R.string.add_to_cart_short));
        }
    }
}
