<?php
/**
 * FCM Token Registration API
 *
 * This endpoint handles registration of FCM tokens for both mobile and web clients
 *
 * Required parameters:
 * - user_id: User ID
 * - token: FCM token
 * - device_id: Unique device identifier
 * - device_type: android, ios, or web
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Include required files
try {
    require_once '../../config/db.php';
    require_once '../../includes/functions.php';
    require_once '../../includes/FCMService.php';
} catch (Exception $e) {
    error_log('FCM Token Registration - Include Error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server configuration error: ' . $e->getMessage()]);
    exit();
}

// Initialize response
$response = [
    'success' => false,
    'message' => 'Unknown error occurred',
    'data' => null
];

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    // If JSON input is empty, try POST data
    if (empty($input)) {
        $input = $_POST;
    }

    // Validate required parameters
    $requiredFields = ['user_id', 'token', 'device_id'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            $response['message'] = "Missing required field: $field";
            echo json_encode($response);
            exit();
        }
    }

    $userId = intval($input['user_id']);
    $token = trim($input['token']);
    $deviceId = trim($input['device_id']);
    $deviceType = isset($input['device_type']) ? trim($input['device_type']) : 'android';

    // Validate device type
    $allowedDeviceTypes = ['android', 'ios', 'web'];
    if (!in_array($deviceType, $allowedDeviceTypes)) {
        $response['message'] = 'Invalid device type. Allowed: ' . implode(', ', $allowedDeviceTypes);
        echo json_encode($response);
        exit();
    }

    // Validate user exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND is_verified = 1");
    $stmt->execute([$userId]);

    if ($stmt->rowCount() === 0) {
        $response['message'] = 'User not found or not verified';
        echo json_encode($response);
        exit();
    }

    // Initialize FCM service
    $fcmService = new FCMService();

    // Register the token
    $result = $fcmService->registerToken($pdo, $userId, $token, $deviceId, $deviceType);

    if ($result['success']) {
        $response['success'] = true;
        $response['message'] = 'FCM token registered successfully';
        $response['data'] = [
            'user_id' => $userId,
            'device_id' => $deviceId,
            'device_type' => $deviceType,
            'registered_at' => date('Y-m-d H:i:s')
        ];

        // Log the registration
        error_log("FCM Token registered for user $userId, device $deviceId ($deviceType)");
    } else {
        $response['message'] = $result['error'] ?? 'Failed to register FCM token';
    }

} catch (Exception $e) {
    $response['message'] = 'Server error: ' . $e->getMessage();
    error_log('FCM Token Registration Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
?>
