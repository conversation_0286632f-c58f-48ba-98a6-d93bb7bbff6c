package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.util.Log;

import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;

import org.json.JSONObject;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Helper class for handling FCM notifications in the app
 * This class provides methods to trigger notification sending from the backend
 */
public class NotificationHelper {
    
    private static final String TAG = "NotificationHelper";
    private final Context context;
    private final ApiService apiService;

    public NotificationHelper(Context context) {
        this.context = context;
        this.apiService = ApiClient.getApiService(context);
    }

    /**
     * Interface for notification callbacks
     */
    public interface NotificationCallback {
        void onSuccess(String message);
        void onError(String error);
    }

    /**
     * Send order creation notification to the user
     * This is typically called after successful order creation
     *
     * @param orderId The ID of the created order
     * @param userId The ID of the user who placed the order
     * @param orderNumber The order number
     * @param total The total amount of the order
     * @param callback Callback for success/error handling
     */
    public void sendOrderCreationNotification(int orderId, int userId, String orderNumber, 
                                            double total, NotificationCallback callback) {
        
        Log.d(TAG, "Sending order creation notification for order: " + orderNumber);
        
        try {
            // Create notification data
            JSONObject notificationData = new JSONObject();
            notificationData.put("title", "Order Placed Successfully");
            notificationData.put("message", "Your order #" + orderNumber + " has been placed successfully. Total: ৳" + String.format("%.2f", total));
            notificationData.put("type", "order_status");
            notificationData.put("user_id", userId);
            notificationData.put("order_id", orderId);
            
            // Additional data
            JSONObject additionalData = new JSONObject();
            additionalData.put("order_number", orderNumber);
            additionalData.put("status", "placed");
            additionalData.put("total", total);
            additionalData.put("timestamp", System.currentTimeMillis() / 1000);
            notificationData.put("data", additionalData);

            // Send notification request to backend
            sendNotificationRequest(notificationData, callback);
            
        } catch (Exception e) {
            Log.e(TAG, "Error creating order notification data: " + e.getMessage(), e);
            if (callback != null) {
                callback.onError("Failed to prepare notification data: " + e.getMessage());
            }
        }
    }

    /**
     * Send order status update notification to the user
     * This is typically called when order status changes
     *
     * @param orderId The ID of the order
     * @param userId The ID of the user who owns the order
     * @param orderNumber The order number
     * @param newStatus The new status of the order
     * @param notes Optional notes about the status update
     * @param callback Callback for success/error handling
     */
    public void sendOrderStatusUpdateNotification(int orderId, int userId, String orderNumber, 
                                                String newStatus, String notes, NotificationCallback callback) {
        
        Log.d(TAG, "Sending order status update notification for order: " + orderNumber + ", status: " + newStatus);
        
        try {
            // Create status-specific messages
            String message = getStatusMessage(newStatus);
            if (notes != null && !notes.trim().isEmpty()) {
                message += ". Note: " + notes;
            }

            // Create notification data
            JSONObject requestData = new JSONObject();
            requestData.put("order_id", orderId);
            requestData.put("status", newStatus);
            requestData.put("user_id", userId);
            if (notes != null && !notes.trim().isEmpty()) {
                requestData.put("notes", notes);
            }

            // Send status notification request to backend
            sendStatusNotificationRequest(requestData, callback);
            
        } catch (Exception e) {
            Log.e(TAG, "Error creating status notification data: " + e.getMessage(), e);
            if (callback != null) {
                callback.onError("Failed to prepare status notification data: " + e.getMessage());
            }
        }
    }

    /**
     * Get user-friendly message for order status
     */
    private String getStatusMessage(String status) {
        switch (status) {
            case "placed":
                return "Your order has been placed successfully";
            case "confirmed":
                return "Your order has been confirmed and is being prepared";
            case "pickup_scheduled":
                return "Pickup has been scheduled for your order";
            case "picked_up":
                return "Your order has been picked up and is on its way to our facility";
            case "processing":
                return "Your order is currently being processed";
            case "ready_for_delivery":
                return "Your order is ready for delivery";
            case "out_for_delivery":
                return "Your order is out for delivery and will arrive soon";
            case "delivered":
                return "Your order has been delivered successfully";
            case "cancelled":
                return "Your order has been cancelled";
            default:
                return "Your order status has been updated to: " + status;
        }
    }

    /**
     * Send notification request to the backend FCM service
     */
    private void sendNotificationRequest(JSONObject notificationData, NotificationCallback callback) {
        try {
            // Extract data from JSON
            String title = notificationData.getString("title");
            String message = notificationData.getString("message");
            String type = notificationData.getString("type");
            Integer userId = notificationData.has("user_id") ? notificationData.getInt("user_id") : null;
            Integer orderId = notificationData.has("order_id") ? notificationData.getInt("order_id") : null;
            String additionalData = notificationData.has("data") ? notificationData.getJSONObject("data").toString() : null;

            Log.d(TAG, "Sending notification request - Title: " + title + ", Type: " + type);

            // Send notification via API
            Call<ApiResponse<Object>> call = apiService.sendNotification(
                title, message, type, userId, orderId, additionalData
            );

            call.enqueue(new Callback<ApiResponse<Object>>() {
                @Override
                public void onResponse(Call<ApiResponse<Object>> call, Response<ApiResponse<Object>> response) {
                    if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                        Log.d(TAG, "Notification sent successfully: " + response.body().getMessage());
                        if (callback != null) {
                            callback.onSuccess(response.body().getMessage());
                        }
                    } else {
                        String errorMsg = response.body() != null ? response.body().getMessage() : "Unknown error";
                        Log.e(TAG, "Failed to send notification: " + errorMsg);
                        if (callback != null) {
                            callback.onError("Failed to send notification: " + errorMsg);
                        }
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<Object>> call, Throwable t) {
                    Log.e(TAG, "Network error sending notification: " + t.getMessage(), t);
                    if (callback != null) {
                        callback.onError("Network error: " + t.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error sending notification request: " + e.getMessage(), e);
            if (callback != null) {
                callback.onError("Failed to send notification request: " + e.getMessage());
            }
        }
    }

    /**
     * Send order status notification request to the backend
     */
    private void sendStatusNotificationRequest(JSONObject requestData, NotificationCallback callback) {
        try {
            // Extract data from JSON
            int orderId = requestData.getInt("order_id");
            String status = requestData.getString("status");
            int userId = requestData.getInt("user_id");
            String notes = requestData.has("notes") ? requestData.getString("notes") : "";

            Log.d(TAG, "Sending status notification request - Order ID: " + orderId + ", Status: " + status);

            // Send status notification via API
            Call<ApiResponse<Object>> call = apiService.sendOrderStatusNotification(
                orderId, status, userId, notes, null, "app"
            );

            call.enqueue(new Callback<ApiResponse<Object>>() {
                @Override
                public void onResponse(Call<ApiResponse<Object>> call, Response<ApiResponse<Object>> response) {
                    if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                        Log.d(TAG, "Status notification sent successfully: " + response.body().getMessage());
                        if (callback != null) {
                            callback.onSuccess(response.body().getMessage());
                        }
                    } else {
                        String errorMsg = response.body() != null ? response.body().getMessage() : "Unknown error";
                        Log.e(TAG, "Failed to send status notification: " + errorMsg);
                        if (callback != null) {
                            callback.onError("Failed to send status notification: " + errorMsg);
                        }
                    }
                }

                @Override
                public void onFailure(Call<ApiResponse<Object>> call, Throwable t) {
                    Log.e(TAG, "Network error sending status notification: " + t.getMessage(), t);
                    if (callback != null) {
                        callback.onError("Network error: " + t.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error sending status notification request: " + e.getMessage(), e);
            if (callback != null) {
                callback.onError("Failed to send status notification request: " + e.getMessage());
            }
        }
    }

    /**
     * Check if FCM token is registered for the current user
     */
    public boolean isFCMTokenRegistered() {
        SessionManager sessionManager = new SessionManager(context);
        String fcmToken = sessionManager.getFCMToken();
        return fcmToken != null && !fcmToken.isEmpty();
    }

    /**
     * Get the current FCM token
     */
    public String getFCMToken() {
        SessionManager sessionManager = new SessionManager(context);
        return sessionManager.getFCMToken();
    }

    /**
     * Log notification event for debugging
     */
    public void logNotificationEvent(String event, String details) {
        Log.d(TAG, "Notification Event: " + event + " - " + details);
    }
}
