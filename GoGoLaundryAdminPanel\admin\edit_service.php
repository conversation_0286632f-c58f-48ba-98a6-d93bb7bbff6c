<?php
/**
 * Edit Service
 *
 * This page allows administrators to edit an existing laundry service
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/ServiceManager.php';

// Initialize service manager
$serviceManager = new ServiceManager($pdo);

// Check if service ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'Invalid service ID.';
    header('Location: services.php');
    exit;
}

$serviceId = (int)$_GET['id'];

// Get service data
$service = $serviceManager->getServiceById($serviceId);

// Check if service exists
if (!$service) {
    $_SESSION['error_message'] = 'Service not found.';
    header('Location: services.php');
    exit;
}

// Initialize variables
$name = $service['name'];
$bnName = $service['bn_name'];
$description = $service['description'];
$bnDescription = $service['bn_description'];
$imageUrl = $service['image_url'];
$sortOrder = $service['sort_order'];
$isActive = $service['is_active'];
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_service'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error_message'] = 'Invalid security token. Please try again.';
        header('Location: edit_service.php?id=' . $serviceId);
        exit;
    }

    // Get form data
    $name = trim($_POST['name'] ?? '');
    $bnName = trim($_POST['bn_name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $bnDescription = trim($_POST['bn_description'] ?? '');
    $imageUrl = trim($_POST['image_url'] ?? '');
    $sortOrder = isset($_POST['sort_order']) ? (int)$_POST['sort_order'] : 0;
    $isActive = isset($_POST['is_active']) && $_POST['is_active'] === '1';

    // Validate form data
    if (empty($name)) {
        $errors['name'] = 'Service name is required.';
    } elseif (strlen($name) > 100) {
        $errors['name'] = 'Service name cannot exceed 100 characters.';
    }

    if (!empty($bnName) && strlen($bnName) > 100) {
        $errors['bn_name'] = 'Bengali name cannot exceed 100 characters.';
    }

    if (strlen($description) > 500) {
        $errors['description'] = 'Description cannot exceed 500 characters.';
    }

    if (!empty($imageUrl) && strlen($imageUrl) > 255) {
        $errors['image_url'] = 'Image URL cannot exceed 255 characters.';
    }

    // If no errors, update service
    if (empty($errors)) {
        $result = $serviceManager->updateService($serviceId, $name, $description, $isActive, $bnName, $bnDescription, $imageUrl, $sortOrder);

        if ($result) {
            $_SESSION['success_message'] = 'Service updated successfully.';
            header('Location: services.php');
            exit;
        } else {
            $_SESSION['error_message'] = 'Failed to update service. Please try again.';
        }
    }
}

// Set page title
$pageTitle = 'Edit Service';
?>

<?php include 'includes/header.php'; ?>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="services.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Services
                        </a>
                    </div>
                </div>

                <?php include 'includes/alerts.php'; ?>

                <!-- Edit Service Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Service Information</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="edit_service.php?id=<?php echo $serviceId; ?>">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                            <div class="mb-3">
                                <label for="name" class="form-label">Service Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['name']) ? 'is-invalid' : ''; ?>"
                                       id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                                <?php if (isset($errors['name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['name']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="bn_name" class="form-label">Bengali Name</label>
                                <input type="text" class="form-control <?php echo isset($errors['bn_name']) ? 'is-invalid' : ''; ?>"
                                       id="bn_name" name="bn_name" value="<?php echo htmlspecialchars($bnName); ?>">
                                <?php if (isset($errors['bn_name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['bn_name']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>"
                                          id="description" name="description" rows="3"><?php echo htmlspecialchars($description); ?></textarea>
                                <?php if (isset($errors['description'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="bn_description" class="form-label">Bengali Description</label>
                                <textarea class="form-control <?php echo isset($errors['bn_description']) ? 'is-invalid' : ''; ?>"
                                          id="bn_description" name="bn_description" rows="3"><?php echo htmlspecialchars($bnDescription); ?></textarea>
                                <?php if (isset($errors['bn_description'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['bn_description']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="image_url" class="form-label">Image URL</label>
                                <input type="text" class="form-control <?php echo isset($errors['image_url']) ? 'is-invalid' : ''; ?>"
                                       id="image_url" name="image_url" value="<?php echo htmlspecialchars($imageUrl); ?>">
                                <?php if (isset($errors['image_url'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['image_url']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="sort_order" class="form-label">Sort Order</label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order"
                                       value="<?php echo htmlspecialchars($sortOrder); ?>">
                                <small class="form-text text-muted">Lower numbers appear first</small>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1"
                                       <?php echo $isActive ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>

                            <button type="submit" name="update_service" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Service
                            </button>
                        </form>
                    </div>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>
