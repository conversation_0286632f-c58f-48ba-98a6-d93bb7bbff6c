<?php
/**
 * NotificationManager class
 *
 * Handles notification-related operations
 */
class NotificationManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get notifications for a user
     *
     * @param int $userId User ID
     * @param int $limit Maximum number of notifications to return
     * @param int $offset Offset for pagination
     * @param bool $onlyUnread Whether to return only unread notifications (default: true)
     * @return array Notifications
     */
    public function getNotificationsForUser($userId, $limit = 50, $offset = 0, $onlyUnread = true) {
        // Build the query based on whether we want only unread notifications
        $whereClause = "WHERE n.user_id = ?";
        $params = [$userId];

        // Add is_read=0 condition if we only want unread notifications
        if ($onlyUnread) {
            $whereClause .= " AND n.is_read = 0";
        }

        $stmt = $this->pdo->prepare("
            SELECT n.*, o.order_number
            FROM notifications n
            LEFT JOIN orders o ON n.order_id = o.id
            $whereClause
            ORDER BY n.created_at DESC
            LIMIT ? OFFSET ?
        ");

        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get unread notifications count for a user
     *
     * @param int $userId User ID
     * @return int Count of unread notifications
     */
    public function getUnreadNotificationsCount($userId) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM notifications
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int)$result['count'];
    }

    /**
     * Mark a notification as read
     *
     * @param int $notificationId Notification ID
     * @param int $userId User ID for verification
     * @return bool True if successful, false otherwise
     */
    public function markAsRead($notificationId, $userId) {
        // First verify the notification belongs to the user
        $stmt = $this->pdo->prepare("
            SELECT id
            FROM notifications
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$notificationId, $userId]);

        if ($stmt->rowCount() === 0) {
            return false;
        }

        // Mark as read
        $stmt = $this->pdo->prepare("
            UPDATE notifications
            SET is_read = 1
            WHERE id = ?
        ");
        $stmt->execute([$notificationId]);

        return $stmt->rowCount() > 0;
    }

    /**
     * Mark all notifications as read for a user
     *
     * @param int $userId User ID
     * @return int Number of notifications marked as read
     */
    public function markAllAsRead($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE notifications
            SET is_read = 1
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$userId]);

        return $stmt->rowCount();
    }

    /**
     * Create a notification
     *
     * @param int $userId User ID
     * @param int|null $orderId Order ID (optional)
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type (order_status, promo, system, custom)
     * @param bool $sendFcm Whether to send FCM notification
     * @param bool $sendSms Whether to send SMS notification
     * @return int|false Notification ID if successful, false otherwise
     */
    public function createNotification($userId, $orderId, $title, $message, $type = 'system', $sendFcm = false, $sendSms = false) {
        $stmt = $this->pdo->prepare("
            INSERT INTO notifications (user_id, order_id, title, message, type, fcm_sent, sms_sent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $fcmSent = $sendFcm ? 0 : 0;
        $smsSent = $sendSms ? 0 : 0;

        $stmt->execute([$userId, $orderId, $title, $message, $type, $fcmSent, $smsSent]);

        if ($stmt->rowCount() > 0) {
            return $this->pdo->lastInsertId();
        }

        return false;
    }

    /**
     * Send notification to all users
     *
     * @param string $title Notification title
     * @param string $message Notification message
     * @param string $type Notification type
     * @param int|null $orderId Order ID (optional)
     * @param bool $sendFcm Whether to send FCM notification
     * @param bool $sendSms Whether to send SMS notification
     * @return int Number of notifications created
     */
    public function sendToAllUsers($title, $message, $type = 'system', $orderId = null, $sendFcm = false, $sendSms = false) {
        // Get all verified users
        $stmt = $this->pdo->prepare("SELECT id FROM users WHERE is_verified = 1");
        $stmt->execute();
        $userIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $count = 0;

        // Insert notification for each user
        $stmt = $this->pdo->prepare("
            INSERT INTO notifications (user_id, order_id, title, message, type, fcm_sent, sms_sent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $fcmSent = $sendFcm ? 0 : 0;
        $smsSent = $sendSms ? 0 : 0;

        foreach ($userIds as $userId) {
            $stmt->execute([$userId, $orderId, $title, $message, $type, $fcmSent, $smsSent]);
            if ($stmt->rowCount() > 0) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Delete a notification
     *
     * @param int $notificationId Notification ID
     * @return bool True if successful, false otherwise
     */
    public function deleteNotification($notificationId) {
        $stmt = $this->pdo->prepare("DELETE FROM notifications WHERE id = ?");
        $stmt->execute([$notificationId]);

        return $stmt->rowCount() > 0;
    }
}
