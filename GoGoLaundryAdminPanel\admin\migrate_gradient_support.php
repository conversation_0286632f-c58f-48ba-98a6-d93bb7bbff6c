<?php
require_once '../config/config.php';
require_once '../config/db.php';

echo "🚀 Starting gradient support migration for promotional_dialogs table...\n\n";

try {
    // Check if gradient columns already exist
    $checkColumns = $pdo->query("SHOW COLUMNS FROM promotional_dialogs LIKE 'background_type'");
    if ($checkColumns->rowCount() > 0) {
        echo "ℹ️ Gradient columns already exist. Migration not needed.\n";
        exit;
    }

    // Add gradient support columns
    $alterSql = "
    ALTER TABLE promotional_dialogs
    ADD COLUMN background_type ENUM('solid', 'gradient') DEFAULT 'solid' AFTER button_color,
    ADD COLUMN gradient_color1 VARCHAR(7) DEFAULT NULL AFTER background_type,
    ADD COLUMN gradient_color2 VARCHAR(7) DEFAULT NULL AFTER gradient_color1,
    ADD COLUMN gradient_direction VARCHAR(10) DEFAULT '45deg' AFTER gradient_color2
    ";

    $pdo->exec($alterSql);
    echo "✅ Successfully added gradient support columns:\n";
    echo "   - background_type (solid/gradient)\n";
    echo "   - gradient_color1 (start color)\n";
    echo "   - gradient_color2 (end color)\n";
    echo "   - gradient_direction (gradient direction)\n\n";

    // Update existing records to use solid background type
    $updateSql = "UPDATE promotional_dialogs SET background_type = 'solid' WHERE background_type IS NULL";
    $pdo->exec($updateSql);
    echo "✅ Updated existing records to use solid background type.\n\n";

    // Insert a sample gradient promotional dialog
    $sampleGradientSql = "
    INSERT INTO promotional_dialogs (
        title, subtitle, description, discount_text, promo_code, button_text,
        background_type, gradient_color1, gradient_color2, gradient_direction,
        text_color, button_color, is_active, start_date, end_date
    ) VALUES (
        'GRADIENT SPECIAL',
        'Beautiful Gradient Backgrounds',
        'Experience our new gradient promotional dialogs with stunning visual effects!',
        'NEW\nGRADIENT\nSTYLE',
        'GRADIENT20',
        'Try Now',
        'gradient',
        '#ff7e5f',
        '#feb47b',
        '45deg',
        '#ffffff',
        '#ffd700',
        1,
        NOW(),
        DATE_ADD(NOW(), INTERVAL 30 DAY)
    )
    ";

    $pdo->exec($sampleGradientSql);
    echo "✅ Added sample gradient promotional dialog.\n\n";

    echo "🎉 Migration completed successfully!\n";
    echo "📝 You can now create promotional dialogs with gradient backgrounds.\n";

} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
