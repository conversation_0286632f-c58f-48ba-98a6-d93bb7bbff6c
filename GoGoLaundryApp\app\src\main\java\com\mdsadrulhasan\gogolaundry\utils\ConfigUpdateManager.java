package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;


import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.AppConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Manager class for handling automatic configuration updates
 */
public class ConfigUpdateManager {
    private static final String TAG = "ConfigUpdateManager";
    private static final long DEFAULT_UPDATE_INTERVAL = 60; // seconds
    private static ConfigUpdateManager instance;

    private final Context context;
    private final SessionManager sessionManager;
    private ScheduledExecutorService scheduler; // No longer final so we can recreate it
    private final Handler mainHandler;
    private final List<ConfigUpdateListener> listeners;
    private boolean isRunning = false;
    private long updateInterval = DEFAULT_UPDATE_INTERVAL;

    /**
     * Interface for configuration update listeners
     */
    public interface ConfigUpdateListener {
        /**
         * Called when configuration is updated
         *
         * @param oldConfig Previous configuration
         * @param newConfig New configuration
         */
        void onConfigUpdated(AppConfig oldConfig, AppConfig newConfig);
    }

    /**
     * Private constructor to enforce singleton pattern
     *
     * @param context Application context
     */
    private ConfigUpdateManager(Context context) {
        this.context = context.getApplicationContext();
        this.sessionManager = new SessionManager(context);
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.listeners = new ArrayList<>();
    }

    /**
     * Create a new scheduler if needed
     */
    private void ensureSchedulerIsActive() {
        if (scheduler == null || scheduler.isShutdown() || scheduler.isTerminated()) {
            Log.d(TAG, "Creating new scheduler as previous one was terminated");
            scheduler = Executors.newSingleThreadScheduledExecutor();
        }
    }

    /**
     * Get singleton instance
     *
     * @param context Context
     * @return ConfigUpdateManager instance
     */
    public static synchronized ConfigUpdateManager getInstance(Context context) {
        if (instance == null) {
            instance = new ConfigUpdateManager(context);
        } else {
            // Ensure the scheduler is active even for existing instances
            instance.ensureSchedulerIsActive();
        }
        return instance;
    }

    /**
     * Set update interval
     *
     * @param seconds Interval in seconds
     */
    public void setUpdateInterval(long seconds) {
        this.updateInterval = seconds;
        if (isRunning) {
            try {
                // Restart with new interval
                stop();
                // Small delay to ensure the scheduler is fully stopped
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    // Ignore
                }
                start();
            } catch (Exception e) {
                Log.e(TAG, "Error updating interval: " + e.getMessage());
                // Reset running state in case of error
                isRunning = false;
            }
        }
    }

    /**
     * Add configuration update listener
     *
     * @param listener Listener to add
     */
    public void addListener(ConfigUpdateListener listener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener);
        }
    }

    /**
     * Remove configuration update listener
     *
     * @param listener Listener to remove
     */
    public void removeListener(ConfigUpdateListener listener) {
        listeners.remove(listener);
    }

    /**
     * Check if a listener is already registered
     *
     * @param listener Listener to check
     * @return true if the listener is already registered, false otherwise
     */
    public boolean hasListener(ConfigUpdateListener listener) {
        return listener != null && listeners.contains(listener);
    }

    /**
     * Start periodic configuration updates
     */
    public void start() {
        if (isRunning) {
            return;
        }

        Log.d(TAG, "Starting configuration update service with interval: " + updateInterval + " seconds");

        // Make sure we have an active scheduler
        ensureSchedulerIsActive();

        isRunning = true;

        try {
            // Schedule periodic updates
            scheduler.scheduleAtFixedRate(this::checkForUpdates, 0, updateInterval, TimeUnit.SECONDS);
        } catch (Exception e) {
            Log.e(TAG, "Error starting scheduler: " + e.getMessage());
            // Try to recover by creating a new scheduler and trying again
            scheduler = Executors.newSingleThreadScheduledExecutor();
            scheduler.scheduleAtFixedRate(this::checkForUpdates, 0, updateInterval, TimeUnit.SECONDS);
        }
    }

    /**
     * Stop periodic configuration updates
     */
    public void stop() {
        if (!isRunning) {
            return;
        }

        Log.d(TAG, "Stopping configuration update service");
        isRunning = false;

        try {
            if (scheduler != null && !scheduler.isShutdown()) {
                scheduler.shutdownNow();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error stopping scheduler: " + e.getMessage());
        }
    }

    /**
     * Force an immediate configuration check
     */
    public void checkNow() {
        // Make sure we have an active scheduler before checking
        ensureSchedulerIsActive();
        checkForUpdates();
    }

    /**
     * Check for configuration updates
     */
    private void checkForUpdates() {
        Log.d(TAG, "Checking for configuration updates");

        // Get current configuration
        final AppConfig oldConfig = sessionManager.getConfig();

        // Force refresh the API client
        ApiClient.forceRefreshApiClient();

        // Fetch configuration from server
        sessionManager.fetchConfigFromServer(new SessionManager.Callback() {
            @Override
            public void onSuccess() {
                // Get new configuration
                AppConfig newConfig = sessionManager.getConfig();

                // Check if configuration has changed
                if (hasConfigChanged(oldConfig, newConfig)) {
                    Log.d(TAG, "Configuration updated: OTP is now " +
                          (newConfig.isOtpEnabled() ? "enabled" : "disabled"));

                    // Notify listeners on main thread
                    notifyListeners(oldConfig, newConfig);
                } else {
                    Log.d(TAG, "No configuration changes detected");
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Error fetching configuration: " + errorMessage);
            }
        });
    }

    /**
     * Check if configuration has changed
     *
     * @param oldConfig Old configuration
     * @param newConfig New configuration
     * @return True if configuration has changed
     */
    private boolean hasConfigChanged(AppConfig oldConfig, AppConfig newConfig) {
        if (oldConfig == null || newConfig == null) {
            return true;
        }

        // Check if OTP status has changed
        return oldConfig.isOtpEnabled() != newConfig.isOtpEnabled();
    }

    /**
     * Notify listeners of configuration update
     *
     * @param oldConfig Old configuration
     * @param newConfig New configuration
     */
    private void notifyListeners(final AppConfig oldConfig, final AppConfig newConfig) {
        mainHandler.post(() -> {
            // Create a copy of the listeners list to avoid ConcurrentModificationException
            List<ConfigUpdateListener> listenersCopy = new ArrayList<>(listeners);

            for (ConfigUpdateListener listener : listenersCopy) {
                try {
                    listener.onConfigUpdated(oldConfig, newConfig);
                } catch (Exception e) {
                    Log.e(TAG, "Error notifying listener: " + e.getMessage());
                    // Remove problematic listener to prevent future errors
                    listeners.remove(listener);
                }
            }
        });
    }
}
