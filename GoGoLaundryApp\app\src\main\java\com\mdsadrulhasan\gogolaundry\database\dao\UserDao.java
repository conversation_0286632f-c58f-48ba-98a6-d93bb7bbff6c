package com.mdsadrulhasan.gogolaundry.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.mdsadrulhasan.gogolaundry.database.entity.UserEntity;

/**
 * Data Access Object for User entity
 */
@Dao
public interface UserDao {
    
    /**
     * Insert a user
     * 
     * @param user User to insert
     * @return ID of inserted user
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(UserEntity user);
    
    /**
     * Update a user
     * 
     * @param user User to update
     */
    @Update
    void update(UserEntity user);
    
    /**
     * Get user by ID
     * 
     * @param id User ID
     * @return User with the given ID
     */
    @Query("SELECT * FROM users WHERE id = :id")
    UserEntity getUserById(int id);
    
    /**
     * Get user by ID as LiveData
     * 
     * @param id User ID
     * @return LiveData of user with the given ID
     */
    @Query("SELECT * FROM users WHERE id = :id")
    LiveData<UserEntity> getUserByIdLive(int id);
    
    /**
     * Get user by phone number
     * 
     * @param phone Phone number
     * @return User with the given phone number
     */
    @Query("SELECT * FROM users WHERE phone = :phone")
    UserEntity getUserByPhone(String phone);
    
    /**
     * Delete all users
     */
    @Query("DELETE FROM users")
    void deleteAll();
}
