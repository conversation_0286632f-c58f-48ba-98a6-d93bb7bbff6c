package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;
import com.mdsadrulhasan.gogolaundry.ui.adapter.ShopServiceAdapter;
import com.mdsadrulhasan.gogolaundry.viewmodel.ShopDetailsViewModel;

import java.util.ArrayList;

/**
 * Fragment for displaying shop services
 */
public class ShopServicesFragment extends Fragment implements ShopServiceAdapter.OnServiceClickListener {

    private static final String TAG = "ShopServicesFragment";
    private static final String ARG_SHOP_ID = "shop_id";

    private RecyclerView servicesRecyclerView;
    private TextView emptyTextView;
    private ShopDetailsViewModel viewModel;
    private ShopServiceAdapter adapter;
    private int shopId;

    public static ShopServicesFragment newInstance(int shopId) {
        Log.d(TAG, "Creating new ShopServicesFragment instance with shopId: " + shopId);
        ShopServicesFragment fragment = new ShopServicesFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_SHOP_ID, shopId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            shopId = getArguments().getInt(ARG_SHOP_ID);
        }
        Log.d(TAG, "onCreate() - shopId: " + shopId);

        // Get shared ViewModel from parent fragment
        try {
            viewModel = new ViewModelProvider(requireParentFragment()).get(ShopDetailsViewModel.class);
            Log.d(TAG, "ViewModel obtained successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error getting ViewModel from parent fragment", e);
            // Fallback: try to get from activity
            try {
                viewModel = new ViewModelProvider(requireActivity()).get(ShopDetailsViewModel.class);
                Log.d(TAG, "ViewModel obtained from activity as fallback");
            } catch (Exception e2) {
                Log.e(TAG, "Error getting ViewModel from activity", e2);
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView() called");
        View view = inflater.inflate(R.layout.fragment_shop_services, container, false);

        initializeViews(view);
        setupRecyclerView();
        observeViewModel();

        Log.d(TAG, "onCreateView() completed successfully");
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d(TAG, "onViewCreated() called - fragment is now attached and view is created");
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume() called - fragment is now visible to user");
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause() called");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView() called");
    }



    private void initializeViews(View view) {
        servicesRecyclerView = view.findViewById(R.id.servicesRecyclerView);
        emptyTextView = view.findViewById(R.id.emptyTextView);
    }

    private void setupRecyclerView() {
        servicesRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new ShopServiceAdapter(new ArrayList<>(), this);
        servicesRecyclerView.setAdapter(adapter);
    }

    private void observeViewModel() {
        // Observe services data
        viewModel.getServices().observe(getViewLifecycleOwner(), services -> {
            Log.d(TAG, "Services data received - count: " + (services != null ? services.size() : "null"));
            if (services != null && !services.isEmpty()) {
                Log.d(TAG, "Displaying " + services.size() + " services");
                servicesRecyclerView.setVisibility(View.VISIBLE);
                emptyTextView.setVisibility(View.GONE);
                adapter.updateServices(services);
            } else {
                Log.d(TAG, "No services found, showing empty message");
                servicesRecyclerView.setVisibility(View.GONE);
                emptyTextView.setVisibility(View.VISIBLE);
                emptyTextView.setText(R.string.no_services_found);
            }
        });

        // Observe loading state
        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            // TODO: Show/hide loading indicator
        });
    }

    @Override
    public void onServiceClick(ShopServiceEntity service) {
        // Filter items by this service
        viewModel.filterByService(service.getServiceId());

        // Switch to items tab
        viewModel.setSelectedTabIndex(1);

        // Show toast
        Toast.makeText(getContext(), "Filtering items by " + service.getServiceName(), Toast.LENGTH_SHORT).show();
    }
}
