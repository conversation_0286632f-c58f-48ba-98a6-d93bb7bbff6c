<?php
/**
 * Admin Manager Class
 *
 * This class handles admin user management and authentication
 */

class AdminManager {
    private $pdo;
    private $maxLoginAttempts = 5;
    private $lockoutTime = 15; // minutes

    /**
     * Constructor
     *
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Authenticate admin user
     *
     * @param string $username Username
     * @param string $password Password
     * @return array|bool Admin data on success, false on failure
     */
    public function login($username, $password) {
        // Get admin user
        $admin = $this->getAdminByUsername($username);

        if (!$admin) {
            return false;
        }

        // Check if account is locked
        if ($admin['locked_until'] !== null && strtotime($admin['locked_until']) > time()) {
            $this->logAdminAction($admin['id'], 'login_attempt', 'Failed login attempt - account locked', getClientIp());
            return 'locked';
        }

        // Verify password
        if (password_verify($password, $admin['password'])) {
            // Reset login attempts
            $this->resetLoginAttempts($admin['id']);

            // Update last login time
            $this->updateLastLogin($admin['id']);

            // Log successful login
            $this->logAdminAction($admin['id'], 'login', 'Successful login', getClientIp());

            // Remove password from admin data
            unset($admin['password']);
            return $admin;
        }

        // Increment login attempts
        $this->incrementLoginAttempts($admin['id']);

        // Check if account should be locked
        if ($admin['login_attempts'] + 1 >= $this->maxLoginAttempts) {
            $this->lockAccount($admin['id']);
            $this->logAdminAction($admin['id'], 'account_locked', 'Account locked due to too many failed login attempts', getClientIp());
            return 'locked';
        }

        // Log failed login
        $this->logAdminAction($admin['id'], 'login_attempt', 'Failed login attempt', getClientIp());

        return false;
    }

    /**
     * Get admin by username
     *
     * @param string $username Username
     * @return array|bool Admin data on success, false on failure
     */
    public function getAdminByUsername($username) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM admin_users
            WHERE username = ?
        ");
        $stmt->execute([$username]);
        return $stmt->fetch();
    }

    /**
     * Get admin by ID
     *
     * @param int $id Admin ID
     * @return array|bool Admin data on success, false on failure
     */
    public function getAdminById($id) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM admin_users
            WHERE id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    /**
     * Increment login attempts
     *
     * @param int $adminId Admin ID
     * @return bool True on success, false on failure
     */
    private function incrementLoginAttempts($adminId) {
        $stmt = $this->pdo->prepare("
            UPDATE admin_users
            SET login_attempts = login_attempts + 1
            WHERE id = ?
        ");
        return $stmt->execute([$adminId]);
    }

    /**
     * Reset login attempts
     *
     * @param int $adminId Admin ID
     * @return bool True on success, false on failure
     */
    private function resetLoginAttempts($adminId) {
        $stmt = $this->pdo->prepare("
            UPDATE admin_users
            SET login_attempts = 0, locked_until = NULL
            WHERE id = ?
        ");
        return $stmt->execute([$adminId]);
    }

    /**
     * Lock account
     *
     * @param int $adminId Admin ID
     * @return bool True on success, false on failure
     */
    private function lockAccount($adminId) {
        $lockedUntil = date('Y-m-d H:i:s', strtotime("+{$this->lockoutTime} minutes"));
        $stmt = $this->pdo->prepare("
            UPDATE admin_users
            SET locked_until = ?
            WHERE id = ?
        ");
        return $stmt->execute([$lockedUntil, $adminId]);
    }

    /**
     * Update last login time
     *
     * @param int $adminId Admin ID
     * @return bool True on success, false on failure
     */
    private function updateLastLogin($adminId) {
        $stmt = $this->pdo->prepare("
            UPDATE admin_users
            SET last_login = NOW()
            WHERE id = ?
        ");
        return $stmt->execute([$adminId]);
    }

    /**
     * Log admin action
     *
     * @param int $adminId Admin ID
     * @param string $action Action
     * @param string $details Details
     * @param string $ipAddress IP address
     * @return bool True on success, false on failure
     */
    public function logAdminAction($adminId, $action, $details, $ipAddress) {
        $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown';
        $stmt = $this->pdo->prepare("
            INSERT INTO admin_logs (admin_id, action, details, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ");
        return $stmt->execute([$adminId, $action, $details, $ipAddress, $userAgent]);
    }

    /**
     * Create CSRF token
     *
     * @return string CSRF token
     */
    public function createCsrfToken() {
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        return $token;
    }

    /**
     * Verify CSRF token
     *
     * @param string $token CSRF token
     * @return bool True if token is valid, false otherwise
     */
    public function verifyCsrfToken($token) {
        // For debugging purposes, always return true if in development environment
        if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
            return true;
        }

        // Check if token exists and matches
        if (!isset($_SESSION['csrf_token'])) {
            error_log('CSRF token not found in session');
            return false;
        }

        if ($_SESSION['csrf_token'] !== $token) {
            error_log('CSRF token mismatch. Session: ' . $_SESSION['csrf_token'] . ', Provided: ' . $token);
            return false;
        }

        return true;
    }

    /**
     * Create a new admin user
     *
     * @param string $username Username
     * @param string $email Email
     * @param string $password Password
     * @param string $fullName Full name
     * @param string $role Role (admin or super_admin)
     * @return int|bool Admin ID on success, false on failure
     */
    public function createAdmin($username, $email, $password, $fullName, $role = 'admin') {
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 10]);

        // Insert admin
        $stmt = $this->pdo->prepare("
            INSERT INTO admin_users (username, email, password, full_name, role)
            VALUES (?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([$username, $email, $hashedPassword, $fullName, $role]);

        if ($result) {
            return $this->pdo->lastInsertId();
        }

        return false;
    }

    /**
     * Update admin password
     *
     * @param int $adminId Admin ID
     * @param string $newPassword New password
     * @return bool True on success, false on failure
     */
    public function updatePassword($adminId, $newPassword) {
        // Hash password
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => 10]);

        // Update password
        $stmt = $this->pdo->prepare("
            UPDATE admin_users
            SET password = ?
            WHERE id = ?
        ");

        return $stmt->execute([$hashedPassword, $adminId]);
    }

    /**
     * Get all admin users with pagination and filtering
     *
     * @param int $page Page number
     * @param int $perPage Items per page
     * @param string $search Search term
     * @return array Admin users and pagination info
     */
    public function getAllAdmins($page = 1, $perPage = 10, $search = '') {
        $offset = ($page - 1) * $perPage;
        $params = [];
        $whereClause = '';

        // Build where clause
        if (!empty($search)) {
            $whereClause = " WHERE (username LIKE ? OR email LIKE ? OR full_name LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        // Count total records
        $countSql = "SELECT COUNT(*) as count FROM admin_users" . $whereClause;
        $countStmt = $this->pdo->prepare($countSql);
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch()['count'];

        // Get records with pagination
        $sql = "
            SELECT id, username, email, full_name, role, is_active, last_login, created_at
            FROM admin_users
            $whereClause
            ORDER BY id ASC
            LIMIT ?, ?
        ";

        // Add pagination parameters
        $params[] = $offset;
        $params[] = $perPage;

        $stmt = $this->pdo->prepare($sql);

        // Bind parameters with correct types
        for ($i = 0; $i < count($params); $i++) {
            if ($i >= count($params) - 2) {
                $stmt->bindValue($i + 1, $params[$i], PDO::PARAM_INT);
            } else {
                $stmt->bindValue($i + 1, $params[$i]);
            }
        }

        $stmt->execute();
        $admins = $stmt->fetchAll();

        // Calculate pagination info
        $totalPages = ceil($totalCount / $perPage);

        return [
            'admins' => $admins,
            'pagination' => [
                'total' => $totalCount,
                'per_page' => $perPage,
                'current_page' => $page,
                'total_pages' => $totalPages
            ]
        ];
    }

    /**
     * Check if admin exists by username
     *
     * @param string $username Username
     * @return bool True if admin exists, false otherwise
     */
    public function adminExistsByUsername($username) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM admin_users
            WHERE username = ?
        ");
        $stmt->execute([$username]);

        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    /**
     * Check if admin exists by email
     *
     * @param string $email Email
     * @return bool True if admin exists, false otherwise
     */
    public function adminExistsByEmail($email) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM admin_users
            WHERE email = ?
        ");
        $stmt->execute([$email]);

        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    /**
     * Add a new admin user
     *
     * @param string $username Username
     * @param string $email Email
     * @param string $password Password
     * @param string $fullName Full name
     * @param string $role Role (admin or super_admin)
     * @param bool $isActive Whether the admin is active
     * @return int|bool Admin ID on success, false on failure
     */
    public function addAdmin($username, $email, $password, $fullName, $role = 'admin', $isActive = true) {
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 10]);

        // Insert admin
        $stmt = $this->pdo->prepare("
            INSERT INTO admin_users (username, email, password, full_name, role, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $username,
            $email,
            $hashedPassword,
            $fullName,
            $role,
            $isActive ? 1 : 0
        ]);

        if ($result) {
            return $this->pdo->lastInsertId();
        }

        return false;
    }

    /**
     * Update admin user
     *
     * @param int $adminId Admin ID
     * @param string $email Email
     * @param string $fullName Full name
     * @param string $role Role (admin or super_admin)
     * @param bool $isActive Whether the admin is active
     * @param string $password New password (optional)
     * @return bool True on success, false on failure
     */
    public function updateAdmin($adminId, $email, $fullName, $role, $isActive, $password = '') {
        try {
            $this->pdo->beginTransaction();

            // Update admin info
            $sql = "
                UPDATE admin_users
                SET email = ?, full_name = ?, role = ?, is_active = ?
                WHERE id = ?
            ";

            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $email,
                $fullName,
                $role,
                $isActive ? 1 : 0,
                $adminId
            ]);

            if (!$result) {
                $this->pdo->rollBack();
                return false;
            }

            // Update password if provided
            if (!empty($password)) {
                $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 10]);

                $stmt = $this->pdo->prepare("
                    UPDATE admin_users
                    SET password = ?
                    WHERE id = ?
                ");

                $result = $stmt->execute([$hashedPassword, $adminId]);

                if (!$result) {
                    $this->pdo->rollBack();
                    return false;
                }
            }

            $this->pdo->commit();
            return true;
        } catch (PDOException $e) {
            $this->pdo->rollBack();
            error_log("Error updating admin: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update admin status (active/inactive)
     *
     * @param int $adminId Admin ID
     * @param bool $isActive Whether the admin is active
     * @return bool True on success, false on failure
     */
    public function updateAdminStatus($adminId, $isActive) {
        $stmt = $this->pdo->prepare("
            UPDATE admin_users
            SET is_active = ?
            WHERE id = ?
        ");

        return $stmt->execute([$isActive ? 1 : 0, $adminId]);
    }

    /**
     * Delete admin user
     *
     * @param int $adminId Admin ID
     * @return bool True on success, false on failure
     */
    public function deleteAdmin($adminId) {
        $stmt = $this->pdo->prepare("
            DELETE FROM admin_users
            WHERE id = ?
        ");

        return $stmt->execute([$adminId]);
    }

    /**
     * Get admin by email
     *
     * @param string $email Email address
     * @return array|bool Admin data on success, false on failure
     */
    public function getAdminByEmail($email) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM admin_users
            WHERE email = ?
        ");
        $stmt->execute([$email]);
        return $stmt->fetch();
    }

    /**
     * Update admin profile (email and full name only)
     *
     * @param int $adminId Admin ID
     * @param string $email Email
     * @param string $fullName Full name
     * @return bool True on success, false on failure
     */
    public function updateAdminProfile($adminId, $email, $fullName) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE admin_users
                SET email = ?, full_name = ?
                WHERE id = ?
            ");

            return $stmt->execute([$email, $fullName, $adminId]);
        } catch (PDOException $e) {
            error_log("Error updating admin profile: " . $e->getMessage());
            return false;
        }
    }
}
