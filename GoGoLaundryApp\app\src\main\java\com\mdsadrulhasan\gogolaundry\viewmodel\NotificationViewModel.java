package com.mdsadrulhasan.gogolaundry.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;
import androidx.lifecycle.ViewModel;


import com.mdsadrulhasan.gogolaundry.model.Notification;
import com.mdsadrulhasan.gogolaundry.repository.NotificationRepository;
import com.mdsadrulhasan.gogolaundry.utils.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * ViewModel for notifications
 */
public class NotificationViewModel extends ViewModel {
    private final NotificationRepository repository;
    private final MediatorLiveData<Resource<List<Notification>>> notifications = new MediatorLiveData<>();
    private final MutableLiveData<Integer> userId = new MutableLiveData<>();
    private LiveData<Resource<List<Notification>>> notificationEntities;
    private final LiveData<Integer> unreadCount;

    /**
     * Constructor
     */
    public NotificationViewModel() {
        repository = NotificationRepository.getInstance();

        // Transform user ID changes into notification entities
        notificationEntities = Transformations.switchMap(userId, id -> {
            if (id != null && id > 0) {
                return repository.getNotificationsByUserId(id, false);
            } else {
                return new MutableLiveData<>(Resource.success(new ArrayList<>()));
            }
        });

        // Add notification entities as a source to the mediator
        notifications.addSource(notificationEntities, resource -> {
            if (resource != null) {
                if (resource.isSuccess() && resource.getData() != null) {
                    // Notifications are already in the correct format
                    notifications.setValue(Resource.success(resource.getData()));
                } else if (resource.isLoading()) {
                    notifications.setValue(Resource.loading(null));
                } else if (resource.isError()) {
                    notifications.setValue(Resource.error(resource.getMessage(), null));
                }
            }
        });

        // Transform user ID changes into unread count
        unreadCount = Transformations.switchMap(userId, id -> {
            if (id != null && id > 0) {
                return repository.getUnreadNotificationsCount(id);
            } else {
                MutableLiveData<Integer> result = new MutableLiveData<>();
                result.setValue(0);
                return result;
            }
        });
    }

    /**
     * Set user ID to load notifications for
     *
     * @param id User ID
     */
    public void setUserId(int id) {
        if (userId.getValue() == null || userId.getValue() != id) {
            userId.setValue(id);
        }
    }

    /**
     * Get notifications
     *
     * @return LiveData with notifications
     */
    public LiveData<Resource<List<Notification>>> getNotifications() {
        return notifications;
    }

    /**
     * Get unread notifications count
     *
     * @return LiveData with unread count
     */
    public LiveData<Integer> getUnreadCount() {
        return unreadCount;
    }

    /**
     * Refresh notifications
     *
     * @param forceRefresh Whether to force a refresh from the server
     */
    public void refreshNotifications(boolean forceRefresh) {
        Integer id = userId.getValue();
        if (id != null && id > 0) {
            // Remove previous source to avoid duplicate updates
            if (notificationEntities != null) {
                notifications.removeSource(notificationEntities);
            }

            // Get notifications with force refresh flag
            notificationEntities = repository.getNotificationsByUserId(id, forceRefresh);

            // Add new source
            notifications.addSource(notificationEntities, resource -> {
                if (resource != null) {
                    if (resource.isSuccess() && resource.getData() != null) {
                        // Notifications are already in the correct format
                        notifications.setValue(Resource.success(resource.getData()));
                    } else if (resource.isLoading()) {
                        notifications.setValue(Resource.loading(null));
                    } else if (resource.isError()) {
                        notifications.setValue(Resource.error(resource.getMessage(), null));
                    }
                }
            });
        }
    }

    /**
     * Refresh notifications from server
     */
    public void refreshNotifications() {
        refreshNotifications(true);
    }

    /**
     * Mark a notification as read
     *
     * @param notificationId Notification ID
     * @return LiveData with updated unread count
     */
    public LiveData<Integer> markAsRead(int notificationId) {
        return repository.markAsRead(notificationId);
    }

    /**
     * Mark all notifications as read
     *
     * @return LiveData with updated unread count
     */
    public LiveData<Integer> markAllAsRead() {
        Integer id = userId.getValue();
        if (id != null && id > 0) {
            return repository.markAllAsRead(id);
        }
        // Return 0 if no user ID
        MutableLiveData<Integer> result = new MutableLiveData<>();
        result.setValue(0);
        return result;
    }
}
