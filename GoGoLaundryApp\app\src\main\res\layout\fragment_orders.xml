<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <!-- Clean Header -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/appbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
     android:layout_margin="3dp"
        app:cardBackgroundColor="@color/primary"
        app:cardCornerRadius="20dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/orders_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/my_orders"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                android:textColor="@color/white"
                android:textStyle="bold"
                app:layout_constraintBottom_toTopOf="@+id/orders_subtitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/orders_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:alpha="0.9"
                android:text="@string/orders_subtitle"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/orders_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Clean Content Container -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/orders_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:scrollbarStyle="outsideOverlay"
            android:fadeScrollbars="true"
            android:scrollbars="vertical"
            android:background="@color/background_light"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_order" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:indeterminateTint="@color/primary"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- Clean Empty State for Orders -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/empty_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:background="@color/background_light"
        android:padding="40dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appbar_layout">

        <!-- Empty State Icon with Clean Background -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/empty_icon_container"
            android:layout_width="100dp"
            android:layout_height="100dp"
            app:cardBackgroundColor="@color/primary_light"
            app:cardCornerRadius="50dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_receipt"
                app:tint="@color/primary" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Empty State Title -->
        <TextView
            android:id="@+id/empty_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="No Orders Yet"
            android:textAlignment="center"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/empty_icon_container" />

        <!-- Empty State Subtitle -->
        <TextView
            android:id="@+id/empty_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:lineSpacingExtra="4dp"
            android:text="Start your laundry journey today!\nPlace your first order and experience our premium service."
            android:textAlignment="center"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
            android:textColor="@color/text_secondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/empty_title" />

        <!-- Action Buttons Container -->
        <LinearLayout
            android:id="@+id/buttons_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="28dp"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/empty_subtitle">

            <!-- Primary Action Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_start_first_order"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:backgroundTint="@color/primary"
                android:text="Start Your First Order"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="28dp"
                app:icon="@drawable/ic_add"
                app:iconGravity="textStart"
                app:iconPadding="12dp"
                app:iconSize="20dp"
                app:iconTint="@android:color/white" />

            <!-- Secondary Action Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_browse_services"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="16dp"
                android:text="Browse Services"
                android:textColor="@color/primary"
                android:textSize="14sp"
                android:textStyle="bold"
                app:cornerRadius="24dp"
                app:icon="@drawable/ic_dashboard"
                app:iconGravity="textStart"
                app:iconPadding="8dp"
                app:iconSize="16dp"
                app:iconTint="@color/primary"
                app:strokeColor="@color/primary"
                app:strokeWidth="2dp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
