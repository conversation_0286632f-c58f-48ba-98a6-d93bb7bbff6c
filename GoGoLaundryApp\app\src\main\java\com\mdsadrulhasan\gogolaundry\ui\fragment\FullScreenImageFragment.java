package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.Manifest;
import android.content.ContentValues;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.CustomTarget;
import com.bumptech.glide.request.transition.Transition;
import com.github.chrisbanes.photoview.PhotoView;
import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * Fragment for displaying images in full screen with zoom/pan functionality
 */
public class FullScreenImageFragment extends Fragment {
    private static final String TAG = "FullScreenImage";
    private static final String ARG_IMAGE_URL = "image_url";
    private static final String ARG_TITLE = "title";
    private static final int PERMISSION_REQUEST_WRITE_STORAGE = 100;

    private String imageUrl;
    private String title;
    private Bitmap currentBitmap;

    // Views
    private PhotoView photoView;
    private ProgressBar progressBar;
    private TextView tvTitle;
    private ImageButton btnClose;
    private MaterialButton btnShare;
    private MaterialButton btnDownload;

    /**
     * Create new instance of FullScreenImageFragment
     *
     * @param imageUrl URL of the image to display
     * @param title    Title to display
     * @return New fragment instance
     */
    public static FullScreenImageFragment newInstance(String imageUrl, String title) {
        FullScreenImageFragment fragment = new FullScreenImageFragment();
        Bundle args = new Bundle();
        args.putString(ARG_IMAGE_URL, imageUrl);
        args.putString(ARG_TITLE, title);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        if (getArguments() != null) {
            imageUrl = getArguments().getString(ARG_IMAGE_URL);
            title = getArguments().getString(ARG_TITLE, getString(R.string.notification_image));
        }
        
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            Log.e(TAG, "No image URL provided to FullScreenImageFragment");
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
            return;
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_fullscreen_image, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupClickListeners();
        loadImage();
    }

    /**
     * Initialize all views
     */
    private void initViews(View view) {
        photoView = view.findViewById(R.id.photo_view);
        progressBar = view.findViewById(R.id.progress_bar);
        tvTitle = view.findViewById(R.id.tv_title);
        btnClose = view.findViewById(R.id.btn_close);
        btnShare = view.findViewById(R.id.btn_share);
        btnDownload = view.findViewById(R.id.btn_download);
        
        // Set title
        tvTitle.setText(title);
    }

    /**
     * Set up click listeners
     */
    private void setupClickListeners() {
        btnClose.setOnClickListener(v -> {
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
        });

        btnShare.setOnClickListener(v -> shareImage());
        btnDownload.setOnClickListener(v -> downloadImage());
    }

    /**
     * Load image using Glide
     */
    private void loadImage() {
        progressBar.setVisibility(View.VISIBLE);
        
        Glide.with(this)
                .asBitmap()
                .load(imageUrl)
                .into(new CustomTarget<Bitmap>() {
                    @Override
                    public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                        currentBitmap = resource;
                        photoView.setImageBitmap(resource);
                        progressBar.setVisibility(View.GONE);
                        Log.d(TAG, "Image loaded successfully");
                    }

                    @Override
                    public void onLoadCleared(@Nullable Drawable placeholder) {
                        // Handle cleanup if needed
                    }

                    @Override
                    public void onLoadFailed(@Nullable Drawable errorDrawable) {
                        progressBar.setVisibility(View.GONE);
                        photoView.setImageResource(R.drawable.ic_error);
                        Toast.makeText(getContext(), "Failed to load image", Toast.LENGTH_SHORT).show();
                        Log.e(TAG, "Failed to load image: " + imageUrl);
                    }
                });
    }

    /**
     * Share the current image
     */
    private void shareImage() {
        if (currentBitmap == null) {
            Toast.makeText(getContext(), "Image not loaded yet", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            // Save bitmap to cache directory
            File cachePath = new File(requireContext().getCacheDir(), "images");
            cachePath.mkdirs();
            
            File imageFile = new File(cachePath, "shared_image_" + System.currentTimeMillis() + ".jpg");
            FileOutputStream stream = new FileOutputStream(imageFile);
            currentBitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
            stream.close();

            // Get URI from file
            Uri uri = FileProvider.getUriForFile(requireContext(), 
                    "com.mdsadrulhasan.gogolaundry.fileprovider", imageFile);

            // Create share intent
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("image/jpeg");
            shareIntent.putExtra(Intent.EXTRA_STREAM, uri);
            shareIntent.putExtra(Intent.EXTRA_TEXT, title);
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            startActivity(Intent.createChooser(shareIntent, getString(R.string.share)));
            
        } catch (IOException e) {
            Log.e(TAG, "Error sharing image", e);
            Toast.makeText(getContext(), getString(R.string.image_share_failed), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Download the current image to gallery
     */
    private void downloadImage() {
        if (currentBitmap == null) {
            Toast.makeText(getContext(), "Image not loaded yet", Toast.LENGTH_SHORT).show();
            return;
        }

        // Check for storage permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ doesn't need WRITE_EXTERNAL_STORAGE for MediaStore
            saveImageToGallery();
        } else {
            // Android 9 and below need WRITE_EXTERNAL_STORAGE permission
            if (ContextCompat.checkSelfPermission(requireContext(), 
                    Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(requireActivity(), 
                        new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, 
                        PERMISSION_REQUEST_WRITE_STORAGE);
            } else {
                saveImageToGallery();
            }
        }
    }

    /**
     * Save image to gallery
     */
    private void saveImageToGallery() {
        try {
            String fileName = "GoGoLaundry_" + System.currentTimeMillis() + ".jpg";
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Use MediaStore for Android 10+
                ContentValues values = new ContentValues();
                values.put(MediaStore.Images.Media.DISPLAY_NAME, fileName);
                values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
                values.put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES);

                Uri uri = requireContext().getContentResolver().insert(
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
                
                if (uri != null) {
                    OutputStream outputStream = requireContext().getContentResolver().openOutputStream(uri);
                    currentBitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
                    outputStream.close();
                    
                    Toast.makeText(getContext(), getString(R.string.image_saved), Toast.LENGTH_SHORT).show();
                }
            } else {
                // Use legacy method for Android 9 and below
                String savedImageURL = MediaStore.Images.Media.insertImage(
                        requireContext().getContentResolver(),
                        currentBitmap,
                        fileName,
                        title
                );
                
                if (savedImageURL != null) {
                    Toast.makeText(getContext(), getString(R.string.image_saved), Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(getContext(), getString(R.string.image_save_failed), Toast.LENGTH_SHORT).show();
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error saving image", e);
            Toast.makeText(getContext(), getString(R.string.image_save_failed), Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_WRITE_STORAGE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                saveImageToGallery();
            } else {
                Toast.makeText(getContext(), "Storage permission required to save image", Toast.LENGTH_SHORT).show();
            }
        }
    }
}
