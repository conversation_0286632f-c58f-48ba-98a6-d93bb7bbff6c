<?php
/**
 * Order Statistics Widget
 * 
 * This file contains the order statistics widget for the admin dashboard
 */

// Get order statistics
$today = date('Y-m-d');
$yesterday = date('Y-m-d', strtotime('-1 day'));
$thisWeekStart = date('Y-m-d', strtotime('monday this week'));
$thisWeekEnd = date('Y-m-d', strtotime('sunday this week'));
$thisMonthStart = date('Y-m-01');
$thisMonthEnd = date('Y-m-t');

// Total orders
$stmt = $pdo->query("SELECT COUNT(*) FROM orders");
$totalOrders = $stmt->fetchColumn();

// Today's orders
$stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?");
$stmt->execute([$today]);
$todayOrders = $stmt->fetchColumn();

// Yesterday's orders
$stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?");
$stmt->execute([$yesterday]);
$yesterdayOrders = $stmt->fetchColumn();

// This week's orders
$stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) BETWEEN ? AND ?");
$stmt->execute([$thisWeekStart, $thisWeekEnd]);
$thisWeekOrders = $stmt->fetchColumn();

// This month's orders
$stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) BETWEEN ? AND ?");
$stmt->execute([$thisMonthStart, $thisMonthEnd]);
$thisMonthOrders = $stmt->fetchColumn();

// Orders by status
$stmt = $pdo->query("
    SELECT status, COUNT(*) as count
    FROM orders
    GROUP BY status
    ORDER BY FIELD(status, 'placed', 'confirmed', 'pickup_scheduled', 'picked_up', 'processing', 'ready_for_delivery', 'out_for_delivery', 'delivered', 'cancelled')
");
$ordersByStatus = $stmt->fetchAll();

// Recent orders
$stmt = $pdo->query("
    SELECT o.id, o.order_number, o.tracking_number, o.status, o.total, o.created_at, u.full_name as customer_name
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    ORDER BY o.created_at DESC
    LIMIT 5
");
$recentOrders = $stmt->fetchAll();

// Calculate percentage change for today vs yesterday
$percentageChange = 0;
if ($yesterdayOrders > 0) {
    $percentageChange = (($todayOrders - $yesterdayOrders) / $yesterdayOrders) * 100;
}
?>

<div class="row">
    <!-- Total Orders Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalOrders) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Orders Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Today's Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($todayOrders) ?></div>
                        <?php if ($percentageChange != 0): ?>
                            <div class="text-xs mt-1">
                                <?php if ($percentageChange > 0): ?>
                                    <span class="text-success">
                                        <i class="fas fa-arrow-up"></i> <?= number_format(abs($percentageChange), 1) ?>%
                                    </span>
                                <?php else: ?>
                                    <span class="text-danger">
                                        <i class="fas fa-arrow-down"></i> <?= number_format(abs($percentageChange), 1) ?>%
                                    </span>
                                <?php endif; ?>
                                from yesterday
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- This Week's Orders Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            This Week's Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($thisWeekOrders) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- This Month's Orders Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            This Month's Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($thisMonthOrders) ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Orders by Status Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Orders by Status</h6>
            </div>
            <div class="card-body">
                <div class="chart-bar">
                    <canvas id="ordersStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
            </div>
            <div class="card-body">
                <?php if (empty($recentOrders)): ?>
                    <p class="text-center">No recent orders found</p>
                <?php else: ?>
                    <div class="list-group">
                        <?php foreach ($recentOrders as $order): ?>
                            <a href="orders.php?search=<?= urlencode($order['order_number']) ?>" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">#<?= htmlspecialchars($order['order_number']) ?></h6>
                                    <small><?= date('M d, H:i', strtotime($order['created_at'])) ?></small>
                                </div>
                                <p class="mb-1"><?= htmlspecialchars($order['customer_name']) ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge badge-<?= getStatusBadgeClass($order['status']) ?>"><?= formatStatus($order['status']) ?></span>
                                    <small><?= number_format($order['total'], 2) ?> BDT</small>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <a href="orders.php" class="btn btn-sm btn-primary">View All Orders</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Set new default font family and font color to mimic Bootstrap's default styling
    Chart.defaults.global.defaultFontFamily = 'Nunito', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
    Chart.defaults.global.defaultFontColor = '#858796';

    // Orders by Status Chart
    var ctx = document.getElementById("ordersStatusChart");
    var statusLabels = [];
    var statusData = [];
    var statusColors = [];
    
    <?php foreach ($ordersByStatus as $status): ?>
        statusLabels.push('<?= formatStatus($status['status']) ?>');
        statusData.push(<?= $status['count'] ?>);
        statusColors.push(getStatusColor('<?= $status['status'] ?>'));
    <?php endforeach; ?>
    
    var ordersStatusChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: statusLabels,
            datasets: [{
                label: "Orders",
                backgroundColor: statusColors,
                hoverBackgroundColor: statusColors.map(color => LightenDarkenColor(color, -20)),
                borderColor: "#4e73df",
                data: statusData,
            }],
        },
        options: {
            maintainAspectRatio: false,
            layout: {
                padding: {
                    left: 10,
                    right: 25,
                    top: 25,
                    bottom: 0
                }
            },
            scales: {
                xAxes: [{
                    time: {
                        unit: 'status'
                    },
                    gridLines: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        maxTicksLimit: 9
                    },
                    maxBarThickness: 25,
                }],
                yAxes: [{
                    ticks: {
                        min: 0,
                        maxTicksLimit: 5,
                        padding: 10,
                        callback: function(value, index, values) {
                            return value;
                        }
                    },
                    gridLines: {
                        color: "rgb(234, 236, 244)",
                        zeroLineColor: "rgb(234, 236, 244)",
                        drawBorder: false,
                        borderDash: [2],
                        zeroLineBorderDash: [2]
                    }
                }],
            },
            legend: {
                display: false
            },
            tooltips: {
                titleMarginBottom: 10,
                titleFontColor: '#6e707e',
                titleFontSize: 14,
                backgroundColor: "rgb(255,255,255)",
                bodyFontColor: "#858796",
                borderColor: '#dddfeb',
                borderWidth: 1,
                xPadding: 15,
                yPadding: 15,
                displayColors: false,
                caretPadding: 10,
                callbacks: {
                    label: function(tooltipItem, chart) {
                        return tooltipItem.yLabel + ' orders';
                    }
                }
            },
        }
    });
    
    function getStatusColor(status) {
        switch (status) {
            case 'placed': return '#858796'; // secondary
            case 'confirmed': return '#36b9cc'; // info
            case 'pickup_scheduled': return '#4e73df'; // primary
            case 'picked_up': return '#f6c23e'; // warning
            case 'processing': return '#f6c23e'; // warning
            case 'ready_for_delivery': return '#36b9cc'; // info
            case 'out_for_delivery': return '#4e73df'; // primary
            case 'delivered': return '#1cc88a'; // success
            case 'cancelled': return '#e74a3b'; // danger
            default: return '#858796'; // secondary
        }
    }
    
    function LightenDarkenColor(col, amt) {
        var usePound = false;
        if (col[0] == "#") {
            col = col.slice(1);
            usePound = true;
        }
        var num = parseInt(col, 16);
        var r = (num >> 16) + amt;
        if (r > 255) r = 255;
        else if (r < 0) r = 0;
        var b = ((num >> 8) & 0x00FF) + amt;
        if (b > 255) b = 255;
        else if (b < 0) b = 0;
        var g = (num & 0x0000FF) + amt;
        if (g > 255) g = 255;
        else if (g < 0) g = 0;
        return (usePound ? "#" : "") + (g | (b << 8) | (r << 16)).toString(16);
    }
</script>
