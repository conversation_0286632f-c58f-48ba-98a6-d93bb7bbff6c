package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;

import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.CartAdapter;
import com.mdsadrulhasan.gogolaundry.model.CartItem;
import com.mdsadrulhasan.gogolaundry.ui.fragment.CheckoutFragment;
import com.mdsadrulhasan.gogolaundry.viewmodel.CartViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for displaying the shopping cart
 */
public class CartFragment extends Fragment implements CartAdapter.CartItemActionListener {

    private static final String TAG = "CartFragment";

    private CartViewModel viewModel;
    private RecyclerView recyclerView;
    private CartAdapter adapter;
    private LinearLayout emptyView; // Changed from TextView to LinearLayout
    private TextView cartItemCount; // New field for item count in header
    private TextView totalTextView;
    private MaterialButton checkoutButton; // Changed to MaterialButton
    private MaterialButton clearCartButton; // Changed to MaterialButton

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize ViewModel
        viewModel = new ViewModelProvider(requireActivity()).get(CartViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_cart, container, false);

        // Initialize views
        recyclerView = view.findViewById(R.id.cart_recycler_view);
        emptyView = view.findViewById(R.id.empty_view);
        cartItemCount = view.findViewById(R.id.cart_item_count);
        totalTextView = view.findViewById(R.id.cart_total);
        checkoutButton = view.findViewById(R.id.checkout_button);
        clearCartButton = view.findViewById(R.id.clear_cart_button);

        Log.d(TAG, "Views initialized - RecyclerView: " + (recyclerView != null ? "found" : "null"));
        Log.d(TAG, "Views initialized - EmptyView: " + (emptyView != null ? "found" : "null"));
        Log.d(TAG, "Views initialized - CartItemCount: " + (cartItemCount != null ? "found" : "null"));

        // Set up RecyclerView
        if (recyclerView != null) {
            recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
            adapter = new CartAdapter(new ArrayList<>(), this);
            recyclerView.setAdapter(adapter);
            Log.d(TAG, "RecyclerView setup complete");
        } else {
            Log.e(TAG, "RecyclerView is null! Cannot set up adapter.");
        }

        // Set up buttons
        checkoutButton.setOnClickListener(v -> onCheckoutClicked());
        clearCartButton.setOnClickListener(v -> onClearCartClicked());

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Set title
        if (getActivity() != null) {
            getActivity().setTitle(R.string.cart);
        }

        // Observe cart items
        viewModel.getCartItems().observe(getViewLifecycleOwner(), cartItems -> {
            Log.d(TAG, "Cart items observer triggered. Items: " + (cartItems != null ? cartItems.size() : "null"));
            if (cartItems != null && !cartItems.isEmpty()) {
                Log.d(TAG, "Showing cart items: " + cartItems.size());
                showCartItems(cartItems);
                updateCartItemCount(cartItems.size());
            } else {
                Log.d(TAG, "Showing empty cart");
                showEmptyCart();
                updateCartItemCount(0);
            }
        });

        // Observe cart total
        viewModel.getCartTotal().observe(getViewLifecycleOwner(), total -> {
            if (total != null) {
                totalTextView.setText("৳" + String.format("%.2f", total));
            } else {
                totalTextView.setText("৳0.00");
            }
        });

        // Refresh cart data
        viewModel.refreshCart();
    }

    /**
     * Update cart item count in header
     *
     * @param count Number of items in cart
     */
    private void updateCartItemCount(int count) {
        if (cartItemCount != null) {
            if (count == 0) {
                cartItemCount.setText("0 items in cart");
            } else if (count == 1) {
                cartItemCount.setText("1 item in cart");
            } else {
                cartItemCount.setText(count + " items in cart");
            }
        }
    }

    /**
     * Show cart items
     *
     * @param cartItems List of cart items
     */
    private void showCartItems(List<CartItem> cartItems) {
        Log.d(TAG, "showCartItems called with " + cartItems.size() + " items");
        Log.d(TAG, "RecyclerView: " + (recyclerView != null ? "not null" : "null"));
        Log.d(TAG, "Adapter: " + (adapter != null ? "not null" : "null"));

        recyclerView.setVisibility(View.VISIBLE);
        emptyView.setVisibility(View.GONE);
        checkoutButton.setEnabled(true);
        clearCartButton.setEnabled(true);
        adapter.updateCartItems(cartItems);

        Log.d(TAG, "RecyclerView visibility: " + recyclerView.getVisibility());
        Log.d(TAG, "Adapter item count: " + adapter.getItemCount());
    }

    /**
     * Show empty cart state
     */
    private void showEmptyCart() {
        recyclerView.setVisibility(View.GONE);
        emptyView.setVisibility(View.VISIBLE);
        checkoutButton.setEnabled(false);
        clearCartButton.setEnabled(false);
    }

    /**
     * Handle checkout button click
     */
    private void onCheckoutClicked() {
        // Check if cart has items
        List<CartItem> items = viewModel.getCartItems().getValue();
        if (items == null || items.isEmpty()) {
            Toast.makeText(getContext(), "Your cart is empty", Toast.LENGTH_SHORT).show();
            return;
        }

        // Navigate to checkout fragment
        if (getActivity() != null) {
            getActivity().getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.fragment_container, new CheckoutFragment())
                    .addToBackStack(null)
                    .commit();
        }
    }

    /**
     * Handle clear cart button click
     */
    private void onClearCartClicked() {
        viewModel.clearCart();
        Toast.makeText(getContext(), "Cart cleared", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onRemoveItem(CartItem item, int position) {
        if (viewModel.removeFromCart(item.getItemId())) {
            Toast.makeText(getContext(), item.getName() + " removed from cart", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onIncreaseQuantity(CartItem item, int position) {
        viewModel.increaseQuantity(item.getItemId());
    }

    @Override
    public void onDecreaseQuantity(CartItem item, int position) {
        viewModel.decreaseQuantity(item.getItemId());
    }
}
