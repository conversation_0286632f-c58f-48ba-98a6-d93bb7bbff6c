package com.mdsadrulhasan.gogolaundry.adapter;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.model.Service;

import java.util.List;

/**
 * Adapter for displaying services in a grid layout
 */
public class ServiceGridAdapter extends RecyclerView.Adapter<ServiceGridAdapter.ServiceViewHolder> {

    private List<Service> services;
    private final OnServiceClickListener listener;

    public interface OnServiceClickListener {
        void onServiceClick(Service service);
    }

    public ServiceGridAdapter(List<Service> services, OnServiceClickListener listener) {
        this.services = services;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ServiceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_service_horizontal, parent, false);
        return new ServiceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ServiceViewHolder holder, int position) {
        Service service = services.get(position);
        holder.bind(service, listener);
    }

    @Override
    public int getItemCount() {
        return services.size();
    }

    public void updateServices(List<Service> services) {
        this.services = services;
        notifyDataSetChanged();
    }

    static class ServiceViewHolder extends RecyclerView.ViewHolder {
        private final ImageView serviceIcon;
        private final TextView serviceName;
        private final TextView servicePrice;

        public ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            serviceIcon = itemView.findViewById(R.id.service_icon);
            serviceName = itemView.findViewById(R.id.service_name);
            servicePrice = itemView.findViewById(R.id.service_price);
        }

        public void bind(Service service, OnServiceClickListener listener) {
            serviceName.setText(service.getName());
            // Hide price for services - prices should only be shown at the item level
            servicePrice.setVisibility(View.GONE);

            // Load service icon if available
            if (service.getIconUrl() != null && !service.getIconUrl().isEmpty()) {
                String iconUrl = service.getIconUrl();
                String imageUrl;

                // Check if the URL is already absolute
                if (iconUrl.startsWith("http://") || iconUrl.startsWith("https://")) {
                    imageUrl = iconUrl;
                } else {
                    // Construct URL based on the API base URL
                    String baseUrl = ApiClient.getBaseUrl();
                    // Remove any leading slashes from iconUrl to avoid double slashes
                    if (iconUrl.startsWith("/")) {
                        iconUrl = iconUrl.substring(1);
                    }

                    // If the icon URL contains "uploads/", assume it's relative to the admin panel root
                    if (iconUrl.contains("uploads/")) {
                        // Go up one level from the API directory to the admin panel root
                        imageUrl = baseUrl + "../" + iconUrl;
                    } else {
                        // Assume it's in the uploads/services directory
                        imageUrl = baseUrl + "../uploads/services/" + iconUrl;
                    }
                }

                // Log the image URL for debugging
                Log.d("ServiceGridAdapter", "Loading service icon from: " + imageUrl);

                // Load the image with Glide
                Glide.with(itemView.getContext())
                        .load(imageUrl)
                        .placeholder(getDefaultIcon(service))
                        .error(getDefaultIcon(service))
                        .into(serviceIcon);
            } else {
                // Set default icon based on service name
                int defaultIcon = getDefaultIcon(service);
                serviceIcon.setImageResource(defaultIcon);
                Log.d("ServiceGridAdapter", "No icon URL available for service: " + service.getName());
            }

            // Set click listener
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onServiceClick(service);
                }
            });
        }

        /**
         * Get default icon based on service name
         *
         * @param service Service
         * @return Resource ID for default icon
         */
        private int getDefaultIcon(Service service) {
            String serviceName = service.getName().toLowerCase();

            if (serviceName.contains("wash") || serviceName.contains("laundry")) {
                return R.drawable.ic_washing_machine;
            } else if (serviceName.contains("iron") || serviceName.contains("press")) {
                return R.drawable.ic_iron;
            } else if (serviceName.contains("dry") || serviceName.contains("clean")) {
                return R.drawable.ic_dry_cleaning;
            } else if (serviceName.contains("stain") || serviceName.contains("remov")) {
                return R.drawable.ic_stain_removal;
            } else {
                return R.drawable.ic_washing_machine; // Default
            }
        }
    }
}
