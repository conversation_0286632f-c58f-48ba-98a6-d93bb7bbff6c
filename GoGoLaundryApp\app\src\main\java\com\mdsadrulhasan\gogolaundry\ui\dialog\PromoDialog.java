package com.mdsadrulhasan.gogolaundry.ui.dialog;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;

import com.bumptech.glide.Glide;
import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.MainActivity;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.data.model.PromoDialogResponse;
import com.mdsadrulhasan.gogolaundry.ui.fragment.ServicesFragment;

/**
 * Promotional dialog that shows on every app launch
 * Features a transparent glassmorphism design with close button positioned above the dialog
 */
public class PromoDialog extends DialogFragment {

    private static final String ARG_PROMO_DATA = "promo_data";

    private ImageButton btnClose;
    private MaterialButton btnShopNow;
    private TextView tvTitle;
    private TextView tvSubtitle;
    private TextView tvDiscountText;
    private TextView tvPromoCode;
    private ImageView ivPromoImage;

    // Layout containers for color application
    private LinearLayout mainContentLayout;
    private LinearLayout headerLayout;
    private LinearLayout productsLayout;
    private LinearLayout discountLayout;

    private PromoDialogResponse.PromoDialog promoData;

    public static PromoDialog newInstance() {
        android.util.Log.d("PromoDialog", "newInstance() called (static content)");
        return new PromoDialog();
    }

    public static PromoDialog newInstance(PromoDialogResponse.PromoDialog promoData) {
        android.util.Log.d("PromoDialog", "newInstance() called with dynamic data");
        PromoDialog fragment = new PromoDialog();
        Bundle args = new Bundle();
        args.putSerializable(ARG_PROMO_DATA, promoData);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        android.util.Log.d("PromoDialog", "onCreate() called");

        // Get promo data from arguments if available
        if (getArguments() != null) {
            promoData = (PromoDialogResponse.PromoDialog) getArguments().getSerializable(ARG_PROMO_DATA);
            android.util.Log.d("PromoDialog", "Promo data loaded: " + (promoData != null ? promoData.getTitle() : "null"));
        }

        // Set dialog style for transparent background with proper sizing
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.PromoDialogTheme);
        android.util.Log.d("PromoDialog", "Dialog style set");
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        android.util.Log.d("PromoDialog", "onCreateDialog() called");
        Dialog dialog = super.onCreateDialog(savedInstanceState);

        // Make dialog background transparent
        Window window = dialog.getWindow();
        if (window != null) {
            android.util.Log.d("PromoDialog", "Setting window properties");
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

            // Add dim effect
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            window.setDimAmount(0.7f);
        }

        android.util.Log.d("PromoDialog", "Dialog created successfully");
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        android.util.Log.d("PromoDialog", "onCreateView() called");
        View view = inflater.inflate(R.layout.dialog_promo_sale, container, false);
        android.util.Log.d("PromoDialog", "Layout inflated successfully");
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        android.util.Log.d("PromoDialog", "onViewCreated() called");

        initViews(view);
        populateViews();
        setupClickListeners();

        // Add entrance animation
        View dialogContent = view.findViewById(R.id.dialog_content);
        if (dialogContent != null) {
            android.util.Log.d("PromoDialog", "Starting entrance animation");
            Animation scaleIn = AnimationUtils.loadAnimation(requireContext(), R.anim.dialog_scale_in);
            dialogContent.startAnimation(scaleIn);
        } else {
            android.util.Log.w("PromoDialog", "Dialog content view not found!");
        }

        android.util.Log.d("PromoDialog", "Dialog setup completed");
    }

    private void initViews(View view) {
        btnClose = view.findViewById(R.id.btn_close);
        btnShopNow = view.findViewById(R.id.btn_shop_now);
        tvTitle = view.findViewById(R.id.tv_title);
        tvSubtitle = view.findViewById(R.id.tv_subtitle);
        tvDiscountText = view.findViewById(R.id.tv_discount_text);
        tvPromoCode = view.findViewById(R.id.tv_promo_code);
        ivPromoImage = view.findViewById(R.id.iv_promo_image);

        // Initialize layout containers for color application
        mainContentLayout = view.findViewById(R.id.main_content_layout);
        headerLayout = view.findViewById(R.id.header_layout);
        productsLayout = view.findViewById(R.id.products_layout);
        discountLayout = view.findViewById(R.id.discount_layout);
    }

    private void populateViews() {
        if (promoData != null) {
            android.util.Log.d("PromoDialog", "Populating views with dynamic data");

            // Set title
            if (tvTitle != null && promoData.getTitle() != null) {
                tvTitle.setText(promoData.getTitle());
            }

            // Set subtitle
            if (tvSubtitle != null && promoData.getSubtitle() != null) {
                tvSubtitle.setText(promoData.getSubtitle());
                tvSubtitle.setVisibility(View.VISIBLE);
            } else if (tvSubtitle != null) {
                tvSubtitle.setVisibility(View.GONE);
            }

            // Set discount text
            if (tvDiscountText != null && promoData.getDiscountText() != null) {
                tvDiscountText.setText(promoData.getDiscountText());
            }

            // Set promo code
            if (tvPromoCode != null && promoData.getPromoCode() != null && !promoData.getPromoCode().isEmpty()) {
                tvPromoCode.setText(promoData.getPromoCode());
                tvPromoCode.setVisibility(View.VISIBLE);
            } else if (tvPromoCode != null) {
                tvPromoCode.setVisibility(View.GONE);
            }

            // Set button text
            if (btnShopNow != null && promoData.getButtonText() != null) {
                btnShopNow.setText(promoData.getButtonText());
            }

            // Load promo image
            if (ivPromoImage != null && promoData.getImageUrl() != null && !promoData.getImageUrl().isEmpty()) {
                android.util.Log.d("PromoDialog", "Loading image: " + promoData.getImageUrl());
                Glide.with(this)
                    .load(promoData.getImageUrl())
                    .placeholder(R.drawable.placeholder_image)
                    .error(R.drawable.placeholder_image)
                    .into(ivPromoImage);
                ivPromoImage.setVisibility(View.VISIBLE);
            } else if (ivPromoImage != null) {
                ivPromoImage.setVisibility(View.GONE);
            }

            // Apply colors if specified
            applyCustomColors();

        } else {
            android.util.Log.d("PromoDialog", "No dynamic data available, using static content");
        }
    }

    private void applyCustomColors() {
        if (promoData == null) return;

        try {
            android.util.Log.d("PromoDialog", "Applying custom colors from API");

            // Apply background (solid color or gradient)
            applyBackgroundStyle();

            // Apply text color to all text elements
            if (promoData.getTextColor() != null && !promoData.getTextColor().isEmpty()) {
                int textColor = Color.parseColor(promoData.getTextColor());
                android.util.Log.d("PromoDialog", "Applying text color: " + promoData.getTextColor());

                if (tvTitle != null) tvTitle.setTextColor(textColor);
                if (tvSubtitle != null) tvSubtitle.setTextColor(textColor);
                if (tvDiscountText != null) {
                    // For discount text, apply to background circle instead of text for better visibility
                    tvDiscountText.setTextColor(Color.parseColor("#1a1a1a")); // Dark text for contrast
                }
                if (tvPromoCode != null) tvPromoCode.setTextColor(textColor);
            }

            // Apply button color with corner radius
            if (promoData.getButtonColor() != null && !promoData.getButtonColor().isEmpty() && btnShopNow != null) {
                int buttonColor = Color.parseColor(promoData.getButtonColor());
                android.util.Log.d("PromoDialog", "Applying button color: " + promoData.getButtonColor());

                GradientDrawable buttonDrawable = new GradientDrawable();
                buttonDrawable.setColor(buttonColor);
                buttonDrawable.setCornerRadius(66f); // 22dp * 3 for enhanced corner radius
                btnShopNow.setBackground(buttonDrawable);

                // Set text color to contrast with button background
                btnShopNow.setTextColor(isColorDark(buttonColor) ? Color.WHITE : Color.BLACK);
            }

            // Apply discount badge color if background color is provided
            if (promoData.getBackgroundColor() != null && !promoData.getBackgroundColor().isEmpty() && tvDiscountText != null) {
                int bgColor = Color.parseColor(promoData.getBackgroundColor());
                // Create a complementary color for the discount badge
                int discountBadgeColor = getComplementaryColor(bgColor);

                GradientDrawable discountDrawable = new GradientDrawable();
                discountDrawable.setShape(GradientDrawable.OVAL);
                discountDrawable.setColor(discountBadgeColor);
                tvDiscountText.setBackground(discountDrawable);
            }

            android.util.Log.d("PromoDialog", "Custom colors applied successfully");

        } catch (Exception e) {
            android.util.Log.w("PromoDialog", "Error applying custom colors: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Apply background style (solid color or gradient) to dialog elements
     */
    private void applyBackgroundStyle() {
        if (promoData == null) return;

        String backgroundType = promoData.getBackgroundType();
        android.util.Log.d("PromoDialog", "Background type: " + backgroundType);

        if ("gradient".equals(backgroundType) &&
            promoData.getGradientColor1() != null && !promoData.getGradientColor1().isEmpty() &&
            promoData.getGradientColor2() != null && !promoData.getGradientColor2().isEmpty()) {

            // Apply gradient background
            applyGradientBackground();

        } else if (promoData.getBackgroundColor() != null && !promoData.getBackgroundColor().isEmpty()) {

            // Apply solid color background
            applySolidBackground();
        }
    }

    /**
     * Apply gradient background to dialog elements
     */
    private void applyGradientBackground() {
        try {
            int color1 = Color.parseColor(promoData.getGradientColor1());
            int color2 = Color.parseColor(promoData.getGradientColor2());
            String direction = promoData.getGradientDirection();

            android.util.Log.d("PromoDialog", "Applying gradient: " + promoData.getGradientColor1() +
                " to " + promoData.getGradientColor2() + " direction: " + direction);

            // Convert CSS direction to Android gradient orientation
            GradientDrawable.Orientation orientation = getGradientOrientation(direction);

            // Apply to main dialog content
            View dialogContent = getView().findViewById(R.id.dialog_content);
            if (dialogContent != null) {
                GradientDrawable gradientDrawable = new GradientDrawable(orientation, new int[]{color1, color2});
                gradientDrawable.setCornerRadius(48f);
                gradientDrawable.setAlpha(230); // Maintain transparency for glassmorphism
                dialogContent.setBackground(gradientDrawable);
            }

            // Apply gradient to LinearLayout containers with transparency
            applyGradientToLinearLayout(mainContentLayout, color1, color2, orientation, 0.1f);
            applyGradientToLinearLayout(headerLayout, color1, color2, orientation, 0.05f);
            applyGradientToLinearLayout(productsLayout, color1, color2, orientation, 0.08f);
            applyGradientToLinearLayout(discountLayout, color1, color2, orientation, 0.12f);

        } catch (Exception e) {
            android.util.Log.w("PromoDialog", "Error applying gradient background: " + e.getMessage());
            // Fallback to solid color if gradient fails
            applySolidBackground();
        }
    }

    /**
     * Apply solid color background to dialog elements
     */
    private void applySolidBackground() {
        try {
            int backgroundColor = Color.parseColor(promoData.getBackgroundColor());
            android.util.Log.d("PromoDialog", "Applying solid background color: " + promoData.getBackgroundColor());

            // Apply to main dialog content with corner radius
            View dialogContent = getView().findViewById(R.id.dialog_content);
            if (dialogContent != null) {
                GradientDrawable backgroundDrawable = new GradientDrawable();
                backgroundDrawable.setColor(backgroundColor);
                backgroundDrawable.setCornerRadius(48f);
                backgroundDrawable.setAlpha(230); // Maintain transparency for glassmorphism
                dialogContent.setBackground(backgroundDrawable);
            }

            // Apply to LinearLayout containers with subtle transparency
            applyColorToLinearLayout(mainContentLayout, backgroundColor, 0.1f);
            applyColorToLinearLayout(headerLayout, backgroundColor, 0.05f);
            applyColorToLinearLayout(productsLayout, backgroundColor, 0.08f);
            applyColorToLinearLayout(discountLayout, backgroundColor, 0.12f);

        } catch (Exception e) {
            android.util.Log.w("PromoDialog", "Error applying solid background: " + e.getMessage());
        }
    }

    /**
     * Convert CSS gradient direction to Android GradientDrawable.Orientation
     */
    private GradientDrawable.Orientation getGradientOrientation(String direction) {
        if (direction == null) return GradientDrawable.Orientation.TL_BR; // Default diagonal

        switch (direction) {
            case "0deg":
                return GradientDrawable.Orientation.LEFT_RIGHT;
            case "45deg":
                return GradientDrawable.Orientation.BL_TR;
            case "90deg":
                return GradientDrawable.Orientation.BOTTOM_TOP;
            case "135deg":
                return GradientDrawable.Orientation.BR_TL;
            case "180deg":
                return GradientDrawable.Orientation.RIGHT_LEFT;
            case "270deg":
                return GradientDrawable.Orientation.TOP_BOTTOM;
            default:
                return GradientDrawable.Orientation.TL_BR; // Default diagonal
        }
    }

    /**
     * Apply gradient to LinearLayout with specified transparency
     */
    private void applyGradientToLinearLayout(LinearLayout layout, int color1, int color2,
                                           GradientDrawable.Orientation orientation, float alpha) {
        if (layout != null) {
            GradientDrawable gradientDrawable = new GradientDrawable(orientation, new int[]{color1, color2});
            gradientDrawable.setCornerRadius(24f);
            gradientDrawable.setAlpha((int) (255 * alpha)); // Apply transparency
            layout.setBackground(gradientDrawable);

            android.util.Log.d("PromoDialog", "Applied gradient to LinearLayout with alpha: " + alpha);
        }
    }

    /**
     * Apply color to LinearLayout with specified transparency and corner radius
     */
    private void applyColorToLinearLayout(LinearLayout layout, int color, float alpha) {
        applyColorToLinearLayout(layout, color, alpha, 24f);
    }

    /**
     * Apply color to LinearLayout with specified transparency and custom corner radius
     */
    private void applyColorToLinearLayout(LinearLayout layout, int color, float alpha, float cornerRadius) {
        if (layout != null) {
            GradientDrawable drawable = new GradientDrawable();
            drawable.setColor(color);
            drawable.setCornerRadius(cornerRadius);
            drawable.setAlpha((int) (255 * alpha)); // Apply transparency
            layout.setBackground(drawable);

            android.util.Log.d("PromoDialog", "Applied color to LinearLayout with alpha: " + alpha +
                ", corner radius: " + cornerRadius);
        }
    }

    /**
     * Check if a color is dark (for text contrast)
     */
    private boolean isColorDark(int color) {
        double darkness = 1 - (0.299 * Color.red(color) + 0.587 * Color.green(color) + 0.114 * Color.blue(color)) / 255;
        return darkness >= 0.5;
    }

    /**
     * Get complementary color for better contrast
     */
    private int getComplementaryColor(int color) {
        int red = 255 - Color.red(color);
        int green = 255 - Color.green(color);
        int blue = 255 - Color.blue(color);
        return Color.rgb(red, green, blue);
    }

    private void setupClickListeners() {
        // Close button
        btnClose.setOnClickListener(v -> dismissDialog());

        // Shop now button - navigate to services
        btnShopNow.setOnClickListener(v -> {
            dismissDialog();
            navigateToServices();
        });

        // Dismiss when clicking outside (on the background)
        if (getView() != null) {
            getView().setOnClickListener(v -> {
                // Only dismiss if clicking on the background, not the dialog content
                if (v.getId() == getView().getId()) {
                    dismissDialog();
                }
            });
        }
    }

    private void dismissDialog() {
        if (isAdded() && !isDetached() && getView() != null) {
            View dialogContent = getView().findViewById(R.id.dialog_content);
            if (dialogContent != null) {
                Animation scaleOut = AnimationUtils.loadAnimation(requireContext(), R.anim.dialog_scale_out);
                scaleOut.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {}

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        if (isAdded() && !isDetached()) {
                            dismiss();
                        }
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {}
                });
                dialogContent.startAnimation(scaleOut);
            } else {
                dismiss();
            }
        }
    }

    private void navigateToServices() {
        try {
            if (getActivity() instanceof MainActivity) {
                MainActivity mainActivity = (MainActivity) getActivity();

                // Navigate to services fragment
                mainActivity.getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                    .replace(R.id.fragment_container, new ServicesFragment())
                    .addToBackStack(null)
                    .commit();
            }
        } catch (Exception e) {
            // Handle navigation error silently
            e.printStackTrace();
        }
    }

    @Override
    public void onStart() {
        super.onStart();

        // Set dialog to wrap content with proper margins
        Dialog dialog = getDialog();
        if (dialog != null && dialog.getWindow() != null) {
            dialog.getWindow().setLayout(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT
            );

            // Ensure dialog is centered
            dialog.getWindow().setGravity(android.view.Gravity.CENTER);
        }
    }
}
