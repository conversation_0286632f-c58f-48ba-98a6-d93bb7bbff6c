<?php
/**
 * Register API Endpoint
 *
 * This endpoint handles user registration
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OtpManager.php';
require_once '../includes/UserManager.php';
require_once '../includes/SettingsManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Initialize settings manager
$settingsManager = new SettingsManager($pdo);

// Check if OTP verification is enabled
$otpEnabled = $settingsManager->isOtpEnabled();

// Validate input
if (empty($inputData['full_name'])) {
    jsonResponse(false, 'Full name is required', [], 400);
}

if (empty($inputData['phone'])) {
    jsonResponse(false, 'Phone number is required', [], 400);
}

if (empty($inputData['password'])) {
    jsonResponse(false, 'Password is required', [], 400);
}

// Only require OTP if OTP verification is enabled
if ($otpEnabled && empty($inputData['otp'])) {
    jsonResponse(false, 'OTP is required', [], 400);
}

$fullName = sanitize($inputData['full_name']);
$phone = sanitize($inputData['phone']);
$email = !empty($inputData['email']) ? sanitize($inputData['email']) : null;
$password = $inputData['password']; // Don't sanitize password
$address = !empty($inputData['address']) ? sanitize($inputData['address']) : null;
$divisionId = !empty($inputData['division_id']) ? (int)$inputData['division_id'] : null;
$districtId = !empty($inputData['district_id']) ? (int)$inputData['district_id'] : null;
$upazillaId = !empty($inputData['upazilla_id']) ? (int)$inputData['upazilla_id'] : null;
$divisionName = !empty($inputData['division_name']) ? sanitize($inputData['division_name']) : null;
$districtName = !empty($inputData['district_name']) ? sanitize($inputData['district_name']) : null;
$upazillaName = !empty($inputData['upazilla_name']) ? sanitize($inputData['upazilla_name']) : null;

// Log location data for debugging
error_log("Registration with location data:");
error_log("Division ID: " . $divisionId . ", Name: " . $divisionName);
error_log("District ID: " . $districtId . ", Name: " . $districtName);
error_log("Upazilla ID: " . $upazillaId . ", Name: " . $upazillaName);

// Validate phone number
if (!validatePhone($phone)) {
    jsonResponse(false, 'Invalid phone number format', [], 400);
}

// Validate email if provided
if ($email && !validateEmail($email)) {
    jsonResponse(false, 'Invalid email format', [], 400);
}

// Validate password strength
if (strlen($password) < 8) {
    jsonResponse(false, 'Password must be at least 8 characters long', [], 400);
}

// Initialize managers
$otpManager = new OtpManager($pdo);
$userManager = new UserManager($pdo);

// Check if user already exists
if ($userManager->userExistsByPhone($phone)) {
    jsonResponse(false, 'Phone number already registered', [], 400);
}

if ($email && $userManager->userExistsByEmail($email)) {
    jsonResponse(false, 'Email already registered', [], 400);
}

// Default to valid OTP
$isValid = true;

// Only verify OTP if OTP verification is enabled
if ($otpEnabled) {
    // Get OTP from input or use empty string if not provided
    $otp = !empty($inputData['otp']) ? sanitize($inputData['otp']) : '';

    // Verify OTP - also check recently used OTPs for registration after verification
    $isValid = $otpManager->verifyOtp($phone, $otp, 'registration', true);

    if (!$isValid) {
        jsonResponse(false, 'Invalid or expired OTP', [], 400);
    }

    // Log successful OTP verification for registration
    error_log("register.php - OTP verified successfully for phone: {$phone}");
}

// Register user
$userId = $userManager->registerUser($fullName, $phone, $email, $password, $address, $divisionId, $districtId, $upazillaId, true, $divisionName, $districtName, $upazillaName);

if (!$userId) {
    jsonResponse(false, 'Failed to register user. Please try again later.', [], 500);
}

// Get user data with location names
$user = $userManager->getUserByPhoneWithLocationNames($phone);

// Remove sensitive data
unset($user['password']);

// Set session
$_SESSION['user_id'] = $user['id'];
$_SESSION['phone'] = $user['phone'];
$_SESSION['is_logged_in'] = true;

// Return success response
jsonResponse(true, 'Registration successful', [
    'user' => $user
]);
