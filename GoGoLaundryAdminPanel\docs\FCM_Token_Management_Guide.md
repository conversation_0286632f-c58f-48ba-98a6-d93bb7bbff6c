# FCM Token Management - Best Practices Guide

## Current Issues

1. **FCM tokens are not registered during user registration/login**
2. **Tokens are only registered if user is already logged in**
3. **No automatic token refresh handling**
4. **Missing token cleanup on logout**

## Recommended Implementation

### 1. Android App Changes

#### A. Update Login Success Handler
Add FCM token registration immediately after successful login:

```java
// In LoginActivity.java - after successful login
private void onLoginSuccess(UserResponse user) {
    // Save user session
    sessionManager.saveUser(user);
    
    // Register FCM token immediately after login
    GoGoLaundryApp.getInstance().registerFCMToken();
    
    // Navigate to main activity
    Intent intent = new Intent(this, MainActivity.class);
    startActivity(intent);
    finish();
}
```

#### B. Update Registration Success Handler
Add FCM token registration immediately after successful registration:

```java
// In SignupActivity.java - after successful registration
private void onRegistrationSuccess(UserResponse user) {
    // Save user session
    sessionManager.saveUser(user);
    
    // Register FCM token immediately after registration
    GoGoLaundryApp.getInstance().registerFCMToken();
    
    // Navigate to main activity
    Intent intent = new Intent(this, MainActivity.class);
    startActivity(intent);
    finish();
}
```

#### C. Improve FCM Service
Update the FCM service to handle token registration better:

```java
// In GoGoLaundryFirebaseMessagingService.java
@Override
public void onNewToken(String token) {
    Log.d(TAG, "Refreshed token: " + token);
    
    // Always save token locally first
    SessionManager sessionManager = new SessionManager(this);
    sessionManager.saveFCMToken(token);
    
    // Send to server (will handle login check internally)
    sendTokenToServer(token);
}

private void sendTokenToServer(String token) {
    SessionManager sessionManager = new SessionManager(this);
    
    if (!sessionManager.isLoggedIn()) {
        Log.d(TAG, "User not logged in, token will be registered after login");
        return;
    }
    
    // Register token with server
    GoGoLaundryApp.getInstance().registerFCMTokenWithServer(token);
}
```

### 2. Backend Improvements

#### A. Enhanced Token Registration API
The current API is good, but we can add better logging and error handling:

```php
// In register_token.php - add better logging
if ($result['success']) {
    // Log successful registration
    error_log("FCM Token registered: User $userId, Device $deviceId ($deviceType)");
    
    // Optional: Clean up old tokens for this user/device
    $stmt = $pdo->prepare("UPDATE fcm_tokens SET is_active = 0 WHERE user_id = ? AND device_id != ? AND device_type = ?");
    $stmt->execute([$userId, $deviceId, $deviceType]);
}
```

#### B. Token Cleanup on Logout
Create a new API endpoint for token cleanup:

```php
// Create: api/fcm/deactivate_token.php
<?php
header('Content-Type: application/json');

require_once '../../config/db.php';
require_once '../../includes/FCMService.php';

$input = json_decode(file_get_contents('php://input'), true);
$userId = $input['user_id'];
$deviceId = $input['device_id'];

$fcmService = new FCMService();
$result = $fcmService->deactivateUserDeviceToken($pdo, $userId, $deviceId);

echo json_encode($result);
?>
```

### 3. Token Lifecycle Management

#### When to Register/Update Tokens:

1. **App First Launch** ✅ (Already implemented)
2. **User Registration** ❌ (Need to add)
3. **User Login** ❌ (Need to add)
4. **Token Refresh** ✅ (Already implemented)
5. **App Resume** ✅ (Already implemented)

#### When to Deactivate Tokens:

1. **User Logout** ❌ (Need to add)
2. **App Uninstall** (Automatic via Firebase)
3. **Device Change** (Automatic via unique device_id)

### 4. Database Best Practices

#### Current Table Structure (Good):
```sql
CREATE TABLE `fcm_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `token` text NOT NULL,
  `device_type` enum('android','ios','web') NOT NULL DEFAULT 'android',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_device` (`user_id`,`device_id`)
);
```

#### Recommended Indexes:
- ✅ `user_id` (for finding user's tokens)
- ✅ `is_active` (for finding active tokens)
- ✅ `user_device` unique (prevents duplicates)

### 5. Testing Checklist

- [ ] Register new user → FCM token should be registered
- [ ] Login existing user → FCM token should be updated
- [ ] App restart → FCM token should be refreshed if needed
- [ ] Logout → FCM token should be deactivated
- [ ] Multiple devices → Each device should have separate token
- [ ] Token refresh → Old token should be replaced

### 6. Monitoring & Debugging

#### Add Logging:
```java
// In Android app
Log.d("FCM", "Token registration status: " + (success ? "SUCCESS" : "FAILED"));
Log.d("FCM", "User ID: " + userId + ", Device ID: " + deviceId);
```

#### Check Database:
```sql
-- View active tokens
SELECT u.full_name, ft.device_type, ft.created_at, ft.updated_at 
FROM fcm_tokens ft 
JOIN users u ON ft.user_id = u.id 
WHERE ft.is_active = 1 
ORDER BY ft.updated_at DESC;

-- Count tokens per user
SELECT u.full_name, COUNT(*) as token_count 
FROM fcm_tokens ft 
JOIN users u ON ft.user_id = u.id 
WHERE ft.is_active = 1 
GROUP BY u.id;
```

## Implementation Priority

1. **High Priority**: Add FCM registration to login/registration success
2. **Medium Priority**: Add token deactivation on logout
3. **Low Priority**: Add advanced token cleanup and monitoring

This will ensure FCM tokens are properly managed throughout the user lifecycle.
