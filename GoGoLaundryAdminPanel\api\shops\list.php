<?php
/**
 * Get all laundry shops
 *
 * Parameters:
 * - active_only (optional): Get only active shops (1) or all shops (0) (default: 1)
 * - verified_only (optional): Get only verified shops (1) or all shops (0) (default: 1)
 * - limit (optional): Maximum number of shops to return (default: 50)
 * - offset (optional): Number of shops to skip (default: 0)
 * - division_id (optional): Filter by division ID
 * - district_id (optional): Filter by district ID
 * - upazilla_id (optional): Filter by upazilla ID
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../../includes/functions.php';

try {
    // Get parameters
    $activeOnly = isset($_GET['active_only']) ? intval($_GET['active_only']) : 1;
    $verifiedOnly = isset($_GET['verified_only']) ? intval($_GET['verified_only']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    $divisionId = isset($_GET['division_id']) ? intval($_GET['division_id']) : null;
    $districtId = isset($_GET['district_id']) ? intval($_GET['district_id']) : null;
    $upazillaId = isset($_GET['upazilla_id']) ? intval($_GET['upazilla_id']) : null;

    // Validate parameters
    if ($limit > 100) {
        $limit = 100; // Maximum limit
    }
    if ($limit < 1) {
        $limit = 50; // Default limit
    }

    // Build WHERE clause
    $whereConditions = [];
    $params = [];

    if ($activeOnly) {
        $whereConditions[] = "ls.is_active = 1";
    }

    if ($verifiedOnly) {
        $whereConditions[] = "ls.is_verified = 1";
    }

    if ($divisionId) {
        $whereConditions[] = "ls.division_id = ?";
        $params[] = $divisionId;
    }

    if ($districtId) {
        $whereConditions[] = "ls.district_id = ?";
        $params[] = $districtId;
    }

    if ($upazillaId) {
        $whereConditions[] = "ls.upazilla_id = ?";
        $params[] = $upazillaId;
    }

    $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

    // SQL query to get shops
    $sql = "
        SELECT
            ls.*,
            d.name as division_name,
            dist.name as district_name,
            up.name as upazilla_name
        FROM laundry_shops ls
        LEFT JOIN divisions d ON ls.division_id = d.id
        LEFT JOIN districts dist ON ls.district_id = dist.id
        LEFT JOIN upazillas up ON ls.upazilla_id = up.id
        $whereClause
        ORDER BY ls.rating DESC, ls.created_at DESC
        LIMIT ? OFFSET ?
    ";

    $params[] = $limit;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $shops = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get total count for pagination
    $countSql = "
        SELECT COUNT(*) as total_count
        FROM laundry_shops ls
        $whereClause
    ";
    $countParams = array_slice($params, 0, -2); // Remove limit and offset
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($countParams);
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total_count'];

    // Format the response
    $formattedShops = [];
    foreach ($shops as $shop) {
        $formattedShop = [
            'id' => intval($shop['id']),
            'name' => $shop['name'],
            'bn_name' => $shop['bn_name'],
            'description' => $shop['description'],
            'bn_description' => $shop['bn_description'],
            'owner_name' => $shop['owner_name'],
            'phone' => $shop['phone'],
            'email' => $shop['email'],
            'address' => $shop['address'],
            'division_id' => $shop['division_id'] ? intval($shop['division_id']) : null,
            'district_id' => $shop['district_id'] ? intval($shop['district_id']) : null,
            'upazilla_id' => $shop['upazilla_id'] ? intval($shop['upazilla_id']) : null,
            'division_name' => $shop['division_name'],
            'district_name' => $shop['district_name'],
            'upazilla_name' => $shop['upazilla_name'],
            'latitude' => floatval($shop['latitude']),
            'longitude' => floatval($shop['longitude']),
            'operating_hours' => $shop['operating_hours'] ? json_decode($shop['operating_hours'], true) : null,
            'rating' => floatval($shop['rating']),
            'total_reviews' => intval($shop['total_reviews']),
            'commission_percentage' => floatval($shop['commission_percentage']),
            'is_active' => boolval($shop['is_active']),
            'is_verified' => boolval($shop['is_verified']),
            'profile_image_url' => $shop['profile_image_url'],
            'cover_image_url' => $shop['cover_image_url'],
            'created_at' => $shop['created_at'],
            'updated_at' => $shop['updated_at']
        ];

        // Get shop services
        $servicesSql = "
            SELECT s.id, s.name, s.bn_name, s.image_url, ss.estimated_hours
            FROM shop_services ss
            INNER JOIN services s ON ss.service_id = s.id
            WHERE ss.shop_id = ? AND ss.is_available = 1 AND s.is_active = 1
            ORDER BY s.sort_order ASC
        ";
        $servicesStmt = $pdo->prepare($servicesSql);
        $servicesStmt->execute([$shop['id']]);
        $formattedShop['services'] = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);

        // Check if shop is currently open
        $formattedShop['is_open'] = isShopOpen($shop['operating_hours']);

        $formattedShops[] = $formattedShop;
    }

    // Response
    $response = [
        'success' => true,
        'message' => 'Shops retrieved successfully',
        'data' => $formattedShops,
        'pagination' => [
            'total' => intval($totalCount),
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $totalCount
        ]
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error retrieving shops: ' . $e->getMessage(),
        'data' => null
    ], JSON_PRETTY_PRINT);
}
?>
