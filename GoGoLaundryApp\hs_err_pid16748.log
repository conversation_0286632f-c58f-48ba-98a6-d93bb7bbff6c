#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=16748, tid=14584
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-bin\cetblhg4pflnnks72fxwobvgv\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Sat May 24 12:05:03 2025 Bangladesh Standard Time elapsed time: 42.018401 seconds (0d 0h 0m 42s)

---------------  T H R E A D  ---------------

Current thread (0x0000023d26e3e8c0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=14584, stack(0x000000edefb00000,0x000000edefc00000) (1024K)]


Current CompileTask:
C2:  42018 22601       4       com.sun.tools.javac.jvm.ClassReader::readClass (670 bytes)

Stack: [0x000000edefb00000,0x000000edefc00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0xc3f3d]
V  [jvm.dll+0xc4473]
V  [jvm.dll+0x65ab51]
V  [jvm.dll+0x65bbd6]
V  [jvm.dll+0x659f0d]
V  [jvm.dll+0x249a55]
V  [jvm.dll+0x2491b1]
V  [jvm.dll+0x1c9634]
V  [jvm.dll+0x258859]
V  [jvm.dll+0x256e3a]
V  [jvm.dll+0x3ef6c6]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000023d79f1ba00, length=153, elements={
0x0000023d0a433d10, 0x0000023d26e33590, 0x0000023d26e341a0, 0x0000023d26e35dc0,
0x0000023d26e36c50, 0x0000023d26e37860, 0x0000023d26e38470, 0x0000023d26e3e8c0,
0x0000023d26e41520, 0x0000023d68161590, 0x0000023d681622b0, 0x0000023d68161c20,
0x0000023d6815fb50, 0x0000023d681601e0, 0x0000023d68160f00, 0x0000023d6e3456d0,
0x0000023d6e3449b0, 0x0000023d6e343c90, 0x0000023d6e347110, 0x0000023d6e34a590,
0x0000023d6e346a80, 0x0000023d6e3477a0, 0x0000023d6e345040, 0x0000023d6e344320,
0x0000023d6e347e30, 0x0000023d6e3463f0, 0x0000023d6e3484c0, 0x0000023d6e348b50,
0x0000023d6e349870, 0x0000023d6e343600, 0x0000023d6e34ac20, 0x0000023d6e671b80,
0x0000023d6e676a40, 0x0000023d6e6714f0, 0x0000023d6e670e60, 0x0000023d6e6763b0,
0x0000023d6e672f30, 0x0000023d6e678480, 0x0000023d6e672210, 0x0000023d6e6735c0,
0x0000023d6e673c50, 0x0000023d6e6742e0, 0x0000023d68579020, 0x0000023d685747f0,
0x0000023d6857a3d0, 0x0000023d68577c70, 0x0000023d68576230, 0x0000023d685768c0,
0x0000023d68576f50, 0x0000023d685775e0, 0x0000023d68578300, 0x0000023d6857aa60,
0x0000023d68578990, 0x0000023d685796b0, 0x0000023d68574e80, 0x0000023d68579d40,
0x0000023d6857b0f0, 0x0000023d68574160, 0x0000023d68575ba0, 0x0000023d6fe487a0,
0x0000023d6fe47a80, 0x0000023d6fe473f0, 0x0000023d73470b80, 0x0000023d7346fe60,
0x0000023d734718a0, 0x0000023d734732e0, 0x0000023d68162940, 0x0000023d6e9ba490,
0x0000023d6e9b7d30, 0x0000023d6e9bab20, 0x0000023d6e9b9e00, 0x0000023d6e9becc0,
0x0000023d6e9b90e0, 0x0000023d6e9b9770, 0x0000023d6e9b8a50, 0x0000023d6e9bb1b0,
0x0000023d6e9be630, 0x0000023d6e9bc560, 0x0000023d6e9bb840, 0x0000023d6e9bbed0,
0x0000023d6e9bd280, 0x0000023d6e9bd910, 0x0000023d6e9bdfa0, 0x0000023d6e9bf350,
0x0000023d6e9b83c0, 0x0000023d72bea090, 0x0000023d72bf0300, 0x0000023d72bea720,
0x0000023d72bedba0, 0x0000023d72bec160, 0x0000023d72bf16b0, 0x0000023d72bec7f0,
0x0000023d72befc70, 0x0000023d72bf0990, 0x0000023d72beadb0, 0x0000023d72beb440,
0x0000023d72bf1020, 0x0000023d72bee8c0, 0x0000023d72bece80, 0x0000023d72beef50,
0x0000023d72bee230, 0x0000023d72bef5e0, 0x0000023d7346f7d0, 0x0000023d73471210,
0x0000023d73471f30, 0x0000023d734725c0, 0x0000023d73472c50, 0x0000023d73667980,
0x0000023d7366a0e0, 0x0000023d736665d0, 0x0000023d736686a0, 0x0000023d784b18c0,
0x0000023d784af160, 0x0000023d784b0510, 0x0000023d784b46b0, 0x0000023d784b4d40,
0x0000023d784b0ba0, 0x0000023d784b53d0, 0x0000023d784b3990, 0x0000023d784b1f50,
0x0000023d784af7f0, 0x0000023d784b1230, 0x0000023d784b25e0, 0x0000023d784b2c70,
0x0000023d784b3300, 0x0000023d7366ced0, 0x0000023d7366b490, 0x0000023d7366d560,
0x0000023d73666c60, 0x0000023d7366dbf0, 0x0000023d736693c0, 0x0000023d736672f0,
0x0000023d73668010, 0x0000023d73668d30, 0x0000023d78b2b570, 0x0000023d78b317e0,
0x0000023d78b2fda0, 0x0000023d78b2bc00, 0x0000023d78b31e70, 0x0000023d78b2aee0,
0x0000023d78b33f40, 0x0000023d78b2f080, 0x0000023d78b2e360, 0x0000023d78b36010,
0x0000023d78b373c0, 0x0000023d78b345d0, 0x0000023d78b36d30, 0x0000023d78b37a50,
0x0000023d78b366a0, 0x0000023d78b380e0, 0x0000023d78b39490, 0x0000023d6854cf30,
0x0000023d7d088d80
}

Java Threads: ( => current thread )
  0x0000023d0a433d10 JavaThread "main"                              [_thread_blocked, id=1232, stack(0x000000edeed00000,0x000000edeee00000) (1024K)]
  0x0000023d26e33590 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7812, stack(0x000000edef500000,0x000000edef600000) (1024K)]
  0x0000023d26e341a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=10168, stack(0x000000edef600000,0x000000edef700000) (1024K)]
  0x0000023d26e35dc0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16528, stack(0x000000edef700000,0x000000edef800000) (1024K)]
  0x0000023d26e36c50 JavaThread "Attach Listener"            daemon [_thread_blocked, id=17668, stack(0x000000edef800000,0x000000edef900000) (1024K)]
  0x0000023d26e37860 JavaThread "Service Thread"             daemon [_thread_blocked, id=6068, stack(0x000000edef900000,0x000000edefa00000) (1024K)]
  0x0000023d26e38470 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=2872, stack(0x000000edefa00000,0x000000edefb00000) (1024K)]
=>0x0000023d26e3e8c0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=14584, stack(0x000000edefb00000,0x000000edefc00000) (1024K)]
  0x0000023d26e41520 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=13084, stack(0x000000edefc00000,0x000000edefd00000) (1024K)]
  0x0000023d68161590 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=2140, stack(0x000000edefd00000,0x000000edefe00000) (1024K)]
  0x0000023d681622b0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=18192, stack(0x000000edefe00000,0x000000edeff00000) (1024K)]
  0x0000023d68161c20 JavaThread "Daemon health stats"               [_thread_blocked, id=10892, stack(0x000000edf0800000,0x000000edf0900000) (1024K)]
  0x0000023d6815fb50 JavaThread "Incoming local TCP Connector on port 56625"        [_thread_in_native, id=3884, stack(0x000000edf0900000,0x000000edf0a00000) (1024K)]
  0x0000023d681601e0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=8600, stack(0x000000edf0a00000,0x000000edf0b00000) (1024K)]
  0x0000023d68160f00 JavaThread "Daemon"                            [_thread_blocked, id=2736, stack(0x000000edf0b00000,0x000000edf0c00000) (1024K)]
  0x0000023d6e3456d0 JavaThread "Handler for socket connection from /127.0.0.1:56625 to /127.0.0.1:56626"        [_thread_in_native, id=10924, stack(0x000000edf0c00000,0x000000edf0d00000) (1024K)]
  0x0000023d6e3449b0 JavaThread "Cancel handler"                    [_thread_blocked, id=13820, stack(0x000000edf0d00000,0x000000edf0e00000) (1024K)]
  0x0000023d6e343c90 JavaThread "Daemon worker"                     [_thread_blocked, id=17260, stack(0x000000edf0e00000,0x000000edf0f00000) (1024K)]
  0x0000023d6e347110 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56625 to /127.0.0.1:56626"        [_thread_blocked, id=3040, stack(0x000000edf0f00000,0x000000edf1000000) (1024K)]
  0x0000023d6e34a590 JavaThread "Stdin handler"                     [_thread_blocked, id=7336, stack(0x000000edf1000000,0x000000edf1100000) (1024K)]
  0x0000023d6e346a80 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=7724, stack(0x000000edf1100000,0x000000edf1200000) (1024K)]
  0x0000023d6e3477a0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=17148, stack(0x000000edf0000000,0x000000edf0100000) (1024K)]
  0x0000023d6e345040 JavaThread "File lock request listener"        [_thread_in_native, id=10672, stack(0x000000edf1200000,0x000000edf1300000) (1024K)]
  0x0000023d6e344320 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)"        [_thread_blocked, id=17720, stack(0x000000edf1300000,0x000000edf1400000) (1024K)]
  0x0000023d6e347e30 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\8.12\fileHashes)"        [_thread_blocked, id=17844, stack(0x000000edf1600000,0x000000edf1700000) (1024K)]
  0x0000023d6e3463f0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\buildOutputCleanup)"        [_thread_blocked, id=10824, stack(0x000000edf1700000,0x000000edf1800000) (1024K)]
  0x0000023d6e3484c0 JavaThread "File watcher server"        daemon [_thread_in_native, id=4560, stack(0x000000edf1800000,0x000000edf1900000) (1024K)]
  0x0000023d6e348b50 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=14728, stack(0x000000edf1900000,0x000000edf1a00000) (1024K)]
  0x0000023d6e349870 JavaThread "File lock release action executor"        [_thread_blocked, id=5048, stack(0x000000edf1a00000,0x000000edf1b00000) (1024K)]
  0x0000023d6e343600 JavaThread "jar transforms"                    [_thread_blocked, id=9580, stack(0x000000edf1b00000,0x000000edf1c00000) (1024K)]
  0x0000023d6e34ac20 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\8.12\checksums)"        [_thread_blocked, id=12968, stack(0x000000edf1400000,0x000000edf1500000) (1024K)]
  0x0000023d6e671b80 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)"        [_thread_blocked, id=17124, stack(0x000000edf1c00000,0x000000edf1d00000) (1024K)]
  0x0000023d6e676a40 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)"        [_thread_blocked, id=9496, stack(0x000000edf1d00000,0x000000edf1e00000) (1024K)]
  0x0000023d6e6714f0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)"        [_thread_blocked, id=14892, stack(0x000000edf1e00000,0x000000edf1f00000) (1024K)]
  0x0000023d6e670e60 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=9316, stack(0x000000edf2000000,0x000000edf2100000) (1024K)]
  0x0000023d6e6763b0 JavaThread "Problems report writer"            [_thread_blocked, id=7608, stack(0x000000edf1f00000,0x000000edf2000000) (1024K)]
  0x0000023d6e672f30 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=15168, stack(0x000000edf2200000,0x000000edf2300000) (1024K)]
  0x0000023d6e678480 JavaThread "Unconstrained build operations"        [_thread_blocked, id=7408, stack(0x000000edf2300000,0x000000edf2400000) (1024K)]
  0x0000023d6e672210 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=14320, stack(0x000000edf2400000,0x000000edf2500000) (1024K)]
  0x0000023d6e6735c0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=15148, stack(0x000000edf2500000,0x000000edf2600000) (1024K)]
  0x0000023d6e673c50 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=10460, stack(0x000000edf2600000,0x000000edf2700000) (1024K)]
  0x0000023d6e6742e0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=2960, stack(0x000000edf2700000,0x000000edf2800000) (1024K)]
  0x0000023d68579020 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=9256, stack(0x000000edf2800000,0x000000edf2900000) (1024K)]
  0x0000023d685747f0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=16980, stack(0x000000edf2900000,0x000000edf2a00000) (1024K)]
  0x0000023d6857a3d0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=6992, stack(0x000000edf2a00000,0x000000edf2b00000) (1024K)]
  0x0000023d68577c70 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=4672, stack(0x000000edf2b00000,0x000000edf2c00000) (1024K)]
  0x0000023d68576230 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=9400, stack(0x000000edf2c00000,0x000000edf2d00000) (1024K)]
  0x0000023d685768c0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=14240, stack(0x000000edf2d00000,0x000000edf2e00000) (1024K)]
  0x0000023d68576f50 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=11004, stack(0x000000edf2e00000,0x000000edf2f00000) (1024K)]
  0x0000023d685775e0 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=132, stack(0x000000edf2f00000,0x000000edf3000000) (1024K)]
  0x0000023d68578300 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=13028, stack(0x000000edf3000000,0x000000edf3100000) (1024K)]
  0x0000023d6857aa60 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=1424, stack(0x000000edf3100000,0x000000edf3200000) (1024K)]
  0x0000023d68578990 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=6364, stack(0x000000edf3200000,0x000000edf3300000) (1024K)]
  0x0000023d685796b0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=10588, stack(0x000000edf3300000,0x000000edf3400000) (1024K)]
  0x0000023d68574e80 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=9484, stack(0x000000edf3400000,0x000000edf3500000) (1024K)]
  0x0000023d68579d40 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=7400, stack(0x000000edf3500000,0x000000edf3600000) (1024K)]
  0x0000023d6857b0f0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=16120, stack(0x000000edf3600000,0x000000edf3700000) (1024K)]
  0x0000023d68574160 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=8688, stack(0x000000edf3700000,0x000000edf3800000) (1024K)]
  0x0000023d68575ba0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=17324, stack(0x000000edf3800000,0x000000edf3900000) (1024K)]
  0x0000023d6fe487a0 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=9004, stack(0x000000edf3900000,0x000000edf3a00000) (1024K)]
  0x0000023d6fe47a80 JavaThread "Memory manager"                    [_thread_blocked, id=9368, stack(0x000000edf2100000,0x000000edf2200000) (1024K)]
  0x0000023d6fe473f0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=10116, stack(0x000000edf3b00000,0x000000edf3c00000) (1024K)]
  0x0000023d73470b80 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=2264, stack(0x000000edf3c00000,0x000000edf3d00000) (1024K)]
  0x0000023d7346fe60 JavaThread "build event listener"              [_thread_blocked, id=2692, stack(0x000000edf3d00000,0x000000edf3e00000) (1024K)]
  0x0000023d734718a0 JavaThread "included builds"                   [_thread_in_Java, id=15284, stack(0x000000edf3a00000,0x000000edf3b00000) (1024K)]
  0x0000023d734732e0 JavaThread "Execution worker"                  [_thread_blocked, id=3544, stack(0x000000edf3e00000,0x000000edf3f00000) (1024K)]
  0x0000023d68162940 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=16108, stack(0x000000edf3f00000,0x000000edf4000000) (1024K)]
  0x0000023d6e9ba490 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=2728, stack(0x000000edf4000000,0x000000edf4100000) (1024K)]
  0x0000023d6e9b7d30 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=16724, stack(0x000000edf4100000,0x000000edf4200000) (1024K)]
  0x0000023d6e9bab20 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=10048, stack(0x000000edf4200000,0x000000edf4300000) (1024K)]
  0x0000023d6e9b9e00 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=9836, stack(0x000000edf4300000,0x000000edf4400000) (1024K)]
  0x0000023d6e9becc0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=11800, stack(0x000000edf4400000,0x000000edf4500000) (1024K)]
  0x0000023d6e9b90e0 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\8.12\executionHistory)"        [_thread_blocked, id=13672, stack(0x000000edf4500000,0x000000edf4600000) (1024K)]
  0x0000023d6e9b9770 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=17912, stack(0x000000edf4600000,0x000000edf4700000) (1024K)]
  0x0000023d6e9b8a50 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=3060, stack(0x000000edf4700000,0x000000edf4800000) (1024K)]
  0x0000023d6e9bb1b0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=18252, stack(0x000000edf4800000,0x000000edf4900000) (1024K)]
  0x0000023d6e9be630 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=17536, stack(0x000000edf4900000,0x000000edf4a00000) (1024K)]
  0x0000023d6e9bc560 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=13024, stack(0x000000edf4a00000,0x000000edf4b00000) (1024K)]
  0x0000023d6e9bb840 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=2268, stack(0x000000edf4b00000,0x000000edf4c00000) (1024K)]
  0x0000023d6e9bbed0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=7700, stack(0x000000edf4c00000,0x000000edf4d00000) (1024K)]
  0x0000023d6e9bd280 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=11788, stack(0x000000edf4d00000,0x000000edf4e00000) (1024K)]
  0x0000023d6e9bd910 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=5424, stack(0x000000edf4e00000,0x000000edf4f00000) (1024K)]
  0x0000023d6e9bdfa0 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=9040, stack(0x000000edf4f00000,0x000000edf5000000) (1024K)]
  0x0000023d6e9bf350 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=16744, stack(0x000000edf5000000,0x000000edf5100000) (1024K)]
  0x0000023d6e9b83c0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=17832, stack(0x000000edf5100000,0x000000edf5200000) (1024K)]
  0x0000023d72bea090 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=14552, stack(0x000000edf5200000,0x000000edf5300000) (1024K)]
  0x0000023d72bf0300 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=16032, stack(0x000000edf5300000,0x000000edf5400000) (1024K)]
  0x0000023d72bea720 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=2540, stack(0x000000edf5500000,0x000000edf5600000) (1024K)]
  0x0000023d72bedba0 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=14568, stack(0x000000edf5600000,0x000000edf5700000) (1024K)]
  0x0000023d72bec160 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=12300, stack(0x000000edf5700000,0x000000edf5800000) (1024K)]
  0x0000023d72bf16b0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=836, stack(0x000000edf5800000,0x000000edf5900000) (1024K)]
  0x0000023d72bec7f0 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=7704, stack(0x000000edf5900000,0x000000edf5a00000) (1024K)]
  0x0000023d72befc70 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=7364, stack(0x000000edf5a00000,0x000000edf5b00000) (1024K)]
  0x0000023d72bf0990 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=560, stack(0x000000edf5b00000,0x000000edf5c00000) (1024K)]
  0x0000023d72beadb0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=17764, stack(0x000000edf5c00000,0x000000edf5d00000) (1024K)]
  0x0000023d72beb440 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=17604, stack(0x000000edf5d00000,0x000000edf5e00000) (1024K)]
  0x0000023d72bf1020 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=1864, stack(0x000000edf5e00000,0x000000edf5f00000) (1024K)]
  0x0000023d72bee8c0 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=9892, stack(0x000000edf5f00000,0x000000edf6000000) (1024K)]
  0x0000023d72bece80 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=16496, stack(0x000000edf6000000,0x000000edf6100000) (1024K)]
  0x0000023d72beef50 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=18180, stack(0x000000edf6100000,0x000000edf6200000) (1024K)]
  0x0000023d72bee230 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=5088, stack(0x000000edf6200000,0x000000edf6300000) (1024K)]
  0x0000023d72bef5e0 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=7968, stack(0x000000edf6300000,0x000000edf6400000) (1024K)]
  0x0000023d7346f7d0 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=14524, stack(0x000000edf6400000,0x000000edf6500000) (1024K)]
  0x0000023d73471210 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=9328, stack(0x000000edf6500000,0x000000edf6600000) (1024K)]
  0x0000023d73471f30 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=2936, stack(0x000000edf6600000,0x000000edf6700000) (1024K)]
  0x0000023d734725c0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=14528, stack(0x000000edf6700000,0x000000edf6800000) (1024K)]
  0x0000023d73472c50 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=17344, stack(0x000000edf6800000,0x000000edf6900000) (1024K)]
  0x0000023d73667980 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=15280, stack(0x000000edf6900000,0x000000edf6a00000) (1024K)]
  0x0000023d7366a0e0 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=7660, stack(0x000000edf6a00000,0x000000edf6b00000) (1024K)]
  0x0000023d736665d0 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=7680, stack(0x000000edf6b00000,0x000000edf6c00000) (1024K)]
  0x0000023d736686a0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=14300, stack(0x000000edf6c00000,0x000000edf6d00000) (1024K)]
  0x0000023d784b18c0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=5044, stack(0x000000edf5400000,0x000000edf5500000) (1024K)]
  0x0000023d784af160 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=9416, stack(0x000000edf6d00000,0x000000edf6e00000) (1024K)]
  0x0000023d784b0510 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=14268, stack(0x000000edf6e00000,0x000000edf6f00000) (1024K)]
  0x0000023d784b46b0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=7468, stack(0x000000edf6f00000,0x000000edf7000000) (1024K)]
  0x0000023d784b4d40 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=3236, stack(0x000000edf7000000,0x000000edf7100000) (1024K)]
  0x0000023d784b0ba0 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=7696, stack(0x000000edf7100000,0x000000edf7200000) (1024K)]
  0x0000023d784b53d0 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=17628, stack(0x000000edf7200000,0x000000edf7300000) (1024K)]
  0x0000023d784b3990 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=17228, stack(0x000000edf7300000,0x000000edf7400000) (1024K)]
  0x0000023d784b1f50 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=6564, stack(0x000000edf7400000,0x000000edf7500000) (1024K)]
  0x0000023d784af7f0 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=15912, stack(0x000000edf7500000,0x000000edf7600000) (1024K)]
  0x0000023d784b1230 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=18408, stack(0x000000edf7600000,0x000000edf7700000) (1024K)]
  0x0000023d784b25e0 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=7112, stack(0x000000edf7700000,0x000000edf7800000) (1024K)]
  0x0000023d784b2c70 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=9068, stack(0x000000edf7800000,0x000000edf7900000) (1024K)]
  0x0000023d784b3300 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=11276, stack(0x000000edf7900000,0x000000edf7a00000) (1024K)]
  0x0000023d7366ced0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=7088, stack(0x000000edf7a00000,0x000000edf7b00000) (1024K)]
  0x0000023d7366b490 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=6416, stack(0x000000edf7b00000,0x000000edf7c00000) (1024K)]
  0x0000023d7366d560 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=15896, stack(0x000000edf7d00000,0x000000edf7e00000) (1024K)]
  0x0000023d73666c60 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=5568, stack(0x000000edf7e00000,0x000000edf7f00000) (1024K)]
  0x0000023d7366dbf0 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=2732, stack(0x000000edf7f00000,0x000000edf8000000) (1024K)]
  0x0000023d736693c0 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=16216, stack(0x000000edf8000000,0x000000edf8100000) (1024K)]
  0x0000023d736672f0 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=11080, stack(0x000000edf8100000,0x000000edf8200000) (1024K)]
  0x0000023d73668010 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=9708, stack(0x000000edf8200000,0x000000edf8300000) (1024K)]
  0x0000023d73668d30 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=7684, stack(0x000000edf8300000,0x000000edf8400000) (1024K)]
  0x0000023d78b2b570 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=18032, stack(0x000000edf8400000,0x000000edf8500000) (1024K)]
  0x0000023d78b317e0 JavaThread "stderr"                            [_thread_in_native, id=16676, stack(0x000000edf8500000,0x000000edf8600000) (1024K)]
  0x0000023d78b2fda0 JavaThread "stdout"                            [_thread_in_native, id=13036, stack(0x000000edf8600000,0x000000edf8700000) (1024K)]
  0x0000023d78b2bc00 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=17928, stack(0x000000edf8700000,0x000000edf8800000) (1024K)]
  0x0000023d78b31e70 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=9784, stack(0x000000edf8800000,0x000000edf8900000) (1024K)]
  0x0000023d78b2aee0 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=13740, stack(0x000000edf8900000,0x000000edf8a00000) (1024K)]
  0x0000023d78b33f40 JavaThread "WorkerExecutor Queue Thread 7"        [_thread_blocked, id=9420, stack(0x000000edf8b00000,0x000000edf8c00000) (1024K)]
  0x0000023d78b2f080 JavaThread "WorkerExecutor Queue Thread 8"        [_thread_blocked, id=2008, stack(0x000000edf8c00000,0x000000edf8d00000) (1024K)]
  0x0000023d78b2e360 JavaThread "WorkerExecutor Queue Thread 9"        [_thread_blocked, id=3496, stack(0x000000edf8d00000,0x000000edf8e00000) (1024K)]
  0x0000023d78b36010 JavaThread "Cache worker for Java compile cache (C:\Users\<USER>\.gradle\caches\8.12\javaCompile)"        [_thread_blocked, id=15292, stack(0x000000edf9500000,0x000000edf9600000) (1024K)]
  0x0000023d78b373c0 JavaThread "Build operations"                  [_thread_blocked, id=18008, stack(0x000000edf9600000,0x000000edf9700000) (1024K)]
  0x0000023d78b345d0 JavaThread "Build operations Thread 2"         [_thread_blocked, id=1868, stack(0x000000edf9700000,0x000000edf9800000) (1024K)]
  0x0000023d78b36d30 JavaThread "Build operations Thread 3"         [_thread_blocked, id=1640, stack(0x000000edf9800000,0x000000edf9900000) (1024K)]
  0x0000023d78b37a50 JavaThread "Build operations Thread 4"         [_thread_blocked, id=14288, stack(0x000000edf9900000,0x000000edf9a00000) (1024K)]
  0x0000023d78b366a0 JavaThread "Build operations Thread 5"         [_thread_blocked, id=1328, stack(0x000000edf9a00000,0x000000edf9b00000) (1024K)]
  0x0000023d78b380e0 JavaThread "Build operations Thread 6"         [_thread_blocked, id=17184, stack(0x000000edf9b00000,0x000000edf9c00000) (1024K)]
  0x0000023d78b39490 JavaThread "Build operations Thread 7"         [_thread_blocked, id=14940, stack(0x000000edf9c00000,0x000000edf9d00000) (1024K)]
  0x0000023d6854cf30 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=18396, stack(0x000000edeff00000,0x000000edf0000000) (1024K)]
  0x0000023d7d088d80 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=2248, stack(0x000000edf7c00000,0x000000edf7d00000) (1024K)]
Total: 153

Other Threads:
  0x0000023d26e17eb0 VMThread "VM Thread"                           [id=8964, stack(0x000000edef400000,0x000000edef500000) (1024K)]
  0x0000023d26df93f0 WatcherThread "VM Periodic Task Thread"        [id=13560, stack(0x000000edef300000,0x000000edef400000) (1024K)]
  0x0000023d0a48bb30 WorkerThread "GC Thread#0"                     [id=11108, stack(0x000000edeee00000,0x000000edeef00000) (1024K)]
  0x0000023d68948080 WorkerThread "GC Thread#1"                     [id=14324, stack(0x000000edf0100000,0x000000edf0200000) (1024K)]
  0x0000023d689356c0 WorkerThread "GC Thread#2"                     [id=4064, stack(0x000000edf0200000,0x000000edf0300000) (1024K)]
  0x0000023d68935a60 WorkerThread "GC Thread#3"                     [id=4112, stack(0x000000edf0300000,0x000000edf0400000) (1024K)]
  0x0000023d68698f30 WorkerThread "GC Thread#4"                     [id=1872, stack(0x000000edf0400000,0x000000edf0500000) (1024K)]
  0x0000023d686992d0 WorkerThread "GC Thread#5"                     [id=15960, stack(0x000000edf0500000,0x000000edf0600000) (1024K)]
  0x0000023d68699670 WorkerThread "GC Thread#6"                     [id=6112, stack(0x000000edf0600000,0x000000edf0700000) (1024K)]
  0x0000023d68699a10 WorkerThread "GC Thread#7"                     [id=1316, stack(0x000000edf0700000,0x000000edf0800000) (1024K)]
  0x0000023d0a49c970 ConcurrentGCThread "G1 Main Marker"            [id=1348, stack(0x000000edeef00000,0x000000edef000000) (1024K)]
  0x0000023d0a49e8d0 WorkerThread "G1 Conc#0"                       [id=10864, stack(0x000000edef000000,0x000000edef100000) (1024K)]
  0x0000023d6e286f80 WorkerThread "G1 Conc#1"                       [id=15904, stack(0x000000edf1500000,0x000000edf1600000) (1024K)]
  0x0000023d26d4d250 ConcurrentGCThread "G1 Refine#0"               [id=12728, stack(0x000000edef100000,0x000000edef200000) (1024K)]
  0x0000023d26d4da70 ConcurrentGCThread "G1 Service"                [id=9848, stack(0x000000edef200000,0x000000edef300000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    42072 22601       4       com.sun.tools.javac.jvm.ClassReader::readClass (670 bytes)
C2 CompilerThread1    42072 22580       4       com.sun.tools.javac.code.Symbol$MethodSymbol::implementation (57 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000023d27000000-0x0000023d27c90000-0x0000023d27c90000), size 13172736, SharedBaseAddress: 0x0000023d27000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000023d28000000-0x0000023d68000000, reserved size: 1073741824
Narrow klass base: 0x0000023d27000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 644096K, used 503205K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 134 young (137216K), 22 survivors (22528K)
 Metaspace       used 128893K, committed 131328K, reserved 1179648K
  class space    used 17300K, committed 18496K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x00000000800ffff0, 0x0000000080100000| 99%| O|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x00000000804ffff0, 0x0000000080500000| 99%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x00000000805eea70, 0x0000000080600000| 93%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x00000000806ffff0, 0x0000000080700000| 99%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080dfffe0, 0x0000000080e00000| 99%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080effff0, 0x0000000080f00000| 99%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x00000000813fd348, 0x0000000081400000| 98%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x00000000814ffff8, 0x0000000081500000| 99%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081afffb0, 0x0000000081b00000| 99%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081efff98, 0x0000000081f00000| 99%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x00000000820fffd8, 0x0000000082100000| 99%| O|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x00000000824fff88, 0x0000000082500000| 99%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082dfffc8, 0x0000000082e00000| 99%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082efffe8, 0x0000000082f00000| 99%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x00000000830fffe8, 0x0000000083100000| 99%| O|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x00000000831ffff0, 0x0000000083200000| 99%| O|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x00000000832fffe8, 0x0000000083300000| 99%| O|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HS|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HS|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HS|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HS|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083fffff0, 0x0000000084000000| 99%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x00000000840ffff0, 0x0000000084100000| 99%| O|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x00000000841fffe8, 0x0000000084200000| 99%| O|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x00000000842fc6e8, 0x0000000084300000| 98%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x00000000843ffff0, 0x0000000084400000| 99%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x00000000844ffff8, 0x0000000084500000| 99%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x00000000845ffff0, 0x0000000084600000| 99%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x00000000849ffff0, 0x0000000084a00000| 99%| O|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084dcf3b0, 0x0000000084e00000| 80%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084ee6b30, 0x0000000084f00000| 90%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000084fffff0, 0x0000000085000000| 99%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x00000000853ffff8, 0x0000000085400000| 99%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x00000000859f4248, 0x0000000085a00000| 95%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085dffff8, 0x0000000085e00000| 99%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x00000000863ffff0, 0x0000000086400000| 99%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x000000008649c2e8, 0x0000000086500000| 61%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%|HS|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Complete 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%|HC|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Complete 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%|HS|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Complete 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%|HS|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Complete 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%|HS|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Complete 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%|HS|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Complete 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%|HC|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Complete 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%|HS|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Complete 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%|HC|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HS|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%|HS|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Complete 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%|HS|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Complete 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%|HC|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Complete 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%|HC|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Complete 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%|HS|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%|HC|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%|HC|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HS|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%|HS|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%|HS|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%|HC|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Complete 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%|HS|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Complete 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%|HC|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Complete 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%|HS|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Complete 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%|HC|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Complete 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| O|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%|HS|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Complete 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%|HS|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Complete 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| O|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| O|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| O|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| O|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| O|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| O|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| O|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| O|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| O|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| O|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| O|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| O|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| O|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| O|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| O|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| O|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| O|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| O|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| O|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| O|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| O|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| O|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| O|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| O|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| O|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| O|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| O|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| O|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| O|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090200000, 0x0000000090200000|100%| O|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090300000, 0x0000000090300000|100%| O|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090400000, 0x0000000090400000|100%| O|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| O|  |TAMS 0x0000000090400000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| O|  |TAMS 0x0000000090500000| PB 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%|HS|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Complete 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%|HC|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Complete 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%|HS|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Complete 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%|HC|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Complete 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%|HS|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Complete 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%|HS|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Complete 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%|HC|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Complete 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| O|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| O|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| O|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| O|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| O|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| O|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| O|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| O|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| O|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| O|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| O|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| O|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| O|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| O|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| O|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092200000, 0x0000000092200000|100%|HS|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Complete 
| 290|0x0000000092200000, 0x0000000092300000, 0x0000000092300000|100%|HC|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Complete 
| 291|0x0000000092300000, 0x0000000092400000, 0x0000000092400000|100%|HS|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Complete 
| 292|0x0000000092400000, 0x0000000092500000, 0x0000000092500000|100%|HC|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Complete 
| 293|0x0000000092500000, 0x0000000092600000, 0x0000000092600000|100%|HS|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Complete 
| 294|0x0000000092600000, 0x0000000092700000, 0x0000000092700000|100%|HS|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Complete 
| 295|0x0000000092700000, 0x0000000092800000, 0x0000000092800000|100%|HS|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Complete 
| 296|0x0000000092800000, 0x0000000092900000, 0x0000000092900000|100%|HS|  |TAMS 0x0000000092800000| PB 0x0000000092800000| Complete 
| 297|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%|HS|  |TAMS 0x0000000092900000| PB 0x0000000092900000| Complete 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%|HC|  |TAMS 0x0000000092a00000| PB 0x0000000092a00000| Complete 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%|HS|  |TAMS 0x0000000092b00000| PB 0x0000000092b00000| Complete 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%|HS|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Complete 
| 301|0x0000000092d00000, 0x0000000092e00000, 0x0000000092e00000|100%|HS|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Complete 
| 302|0x0000000092e00000, 0x0000000092f00000, 0x0000000092f00000|100%|HC|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Complete 
| 303|0x0000000092f00000, 0x0000000093000000, 0x0000000093000000|100%|HS|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Complete 
| 304|0x0000000093000000, 0x0000000093100000, 0x0000000093100000|100%|HS|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Complete 
| 305|0x0000000093100000, 0x0000000093200000, 0x0000000093200000|100%|HS|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Complete 
| 306|0x0000000093200000, 0x0000000093300000, 0x0000000093300000|100%|HC|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Complete 
| 307|0x0000000093300000, 0x0000000093400000, 0x0000000093400000|100%|HS|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Complete 
| 308|0x0000000093400000, 0x0000000093500000, 0x0000000093500000|100%|HS|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Complete 
| 309|0x0000000093500000, 0x0000000093600000, 0x0000000093600000|100%|HS|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Complete 
| 310|0x0000000093600000, 0x0000000093700000, 0x0000000093700000|100%|HC|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Complete 
| 311|0x0000000093700000, 0x0000000093800000, 0x0000000093800000|100%| O|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093900000, 0x0000000093900000|100%|HS|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Complete 
| 313|0x0000000093900000, 0x0000000093a00000, 0x0000000093a00000|100%|HS|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Complete 
| 314|0x0000000093a00000, 0x0000000093b00000, 0x0000000093b00000|100%|HS|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Complete 
| 315|0x0000000093b00000, 0x0000000093c00000, 0x0000000093c00000|100%| O|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093d00000, 0x0000000093d00000|100%| O|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093e00000, 0x0000000093e00000|100%| O|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093f00000, 0x0000000093f00000|100%| O|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000094000000, 0x0000000094000000|100%| O|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094100000, 0x0000000094100000|100%| O|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094200000, 0x0000000094200000|100%| O|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| O|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094400000, 0x0000000094400000|100%| O|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094500000, 0x0000000094500000|100%| O|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094600000, 0x0000000094600000|100%| O|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094700000, 0x0000000094700000|100%| O|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%|HS|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Complete 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%|HS|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Complete 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%|HC|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Complete 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094b00000, 0x0000000094c00000|  0%| F|  |TAMS 0x0000000094b00000| PB 0x0000000094b00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094c00000, 0x0000000094d00000|  0%| F|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000| PB 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094e00000, 0x0000000094f00000|  0%| F|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095200000, 0x0000000095300000|  0%| F|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095300000, 0x0000000095400000|  0%| F|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095400000, 0x0000000095500000|  0%| F|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095500000, 0x0000000095600000|  0%| F|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095600000, 0x0000000095700000|  0%| F|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095700000, 0x0000000095800000|  0%| F|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095800000, 0x0000000095900000|  0%| F|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095a00000, 0x0000000095b00000|  0%| F|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095b00000, 0x0000000095c00000|  0%| F|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095c00000, 0x0000000095d00000|  0%| F|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095d00000, 0x0000000095e00000|  0%| F|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000| PB 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked 
| 354|0x0000000096200000, 0x0000000096200000, 0x0000000096300000|  0%| F|  |TAMS 0x0000000096200000| PB 0x0000000096200000| Untracked 
| 355|0x0000000096300000, 0x0000000096300000, 0x0000000096400000|  0%| F|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Untracked 
| 356|0x0000000096400000, 0x0000000096400000, 0x0000000096500000|  0%| F|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Untracked 
| 357|0x0000000096500000, 0x0000000096500000, 0x0000000096600000|  0%| F|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Untracked 
| 358|0x0000000096600000, 0x0000000096600000, 0x0000000096700000|  0%| F|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Untracked 
| 359|0x0000000096700000, 0x0000000096700000, 0x0000000096800000|  0%| F|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Untracked 
| 360|0x0000000096800000, 0x0000000096800000, 0x0000000096900000|  0%| F|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Untracked 
| 361|0x0000000096900000, 0x0000000096900000, 0x0000000096a00000|  0%| F|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Untracked 
| 362|0x0000000096a00000, 0x0000000096a00000, 0x0000000096b00000|  0%| F|  |TAMS 0x0000000096a00000| PB 0x0000000096a00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096b00000, 0x0000000096c00000|  0%| F|  |TAMS 0x0000000096b00000| PB 0x0000000096b00000| Untracked 
| 364|0x0000000096c00000, 0x0000000096c00000, 0x0000000096d00000|  0%| F|  |TAMS 0x0000000096c00000| PB 0x0000000096c00000| Untracked 
| 365|0x0000000096d00000, 0x0000000096d00000, 0x0000000096e00000|  0%| F|  |TAMS 0x0000000096d00000| PB 0x0000000096d00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096e00000, 0x0000000096f00000|  0%| F|  |TAMS 0x0000000096e00000| PB 0x0000000096e00000| Untracked 
| 367|0x0000000096f00000, 0x0000000097000000, 0x0000000097000000|100%| O|  |TAMS 0x0000000096f00000| PB 0x0000000096f00000| Untracked 
| 368|0x0000000097000000, 0x0000000097100000, 0x0000000097100000|100%| O|  |TAMS 0x0000000097000000| PB 0x0000000097000000| Untracked 
| 369|0x0000000097100000, 0x0000000097200000, 0x0000000097200000|100%| O|  |TAMS 0x0000000097100000| PB 0x0000000097100000| Untracked 
| 370|0x0000000097200000, 0x0000000097300000, 0x0000000097300000|100%| O|  |TAMS 0x0000000097200000| PB 0x0000000097200000| Untracked 
| 371|0x0000000097300000, 0x0000000097400000, 0x0000000097400000|100%| O|  |TAMS 0x0000000097300000| PB 0x0000000097300000| Untracked 
| 372|0x0000000097400000, 0x000000009743b080, 0x0000000097500000| 23%| O|  |TAMS 0x0000000097400000| PB 0x0000000097400000| Untracked 
| 373|0x0000000097500000, 0x0000000097500000, 0x0000000097600000|  0%| F|  |TAMS 0x0000000097500000| PB 0x0000000097500000| Untracked 
| 374|0x0000000097600000, 0x0000000097600000, 0x0000000097700000|  0%| F|  |TAMS 0x0000000097600000| PB 0x0000000097600000| Untracked 
| 375|0x0000000097700000, 0x0000000097700000, 0x0000000097800000|  0%| F|  |TAMS 0x0000000097700000| PB 0x0000000097700000| Untracked 
| 376|0x0000000097800000, 0x0000000097800000, 0x0000000097900000|  0%| F|  |TAMS 0x0000000097800000| PB 0x0000000097800000| Untracked 
| 377|0x0000000097900000, 0x0000000097900000, 0x0000000097a00000|  0%| F|  |TAMS 0x0000000097900000| PB 0x0000000097900000| Untracked 
| 378|0x0000000097a00000, 0x0000000097a00000, 0x0000000097b00000|  0%| F|  |TAMS 0x0000000097a00000| PB 0x0000000097a00000| Untracked 
| 379|0x0000000097b00000, 0x0000000097b00000, 0x0000000097c00000|  0%| F|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097c00000, 0x0000000097d00000|  0%| F|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097d00000, 0x0000000097e00000|  0%| F|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097e00000, 0x0000000097f00000|  0%| F|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked 
| 383|0x0000000097f00000, 0x0000000097f00000, 0x0000000098000000|  0%| F|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked 
| 384|0x0000000098000000, 0x0000000098000000, 0x0000000098100000|  0%| F|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked 
| 385|0x0000000098100000, 0x0000000098100000, 0x0000000098200000|  0%| F|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked 
| 386|0x0000000098200000, 0x0000000098200000, 0x0000000098300000|  0%| F|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098300000, 0x0000000098400000|  0%| F|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098400000, 0x0000000098500000|  0%| F|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098500000, 0x0000000098600000|  0%| F|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked 
| 390|0x0000000098600000, 0x0000000098600000, 0x0000000098700000|  0%| F|  |TAMS 0x0000000098600000| PB 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098700000, 0x0000000098800000|  0%| F|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098800000, 0x0000000098900000|  0%| F|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked 
| 393|0x0000000098900000, 0x0000000098900000, 0x0000000098a00000|  0%| F|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked 
| 394|0x0000000098a00000, 0x0000000098a00000, 0x0000000098b00000|  0%| F|  |TAMS 0x0000000098a00000| PB 0x0000000098a00000| Untracked 
| 395|0x0000000098b00000, 0x0000000098b00000, 0x0000000098c00000|  0%| F|  |TAMS 0x0000000098b00000| PB 0x0000000098b00000| Untracked 
| 396|0x0000000098c00000, 0x0000000098c00000, 0x0000000098d00000|  0%| F|  |TAMS 0x0000000098c00000| PB 0x0000000098c00000| Untracked 
| 397|0x0000000098d00000, 0x0000000098d00000, 0x0000000098e00000|  0%| F|  |TAMS 0x0000000098d00000| PB 0x0000000098d00000| Untracked 
| 398|0x0000000098e00000, 0x0000000098e00000, 0x0000000098f00000|  0%| F|  |TAMS 0x0000000098e00000| PB 0x0000000098e00000| Untracked 
| 399|0x0000000098f00000, 0x0000000098f00000, 0x0000000099000000|  0%| F|  |TAMS 0x0000000098f00000| PB 0x0000000098f00000| Untracked 
| 400|0x0000000099000000, 0x0000000099100000, 0x0000000099100000|100%| S|CS|TAMS 0x0000000099000000| PB 0x0000000099000000| Complete 
| 401|0x0000000099100000, 0x0000000099200000, 0x0000000099200000|100%| S|CS|TAMS 0x0000000099100000| PB 0x0000000099100000| Complete 
| 402|0x0000000099200000, 0x0000000099300000, 0x0000000099300000|100%| S|CS|TAMS 0x0000000099200000| PB 0x0000000099200000| Complete 
| 403|0x0000000099300000, 0x0000000099400000, 0x0000000099400000|100%| S|CS|TAMS 0x0000000099300000| PB 0x0000000099300000| Complete 
| 404|0x0000000099400000, 0x0000000099500000, 0x0000000099500000|100%| S|CS|TAMS 0x0000000099400000| PB 0x0000000099400000| Complete 
| 405|0x0000000099500000, 0x0000000099600000, 0x0000000099600000|100%| S|CS|TAMS 0x0000000099500000| PB 0x0000000099500000| Complete 
| 406|0x0000000099600000, 0x0000000099700000, 0x0000000099700000|100%| S|CS|TAMS 0x0000000099600000| PB 0x0000000099600000| Complete 
| 407|0x0000000099700000, 0x0000000099800000, 0x0000000099800000|100%| S|CS|TAMS 0x0000000099700000| PB 0x0000000099700000| Complete 
| 408|0x0000000099800000, 0x0000000099900000, 0x0000000099900000|100%| S|CS|TAMS 0x0000000099800000| PB 0x0000000099800000| Complete 
| 409|0x0000000099900000, 0x0000000099a00000, 0x0000000099a00000|100%| S|CS|TAMS 0x0000000099900000| PB 0x0000000099900000| Complete 
| 410|0x0000000099a00000, 0x0000000099b00000, 0x0000000099b00000|100%| S|CS|TAMS 0x0000000099a00000| PB 0x0000000099a00000| Complete 
| 411|0x0000000099b00000, 0x0000000099c00000, 0x0000000099c00000|100%| S|CS|TAMS 0x0000000099b00000| PB 0x0000000099b00000| Complete 
| 412|0x0000000099c00000, 0x0000000099d00000, 0x0000000099d00000|100%| S|CS|TAMS 0x0000000099c00000| PB 0x0000000099c00000| Complete 
| 413|0x0000000099d00000, 0x0000000099e00000, 0x0000000099e00000|100%| S|CS|TAMS 0x0000000099d00000| PB 0x0000000099d00000| Complete 
| 414|0x0000000099e00000, 0x0000000099f00000, 0x0000000099f00000|100%| S|CS|TAMS 0x0000000099e00000| PB 0x0000000099e00000| Complete 
| 415|0x0000000099f00000, 0x000000009a000000, 0x000000009a000000|100%| S|CS|TAMS 0x0000000099f00000| PB 0x0000000099f00000| Complete 
| 416|0x000000009a000000, 0x000000009a100000, 0x000000009a100000|100%| S|CS|TAMS 0x000000009a000000| PB 0x000000009a000000| Complete 
| 417|0x000000009a100000, 0x000000009a200000, 0x000000009a200000|100%| S|CS|TAMS 0x000000009a100000| PB 0x000000009a100000| Complete 
| 418|0x000000009a200000, 0x000000009a300000, 0x000000009a300000|100%| S|CS|TAMS 0x000000009a200000| PB 0x000000009a200000| Complete 
| 419|0x000000009a300000, 0x000000009a400000, 0x000000009a400000|100%| S|CS|TAMS 0x000000009a300000| PB 0x000000009a300000| Complete 
| 420|0x000000009a400000, 0x000000009a500000, 0x000000009a500000|100%| S|CS|TAMS 0x000000009a400000| PB 0x000000009a400000| Complete 
| 421|0x000000009a500000, 0x000000009a600000, 0x000000009a600000|100%| S|CS|TAMS 0x000000009a500000| PB 0x000000009a500000| Complete 
| 422|0x000000009a600000, 0x000000009a600000, 0x000000009a700000|  0%| F|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a700000, 0x000000009a800000|  0%| F|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a800000, 0x000000009a900000|  0%| F|  |TAMS 0x000000009a800000| PB 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ab00000, 0x000000009ac00000|  0%| F|  |TAMS 0x000000009ab00000| PB 0x000000009ab00000| Untracked 
| 428|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ad00000, 0x000000009ae00000|  0%| F|  |TAMS 0x000000009ad00000| PB 0x000000009ad00000| Untracked 
| 430|0x000000009ae00000, 0x000000009ae00000, 0x000000009af00000|  0%| F|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked 
| 431|0x000000009af00000, 0x000000009af00000, 0x000000009b000000|  0%| F|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked 
| 432|0x000000009b000000, 0x000000009b000000, 0x000000009b100000|  0%| F|  |TAMS 0x000000009b000000| PB 0x000000009b000000| Untracked 
| 433|0x000000009b100000, 0x000000009b100000, 0x000000009b200000|  0%| F|  |TAMS 0x000000009b100000| PB 0x000000009b100000| Untracked 
| 434|0x000000009b200000, 0x000000009b200000, 0x000000009b300000|  0%| F|  |TAMS 0x000000009b200000| PB 0x000000009b200000| Untracked 
| 435|0x000000009b300000, 0x000000009b300000, 0x000000009b400000|  0%| F|  |TAMS 0x000000009b300000| PB 0x000000009b300000| Untracked 
| 436|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Untracked 
| 437|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked 
| 438|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked 
| 439|0x000000009b700000, 0x000000009b700000, 0x000000009b800000|  0%| F|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked 
| 440|0x000000009b800000, 0x000000009b800000, 0x000000009b900000|  0%| F|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked 
| 441|0x000000009b900000, 0x000000009b900000, 0x000000009ba00000|  0%| F|  |TAMS 0x000000009b900000| PB 0x000000009b900000| Untracked 
| 442|0x000000009ba00000, 0x000000009ba00000, 0x000000009bb00000|  0%| F|  |TAMS 0x000000009ba00000| PB 0x000000009ba00000| Untracked 
| 443|0x000000009bb00000, 0x000000009bb00000, 0x000000009bc00000|  0%| F|  |TAMS 0x000000009bb00000| PB 0x000000009bb00000| Untracked 
| 444|0x000000009bc00000, 0x000000009bc00000, 0x000000009bd00000|  0%| F|  |TAMS 0x000000009bc00000| PB 0x000000009bc00000| Untracked 
| 445|0x000000009bd00000, 0x000000009bd00000, 0x000000009be00000|  0%| F|  |TAMS 0x000000009bd00000| PB 0x000000009bd00000| Untracked 
| 446|0x000000009be00000, 0x000000009be00000, 0x000000009bf00000|  0%| F|  |TAMS 0x000000009be00000| PB 0x000000009be00000| Untracked 
| 447|0x000000009bf00000, 0x000000009bf00000, 0x000000009c000000|  0%| F|  |TAMS 0x000000009bf00000| PB 0x000000009bf00000| Untracked 
| 448|0x000000009c000000, 0x000000009c000000, 0x000000009c100000|  0%| F|  |TAMS 0x000000009c000000| PB 0x000000009c000000| Untracked 
| 449|0x000000009c100000, 0x000000009c100000, 0x000000009c200000|  0%| F|  |TAMS 0x000000009c100000| PB 0x000000009c100000| Untracked 
| 450|0x000000009c200000, 0x000000009c200000, 0x000000009c300000|  0%| F|  |TAMS 0x000000009c200000| PB 0x000000009c200000| Untracked 
| 451|0x000000009c300000, 0x000000009c300000, 0x000000009c400000|  0%| F|  |TAMS 0x000000009c300000| PB 0x000000009c300000| Untracked 
| 452|0x000000009c400000, 0x000000009c400000, 0x000000009c500000|  0%| F|  |TAMS 0x000000009c400000| PB 0x000000009c400000| Untracked 
| 453|0x000000009c500000, 0x000000009c500000, 0x000000009c600000|  0%| F|  |TAMS 0x000000009c500000| PB 0x000000009c500000| Untracked 
| 454|0x000000009c600000, 0x000000009c600000, 0x000000009c700000|  0%| F|  |TAMS 0x000000009c600000| PB 0x000000009c600000| Untracked 
| 455|0x000000009c700000, 0x000000009c700000, 0x000000009c800000|  0%| F|  |TAMS 0x000000009c700000| PB 0x000000009c700000| Untracked 
| 456|0x000000009c800000, 0x000000009c800000, 0x000000009c900000|  0%| F|  |TAMS 0x000000009c800000| PB 0x000000009c800000| Untracked 
| 457|0x000000009c900000, 0x000000009c900000, 0x000000009ca00000|  0%| F|  |TAMS 0x000000009c900000| PB 0x000000009c900000| Untracked 
| 458|0x000000009ca00000, 0x000000009ca00000, 0x000000009cb00000|  0%| F|  |TAMS 0x000000009ca00000| PB 0x000000009ca00000| Untracked 
| 459|0x000000009cb00000, 0x000000009cb00000, 0x000000009cc00000|  0%| F|  |TAMS 0x000000009cb00000| PB 0x000000009cb00000| Untracked 
| 460|0x000000009cc00000, 0x000000009cc00000, 0x000000009cd00000|  0%| F|  |TAMS 0x000000009cc00000| PB 0x000000009cc00000| Untracked 
| 461|0x000000009cd00000, 0x000000009cd00000, 0x000000009ce00000|  0%| F|  |TAMS 0x000000009cd00000| PB 0x000000009cd00000| Untracked 
| 462|0x000000009ce00000, 0x000000009ce00000, 0x000000009cf00000|  0%| F|  |TAMS 0x000000009ce00000| PB 0x000000009ce00000| Untracked 
| 463|0x000000009cf00000, 0x000000009cf00000, 0x000000009d000000|  0%| F|  |TAMS 0x000000009cf00000| PB 0x000000009cf00000| Untracked 
| 464|0x000000009d000000, 0x000000009d000000, 0x000000009d100000|  0%| F|  |TAMS 0x000000009d000000| PB 0x000000009d000000| Untracked 
| 465|0x000000009d100000, 0x000000009d100000, 0x000000009d200000|  0%| F|  |TAMS 0x000000009d100000| PB 0x000000009d100000| Untracked 
| 466|0x000000009d200000, 0x000000009d200000, 0x000000009d300000|  0%| F|  |TAMS 0x000000009d200000| PB 0x000000009d200000| Untracked 
| 467|0x000000009d300000, 0x000000009d300000, 0x000000009d400000|  0%| F|  |TAMS 0x000000009d300000| PB 0x000000009d300000| Untracked 
| 468|0x000000009d400000, 0x000000009d400000, 0x000000009d500000|  0%| F|  |TAMS 0x000000009d400000| PB 0x000000009d400000| Untracked 
| 469|0x000000009d500000, 0x000000009d500000, 0x000000009d600000|  0%| F|  |TAMS 0x000000009d500000| PB 0x000000009d500000| Untracked 
| 470|0x000000009d600000, 0x000000009d600000, 0x000000009d700000|  0%| F|  |TAMS 0x000000009d600000| PB 0x000000009d600000| Untracked 
| 471|0x000000009d700000, 0x000000009d700000, 0x000000009d800000|  0%| F|  |TAMS 0x000000009d700000| PB 0x000000009d700000| Untracked 
| 472|0x000000009d800000, 0x000000009d800000, 0x000000009d900000|  0%| F|  |TAMS 0x000000009d800000| PB 0x000000009d800000| Untracked 
| 473|0x000000009d900000, 0x000000009d900000, 0x000000009da00000|  0%| F|  |TAMS 0x000000009d900000| PB 0x000000009d900000| Untracked 
| 474|0x000000009da00000, 0x000000009da00000, 0x000000009db00000|  0%| F|  |TAMS 0x000000009da00000| PB 0x000000009da00000| Untracked 
| 475|0x000000009db00000, 0x000000009db00000, 0x000000009dc00000|  0%| F|  |TAMS 0x000000009db00000| PB 0x000000009db00000| Untracked 
| 476|0x000000009dc00000, 0x000000009dc00000, 0x000000009dd00000|  0%| F|  |TAMS 0x000000009dc00000| PB 0x000000009dc00000| Untracked 
| 477|0x000000009dd00000, 0x000000009dd00000, 0x000000009de00000|  0%| F|  |TAMS 0x000000009dd00000| PB 0x000000009dd00000| Untracked 
| 478|0x000000009de00000, 0x000000009de00000, 0x000000009df00000|  0%| F|  |TAMS 0x000000009de00000| PB 0x000000009de00000| Untracked 
| 479|0x000000009df00000, 0x000000009df00000, 0x000000009e000000|  0%| F|  |TAMS 0x000000009df00000| PB 0x000000009df00000| Untracked 
| 480|0x000000009e000000, 0x000000009e000000, 0x000000009e100000|  0%| F|  |TAMS 0x000000009e000000| PB 0x000000009e000000| Untracked 
| 481|0x000000009e100000, 0x000000009e100000, 0x000000009e200000|  0%| F|  |TAMS 0x000000009e100000| PB 0x000000009e100000| Untracked 
| 482|0x000000009e200000, 0x000000009e2e7918, 0x000000009e300000| 90%| E|  |TAMS 0x000000009e200000| PB 0x000000009e200000| Complete 
| 483|0x000000009e300000, 0x000000009e400000, 0x000000009e400000|100%| E|CS|TAMS 0x000000009e300000| PB 0x000000009e300000| Complete 
| 484|0x000000009e400000, 0x000000009e500000, 0x000000009e500000|100%| E|CS|TAMS 0x000000009e400000| PB 0x000000009e400000| Complete 
| 485|0x000000009e500000, 0x000000009e600000, 0x000000009e600000|100%| E|CS|TAMS 0x000000009e500000| PB 0x000000009e500000| Complete 
| 486|0x000000009e600000, 0x000000009e700000, 0x000000009e700000|100%| E|CS|TAMS 0x000000009e600000| PB 0x000000009e600000| Complete 
| 487|0x000000009e700000, 0x000000009e800000, 0x000000009e800000|100%| E|CS|TAMS 0x000000009e700000| PB 0x000000009e700000| Complete 
| 488|0x000000009e800000, 0x000000009e900000, 0x000000009e900000|100%| E|CS|TAMS 0x000000009e800000| PB 0x000000009e800000| Complete 
| 489|0x000000009e900000, 0x000000009ea00000, 0x000000009ea00000|100%| E|CS|TAMS 0x000000009e900000| PB 0x000000009e900000| Complete 
| 490|0x000000009ea00000, 0x000000009eb00000, 0x000000009eb00000|100%| E|CS|TAMS 0x000000009ea00000| PB 0x000000009ea00000| Complete 
| 491|0x000000009eb00000, 0x000000009ec00000, 0x000000009ec00000|100%| E|CS|TAMS 0x000000009eb00000| PB 0x000000009eb00000| Complete 
| 492|0x000000009ec00000, 0x000000009ed00000, 0x000000009ed00000|100%| E|CS|TAMS 0x000000009ec00000| PB 0x000000009ec00000| Complete 
| 493|0x000000009ed00000, 0x000000009ee00000, 0x000000009ee00000|100%| E|CS|TAMS 0x000000009ed00000| PB 0x000000009ed00000| Complete 
| 494|0x000000009ee00000, 0x000000009ef00000, 0x000000009ef00000|100%| E|CS|TAMS 0x000000009ee00000| PB 0x000000009ee00000| Complete 
| 495|0x000000009ef00000, 0x000000009f000000, 0x000000009f000000|100%| E|CS|TAMS 0x000000009ef00000| PB 0x000000009ef00000| Complete 
| 496|0x000000009f000000, 0x000000009f100000, 0x000000009f100000|100%| E|CS|TAMS 0x000000009f000000| PB 0x000000009f000000| Complete 
| 497|0x000000009f100000, 0x000000009f200000, 0x000000009f200000|100%| E|CS|TAMS 0x000000009f100000| PB 0x000000009f100000| Complete 
| 498|0x000000009f200000, 0x000000009f300000, 0x000000009f300000|100%| E|CS|TAMS 0x000000009f200000| PB 0x000000009f200000| Complete 
| 499|0x000000009f300000, 0x000000009f400000, 0x000000009f400000|100%| E|CS|TAMS 0x000000009f300000| PB 0x000000009f300000| Complete 
| 500|0x000000009f400000, 0x000000009f500000, 0x000000009f500000|100%| E|CS|TAMS 0x000000009f400000| PB 0x000000009f400000| Complete 
| 501|0x000000009f500000, 0x000000009f600000, 0x000000009f600000|100%| E|CS|TAMS 0x000000009f500000| PB 0x000000009f500000| Complete 
| 502|0x000000009f600000, 0x000000009f700000, 0x000000009f700000|100%| E|CS|TAMS 0x000000009f600000| PB 0x000000009f600000| Complete 
| 503|0x000000009f700000, 0x000000009f800000, 0x000000009f800000|100%| E|CS|TAMS 0x000000009f700000| PB 0x000000009f700000| Complete 
| 504|0x000000009f800000, 0x000000009f900000, 0x000000009f900000|100%| E|CS|TAMS 0x000000009f800000| PB 0x000000009f800000| Complete 
| 505|0x000000009f900000, 0x000000009fa00000, 0x000000009fa00000|100%| E|CS|TAMS 0x000000009f900000| PB 0x000000009f900000| Complete 
| 506|0x000000009fa00000, 0x000000009fb00000, 0x000000009fb00000|100%| E|CS|TAMS 0x000000009fa00000| PB 0x000000009fa00000| Complete 
| 507|0x000000009fb00000, 0x000000009fc00000, 0x000000009fc00000|100%| E|CS|TAMS 0x000000009fb00000| PB 0x000000009fb00000| Complete 
| 508|0x000000009fc00000, 0x000000009fd00000, 0x000000009fd00000|100%| E|CS|TAMS 0x000000009fc00000| PB 0x000000009fc00000| Complete 
| 509|0x000000009fd00000, 0x000000009fe00000, 0x000000009fe00000|100%| E|CS|TAMS 0x000000009fd00000| PB 0x000000009fd00000| Complete 
| 510|0x000000009fe00000, 0x000000009ff00000, 0x000000009ff00000|100%| E|CS|TAMS 0x000000009fe00000| PB 0x000000009fe00000| Complete 
| 511|0x000000009ff00000, 0x00000000a0000000, 0x00000000a0000000|100%| E|CS|TAMS 0x000000009ff00000| PB 0x000000009ff00000| Complete 
| 512|0x00000000a0000000, 0x00000000a0100000, 0x00000000a0100000|100%| E|CS|TAMS 0x00000000a0000000| PB 0x00000000a0000000| Complete 
| 513|0x00000000a0100000, 0x00000000a0200000, 0x00000000a0200000|100%| E|CS|TAMS 0x00000000a0100000| PB 0x00000000a0100000| Complete 
| 514|0x00000000a0200000, 0x00000000a0300000, 0x00000000a0300000|100%| E|CS|TAMS 0x00000000a0200000| PB 0x00000000a0200000| Complete 
| 515|0x00000000a0300000, 0x00000000a0400000, 0x00000000a0400000|100%| E|CS|TAMS 0x00000000a0300000| PB 0x00000000a0300000| Complete 
| 516|0x00000000a0400000, 0x00000000a0500000, 0x00000000a0500000|100%| E|CS|TAMS 0x00000000a0400000| PB 0x00000000a0400000| Complete 
| 517|0x00000000a0500000, 0x00000000a0600000, 0x00000000a0600000|100%| E|CS|TAMS 0x00000000a0500000| PB 0x00000000a0500000| Complete 
| 518|0x00000000a0600000, 0x00000000a0700000, 0x00000000a0700000|100%| E|CS|TAMS 0x00000000a0600000| PB 0x00000000a0600000| Complete 
| 519|0x00000000a0700000, 0x00000000a0800000, 0x00000000a0800000|100%| E|CS|TAMS 0x00000000a0700000| PB 0x00000000a0700000| Complete 
| 520|0x00000000a0800000, 0x00000000a0900000, 0x00000000a0900000|100%| E|CS|TAMS 0x00000000a0800000| PB 0x00000000a0800000| Complete 
| 521|0x00000000a0900000, 0x00000000a0a00000, 0x00000000a0a00000|100%| E|CS|TAMS 0x00000000a0900000| PB 0x00000000a0900000| Complete 
| 522|0x00000000a0a00000, 0x00000000a0b00000, 0x00000000a0b00000|100%| E|CS|TAMS 0x00000000a0a00000| PB 0x00000000a0a00000| Complete 
| 523|0x00000000a0b00000, 0x00000000a0c00000, 0x00000000a0c00000|100%| E|CS|TAMS 0x00000000a0b00000| PB 0x00000000a0b00000| Complete 
| 524|0x00000000a0c00000, 0x00000000a0d00000, 0x00000000a0d00000|100%| E|CS|TAMS 0x00000000a0c00000| PB 0x00000000a0c00000| Complete 
| 525|0x00000000a0d00000, 0x00000000a0e00000, 0x00000000a0e00000|100%| E|CS|TAMS 0x00000000a0d00000| PB 0x00000000a0d00000| Complete 
| 526|0x00000000a0e00000, 0x00000000a0f00000, 0x00000000a0f00000|100%| E|CS|TAMS 0x00000000a0e00000| PB 0x00000000a0e00000| Complete 
| 527|0x00000000a0f00000, 0x00000000a1000000, 0x00000000a1000000|100%| E|CS|TAMS 0x00000000a0f00000| PB 0x00000000a0f00000| Complete 
| 528|0x00000000a1000000, 0x00000000a1100000, 0x00000000a1100000|100%| E|CS|TAMS 0x00000000a1000000| PB 0x00000000a1000000| Complete 
| 529|0x00000000a1100000, 0x00000000a1200000, 0x00000000a1200000|100%| E|CS|TAMS 0x00000000a1100000| PB 0x00000000a1100000| Complete 
| 530|0x00000000a1200000, 0x00000000a1300000, 0x00000000a1300000|100%| E|CS|TAMS 0x00000000a1200000| PB 0x00000000a1200000| Complete 
| 531|0x00000000a1300000, 0x00000000a1400000, 0x00000000a1400000|100%| E|CS|TAMS 0x00000000a1300000| PB 0x00000000a1300000| Complete 
| 532|0x00000000a1400000, 0x00000000a1500000, 0x00000000a1500000|100%| E|CS|TAMS 0x00000000a1400000| PB 0x00000000a1400000| Complete 
| 533|0x00000000a1500000, 0x00000000a1600000, 0x00000000a1600000|100%| E|CS|TAMS 0x00000000a1500000| PB 0x00000000a1500000| Complete 
| 534|0x00000000a1600000, 0x00000000a1700000, 0x00000000a1700000|100%| E|CS|TAMS 0x00000000a1600000| PB 0x00000000a1600000| Complete 
| 535|0x00000000a1700000, 0x00000000a1800000, 0x00000000a1800000|100%| E|CS|TAMS 0x00000000a1700000| PB 0x00000000a1700000| Complete 
| 536|0x00000000a1800000, 0x00000000a1900000, 0x00000000a1900000|100%| E|CS|TAMS 0x00000000a1800000| PB 0x00000000a1800000| Complete 
| 537|0x00000000a1900000, 0x00000000a1a00000, 0x00000000a1a00000|100%| E|CS|TAMS 0x00000000a1900000| PB 0x00000000a1900000| Complete 
| 538|0x00000000a1a00000, 0x00000000a1b00000, 0x00000000a1b00000|100%| E|CS|TAMS 0x00000000a1a00000| PB 0x00000000a1a00000| Complete 
| 539|0x00000000a1b00000, 0x00000000a1c00000, 0x00000000a1c00000|100%| E|CS|TAMS 0x00000000a1b00000| PB 0x00000000a1b00000| Complete 
| 540|0x00000000a1c00000, 0x00000000a1d00000, 0x00000000a1d00000|100%| E|CS|TAMS 0x00000000a1c00000| PB 0x00000000a1c00000| Complete 
| 541|0x00000000a1d00000, 0x00000000a1e00000, 0x00000000a1e00000|100%| E|CS|TAMS 0x00000000a1d00000| PB 0x00000000a1d00000| Complete 
| 542|0x00000000a1e00000, 0x00000000a1f00000, 0x00000000a1f00000|100%| E|CS|TAMS 0x00000000a1e00000| PB 0x00000000a1e00000| Complete 
| 543|0x00000000a1f00000, 0x00000000a2000000, 0x00000000a2000000|100%| E|CS|TAMS 0x00000000a1f00000| PB 0x00000000a1f00000| Complete 
| 544|0x00000000a2000000, 0x00000000a2100000, 0x00000000a2100000|100%| E|CS|TAMS 0x00000000a2000000| PB 0x00000000a2000000| Complete 
| 545|0x00000000a2100000, 0x00000000a2200000, 0x00000000a2200000|100%| E|CS|TAMS 0x00000000a2100000| PB 0x00000000a2100000| Complete 
| 546|0x00000000a2200000, 0x00000000a2300000, 0x00000000a2300000|100%| E|CS|TAMS 0x00000000a2200000| PB 0x00000000a2200000| Complete 
| 547|0x00000000a2300000, 0x00000000a2400000, 0x00000000a2400000|100%| E|CS|TAMS 0x00000000a2300000| PB 0x00000000a2300000| Complete 
| 548|0x00000000a2400000, 0x00000000a2500000, 0x00000000a2500000|100%| E|CS|TAMS 0x00000000a2400000| PB 0x00000000a2400000| Complete 
| 549|0x00000000a2500000, 0x00000000a2600000, 0x00000000a2600000|100%| E|CS|TAMS 0x00000000a2500000| PB 0x00000000a2500000| Complete 
| 550|0x00000000a2600000, 0x00000000a2700000, 0x00000000a2700000|100%| E|CS|TAMS 0x00000000a2600000| PB 0x00000000a2600000| Complete 
| 551|0x00000000a2700000, 0x00000000a2800000, 0x00000000a2800000|100%| E|CS|TAMS 0x00000000a2700000| PB 0x00000000a2700000| Complete 
| 552|0x00000000a2800000, 0x00000000a2900000, 0x00000000a2900000|100%| E|CS|TAMS 0x00000000a2800000| PB 0x00000000a2800000| Complete 
| 553|0x00000000a2900000, 0x00000000a2a00000, 0x00000000a2a00000|100%| E|CS|TAMS 0x00000000a2900000| PB 0x00000000a2900000| Complete 
| 554|0x00000000a2a00000, 0x00000000a2b00000, 0x00000000a2b00000|100%| E|CS|TAMS 0x00000000a2a00000| PB 0x00000000a2a00000| Complete 
| 555|0x00000000a2b00000, 0x00000000a2c00000, 0x00000000a2c00000|100%| E|CS|TAMS 0x00000000a2b00000| PB 0x00000000a2b00000| Complete 
| 556|0x00000000a2c00000, 0x00000000a2d00000, 0x00000000a2d00000|100%| E|CS|TAMS 0x00000000a2c00000| PB 0x00000000a2c00000| Complete 
| 557|0x00000000a2d00000, 0x00000000a2e00000, 0x00000000a2e00000|100%| E|CS|TAMS 0x00000000a2d00000| PB 0x00000000a2d00000| Complete 
| 558|0x00000000a2e00000, 0x00000000a2f00000, 0x00000000a2f00000|100%| E|CS|TAMS 0x00000000a2e00000| PB 0x00000000a2e00000| Complete 
| 559|0x00000000a2f00000, 0x00000000a3000000, 0x00000000a3000000|100%| E|CS|TAMS 0x00000000a2f00000| PB 0x00000000a2f00000| Complete 
| 560|0x00000000a3000000, 0x00000000a3100000, 0x00000000a3100000|100%| E|CS|TAMS 0x00000000a3000000| PB 0x00000000a3000000| Complete 
| 561|0x00000000a3100000, 0x00000000a3200000, 0x00000000a3200000|100%| E|CS|TAMS 0x00000000a3100000| PB 0x00000000a3100000| Complete 
| 562|0x00000000a3200000, 0x00000000a3300000, 0x00000000a3300000|100%| E|CS|TAMS 0x00000000a3200000| PB 0x00000000a3200000| Complete 
| 563|0x00000000a3300000, 0x00000000a3400000, 0x00000000a3400000|100%| E|CS|TAMS 0x00000000a3300000| PB 0x00000000a3300000| Complete 
| 564|0x00000000a3400000, 0x00000000a3500000, 0x00000000a3500000|100%| E|CS|TAMS 0x00000000a3400000| PB 0x00000000a3400000| Complete 
| 565|0x00000000a3500000, 0x00000000a3600000, 0x00000000a3600000|100%| E|CS|TAMS 0x00000000a3500000| PB 0x00000000a3500000| Complete 
| 566|0x00000000a3600000, 0x00000000a3700000, 0x00000000a3700000|100%| E|CS|TAMS 0x00000000a3600000| PB 0x00000000a3600000| Complete 
| 567|0x00000000a3700000, 0x00000000a3800000, 0x00000000a3800000|100%| E|CS|TAMS 0x00000000a3700000| PB 0x00000000a3700000| Complete 
| 568|0x00000000a3800000, 0x00000000a3900000, 0x00000000a3900000|100%| E|CS|TAMS 0x00000000a3800000| PB 0x00000000a3800000| Complete 
| 569|0x00000000a3900000, 0x00000000a3a00000, 0x00000000a3a00000|100%| E|CS|TAMS 0x00000000a3900000| PB 0x00000000a3900000| Complete 
| 570|0x00000000a3a00000, 0x00000000a3b00000, 0x00000000a3b00000|100%| E|  |TAMS 0x00000000a3a00000| PB 0x00000000a3a00000| Complete 
| 571|0x00000000a3b00000, 0x00000000a3c00000, 0x00000000a3c00000|100%| E|CS|TAMS 0x00000000a3b00000| PB 0x00000000a3b00000| Complete 
| 572|0x00000000a3c00000, 0x00000000a3d00000, 0x00000000a3d00000|100%| E|CS|TAMS 0x00000000a3c00000| PB 0x00000000a3c00000| Complete 
| 573|0x00000000a3d00000, 0x00000000a3e00000, 0x00000000a3e00000|100%| E|CS|TAMS 0x00000000a3d00000| PB 0x00000000a3d00000| Complete 
| 574|0x00000000a3e00000, 0x00000000a3f00000, 0x00000000a3f00000|100%| E|CS|TAMS 0x00000000a3e00000| PB 0x00000000a3e00000| Complete 
| 575|0x00000000a3f00000, 0x00000000a4000000, 0x00000000a4000000|100%| E|CS|TAMS 0x00000000a3f00000| PB 0x00000000a3f00000| Complete 
| 576|0x00000000a4000000, 0x00000000a4100000, 0x00000000a4100000|100%| E|CS|TAMS 0x00000000a4000000| PB 0x00000000a4000000| Complete 
| 577|0x00000000a4100000, 0x00000000a4200000, 0x00000000a4200000|100%| E|CS|TAMS 0x00000000a4100000| PB 0x00000000a4100000| Complete 
| 578|0x00000000a4200000, 0x00000000a4300000, 0x00000000a4300000|100%| E|CS|TAMS 0x00000000a4200000| PB 0x00000000a4200000| Complete 
| 579|0x00000000a4300000, 0x00000000a4400000, 0x00000000a4400000|100%| E|CS|TAMS 0x00000000a4300000| PB 0x00000000a4300000| Complete 
| 580|0x00000000a4400000, 0x00000000a4500000, 0x00000000a4500000|100%| E|CS|TAMS 0x00000000a4400000| PB 0x00000000a4400000| Complete 
| 581|0x00000000a4500000, 0x00000000a4600000, 0x00000000a4600000|100%| E|CS|TAMS 0x00000000a4500000| PB 0x00000000a4500000| Complete 
| 582|0x00000000a4600000, 0x00000000a4700000, 0x00000000a4700000|100%| E|CS|TAMS 0x00000000a4600000| PB 0x00000000a4600000| Complete 
| 583|0x00000000a4700000, 0x00000000a4800000, 0x00000000a4800000|100%| E|CS|TAMS 0x00000000a4700000| PB 0x00000000a4700000| Complete 
| 584|0x00000000a4800000, 0x00000000a4900000, 0x00000000a4900000|100%| E|CS|TAMS 0x00000000a4800000| PB 0x00000000a4800000| Complete 
| 585|0x00000000a4900000, 0x00000000a4a00000, 0x00000000a4a00000|100%| E|CS|TAMS 0x00000000a4900000| PB 0x00000000a4900000| Complete 
| 586|0x00000000a4a00000, 0x00000000a4b00000, 0x00000000a4b00000|100%| E|CS|TAMS 0x00000000a4a00000| PB 0x00000000a4a00000| Complete 
|2006|0x00000000fd600000, 0x00000000fd700000, 0x00000000fd700000|100%| O|  |TAMS 0x00000000fd600000| PB 0x00000000fd600000| Untracked 
|2007|0x00000000fd700000, 0x00000000fd800000, 0x00000000fd800000|100%| E|CS|TAMS 0x00000000fd700000| PB 0x00000000fd700000| Complete 
|2008|0x00000000fd800000, 0x00000000fd900000, 0x00000000fd900000|100%| O|  |TAMS 0x00000000fd800000| PB 0x00000000fd800000| Untracked 
|2009|0x00000000fd900000, 0x00000000fda00000, 0x00000000fda00000|100%| O|  |TAMS 0x00000000fd900000| PB 0x00000000fd900000| Untracked 
|2010|0x00000000fda00000, 0x00000000fdb00000, 0x00000000fdb00000|100%| O|  |TAMS 0x00000000fda00000| PB 0x00000000fda00000| Untracked 
|2011|0x00000000fdb00000, 0x00000000fdc00000, 0x00000000fdc00000|100%| O|  |TAMS 0x00000000fdb00000| PB 0x00000000fdb00000| Untracked 
|2012|0x00000000fdc00000, 0x00000000fdd00000, 0x00000000fdd00000|100%| O|  |TAMS 0x00000000fdc00000| PB 0x00000000fdc00000| Untracked 
|2013|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| O|  |TAMS 0x00000000fdd00000| PB 0x00000000fdd00000| Untracked 
|2014|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| O|  |TAMS 0x00000000fde00000| PB 0x00000000fde00000| Untracked 
|2015|0x00000000fdf00000, 0x00000000fe000000, 0x00000000fe000000|100%| O|  |TAMS 0x00000000fdf00000| PB 0x00000000fdf00000| Untracked 
|2016|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| O|  |TAMS 0x00000000fe000000| PB 0x00000000fe000000| Untracked 
|2017|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| O|  |TAMS 0x00000000fe100000| PB 0x00000000fe100000| Untracked 
|2018|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| O|  |TAMS 0x00000000fe200000| PB 0x00000000fe200000| Untracked 
|2019|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| O|  |TAMS 0x00000000fe300000| PB 0x00000000fe300000| Untracked 
|2020|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| O|  |TAMS 0x00000000fe400000| PB 0x00000000fe400000| Untracked 
|2021|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| O|  |TAMS 0x00000000fe500000| PB 0x00000000fe500000| Untracked 
|2022|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| O|  |TAMS 0x00000000fe600000| PB 0x00000000fe600000| Untracked 
|2023|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| O|  |TAMS 0x00000000fe700000| PB 0x00000000fe700000| Untracked 
|2024|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| O|  |TAMS 0x00000000fe800000| PB 0x00000000fe800000| Untracked 
|2025|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| O|  |TAMS 0x00000000fe900000| PB 0x00000000fe900000| Untracked 
|2026|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| O|  |TAMS 0x00000000fea00000| PB 0x00000000fea00000| Untracked 
|2027|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| O|  |TAMS 0x00000000feb00000| PB 0x00000000feb00000| Untracked 
|2028|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| O|  |TAMS 0x00000000fec00000| PB 0x00000000fec00000| Untracked 
|2029|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| O|  |TAMS 0x00000000fed00000| PB 0x00000000fed00000| Untracked 
|2030|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| O|  |TAMS 0x00000000fee00000| PB 0x00000000fee00000| Untracked 
|2031|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| E|CS|TAMS 0x00000000fef00000| PB 0x00000000fef00000| Complete 
|2032|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| O|  |TAMS 0x00000000ff000000| PB 0x00000000ff000000| Untracked 
|2033|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| O|  |TAMS 0x00000000ff100000| PB 0x00000000ff100000| Untracked 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| O|  |TAMS 0x00000000ff200000| PB 0x00000000ff200000| Untracked 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff300000| PB 0x00000000ff300000| Untracked 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff400000| Untracked 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff500000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff600000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff800000| Untracked 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Complete 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000| PB 0x00000000fff00000| Complete 

Card table byte_map: [0x0000023d21fd0000,0x0000023d223d0000] _byte_map_base: 0x0000023d21bd0000

Marking Bits: (CMBitMap*) 0x0000023d0a48bee0
 Bits: [0x0000023d223d0000, 0x0000023d243d0000)

Polling page: 0x0000023d09c00000

Metaspace:

Usage:
  Non-class:    108.98 MB used.
      Class:     16.90 MB used.
       Both:    125.88 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     110.19 MB ( 86%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      18.06 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     128.25 MB ( 11%) committed. 

Chunk freelists:
   Non-Class:  1.42 MB
       Class:  13.95 MB
        Both:  15.38 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 193.75 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 4272.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 2051.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 9966.
num_chunk_merges: 9.
num_chunk_splits: 6682.
num_chunks_enlarged: 4398.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=15798Kb max_used=15798Kb free=104202Kb
 bounds [0x0000023d1a370000, 0x0000023d1b2e0000, 0x0000023d218a0000]
CodeHeap 'profiled nmethods': size=120000Kb used=39410Kb max_used=39410Kb free=80589Kb
 bounds [0x0000023d128a0000, 0x0000023d14f20000, 0x0000023d19dd0000]
CodeHeap 'non-nmethods': size=5760Kb used=2961Kb max_used=3038Kb free=2799Kb
 bounds [0x0000023d19dd0000, 0x0000023d1a0e0000, 0x0000023d1a370000]
 total_blobs=20956 nmethods=19919 adapters=939
 compilation: disabled (not enough contiguous free space left)
              stopped_count=1, restarted_count=0
 full_count=1

Compilation events (20 events):
Event: 41.927 Thread 0x0000023d26e41520 22624       3       com.sun.tools.javac.code.Types::findDescriptorSymbol (12 bytes)
Event: 41.927 Thread 0x0000023d26e41520 nmethod 22624 0x0000023d14f0f510 code [0x0000023d14f0f6c0, 0x0000023d14f0f850]
Event: 41.929 Thread 0x0000023d26e41520 22625       3       com.sun.tools.javac.comp.Check$$Lambda/0x0000023d291ccf60::accept (12 bytes)
Event: 41.929 Thread 0x0000023d26e41520 nmethod 22625 0x0000023d14f0f990 code [0x0000023d14f0fb80, 0x0000023d14f10190]
Event: 41.931 Thread 0x0000023d26e41520 22626       3       com.sun.tools.javac.comp.Infer$CheckUpperBounds::apply (339 bytes)
Event: 41.935 Thread 0x0000023d26e41520 nmethod 22626 0x0000023d14f10390 code [0x0000023d14f10960, 0x0000023d14f144d8]
Event: 41.935 Thread 0x0000023d26e41520 22629       3       com.sun.tools.javac.code.Type$UndetVar$InferenceBound::lessThan (61 bytes)
Event: 41.935 Thread 0x0000023d26e41520 nmethod 22629 0x0000023d14f15590 code [0x0000023d14f15780, 0x0000023d14f15b90]
Event: 41.935 Thread 0x0000023d26e41520 22627       3       com.sun.tools.javac.comp.Infer$CheckUpperBounds$$Lambda/0x0000023d291d22d8::<init> (10 bytes)
Event: 41.936 Thread 0x0000023d26e41520 nmethod 22627 0x0000023d14f15d10 code [0x0000023d14f15ec0, 0x0000023d14f16078]
Event: 41.936 Thread 0x0000023d26e41520 22628       3       com.sun.tools.javac.code.Types$$Lambda/0x0000023d291d2748::get (16 bytes)
Event: 41.936 Thread 0x0000023d26e41520 nmethod 22628 0x0000023d14f16110 code [0x0000023d14f162c0, 0x0000023d14f16410]
Event: 41.936 Thread 0x0000023d26e41520 22630       3       com.sun.tools.javac.code.Types::returnTypeSubstitutable (41 bytes)
Event: 41.937 Thread 0x0000023d26e41520 nmethod 22630 0x0000023d14f16510 code [0x0000023d14f167c0, 0x0000023d14f175e0]
Event: 41.937 Thread 0x0000023d26e41520 22631       3       com.sun.tools.javac.code.Types::resultSubtype (42 bytes)
Event: 41.938 Thread 0x0000023d26e41520 nmethod 22631 0x0000023d14f17a90 code [0x0000023d14f17ca0, 0x0000023d14f18188]
Event: 41.971 Thread 0x0000023d26e41520 22632       3       java.lang.RuntimeException::<init> (5 bytes)
Event: 41.971 Thread 0x0000023d26e41520 nmethod 22632 0x0000023d14f18390 code [0x0000023d14f18560, 0x0000023d14f18978]
Event: 41.972 Thread 0x0000023d26e41520 22633       3       com.sun.tools.javac.code.Types$CandidatesCache$Entry::hashCode (25 bytes)
Event: 41.972 Thread 0x0000023d26e41520 nmethod 22633 0x0000023d14f18a90 code [0x0000023d14f18c80, 0x0000023d14f191c0]

GC Heap History (20 events):
Event: 22.890 GC heap before
{Heap before GC invocations=42 (full 1):
 garbage-first heap   total 198656K, used 194051K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 70 young (71680K), 3 survivors (3072K)
 Metaspace       used 102780K, committed 105024K, reserved 1179648K
  class space    used 14066K, committed 15168K, reserved 1048576K
}
Event: 22.895 GC heap after
{Heap after GC invocations=43 (full 1):
 garbage-first heap   total 214016K, used 138592K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 102780K, committed 105024K, reserved 1179648K
  class space    used 14066K, committed 15168K, reserved 1048576K
}
Event: 22.938 GC heap before
{Heap before GC invocations=43 (full 1):
 garbage-first heap   total 214016K, used 140640K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 9 survivors (9216K)
 Metaspace       used 103059K, committed 105344K, reserved 1179648K
  class space    used 14109K, committed 15232K, reserved 1048576K
}
Event: 22.942 GC heap after
{Heap after GC invocations=44 (full 1):
 garbage-first heap   total 214016K, used 140245K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 103059K, committed 105344K, reserved 1179648K
  class space    used 14109K, committed 15232K, reserved 1048576K
}
Event: 23.367 GC heap before
{Heap before GC invocations=45 (full 1):
 garbage-first heap   total 360448K, used 232405K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 50 young (51200K), 2 survivors (2048K)
 Metaspace       used 103627K, committed 105856K, reserved 1179648K
  class space    used 14198K, committed 15296K, reserved 1048576K
}
Event: 23.379 GC heap after
{Heap after GC invocations=46 (full 1):
 garbage-first heap   total 360448K, used 175440K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 103627K, committed 105856K, reserved 1179648K
  class space    used 14198K, committed 15296K, reserved 1048576K
}
Event: 24.955 GC heap before
{Heap before GC invocations=46 (full 1):
 garbage-first heap   total 360448K, used 323920K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 147 young (150528K), 7 survivors (7168K)
 Metaspace       used 106481K, committed 108736K, reserved 1179648K
  class space    used 14781K, committed 15872K, reserved 1048576K
}
Event: 25.014 GC heap after
{Heap after GC invocations=47 (full 1):
 garbage-first heap   total 613376K, used 238165K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 106481K, committed 108736K, reserved 1179648K
  class space    used 14781K, committed 15872K, reserved 1048576K
}
Event: 28.261 GC heap before
{Heap before GC invocations=47 (full 1):
 garbage-first heap   total 613376K, used 411221K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 189 young (193536K), 19 survivors (19456K)
 Metaspace       used 109840K, committed 112064K, reserved 1179648K
  class space    used 15224K, committed 16320K, reserved 1048576K
}
Event: 28.355 GC heap after
{Heap after GC invocations=48 (full 1):
 garbage-first heap   total 613376K, used 304313K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 24 survivors (24576K)
 Metaspace       used 109840K, committed 112064K, reserved 1179648K
  class space    used 15224K, committed 16320K, reserved 1048576K
}
Event: 31.353 GC heap before
{Heap before GC invocations=48 (full 1):
 garbage-first heap   total 613376K, used 450745K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 167 young (171008K), 24 survivors (24576K)
 Metaspace       used 112541K, committed 114816K, reserved 1179648K
  class space    used 15584K, committed 16704K, reserved 1048576K
}
Event: 31.395 GC heap after
{Heap after GC invocations=49 (full 1):
 garbage-first heap   total 613376K, used 326841K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 19 survivors (19456K)
 Metaspace       used 112541K, committed 114816K, reserved 1179648K
  class space    used 15584K, committed 16704K, reserved 1048576K
}
Event: 33.051 GC heap before
{Heap before GC invocations=49 (full 1):
 garbage-first heap   total 613376K, used 486585K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 169 young (173056K), 19 survivors (19456K)
 Metaspace       used 113837K, committed 116160K, reserved 1179648K
  class space    used 15731K, committed 16896K, reserved 1048576K
}
Event: 33.076 GC heap after
{Heap after GC invocations=50 (full 1):
 garbage-first heap   total 613376K, used 343865K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 13 survivors (13312K)
 Metaspace       used 113837K, committed 116160K, reserved 1179648K
  class space    used 15731K, committed 16896K, reserved 1048576K
}
Event: 34.785 GC heap before
{Heap before GC invocations=50 (full 1):
 garbage-first heap   total 613376K, used 515897K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 155 young (158720K), 13 survivors (13312K)
 Metaspace       used 116516K, committed 118784K, reserved 1179648K
  class space    used 16061K, committed 17152K, reserved 1048576K
}
Event: 34.810 GC heap after
{Heap after GC invocations=51 (full 1):
 garbage-first heap   total 613376K, used 387586K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 20 young (20480K), 20 survivors (20480K)
 Metaspace       used 116516K, committed 118784K, reserved 1179648K
  class space    used 16061K, committed 17152K, reserved 1048576K
}
Event: 36.359 GC heap before
{Heap before GC invocations=52 (full 1):
 garbage-first heap   total 644096K, used 536066K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 159 young (162816K), 20 survivors (20480K)
 Metaspace       used 120831K, committed 123136K, reserved 1179648K
  class space    used 16596K, committed 17728K, reserved 1048576K
}
Event: 36.378 GC heap after
{Heap after GC invocations=53 (full 1):
 garbage-first heap   total 644096K, used 400137K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 120831K, committed 123136K, reserved 1179648K
  class space    used 16596K, committed 17728K, reserved 1048576K
}
Event: 39.485 GC heap before
{Heap before GC invocations=53 (full 1):
 garbage-first heap   total 644096K, used 581385K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 170 young (174080K), 12 survivors (12288K)
 Metaspace       used 123737K, committed 126144K, reserved 1179648K
  class space    used 16863K, committed 18048K, reserved 1048576K
}
Event: 39.508 GC heap after
{Heap after GC invocations=54 (full 1):
 garbage-first heap   total 644096K, used 388517K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 22 survivors (22528K)
 Metaspace       used 123737K, committed 126144K, reserved 1179648K
  class space    used 16863K, committed 18048K, reserved 1048576K
}

Dll operation events (16 events):
Event: 0.007 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.035 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.070 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.073 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.077 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.078 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.080 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.291 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.408 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.535 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 0.542 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 1.548 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.549 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.709 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 1.830 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
Event: 2.397 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform7401446182780441484dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 41.826 Thread 0x0000023d734718a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023d1b24c0d0 relative=0x00000000000005b0
Event: 41.826 Thread 0x0000023d734718a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023d1b24c0d0 method=com.sun.tools.javac.comp.Attr.selectSym(Lcom/sun/tools/javac/tree/JCTree$JCFieldAccess;Lcom/sun/tools/javac/code/Symbol;Lcom/sun/tools/javac/code/Type;Lcom/sun/tools/javac/c
Event: 41.826 Thread 0x0000023d734718a0 DEOPT PACKING pc=0x0000023d1b24c0d0 sp=0x000000edf3af80a0
Event: 41.826 Thread 0x0000023d734718a0 DEOPT UNPACKING pc=0x0000023d19e246a2 sp=0x000000edf3af8038 mode 2
Event: 41.839 Thread 0x0000023d734718a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023d1aea8f28 relative=0x0000000000000088
Event: 41.839 Thread 0x0000023d734718a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023d1aea8f28 method=java.lang.Integer.equals(Ljava/lang/Object;)Z @ 1 c2
Event: 41.839 Thread 0x0000023d734718a0 DEOPT PACKING pc=0x0000023d1aea8f28 sp=0x000000edf3af9200
Event: 41.839 Thread 0x0000023d734718a0 DEOPT UNPACKING pc=0x0000023d19e246a2 sp=0x000000edf3af9190 mode 2
Event: 41.839 Thread 0x0000023d734718a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023d1aea8f28 relative=0x0000000000000088
Event: 41.839 Thread 0x0000023d734718a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023d1aea8f28 method=java.lang.Integer.equals(Ljava/lang/Object;)Z @ 1 c2
Event: 41.839 Thread 0x0000023d734718a0 DEOPT PACKING pc=0x0000023d1aea8f28 sp=0x000000edf3af9200
Event: 41.839 Thread 0x0000023d734718a0 DEOPT UNPACKING pc=0x0000023d19e246a2 sp=0x000000edf3af9190 mode 2
Event: 41.909 Thread 0x0000023d734718a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023d1b172830 relative=0x0000000000000410
Event: 41.909 Thread 0x0000023d734718a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023d1b172830 method=com.sun.tools.javac.code.Symbol$TypeSymbol.formFullName(Lcom/sun/tools/javac/util/Name;Lcom/sun/tools/javac/code/Symbol;)Lcom/sun/tools/javac/util/Name; @ 26 c2
Event: 41.909 Thread 0x0000023d734718a0 DEOPT PACKING pc=0x0000023d1b172830 sp=0x000000edf3af8180
Event: 41.909 Thread 0x0000023d734718a0 DEOPT UNPACKING pc=0x0000023d19e246a2 sp=0x000000edf3af8148 mode 2
Event: 41.916 Thread 0x0000023d734718a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023d1b2d6034 relative=0x00000000000035d4
Event: 41.916 Thread 0x0000023d734718a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023d1b2d6034 method=com.sun.tools.javac.code.Symbol$MethodSymbol.isOverridableIn(Lcom/sun/tools/javac/code/Symbol$TypeSymbol;)Z @ 9 c2
Event: 41.916 Thread 0x0000023d734718a0 DEOPT PACKING pc=0x0000023d1b2d6034 sp=0x000000edf3af7fa0
Event: 41.916 Thread 0x0000023d734718a0 DEOPT UNPACKING pc=0x0000023d19e246a2 sp=0x000000edf3af7ca0 mode 2

Classes loaded (20 events):
Event: 40.917 Loading class java/text/BreakIterator
Event: 40.917 Loading class java/text/BreakIterator done
Event: 40.957 Loading class java/lang/runtime/ObjectMethods
Event: 40.957 Loading class java/lang/runtime/ObjectMethods done
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers done
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers$1
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers$1 done
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers$2
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers$2 done
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers$3
Event: 40.958 Loading class java/lang/invoke/MethodHandleImpl$Makers$3 done
Event: 40.961 Loading class java/lang/runtime/ObjectMethods$1
Event: 40.961 Loading class java/lang/runtime/ObjectMethods$1 done
Event: 40.967 Loading class java/lang/invoke/DirectMethodHandle$1
Event: 40.968 Loading class java/lang/invoke/DirectMethodHandle$1 done
Event: 40.980 Loading class java/util/regex/Pattern$Pos
Event: 40.980 Loading class java/util/regex/Pattern$Pos done
Event: 40.992 Loading class java/util/function/ToIntBiFunction
Event: 40.992 Loading class java/util/function/ToIntBiFunction done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 41.501 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009eedc2d8}> (0x000000009eedc2d8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.546 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ec030d8}> (0x000000009ec030d8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.549 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ec15408}> (0x000000009ec15408) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.571 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ecc0578}> (0x000000009ecc0578) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.578 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ecd3fd8}> (0x000000009ecd3fd8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.583 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ece72d8}> (0x000000009ece72d8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.618 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009eb8b288}> (0x000000009eb8b288) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.622 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009eb98f50}> (0x000000009eb98f50) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.648 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ea55d00}> (0x000000009ea55d00) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.675 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ea71e48}> (0x000000009ea71e48) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.696 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009ea89b18}> (0x000000009ea89b18) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.720 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e92bdf8}> (0x000000009e92bdf8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.739 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e945f30}> (0x000000009e945f30) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.750 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e9591e0}> (0x000000009e9591e0) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.792 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e82d980}> (0x000000009e82d980) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.799 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e84b788}> (0x000000009e84b788) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.807 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e85eda8}> (0x000000009e85eda8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.834 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e751e00}> (0x000000009e751e00) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.846 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e77f490}> (0x000000009e77f490) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 41.900 Thread 0x0000023d734718a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009e6d9988}> (0x000000009e6d9988) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 40.943 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 40.943 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 41.040 Executing VM operation: ICBufferFull
Event: 41.041 Executing VM operation: ICBufferFull done
Event: 41.167 Executing VM operation: ICBufferFull
Event: 41.167 Executing VM operation: ICBufferFull done
Event: 41.197 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 41.198 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 41.201 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 41.201 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 41.256 Executing VM operation: ICBufferFull
Event: 41.256 Executing VM operation: ICBufferFull done
Event: 41.366 Executing VM operation: ICBufferFull
Event: 41.366 Executing VM operation: ICBufferFull done
Event: 41.469 Executing VM operation: ICBufferFull
Event: 41.470 Executing VM operation: ICBufferFull done
Event: 41.607 Executing VM operation: ICBufferFull
Event: 41.608 Executing VM operation: ICBufferFull done
Event: 41.915 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 41.915 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Events (20 events):
Event: 35.230 Thread 0x0000023d26e17eb0 flushing nmethod 0x0000023d1a516590
Event: 35.230 Thread 0x0000023d26e17eb0 flushing nmethod 0x0000023d1a421090
Event: 35.230 Thread 0x0000023d26e17eb0 flushing nmethod 0x0000023d1a50ff90
Event: 35.230 Thread 0x0000023d26e17eb0 flushing nmethod 0x0000023d1a4f4610
Event: 35.230 Thread 0x0000023d26e17eb0 flushing nmethod 0x0000023d1a40a990
Event: 35.230 Thread 0x0000023d26e17eb0 flushing nmethod 0x0000023d1a417790
Event: 37.383 Thread 0x0000023d78b2e9f0 Thread exited: 0x0000023d78b2e9f0
Event: 37.383 Thread 0x0000023d78b2c290 Thread exited: 0x0000023d78b2c290
Event: 37.383 Thread 0x0000023d78b32b90 Thread exited: 0x0000023d78b32b90
Event: 37.383 Thread 0x0000023d78b30430 Thread exited: 0x0000023d78b30430
Event: 37.383 Thread 0x0000023d78b30ac0 Thread exited: 0x0000023d78b30ac0
Event: 37.383 Thread 0x0000023d78b2f710 Thread exited: 0x0000023d78b2f710
Event: 37.383 Thread 0x0000023d78b2dcd0 Thread exited: 0x0000023d78b2dcd0
Event: 37.384 Thread 0x0000023d78b31150 Thread exited: 0x0000023d78b31150
Event: 38.231 Thread 0x0000023d68550c80 Thread exited: 0x0000023d68550c80
Event: 38.450 Thread 0x0000023d6854cf30 Thread exited: 0x0000023d6854cf30
Event: 38.816 Thread 0x0000023d6854cf30 Thread added: 0x0000023d6854cf30
Event: 39.036 Thread 0x0000023d7d089450 Thread added: 0x0000023d7d089450
Event: 40.185 Thread 0x0000023d7d089450 Thread exited: 0x0000023d7d089450
Event: 40.260 Thread 0x0000023d7d088d80 Thread added: 0x0000023d7d088d80


Dynamic libraries:
0x00007ff795900000 - 0x00007ff795910000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ffa9c8d0000 - 0x00007ffa9cac8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa9a920000 - 0x00007ffa9a9e2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa9a120000 - 0x00007ffa9a416000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa9a420000 - 0x00007ffa9a520000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa87f40000 - 0x00007ffa87f5b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ffa88020000 - 0x00007ffa88039000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ffa9baf0000 - 0x00007ffa9bba1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa9b2f0000 - 0x00007ffa9b38e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa9c7c0000 - 0x00007ffa9c85f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa9c070000 - 0x00007ffa9c193000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa9a750000 - 0x00007ffa9a777000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffa9bec0000 - 0x00007ffa9c05d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa9a680000 - 0x00007ffa9a6a2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa890c0000 - 0x00007ffa8935a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ffa9c860000 - 0x00007ffa9c88b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa9a780000 - 0x00007ffa9a899000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa9a6b0000 - 0x00007ffa9a74d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa91d10000 - 0x00007ffa91d1a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa9a8f0000 - 0x00007ffa9a91f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa93170000 - 0x00007ffa9317c000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ffa67320000 - 0x00007ffa673ae000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ffa24f90000 - 0x00007ffa25ca7000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ffa9bbc0000 - 0x00007ffa9bc2b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa99dc0000 - 0x00007ffa99e0b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa8ec00000 - 0x00007ffa8ec27000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa99da0000 - 0x00007ffa99db2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa987d0000 - 0x00007ffa987e2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa91f70000 - 0x00007ffa91f7a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ffa97db0000 - 0x00007ffa97fb1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa88460000 - 0x00007ffa88494000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa9a090000 - 0x00007ffa9a112000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa7c5b0000 - 0x00007ffa7c5bf000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007ffa85230000 - 0x00007ffa8524f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ffa9aac0000 - 0x00007ffa9b22e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa97fc0000 - 0x00007ffa98763000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa9c1a0000 - 0x00007ffa9c4f3000 	C:\WINDOWS\System32\combase.dll
0x00007ffa998c0000 - 0x00007ffa998eb000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffa9a9f0000 - 0x00007ffa9aabd000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa9c710000 - 0x00007ffa9c7bd000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa9c6a0000 - 0x00007ffa9c6fb000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa99e90000 - 0x00007ffa99eb5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa671b0000 - 0x00007ffa67287000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007ffa7f3b0000 - 0x00007ffa7f3c8000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ffa90850000 - 0x00007ffa90860000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ffa96140000 - 0x00007ffa9624a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa99620000 - 0x00007ffa9968a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa7f3d0000 - 0x00007ffa7f3e6000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ffa89e00000 - 0x00007ffa89e10000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007ffa68a40000 - 0x00007ffa68a67000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x0000000063650000 - 0x00000000636c3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffa89af0000 - 0x00007ffa89afa000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ffa884d0000 - 0x00007ffa884db000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ffa9bbb0000 - 0x00007ffa9bbb8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa99810000 - 0x00007ffa99828000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa98f40000 - 0x00007ffa98f78000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa99e10000 - 0x00007ffa99e3e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa99830000 - 0x00007ffa9983c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa99300000 - 0x00007ffa9933b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa9c700000 - 0x00007ffa9c708000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa85220000 - 0x00007ffa85229000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007ffa7c5a0000 - 0x00007ffa7c5ae000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007ffa9a520000 - 0x00007ffa9a67d000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa99930000 - 0x00007ffa99957000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa998f0000 - 0x00007ffa9992b000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa78c40000 - 0x00007ffa78c47000 	C:\WINDOWS\system32\wshunix.dll
0x0000000063550000 - 0x00000000635c3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform7401446182780441484dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform7401446182780441484dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-bin\cetblhg4pflnnks72fxwobvgv\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-bin\cetblhg4pflnnks72fxwobvgv\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
CLASSPATH=C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
USERNAME=ntc
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 4:51 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4191M free)
TotalPageFile size 22476M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 1041M, peak: 1044M
current process commit charge ("private bytes"): 1081M, peak: 1089M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
