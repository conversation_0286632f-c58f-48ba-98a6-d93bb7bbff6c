<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/OrderCardStyle"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/margin_medium"
        android:paddingEnd="@dimen/margin_medium"
        android:paddingTop="@dimen/margin_medium"
        android:paddingBottom="@dimen/margin_medium">

        <TextView
            android:id="@+id/order_id"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAlignment="viewStart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Order #12345" />

        <TextView
            android:id="@+id/order_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_extra_small"
            android:textSize="@dimen/text_size_micro"
            android:textColor="@color/text_secondary"
            android:maxLines="1"
            android:ellipsize="end"
            android:textAlignment="viewStart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/order_id"
            tools:text="Order Date: 2023-06-15" />

        <View
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="@dimen/divider_height"
            android:layout_marginTop="@dimen/margin_small"
            android:background="@color/divider"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/order_date" />

        <TextView
            android:id="@+id/order_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small"
            android:textSize="@dimen/text_size_small"
            android:textColor="@color/success"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/order_total"
            app:layout_constraintTop_toBottomOf="@id/divider"
            tools:text="Delivered" />

        <TextView
            android:id="@+id/order_total"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small"
            android:textSize="@dimen/text_size_medium"
            android:textColor="@color/primary"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider"
            tools:text="৳450" />

        <LinearLayout
            android:id="@+id/buttons_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_medium"
            android:orientation="horizontal"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/order_status"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_track_order"
                style="@style/OrderTrackButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/track_order"
                android:layout_marginEnd="@dimen/margin_extra_small" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_reorder"
                style="@style/OrderButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/reorder"
                android:layout_marginStart="@dimen/margin_extra_small" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
