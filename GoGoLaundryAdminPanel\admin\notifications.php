<?php
/**
 * Notifications Management Page
 *
 * This page handles the notifications management in the admin panel
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/FCMService.php';

// Include authentication middleware
require_once 'auth.php';

// Initialize variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Build query
$query = "
    SELECT n.*, u.full_name as user_name, o.order_number
    FROM notifications n
    LEFT JOIN users u ON n.user_id = u.id
    LEFT JOIN orders o ON n.order_id = o.id
    WHERE 1=1
";

$countQuery = "SELECT COUNT(*) FROM notifications n WHERE 1=1";
$params = [];

// Add search condition
if (!empty($search)) {
    $searchCondition = " AND (n.title LIKE ? OR n.message LIKE ? OR u.full_name LIKE ? OR o.order_number LIKE ?)";
    $query .= $searchCondition;
    $countQuery .= $searchCondition;
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
}

// Add type condition
if (!empty($type)) {
    $typeCondition = " AND n.type = ?";
    $query .= $typeCondition;
    $countQuery .= $typeCondition;
    $params[] = $type;
}

// Add date range condition
if (!empty($startDate) && !empty($endDate)) {
    $dateCondition = " AND DATE(n.created_at) BETWEEN ? AND ?";
    $query .= $dateCondition;
    $countQuery .= $dateCondition;
    $params[] = $startDate;
    $params[] = $endDate;
}

// Execute count query (without LIMIT and OFFSET parameters)
$stmt = $pdo->prepare($countQuery);
$stmt->execute($params);
$totalNotifications = $stmt->fetchColumn();

// Calculate total pages
$totalPages = ceil($totalNotifications / $limit);

// Add order by
$query .= " ORDER BY n.created_at DESC";

// Add limit and offset
$query .= " LIMIT ? OFFSET ?";
$mainQueryParams = $params;
$mainQueryParams[] = $limit;
$mainQueryParams[] = $offset;

// Execute main query
$stmt = $pdo->prepare($query);
$stmt->execute($mainQueryParams);
$notifications = $stmt->fetchAll();

// Get users for sending notifications
$stmt = $pdo->prepare("SELECT id, full_name, phone FROM users WHERE is_verified = 1 ORDER BY full_name");
$stmt->execute();
$users = $stmt->fetchAll();

// Handle form submission for sending notification
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_notification'])) {
    // Debug: Log all POST and FILES data
    error_log("=== NOTIFICATION FORM SUBMISSION DEBUG ===");
    error_log("POST data: " . print_r($_POST, true));
    error_log("FILES data: " . print_r($_FILES, true));
    error_log("=== END DEBUG ===");

    $title = $_POST['title'];
    $message = $_POST['message'];
    $type = $_POST['notification_type'];
    $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : null;
    $orderIdInput = isset($_POST['order_id']) && !empty(trim($_POST['order_id'])) ? trim($_POST['order_id']) : null;
    $orderId = null;
    $sendToAll = isset($_POST['send_to_all']) && $_POST['send_to_all'] === 'on';
    $sendFcm = isset($_POST['send_fcm']) && $_POST['send_fcm'] === 'on';
    $sendSms = isset($_POST['send_sms']) && $_POST['send_sms'] === 'on';

    // Initialize errors array first
    $errors = [];

    // Handle image upload or URL
    $imageUrl = null;
    $uploadDir = '../uploads/notifications/';

    // Create upload directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }



    // Check if image URL is provided
    if (!empty($_POST['image_url'])) {
        $imageUrl = trim($_POST['image_url']);
        // Validate URL format
        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid image URL format';
        }

    }
    // Check if image file is uploaded
    elseif (isset($_FILES['image_file']) && $_FILES['image_file']['error'] === UPLOAD_ERR_OK) {
        $uploadedFile = $_FILES['image_file'];
        $fileName = $uploadedFile['name'];
        $fileTmpName = $uploadedFile['tmp_name'];
        $fileSize = $uploadedFile['size'];
        $fileError = $uploadedFile['error'];



        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        $fileType = mime_content_type($fileTmpName);

        if (!in_array($fileType, $allowedTypes)) {
            $errors[] = 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.';

        }

        // Validate file size (max 5MB)
        if ($fileSize > 5 * 1024 * 1024) {
            $errors[] = 'File size too large. Maximum size is 5MB.';

        }

        if (empty($errors)) {
            // Generate unique filename
            $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
            $newFileName = 'notification_' . time() . '_' . uniqid() . '.' . $fileExtension;
            $targetPath = $uploadDir . $newFileName;

            if (move_uploaded_file($fileTmpName, $targetPath)) {
                // Create full URL instead of relative path
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                $host = $_SERVER['HTTP_HOST'];
                $imageUrl = $protocol . '://' . $host . '/GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/' . $newFileName;
            } else {
                $errors[] = 'Failed to upload image file.';
            }
        }
    } elseif (isset($_FILES['image_file']) && $_FILES['image_file']['error'] !== UPLOAD_ERR_NO_FILE) {
        // Handle upload errors
        $uploadError = $_FILES['image_file']['error'];
        switch ($uploadError) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errors[] = 'File size exceeds the maximum allowed size.';
                break;
            case UPLOAD_ERR_PARTIAL:
                $errors[] = 'File was only partially uploaded.';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $errors[] = 'Missing temporary folder.';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $errors[] = 'Failed to write file to disk.';
                break;
            case UPLOAD_ERR_EXTENSION:
                $errors[] = 'File upload stopped by extension.';
                break;
            default:
                $errors[] = 'Unknown upload error.';
                break;
        }

    }

    if (empty($title)) {
        $errors[] = 'Title is required';
    }

    if (empty($message)) {
        $errors[] = 'Message is required';
    }

    if (!$sendToAll && empty($userId)) {
        $errors[] = 'Please select a user or check "Send to all users"';
    }

    // Validate and resolve order ID if provided
    if ($orderIdInput !== null) {
        // Check if input is numeric (order ID) or alphanumeric (order number)
        if (is_numeric($orderIdInput)) {
            // Input is numeric - treat as order ID
            $stmt = $pdo->prepare("SELECT id FROM orders WHERE id = ?");
            $stmt->execute([(int)$orderIdInput]);
            if ($stmt->rowCount() > 0) {
                $orderId = (int)$orderIdInput;
            } else {
                $errors[] = 'Invalid order ID. Order does not exist.';
            }
        } else {
            // Input is alphanumeric - treat as order number
            $stmt = $pdo->prepare("SELECT id FROM orders WHERE order_number = ?");
            $stmt->execute([$orderIdInput]);
            $result = $stmt->fetch();
            if ($result) {
                $orderId = (int)$result['id'];
            } else {
                $errors[] = 'Invalid order number. Order does not exist.';
            }
        }
    }

    if (empty($errors)) {
        try {
            // Start transaction
            $pdo->beginTransaction();

            if ($sendToAll) {
                // Get all verified users
                $stmt = $pdo->prepare("SELECT id FROM users WHERE is_verified = 1");
                $stmt->execute();
                $userIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

                // Insert notification for each user
                $stmt = $pdo->prepare("
                    INSERT INTO notifications (user_id, order_id, title, message, type, image_url, fcm_sent, sms_sent)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");

                foreach ($userIds as $uid) {
                    $stmt->execute([$uid, $orderId, $title, $message, $type, $imageUrl, $sendFcm ? 0 : 0, $sendSms ? 0 : 0]);
                }

                // Log activity
                logAdminActivity($pdo, $adminData['id'], 'send_notification', "Sent notification to all users: $title");

                // Send FCM notifications
                if ($sendFcm) {
                    try {
                        $fcmService = new FCMService();
                        $notificationData = [
                            'type' => $type,
                            'order_id' => $orderId,
                            'image_url' => $imageUrl,
                            'timestamp' => time()
                        ];

                        $fcmResult = $fcmService->sendToAllUsers($pdo, $title, $message, $notificationData);

                        if ($fcmResult['success']) {
                            // Mark notifications as FCM sent
                            $pdo->exec("UPDATE notifications SET fcm_sent = 1 WHERE fcm_sent = 0 AND created_at >= NOW() - INTERVAL 1 MINUTE");
                            error_log("FCM notifications sent to all users successfully");
                        } else {
                            error_log("Failed to send FCM notifications to all users: " . ($fcmResult['error'] ?? 'Unknown error'));
                        }
                    } catch (Exception $e) {
                        error_log("FCM notification error: " . $e->getMessage());
                    }
                }

                // Send SMS notifications in the background
                if ($sendSms) {
                    // This would typically be handled by a background job
                    // For now, we'll just mark them as sent
                    $pdo->exec("UPDATE notifications SET sms_sent = 1 WHERE sms_sent = 0");
                }
            } else {
                // Insert notification for a single user
                $stmt = $pdo->prepare("
                    INSERT INTO notifications (user_id, order_id, title, message, type, image_url, fcm_sent, sms_sent)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([$userId, $orderId, $title, $message, $type, $imageUrl, $sendFcm ? 0 : 0, $sendSms ? 0 : 0]);

                // Get user name
                $stmt = $pdo->prepare("SELECT full_name FROM users WHERE id = ?");
                $stmt->execute([$userId]);
                $userName = $stmt->fetchColumn();

                // Log activity
                logAdminActivity($pdo, $adminData['id'], 'send_notification', "Sent notification to $userName: $title");

                // Send FCM notification
                if ($sendFcm) {
                    try {
                        $fcmService = new FCMService();
                        $notificationData = [
                            'type' => $type,
                            'order_id' => $orderId,
                            'image_url' => $imageUrl,
                            'timestamp' => time()
                        ];

                        $fcmResult = $fcmService->sendToUser($pdo, $userId, $title, $message, $notificationData);

                        if ($fcmResult['success']) {
                            // Mark notification as FCM sent
                            $stmt = $pdo->prepare("UPDATE notifications SET fcm_sent = 1 WHERE user_id = ? AND fcm_sent = 0 AND created_at >= NOW() - INTERVAL 1 MINUTE");
                            $stmt->execute([$userId]);
                            error_log("FCM notification sent to user $userId successfully");
                        } else {
                            error_log("Failed to send FCM notification to user $userId: " . ($fcmResult['error'] ?? 'Unknown error'));
                        }
                    } catch (Exception $e) {
                        error_log("FCM notification error for user $userId: " . $e->getMessage());
                    }
                }

                // Send SMS notification
                if ($sendSms) {
                    // Get user's phone number
                    $stmt = $pdo->prepare("SELECT phone FROM users WHERE id = ?");
                    $stmt->execute([$userId]);
                    $phone = $stmt->fetchColumn();

                    if (!empty($phone)) {
                        // Send SMS
                        // This would typically call an external service
                        // For now, we'll just mark it as sent
                        $pdo->exec("UPDATE notifications SET sms_sent = 1 WHERE user_id = ? AND sms_sent = 0");
                    }
                }
            }

            // Commit transaction
            $pdo->commit();

            if ($imageUrl) {
                $_SESSION['success_message'] = 'Notification sent successfully with image: ' . basename($imageUrl);
            } else {
                $_SESSION['success_message'] = 'Notification sent successfully (no image)';
            }
        } catch (PDOException $e) {
            // Rollback transaction
            $pdo->rollBack();

            $_SESSION['error_message'] = 'Failed to send notification: ' . $e->getMessage();
        }

        // Redirect to maintain pagination and search
        header("Location: notifications.php?page=$page&search=" . urlencode($search) . "&type=" . urlencode($type) . "&start_date=" . urlencode($startDate) . "&end_date=" . urlencode($endDate));
        exit;
    } else {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Page title and breadcrumbs
$pageTitle = 'Notifications Management';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Notifications', 'link' => '']
];

// Include header
include 'includes/header.php';
?>

<style>
/* Professional Send Notification Modal Styles */



.upload-area {
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #0d6efd !important;
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateY(-1px);
}

.upload-icon {
    transition: all 0.2s ease;
}

.upload-area:hover .upload-icon i {
    transform: scale(1.05);
    color: #0d6efd !important;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control-lg {
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

.border-2 {
    border-width: 2px !important;
}

.form-check-lg .form-check-input {
    width: 1.5em;
    height: 1.5em;
    margin-top: 0.125em;
}

.form-check-lg .form-check-label {
    font-size: 1.05rem;
    margin-left: 0.5rem;
}

.cursor-pointer {
    cursor: pointer;
}

.nav-pills .nav-link {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-pills .nav-link.active {
    background-color: #0d6efd;
    color: white;
}

.card {
    border-radius: 8px;
    border: 1px solid #e3e6f0;
}

.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e3e6f0;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.form-text {
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 6px;
}

/* Animation for modal */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-body {
        padding: 1.5rem !important;
    }
}
</style>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Notifications Management</h1>

    <!-- Success/Error Messages -->
    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= htmlspecialchars($_SESSION['success_message']) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['success_message']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?= $_SESSION['error_message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php unset($_SESSION['error_message']); ?>
    <?php endif; ?>

    <!-- Search and Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search and Filter</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="notifications.php" class="row">
                <div class="col-md-3 mb-3">
                    <label for="search">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Title, Message, User, Order #">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="type">Type</label>
                    <select class="form-control" id="type" name="type">
                        <option value="">All Types</option>
                        <option value="order_status" <?= $type === 'order_status' ? 'selected' : '' ?>>Order Status</option>
                        <option value="promo" <?= $type === 'promo' ? 'selected' : '' ?>>Promotion</option>
                        <option value="system" <?= $type === 'system' ? 'selected' : '' ?>>System</option>
                        <option value="custom" <?= $type === 'custom' ? 'selected' : '' ?>>Custom</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="start_date">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                </div>
                <div class="col-md-2 mb-3">
                    <label for="end_date">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>">
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary mr-2">Search</button>
                    <a href="notifications.php" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Send Notification Button -->
    <div class="mb-4">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#sendNotificationModal">
            <i class="fas fa-paper-plane"></i> Send Notification
        </button>
    </div>

    <!-- Notifications Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Notifications</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Message</th>
                            <th>Image</th>
                            <th>User</th>
                            <th>Order</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($notifications)): ?>
                            <tr>
                                <td colspan="9" class="text-center">No notifications found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($notifications as $notification): ?>
                                <tr>
                                    <td><?= htmlspecialchars($notification['title']) ?></td>
                                    <td><?= htmlspecialchars(substr($notification['message'], 0, 50)) . (strlen($notification['message']) > 50 ? '...' : '') ?></td>
                                    <td>
                                        <?php if (!empty($notification['image_url'])): ?>
                                            <img src="<?= htmlspecialchars($notification['image_url']) ?>"
                                                 alt="Notification Image"
                                                 class="img-thumbnail"
                                                 style="max-width: 60px; max-height: 60px; cursor: pointer;"
                                                 onclick="showImageModal('<?= htmlspecialchars($notification['image_url']) ?>', '<?= htmlspecialchars($notification['title']) ?>')">
                                        <?php else: ?>
                                            <span class="text-muted">No Image</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= htmlspecialchars($notification['user_name'] ?? 'N/A') ?></td>
                                    <td>
                                        <?php if ($notification['order_id'] && $notification['order_number']): ?>
                                            <a href="order_details.php?id=<?= $notification['order_id'] ?>"><?= htmlspecialchars($notification['order_number']) ?></a>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= getNotificationTypeBadgeClass($notification['type']) ?>">
                                            <?= formatNotificationType($notification['type']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($notification['is_read']): ?>
                                            <span class="badge bg-success">Read</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Unread</span>
                                        <?php endif; ?>

                                        <?php if ($notification['fcm_sent'] === '1'): ?>
                                            <span class="badge bg-info">FCM Sent</span>
                                        <?php elseif ($notification['fcm_sent'] === '0'): ?>
                                            <span class="badge bg-danger">FCM Pending</span>
                                        <?php endif; ?>

                                        <?php if ($notification['sms_sent'] === '1'): ?>
                                            <span class="badge bg-info">SMS Sent</span>
                                        <?php elseif ($notification['sms_sent'] === '0'): ?>
                                            <span class="badge bg-danger">SMS Pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('M d, Y H:i', strtotime($notification['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button"
                                                    class="btn btn-sm btn-primary"
                                                    onclick="resendNotification(<?= $notification['id'] ?>, '<?= htmlspecialchars($notification['title']) ?>', '<?= htmlspecialchars($notification['user_name'] ?? 'N/A') ?>')"
                                                    title="Resend Notification">
                                                <i class="fas fa-redo"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-sm btn-info"
                                                    onclick="viewNotificationDetails(<?= $notification['id'] ?>)"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mt-4">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&type=<?= urlencode($type) ?>&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">
                                    Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&type=<?= urlencode($type) ?>&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&type=<?= urlencode($type) ?>&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">
                                    Next
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Send Notification Modal -->
<div class="modal fade" id="sendNotificationModal" tabindex="-1" role="dialog" aria-labelledby="sendNotificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content border-0 shadow-lg">
            <!-- Professional Header -->
            <div class="modal-header border-0 bg-white">
                <div class="d-flex align-items-center">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                        <i class="fas fa-paper-plane text-primary fs-5"></i>
                    </div>
                    <div>
                        <h4 class="modal-title mb-1 fw-bold text-dark" id="sendNotificationModalLabel">Send Notification</h4>
                        <p class="text-muted mb-0 small">Create and send notifications to your users</p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <form method="POST" action="notifications.php" enctype="multipart/form-data">
                <div class="modal-body p-4">
                    <!-- Content Section -->
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Title Input -->
                            <div class="mb-4">
                                <label for="title" class="form-label fw-bold text-dark">
                                    <i class="fas fa-heading text-primary me-2"></i>Notification Title
                                </label>
                                <input type="text" class="form-control form-control-lg border-2" id="title" name="title"
                                       placeholder="Enter a compelling title..." required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info"></i> Keep it short and engaging (max 50 characters)
                                </div>
                            </div>

                            <!-- Message Input -->
                            <div class="mb-4">
                                <label for="message" class="form-label fw-bold text-dark">
                                    <i class="fas fa-comment-alt text-primary me-2"></i>Message Content
                                </label>
                                <textarea class="form-control border-2" id="message" name="message" rows="4"
                                          placeholder="Write your message here..." required></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info"></i> Clear and concise message for better engagement
                                </div>
                            </div>

                            <!-- Type Selection -->
                            <div class="mb-4">
                                <label for="notification_type" class="form-label fw-bold text-dark">
                                    <i class="fas fa-tag text-primary me-2"></i>Notification Type
                                </label>
                                <select class="form-select border-2" id="notification_type" name="notification_type" required>
                                    <option value="">Choose notification type...</option>
                                    <option value="custom">📝 Custom Message</option>
                                    <option value="promo">🎉 Promotion/Offer</option>
                                    <option value="system">⚙️ System Update</option>
                                    <option value="order_status">📦 Order Status</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Image Upload Section -->
                            <div class="mb-4">
                                <label class="form-label fw-bold text-dark">
                                    <i class="fas fa-image text-primary me-2"></i>Notification Image
                                    <span class="badge bg-light text-muted ms-2">Optional</span>
                                </label>

                                <!-- Image Upload Tabs -->
                                <ul class="nav nav-pills nav-fill mb-3" id="imageUploadTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="upload-tab" data-bs-toggle="pill"
                                                data-bs-target="#upload-panel" type="button" role="tab">
                                            <i class="fas fa-upload me-1"></i>Upload
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="url-tab" data-bs-toggle="pill"
                                                data-bs-target="#url-panel" type="button" role="tab">
                                            <i class="fas fa-link me-1"></i>URL
                                        </button>
                                    </li>
                                </ul>

                                <div class="tab-content" id="imageUploadContent">
                                    <!-- Upload Panel -->
                                    <div class="tab-pane fade show active" id="upload-panel" role="tabpanel">
                                        <input type="file" class="d-none" id="image_file" name="image_file" accept="image/*">
                                        <div class="upload-area border-2 border-dashed border-primary rounded-3 p-4 text-center">
                                            <label for="image_file" class="cursor-pointer">
                                                <div class="upload-icon mb-2">
                                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary"></i>
                                                </div>
                                                <h6 class="text-primary">Click to upload image</h6>
                                                <p class="text-muted small mb-0">
                                                    JPEG, PNG, GIF, WebP<br>
                                                    Max size: 5MB
                                                </p>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- URL Panel -->
                                    <div class="tab-pane fade" id="url-panel" role="tabpanel">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="fas fa-link text-primary"></i>
                                            </span>
                                            <input type="url" class="form-control" id="image_url" name="image_url"
                                                   placeholder="https://example.com/image.jpg">
                                        </div>
                                        <div class="form-text mt-2">
                                            <i class="fas fa-info-circle text-info"></i> Enter a direct link to an image
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Order ID -->
                            <div class="mb-4">
                                <label for="order_id" class="form-label fw-bold text-dark">
                                    <i class="fas fa-shopping-cart text-primary me-2"></i>Related Order
                                    <span class="badge bg-light text-muted ms-2">Optional</span>
                                </label>
                                <input type="text" class="form-control border-2" id="order_id" name="order_id"
                                       placeholder="ORD2505248446 or 123">
                                <div class="form-text">
                                    <i class="fas fa-info-circle text-info"></i> Link this notification to a specific order
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recipients Section -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary mb-3">
                                        <i class="fas fa-users me-2"></i>Select Recipients
                                    </h6>

                                    <!-- Recipient Toggle -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch form-check-lg mb-3">
                                                <input class="form-check-input" type="checkbox" id="send_to_all" name="send_to_all">
                                                <label class="form-check-label fw-bold" for="send_to_all">
                                                    <i class="fas fa-broadcast-tower text-warning me-2"></i>
                                                    Send to All Users
                                                </label>
                                            </div>

                                            <!-- User Selection -->
                                            <div id="user_select_container">
                                                <label for="user_id" class="form-label fw-bold text-dark">
                                                    <i class="fas fa-user text-primary me-2"></i>Select User
                                                </label>
                                                <select class="form-select border-2" id="user_id" name="user_id" required>
                                                    <option value="">Choose a user...</option>
                                                    <?php foreach ($users as $user): ?>
                                                        <option value="<?= $user['id'] ?>">
                                                            <?= htmlspecialchars($user['full_name']) ?>
                                                            (<?= htmlspecialchars($user['phone']) ?>)
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <h6 class="text-primary mb-3">
                                                <i class="fas fa-paper-plane me-2"></i>Delivery Methods
                                            </h6>

                                            <div class="form-check form-check-lg mb-2">
                                                <input class="form-check-input" type="checkbox" id="send_fcm" name="send_fcm" checked>
                                                <label class="form-check-label" for="send_fcm">
                                                    <i class="fas fa-mobile-alt text-success me-2"></i>
                                                    Push Notification (FCM)
                                                </label>
                                            </div>

                                            <div class="form-check form-check-lg">
                                                <input class="form-check-input" type="checkbox" id="send_sms" name="send_sms">
                                                <label class="form-check-label" for="send_sms">
                                                    <i class="fas fa-sms text-info me-2"></i>
                                                    SMS Message
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Professional Footer -->
                <div class="modal-footer border-top bg-white p-4">
                    <div class="d-flex justify-content-end gap-3 w-100">
                        <button type="button" class="btn btn-outline-secondary px-4 py-2" data-bs-dismiss="modal">
                            Cancel
                        </button>
                        <button type="submit" name="send_notification" class="btn btn-primary px-4 py-2">
                            <i class="fas fa-paper-plane me-2"></i>Send Notification
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Notification Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Notification Image" class="img-fluid" style="max-height: 500px;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a id="downloadImageBtn" href="" download class="btn btn-primary">Download Image</a>
            </div>
        </div>
    </div>
</div>

<!-- Resend Confirmation Modal -->
<div class="modal fade" id="resendModal" tabindex="-1" role="dialog" aria-labelledby="resendModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resendModalLabel">Resend Notification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to resend this notification?</p>
                <p><strong>Title:</strong> <span id="resendNotificationTitle"></span></p>
                <p><strong>Original User:</strong> <span id="resendOriginalUser"></span></p>

                <!-- Recipient Selection -->
                <div class="mb-3">
                    <h6 class="text-primary">Send To:</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="resendRecipient" id="resendToOriginal" value="original" checked>
                        <label class="form-check-label" for="resendToOriginal">
                            <strong>Original User Only</strong> - <span id="resendOriginalUserName"></span>
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="resendRecipient" id="resendToAll" value="all">
                        <label class="form-check-label" for="resendToAll">
                            <strong>All Users</strong> - Send to all verified users
                        </label>
                    </div>
                </div>

                <!-- Delivery Method Selection -->
                <div class="mb-3">
                    <h6 class="text-primary">Delivery Methods:</h6>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="resendFcm" checked>
                        <label class="form-check-label" for="resendFcm">
                            Send as push notification (FCM)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="resendSms">
                        <label class="form-check-label" for="resendSms">
                            Send as SMS
                        </label>
                    </div>
                </div>

                <!-- Warning for All Users -->
                <div class="alert alert-warning" id="resendAllWarning" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This will send the notification to ALL verified users in the system. This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmResendBtn">Resend</button>
            </div>
        </div>
    </div>
</div>

<!-- Notification Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">Notification Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="notificationDetailsContent">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
    $(document).ready(function() {
        // Initialize file upload event binding
        bindFileUploadEvent();

        // Toggle user select based on send to all checkbox
        $('#send_to_all').change(function() {
            if ($(this).is(':checked')) {
                $('#user_select_container').hide();
                $('#user_id').prop('required', false);
            } else {
                $('#user_select_container').show();
                $('#user_id').prop('required', true);
            }
        });

        // Clear image URL when switching to upload tab
        $('#upload-tab').on('click', function() {
            $('#image_url').val('');
        });

        // Clear file upload when switching to URL tab
        $('#url-tab').on('click', function() {
            // Clear the file input but don't reset the area
            $('#image_file').val('');
            resetUploadArea();
        });

        // Character counter for title
        $('#title').on('input', function() {
            const maxLength = 50;
            const currentLength = $(this).val().length;
            const remaining = maxLength - currentLength;

            let formText = $(this).siblings('.form-text');
            if (remaining < 10) {
                formText.html(`<i class="fas fa-exclamation-triangle text-warning"></i> ${remaining} characters remaining`);
            } else {
                formText.html('<i class="fas fa-info-circle text-info"></i> Keep it short and engaging (max 50 characters)');
            }
        });

        // Form validation and debugging
        $('form').on('submit', function(e) {
            const sendFcm = $('#send_fcm').is(':checked');
            const sendSms = $('#send_sms').is(':checked');

            if (!sendFcm && !sendSms) {
                e.preventDefault();
                alert('Please select at least one delivery method (FCM or SMS)');
                return false;
            }

            // Debug: Log form data
            console.log('Form submission debug:');
            console.log('File input element:', $('#image_file')[0]);
            console.log('File input files:', $('#image_file')[0].files);
            console.log('File selected:', $('#image_file')[0].files[0]);
            console.log('Image URL:', $('#image_url').val());
            console.log('Title:', $('#title').val());
            console.log('Message:', $('#message').val());
            console.log('Send to all:', $('#send_to_all').is(':checked'));
            console.log('User ID:', $('#user_id').val());

            // Check if file input exists and has files
            if ($('#image_file')[0] && $('#image_file')[0].files.length > 0) {
                console.log('✓ File is selected and should be uploaded');
            } else {
                console.log('✗ No file selected');
            }
        });
    });

    // Clear file upload
    function clearFileUpload() {
        $('#image_file').val('');
        resetUploadArea();
    }

    // Reset upload area to original state
    function resetUploadArea() {
        $('.upload-area').html(`
            <label for="image_file" class="cursor-pointer">
                <div class="upload-icon mb-2">
                    <i class="fas fa-cloud-upload-alt fa-3x text-primary"></i>
                </div>
                <h6 class="text-primary">Click to upload image</h6>
                <p class="text-muted small mb-0">
                    JPEG, PNG, GIF, WebP<br>
                    Max size: 5MB
                </p>
            </label>
        `);
    }

    // Bind file upload event
    function bindFileUploadEvent() {
        $('#image_file').off('change').on('change', function() {
            const file = this.files[0];
            if (file) {
                const fileName = file.name;
                const fileSize = (file.size / 1024 / 1024).toFixed(2);

                // Validate file size
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size too large. Maximum size is 5MB.');
                    $(this).val('');
                    return;
                }

                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
                    $(this).val('');
                    return;
                }

                const uploadArea = $('.upload-area');
                uploadArea.html(`
                    <div class="upload-icon mb-2">
                        <i class="fas fa-check-circle fa-3x text-success"></i>
                    </div>
                    <h6 class="text-success">File Selected</h6>
                    <p class="text-muted small mb-0">
                        ${fileName}<br>
                        Size: ${fileSize} MB
                    </p>
                    <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="clearFileUpload()">
                        <i class="fas fa-times me-1"></i>Remove
                    </button>
                `);
            }
        });
    }

    // Show image in modal
    function showImageModal(imageUrl, title) {
        $('#modalImage').attr('src', imageUrl);
        $('#downloadImageBtn').attr('href', imageUrl);
        $('#imageModalLabel').text(title + ' - Image');
        $('#imageModal').modal('show');
    }

    // Resend notification
    let currentNotificationId = null;

    function resendNotification(notificationId, title, userName) {
        currentNotificationId = notificationId;
        $('#resendNotificationTitle').text(title);
        $('#resendOriginalUser').text(userName);
        $('#resendOriginalUserName').text(userName);

        // Reset to original user selection
        $('#resendToOriginal').prop('checked', true);
        $('#resendAllWarning').hide();

        $('#resendModal').modal('show');
    }

    // Show/hide warning when recipient selection changes
    $(document).on('change', 'input[name="resendRecipient"]', function() {
        if ($(this).val() === 'all') {
            $('#resendAllWarning').show();
        } else {
            $('#resendAllWarning').hide();
        }
    });

    // Confirm resend
    $('#confirmResendBtn').click(function() {
        if (currentNotificationId) {
            const sendFcm = $('#resendFcm').is(':checked');
            const sendSms = $('#resendSms').is(':checked');
            const recipient = $('input[name="resendRecipient"]:checked').val();

            // Validation
            if (!sendFcm && !sendSms) {
                alert('Please select at least one delivery method (FCM or SMS)');
                return;
            }

            // Show loading state
            $(this).prop('disabled', true).text('Sending...');

            $.ajax({
                url: 'api/resend_notification.php',
                method: 'POST',
                data: {
                    notification_id: currentNotificationId,
                    send_fcm: sendFcm,
                    send_sms: sendSms,
                    recipient: recipient
                },
                success: function(response) {
                    console.log('Resend response:', response); // Debug log
                    if (response.success) {
                        let message = 'Notification resent successfully!';
                        if (recipient === 'all') {
                            message = 'Notification sent to all users successfully!';
                        }
                        alert(message);
                        location.reload(); // Refresh to show updated status
                    } else {
                        // Debug: Log the full response to understand the error structure
                        console.error('Resend failed:', response);

                        // Check if there are specific error details
                        let errorMessage = 'Failed to resend notification';
                        if (response.error) {
                            errorMessage = response.error;
                        } else if (response.errors && typeof response.errors === 'object') {
                            // If there are multiple errors, combine them
                            const errorMessages = Object.values(response.errors);
                            errorMessage = errorMessages.join(', ');
                        }

                        alert(errorMessage);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error occurred while resending notification');
                },
                complete: function() {
                    $('#confirmResendBtn').prop('disabled', false).text('Resend');
                    $('#resendModal').modal('hide');
                }
            });
        }
    });



    // View notification details
    function viewNotificationDetails(notificationId) {
        $('#notificationDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
        $('#detailsModal').modal('show');

        $.ajax({
            url: 'api/notification_details.php',
            method: 'GET',
            data: { id: notificationId },
            dataType: 'json',
            success: function(response) {
                console.log('Response received:', response);
                if (response && response.success) {
                    $('#notificationDetailsContent').html(response.html);
                } else {
                    const errorMsg = response && response.error ? response.error : 'Failed to load notification details';
                    $('#notificationDetailsContent').html('<div class="alert alert-danger">' + errorMsg + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                $('#notificationDetailsContent').html('<div class="alert alert-danger">Error occurred while loading details: ' + error + '</div>');
            }
        });
    }
</script>

<?php
/**
 * Get notification type badge class
 *
 * @param string $type Notification type
 * @return string Badge class
 */
function getNotificationTypeBadgeClass($type) {
    switch ($type) {
        case 'order_status': return 'primary';
        case 'promo': return 'success';
        case 'system': return 'info';
        case 'custom': return 'warning';
        default: return 'secondary';
    }
}

/**
 * Format notification type
 *
 * @param string $type Notification type
 * @return string Formatted type
 */
function formatNotificationType($type) {
    switch ($type) {
        case 'order_status': return 'Order Status';
        case 'promo': return 'Promotion';
        case 'system': return 'System';
        case 'custom': return 'Custom';
        default: return ucfirst($type);
    }
}

/**
 * Log admin activity
 *
 * @param PDO $pdo PDO database connection
 * @param int $adminId Admin ID
 * @param string $action Action
 * @param string $details Details
 * @return bool True on success, false on failure
 */
function logAdminActivity($pdo, $adminId, $action, $details) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, action, details, ip_address)
            VALUES (?, ?, ?, ?)
        ");

        return $stmt->execute([
            $adminId,
            $action,
            $details,
            $_SERVER['REMOTE_ADDR']
        ]);
    } catch (PDOException $e) {
        error_log('Failed to log admin activity: ' . $e->getMessage());
        return false;
    }
}
?>
