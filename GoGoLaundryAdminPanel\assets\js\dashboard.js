/**
 * Dashboard Page JavaScript
 *
 * This file handles the dashboard page functionality
 */

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Confirm logout
    $('a[href="logout.php"]').on('click', function(e) {
        if (!confirm('Are you sure you want to logout?')) {
            e.preventDefault();
        }
    });

    // Refresh dashboard data
    $('#refreshDashboard').on('click', function() {
        const button = $(this);

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin"></i> Refreshing...');
        button.prop('disabled', true);

        // Simulate refresh with a timeout (replace with actual AJAX call)
        setTimeout(function() {
            // Reset button state
            button.html('<i class="fas fa-sync-alt"></i> Refresh Data');
            button.prop('disabled', false);

            // Show success message
            showToast('Dashboard data refreshed successfully!', 'success');

            // Refresh charts
            if (typeof fetchChartData === 'function') {
                fetchChartData();
            }
        }, 1500);
    });

    // Refresh charts
    $('#refreshCharts').on('click', function() {
        const button = $(this);

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin"></i>');
        button.prop('disabled', true);

        // Refresh charts
        if (typeof fetchChartData === 'function') {
            fetchChartData();

            // Reset button state after a delay
            setTimeout(function() {
                button.html('<i class="fas fa-sync-alt"></i>');
                button.prop('disabled', false);

                // Show success message
                showToast('Charts refreshed successfully!', 'success');
            }, 1500);
        } else {
            // Reset button state
            button.html('<i class="fas fa-sync-alt"></i>');
            button.prop('disabled', false);

            // Show error message
            showToast('Error refreshing charts. Please try again.', 'error');
        }
    });

    // Refresh system status
    $('.refresh-status-btn').on('click', function() {
        const button = $(this);

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin"></i> Refreshing...');
        button.prop('disabled', true);

        // Simulate refresh with a timeout (replace with actual AJAX call)
        setTimeout(function() {
            // Reset button state
            button.html('<i class="fas fa-sync-alt"></i> Refresh');
            button.prop('disabled', false);

            // Show success message
            showToast('System status refreshed successfully!', 'success');
        }, 1500);
    });

    // Handle period buttons for revenue chart
    $('.btn-group .btn[data-period]').on('click', function() {
        const button = $(this);
        const period = button.data('period');

        // Remove active class from all buttons
        $('.btn-group .btn[data-period]').removeClass('active');

        // Add active class to clicked button
        button.addClass('active');

        // Update chart based on selected period
        if (typeof revenueChart !== 'undefined') {
            if (period === 'week') {
                revenueChart.data.labels = getLastNDays(7);
                revenueChart.data.datasets[0].data = getRandomData(7, 1000, 5000);
            } else if (period === 'month') {
                revenueChart.data.labels = getLastNDays(30);
                revenueChart.data.datasets[0].data = getRandomData(30, 1000, 5000);
            } else if (period === 'year') {
                revenueChart.data.labels = getMonths();
                revenueChart.data.datasets[0].data = getRandomData(12, 10000, 50000);
            }

            // Update chart
            revenueChart.update();
        }
    });

    // Function to show toast notifications
    function showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        if ($('#toastContainer').length === 0) {
            $('body').append('<div id="toastContainer" class="position-fixed bottom-0 end-0 p-3" style="z-index: 11"></div>');
        }

        // Set toast color based on type
        let bgClass = 'bg-info';
        let icon = 'info-circle';

        switch (type) {
            case 'success':
                bgClass = 'bg-success';
                icon = 'check-circle';
                break;
            case 'error':
                bgClass = 'bg-danger';
                icon = 'exclamation-circle';
                break;
            case 'warning':
                bgClass = 'bg-warning';
                icon = 'exclamation-triangle';
                break;
        }

        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header ${bgClass} text-white">
                    <i class="fas fa-${icon} me-2"></i>
                    <strong class="me-auto">Notification</strong>
                    <small>Just now</small>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        // Add toast to container
        $('#toastContainer').append(toastHtml);

        // Initialize and show toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });

        toast.show();

        // Remove toast after it's hidden
        $(toastElement).on('hidden.bs.toast', function() {
            $(this).remove();
        });
    }
});
