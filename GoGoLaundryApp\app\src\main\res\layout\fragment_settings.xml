<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:fillViewport="true"
    tools:context=".ui.fragment.SettingsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/margin_medium">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/margin_medium"
            android:text="@string/settings"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
            android:textColor="@color/text_primary" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/language_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/margin_medium"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="@dimen/card_corner_radius"
            app:cardElevation="@dimen/elevation_card">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/margin_medium">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/margin_small"
                    android:text="@string/language"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/margin_medium"
                    android:text="@string/language_description"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/text_secondary" />

                <RadioGroup
                    android:id="@+id/language_radio_group"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/margin_medium">

                    <RadioButton
                        android:id="@+id/radio_english"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin_small"
                        android:text="English"
                        android:textColor="@color/text_primary" />

                    <RadioButton
                        android:id="@+id/radio_bangla"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/margin_small"
                        android:text="বাংলা"
                        android:textColor="@color/text_primary" />

                    <RadioButton
                        android:id="@+id/radio_arabic"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="العربية"
                        android:textColor="@color/text_primary" />
                </RadioGroup>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/apply_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:text="@string/apply" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>


    </LinearLayout>
</ScrollView>
