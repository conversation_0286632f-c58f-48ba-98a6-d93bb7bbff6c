package com.mdsadrulhasan.gogolaundry.fcm;

import com.google.gson.annotations.SerializedName;

/**
 * FCM Token Registration Request Model
 */
public class FCMTokenRequest {
    @SerializedName("user_id")
    private int userId;
    
    @SerializedName("token")
    private String token;
    
    @SerializedName("device_id")
    private String deviceId;
    
    @SerializedName("device_type")
    private String deviceType;

    public FCMTokenRequest(int userId, String token, String deviceId, String deviceType) {
        this.userId = userId;
        this.token = token;
        this.deviceId = deviceId;
        this.deviceType = deviceType;
    }

    // Getters and setters
    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }
}
