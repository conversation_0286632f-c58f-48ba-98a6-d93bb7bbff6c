package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.OpenableColumns;
import android.util.Log;

import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Utility class for image-related operations
 */
public class ImageUtils {
    private static final String TAG = "ImageUtils";
    private static Uri cameraImageUri;

    /**
     * Take a picture using the device camera
     *
     * @param fragment Fragment requesting the image capture
     * @param requestCode Request code for onActivityResult
     */
    public static void takePicture(Fragment fragment, int requestCode) {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(fragment.requireActivity().getPackageManager()) != null) {
            // Create the file where the photo should go
            File photoFile = null;
            try {
                photoFile = createImageFile(fragment.requireContext());
            } catch (IOException ex) {
                Log.e(TAG, "Error creating image file", ex);
            }

            // Continue only if the file was successfully created
            if (photoFile != null) {
                Uri photoURI = FileProvider.getUriForFile(
                        fragment.requireContext(),
                        fragment.requireContext().getPackageName() + ".fileprovider",
                        photoFile);
                cameraImageUri = photoURI;
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                fragment.startActivityForResult(takePictureIntent, requestCode);
            }
        }
    }

    /**
     * Pick an image from the gallery
     *
     * @param fragment Fragment requesting the image pick
     * @param requestCode Request code for onActivityResult
     */
    public static void pickImage(Fragment fragment, int requestCode) {
        Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        intent.setType("image/*");
        fragment.startActivityForResult(intent, requestCode);
    }

    /**
     * Create a temporary image file
     *
     * @param context Context
     * @return File object for the new image
     * @throws IOException If file creation fails
     */
    private static File createImageFile(Context context) throws IOException {
        // Create an image file name
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        return File.createTempFile(
                imageFileName,  /* prefix */
                ".jpg",         /* suffix */
                storageDir      /* directory */
        );
    }

    /**
     * Get the URI of the last camera image
     *
     * @return URI of the camera image
     */
    public static Uri getCameraImageUri() {
        return cameraImageUri;
    }

    /**
     * Get the real path from a URI
     *
     * @param context Context
     * @param uri URI to resolve
     * @return Real file path
     */
    public static String getRealPathFromURI(Context context, Uri uri) {
        String filePath = "";
        if (uri == null) return filePath;

        if (uri.getScheme() != null && uri.getScheme().equals("content")) {
            try {
                // For content URIs, copy the file to a temporary location
                String fileName = getFileNameFromUri(context, uri);
                File tempFile = new File(context.getCacheDir(), fileName);
                copyUriToFile(context, uri, tempFile);
                filePath = tempFile.getAbsolutePath();
            } catch (Exception e) {
                Log.e(TAG, "Error getting real path from URI", e);
            }
        } else if (uri.getScheme() != null && uri.getScheme().equals("file")) {
            filePath = uri.getPath();
        }

        return filePath;
    }

    /**
     * Get file name from URI
     *
     * @param context Context
     * @param uri URI to get file name from
     * @return File name
     */
    private static String getFileNameFromUri(Context context, Uri uri) {
        String result = null;
        if (uri.getScheme().equals("content")) {
            try (Cursor cursor = context.getContentResolver().query(uri, null, null, null, null)) {
                if (cursor != null && cursor.moveToFirst()) {
                    int nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME);
                    if (nameIndex >= 0) {
                        result = cursor.getString(nameIndex);
                    }
                }
            }
        }
        if (result == null) {
            result = uri.getPath();
            int cut = result.lastIndexOf('/');
            if (cut != -1) {
                result = result.substring(cut + 1);
            }
        }
        return result;
    }

    /**
     * Copy URI to file
     *
     * @param context Context
     * @param uri Source URI
     * @param destFile Destination file
     * @throws IOException If copy fails
     */
    private static void copyUriToFile(Context context, Uri uri, File destFile) throws IOException {
        try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
             OutputStream outputStream = new FileOutputStream(destFile)) {
            if (inputStream == null) {
                throw new IOException("Failed to open input stream");
            }
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }
}
