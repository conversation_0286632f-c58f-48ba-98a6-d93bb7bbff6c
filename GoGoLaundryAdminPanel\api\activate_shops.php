<?php
/**
 * Activate all shops for testing
 */

require_once '../includes/functions.php';

try {
    // Check current shop status
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM laundry_shops");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total shops in database: " . $result['total'] . "<br>";

    $stmt = $pdo->query("SELECT COUNT(*) as active FROM laundry_shops WHERE is_active = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Active shops: " . $result['active'] . "<br>";

    $stmt = $pdo->query("SELECT COUNT(*) as verified FROM laundry_shops WHERE is_verified = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Verified shops: " . $result['verified'] . "<br>";

    $stmt = $pdo->query("SELECT COUNT(*) as active_verified FROM laundry_shops WHERE is_active = 1 AND is_verified = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Active & Verified shops: " . $result['active_verified'] . "<br><br>";

    // If no active & verified shops, activate and verify all shops
    if ($result['active_verified'] == 0) {
        echo "No active & verified shops found. Activating and verifying all shops...<br>";
        $updateStmt = $pdo->prepare("UPDATE laundry_shops SET is_active = 1, is_verified = 1");
        $updateStmt->execute();
        echo "✅ All shops have been activated and verified!<br><br>";

        // Check again
        $stmt = $pdo->query("SELECT COUNT(*) as active_verified FROM laundry_shops WHERE is_active = 1 AND is_verified = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Active & Verified shops now: " . $result['active_verified'] . "<br>";
    }

    // Show all shops
    $stmt = $pdo->query("SELECT id, name, is_active, is_verified, created_at FROM laundry_shops ORDER BY created_at DESC");
    $shops = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>All Shops:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Active</th><th>Verified</th><th>Created</th></tr>";
    foreach ($shops as $shop) {
        echo "<tr>";
        echo "<td>" . $shop['id'] . "</td>";
        echo "<td>" . htmlspecialchars($shop['name']) . "</td>";
        echo "<td>" . ($shop['is_active'] ? '✅ Yes' : '❌ No') . "</td>";
        echo "<td>" . ($shop['is_verified'] ? '✅ Yes' : '❌ No') . "</td>";
        echo "<td>" . $shop['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<br><h3>Test API Endpoints:</h3>";
    echo "<p><a href='shops/list.php?active_only=1&verified_only=1&limit=10' target='_blank'>📋 List All Shops</a></p>";
    echo "<p><a href='shops/search.php?query=laundry&active_only=1&verified_only=1&limit=5' target='_blank'>🔍 Search Shops</a></p>";
    echo "<p><a href='shops/nearby.php?latitude=23.8103&longitude=90.4125&radius=10&limit=5' target='_blank'>📍 Nearby Shops</a></p>";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
