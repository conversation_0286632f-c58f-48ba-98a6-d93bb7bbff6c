package com.mdsadrulhasan.gogolaundry.ui.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.mdsadrulhasan.gogolaundry.ui.fragment.ShopItemsFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.ShopServicesFragment;

/**
 * ViewPager2 adapter for shop details tabs (Services and Items)
 */
public class ShopDetailsPagerAdapter extends FragmentStateAdapter {

    private static final int TAB_COUNT = 2;
    private static final int TAB_SERVICES = 0;
    private static final int TAB_ITEMS = 1;

    private final int shopId;

    public ShopDetailsPagerAdapter(@NonNull Fragment fragment, int shopId) {
        // Use the Fragment constructor instead of FragmentManager/Lifecycle
        super(fragment);
        this.shopId = shopId;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        android.util.Log.e("ShopDetailsPagerAdapter", "*** createFragment() called for position: " + position + " with shopId: " + shopId + " ***");

        Fragment fragment;
        switch (position) {
            case TAB_SERVICES:
                android.util.Log.e("ShopDetailsPagerAdapter", "*** Creating ShopServicesFragment for shopId: " + shopId + " ***");
                fragment = ShopServicesFragment.newInstance(shopId);
                android.util.Log.e("ShopDetailsPagerAdapter", "*** ShopServicesFragment created: " + (fragment != null ? "SUCCESS" : "NULL") + " ***");
                return fragment;
            case TAB_ITEMS:
                android.util.Log.e("ShopDetailsPagerAdapter", "*** Creating ShopItemsFragment for shopId: " + shopId + " ***");
                fragment = ShopItemsFragment.newInstance(shopId);
                android.util.Log.e("ShopDetailsPagerAdapter", "*** ShopItemsFragment created: " + (fragment != null ? "SUCCESS" : "NULL") + " ***");
                return fragment;
            default:
                throw new IllegalArgumentException("Invalid tab position: " + position);
        }
    }

    @Override
    public int getItemCount() {
        return TAB_COUNT;
    }
}
