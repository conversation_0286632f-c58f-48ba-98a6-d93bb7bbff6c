package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.OrderTrackingResponse;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.model.OrderStatusHistory;
import com.mdsadrulhasan.gogolaundry.utils.OrderTrackingManager;
import com.mdsadrulhasan.gogolaundry.utils.Resource;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * ViewModel for order tracking
 */
public class OrderTrackingViewModel extends AndroidViewModel {
    private static final String TAG = "OrderTrackingViewModel";
    
    private final MediatorLiveData<Resource<Order>> orderData = new MediatorLiveData<>();
    private final MutableLiveData<List<OrderStatusHistory>> statusHistory = new MutableLiveData<>();
    private final OrderTrackingManager orderTrackingManager;
    private final ApiService apiService;
    
    public OrderTrackingViewModel(@NonNull Application application) {
        super(application);
        orderTrackingManager = OrderTrackingManager.getInstance();
        apiService = ApiClient.getApiService(application);
        
        // Initialize with empty list
        statusHistory.setValue(new ArrayList<>());
    }
    
    /**
     * Get order data
     *
     * @return LiveData of order resource
     */
    public LiveData<Resource<Order>> getOrderData() {
        return orderData;
    }
    
    /**
     * Get status history
     *
     * @return LiveData of status history
     */
    public List<OrderStatusHistory> getStatusHistory() {
        return statusHistory.getValue();
    }
    
    /**
     * Track order by tracking number
     *
     * @param trackingNumber Order tracking number
     */
    public void trackOrderByTrackingNumber(String trackingNumber) {
        // Set loading state
        orderData.setValue(Resource.loading(null));
        
        // Make API call to track order
        Call<ApiResponse<OrderTrackingResponse>> call = apiService.trackOrder(trackingNumber);
        call.enqueue(new Callback<ApiResponse<OrderTrackingResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<OrderTrackingResponse>> call, Response<ApiResponse<OrderTrackingResponse>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    OrderTrackingResponse trackingResponse = response.body().getData();
                    
                    if (trackingResponse != null && trackingResponse.getOrder() != null) {
                        Order order = trackingResponse.getOrder();
                        
                        // Log the order details for debugging
                        Log.d(TAG, "Order tracking successful: " + order.getOrderNumber() + ", Status: " + order.getStatus());
                        
                        // Update status history
                        if (trackingResponse.getStatusHistory() != null) {
                            statusHistory.setValue(trackingResponse.getStatusHistory());
                        }
                        
                        // Update order items if available
                        if (trackingResponse.getItems() != null) {
                            order.setOrderItems(trackingResponse.getItems());
                        }
                        
                        // Update order data
                        orderData.setValue(Resource.success(order));
                    } else {
                        // No order found in response
                        Log.e(TAG, "Order tracking response is missing order data");
                        orderData.setValue(Resource.error("Order tracking response is missing order data", null));
                    }
                } else {
                    // API error
                    String errorMessage = "Failed to track order";
                    int statusCode = response.code();
                    
                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    }
                    
                    // Special handling for 404 Not Found
                    if (statusCode == 404 || (errorMessage != null && errorMessage.contains("not found"))) {
                        errorMessage = "Order with tracking number " + trackingNumber + " not found";
                        
                        // Try to find the order in local database
                        fallbackToLocalData(trackingNumber);
                    } else {
                        orderData.setValue(Resource.error(errorMessage, null));
                    }
                }
            }
            
            @Override
            public void onFailure(Call<ApiResponse<OrderTrackingResponse>> call, Throwable t) {
                // Network error
                String errorMessage = "Network error: " + t.getMessage();
                
                // Simplify error message for common network issues
                if (t.getMessage() != null && t.getMessage().contains("Unable to resolve host")) {
                    errorMessage = "No internet connection. Please check your network settings.";
                }
                
                // Log the error for debugging
                Log.e(TAG, "API Error: " + errorMessage, t);
                
                // Fallback to local data
                fallbackToLocalData(trackingNumber);
            }
        });
    }
    
    /**
     * Track order by order number
     *
     * @param orderNumber Order number
     */
    public void trackOrderByOrderNumber(String orderNumber) {
        // For now, just use the same method as tracking by tracking number
        trackOrderByTrackingNumber(orderNumber);
    }
    
    /**
     * Fallback to local data when API call fails
     *
     * @param trackingNumber Order tracking number
     */
    private void fallbackToLocalData(String trackingNumber) {
        // Add source to mediator
        LiveData<Resource<Order>> localDataSource = orderTrackingManager.trackOrderByTrackingNumber(trackingNumber);
        orderData.addSource(localDataSource, resource -> {
            if (resource != null) {
                orderData.setValue(resource);
                
                // Remove source after getting the result
                orderData.removeSource(localDataSource);
            }
        });
    }
}
