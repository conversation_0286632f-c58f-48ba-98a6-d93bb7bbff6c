﻿<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Cart Header Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/header_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/primary"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="24dp">

                <!-- Header Title with Icon -->
                <LinearLayout
                    android:id="@+id/title_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_cart"
                        app:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Shopping Cart"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                            android:textColor="@color/white"
                            android:textStyle="bold"
                            android:letterSpacing="0.02" />

                        <TextView
                            android:id="@+id/cart_item_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:alpha="0.9"
                            android:text="0 items in cart"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/white" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Cart Content Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="2dp"
            app:strokeColor="@color/card_stroke_light"
            app:strokeWidth="0.5dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <!-- Section Title -->
                <LinearLayout
                    android:id="@+id/cart_content_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_cart"
                        app:tint="@color/primary" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Cart Items"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Cart Items Container -->
                <FrameLayout
                    android:id="@+id/cart_items_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:minHeight="200dp"
                    app:layout_constraintTop_toBottomOf="@id/cart_content_header">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/cart_recycler_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipToPadding="false"
                        android:nestedScrollingEnabled="false"
                        android:overScrollMode="never"
                        android:padding="8dp"
                        android:scrollbars="vertical"
                        android:fadeScrollbars="true"
                        android:scrollbarStyle="outsideOverlay"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_cart" />

                    <!-- Empty State -->
                    <LinearLayout
                        android:id="@+id/empty_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:layout_marginBottom="16dp"
                            android:alpha="0.7"
                            android:src="@drawable/ic_cart"
                            app:tint="@color/primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="Your cart is empty"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                            android:textColor="@color/text_primary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Add some items to get started"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/text_secondary"
                            android:alpha="0.8" />

                    </LinearLayout>

                </FrameLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Cart Summary Section -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="2dp"
            app:strokeColor="@color/card_stroke_light"
            app:strokeWidth="0.5dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <!-- Section Title -->
                <LinearLayout
                    android:id="@+id/summary_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_receipt"
                        app:tint="@color/primary" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Order Summary"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/text_primary"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Total Display -->
                <LinearLayout
                    android:id="@+id/total_display_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/home_modern_card"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    app:layout_constraintTop_toBottomOf="@id/summary_header">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total Amount"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                            android:textColor="@color/text_secondary"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/cart_total"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="৳0.00"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                            android:textColor="@color/primary"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <com.google.android.material.chip.Chip
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="TOTAL"
                        android:textColor="@color/primary"
                        android:textStyle="bold"
                        app:chipBackgroundColor="@color/primary_light"
                        app:chipStrokeColor="@color/primary"
                        app:chipStrokeWidth="1dp" />

                </LinearLayout>

                <!-- Action Buttons -->
                <LinearLayout
                    android:id="@+id/action_buttons_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toBottomOf="@id/total_display_container">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/checkout_button"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="Checkout"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        app:backgroundTint="@color/primary"
                        app:cornerRadius="16dp"
                        app:icon="@drawable/ic_arrow_forward"
                        app:iconTint="@color/white"
                        app:iconGravity="textEnd"
                        app:iconSize="18dp"
                        style="@style/Widget.MaterialComponents.Button" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/clear_cart_button"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="0.7"
                        android:layout_marginStart="8dp"
                        android:text="Clear"
                        android:textColor="@color/error"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        app:strokeColor="@color/error"
                        app:strokeWidth="2dp"
                        app:cornerRadius="16dp"
                        app:icon="@drawable/ic_delete"
                        app:iconTint="@color/error"
                        app:iconGravity="textStart"
                        app:iconSize="18dp" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
