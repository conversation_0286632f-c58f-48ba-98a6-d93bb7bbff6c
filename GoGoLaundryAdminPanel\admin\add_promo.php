<?php
/**
 * Add New Promo Code
 *
 * This page allows administrators to add new promo codes
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/PromoCodeManager.php';

// Initialize promo code manager
$promoCodeManager = new PromoCodeManager($pdo);

// Initialize variables
$code = '';
$discountType = 'percentage';
$discountValue = '';
$minOrderValue = '0';
$maxDiscount = '';
$startDate = date('Y-m-d H:i:s');
$endDate = date('Y-m-d H:i:s', strtotime('+30 days'));
$isActive = true;
$usageLimit = '';
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error_message'] = 'Invalid security token. Please try again.';
        header('Location: add_promo.php');
        exit;
    }

    // Get form data
    $code = trim($_POST['code']);
    $discountType = $_POST['discount_type'];
    $discountValue = trim($_POST['discount_value']);
    $minOrderValue = trim($_POST['min_order_value']);
    $maxDiscount = trim($_POST['max_discount']);
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];
    $isActive = isset($_POST['is_active']);
    $usageLimit = trim($_POST['usage_limit']);

    // Validate code
    if (empty($code)) {
        $errors['code'] = 'Promo code is required.';
    } elseif (strlen($code) > 20) {
        $errors['code'] = 'Promo code cannot exceed 20 characters.';
    } elseif (!preg_match('/^[A-Za-z0-9]+$/', $code)) {
        $errors['code'] = 'Promo code can only contain letters and numbers.';
    } elseif ($promoCodeManager->codeExists($code)) {
        $errors['code'] = 'This promo code already exists.';
    }

    // Validate discount type
    if (!in_array($discountType, ['percentage', 'fixed'])) {
        $errors['discount_type'] = 'Invalid discount type.';
    }

    // Validate discount value
    if (empty($discountValue)) {
        $errors['discount_value'] = 'Discount value is required.';
    } elseif (!is_numeric($discountValue) || $discountValue <= 0) {
        $errors['discount_value'] = 'Discount value must be a positive number.';
    } elseif ($discountType === 'percentage' && $discountValue > 100) {
        $errors['discount_value'] = 'Percentage discount cannot exceed 100%.';
    }

    // Validate minimum order value
    if (empty($minOrderValue)) {
        $minOrderValue = 0;
    } elseif (!is_numeric($minOrderValue) || $minOrderValue < 0) {
        $errors['min_order_value'] = 'Minimum order value must be a non-negative number.';
    }

    // Validate maximum discount (for percentage discounts)
    if ($discountType === 'percentage' && !empty($maxDiscount)) {
        if (!is_numeric($maxDiscount) || $maxDiscount <= 0) {
            $errors['max_discount'] = 'Maximum discount must be a positive number.';
        }
    } else {
        $maxDiscount = null;
    }

    // Validate dates
    if (empty($startDate)) {
        $errors['start_date'] = 'Start date is required.';
    }
    if (empty($endDate)) {
        $errors['end_date'] = 'End date is required.';
    }
    if (!empty($startDate) && !empty($endDate) && strtotime($startDate) >= strtotime($endDate)) {
        $errors['end_date'] = 'End date must be after start date.';
    }

    // Validate usage limit
    if (!empty($usageLimit)) {
        if (!is_numeric($usageLimit) || $usageLimit <= 0 || floor($usageLimit) != $usageLimit) {
            $errors['usage_limit'] = 'Usage limit must be a positive integer.';
        }
    } else {
        $usageLimit = null;
    }

    // If no errors, add promo code
    if (empty($errors)) {
        $result = $promoCodeManager->addPromoCode(
            $code,
            $discountType,
            $discountValue,
            $minOrderValue,
            $maxDiscount,
            $startDate,
            $endDate,
            $isActive,
            $usageLimit
        );

        if ($result) {
            $_SESSION['success_message'] = 'Promo code added successfully.';
            header('Location: promo_codes.php');
            exit;
        } else {
            $_SESSION['error_message'] = 'Failed to add promo code. Please try again.';
        }
    }
}

// Set page title
$pageTitle = 'Add New Promo Code';
?>

<?php include 'includes/header.php'; ?>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="promo_codes.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Promo Codes
                        </a>
                    </div>
                </div>

                <?php include 'includes/alerts.php'; ?>

                <!-- Add Promo Code Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Promo Code Details</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="add_promo.php">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="code" class="form-label">Promo Code*</label>
                                    <input type="text" class="form-control <?php echo isset($errors['code']) ? 'is-invalid' : ''; ?>" 
                                           id="code" name="code" value="<?php echo htmlspecialchars($code); ?>" 
                                           placeholder="e.g., SUMMER2023" required>
                                    <?php if (isset($errors['code'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['code']; ?></div>
                                    <?php endif; ?>
                                    <small class="text-muted">Letters and numbers only, no spaces or special characters.</small>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="discount_type" class="form-label">Discount Type*</label>
                                    <select class="form-select <?php echo isset($errors['discount_type']) ? 'is-invalid' : ''; ?>" 
                                            id="discount_type" name="discount_type" required>
                                        <option value="percentage" <?php echo $discountType === 'percentage' ? 'selected' : ''; ?>>Percentage (%)</option>
                                        <option value="fixed" <?php echo $discountType === 'fixed' ? 'selected' : ''; ?>>Fixed Amount</option>
                                    </select>
                                    <?php if (isset($errors['discount_type'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['discount_type']; ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="discount_value" class="form-label">Discount Value*</label>
                                    <input type="number" step="0.01" class="form-control <?php echo isset($errors['discount_value']) ? 'is-invalid' : ''; ?>" 
                                           id="discount_value" name="discount_value" value="<?php echo htmlspecialchars($discountValue); ?>" 
                                           placeholder="e.g., 10" required>
                                    <?php if (isset($errors['discount_value'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['discount_value']; ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="min_order_value" class="form-label">Minimum Order Value</label>
                                    <input type="number" step="0.01" class="form-control <?php echo isset($errors['min_order_value']) ? 'is-invalid' : ''; ?>" 
                                           id="min_order_value" name="min_order_value" value="<?php echo htmlspecialchars($minOrderValue); ?>" 
                                           placeholder="e.g., 50">
                                    <?php if (isset($errors['min_order_value'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['min_order_value']; ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-4" id="max_discount_container">
                                    <label for="max_discount" class="form-label">Maximum Discount (for %)</label>
                                    <input type="number" step="0.01" class="form-control <?php echo isset($errors['max_discount']) ? 'is-invalid' : ''; ?>" 
                                           id="max_discount" name="max_discount" value="<?php echo htmlspecialchars($maxDiscount); ?>" 
                                           placeholder="e.g., 100">
                                    <?php if (isset($errors['max_discount'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['max_discount']; ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="start_date" class="form-label">Start Date*</label>
                                    <input type="datetime-local" class="form-control <?php echo isset($errors['start_date']) ? 'is-invalid' : ''; ?>" 
                                           id="start_date" name="start_date" value="<?php echo date('Y-m-d\TH:i', strtotime($startDate)); ?>" required>
                                    <?php if (isset($errors['start_date'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['start_date']; ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="end_date" class="form-label">End Date*</label>
                                    <input type="datetime-local" class="form-control <?php echo isset($errors['end_date']) ? 'is-invalid' : ''; ?>" 
                                           id="end_date" name="end_date" value="<?php echo date('Y-m-d\TH:i', strtotime($endDate)); ?>" required>
                                    <?php if (isset($errors['end_date'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['end_date']; ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="usage_limit" class="form-label">Usage Limit</label>
                                    <input type="number" class="form-control <?php echo isset($errors['usage_limit']) ? 'is-invalid' : ''; ?>" 
                                           id="usage_limit" name="usage_limit" value="<?php echo htmlspecialchars($usageLimit); ?>" 
                                           placeholder="Leave empty for unlimited">
                                    <?php if (isset($errors['usage_limit'])): ?>
                                        <div class="invalid-feedback"><?php echo $errors['usage_limit']; ?></div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $isActive ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_active">
                                            Active
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Promo Code
                                </button>
                                <a href="promo_codes.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>

<script>
    // Show/hide max discount field based on discount type
    document.addEventListener('DOMContentLoaded', function() {
        const discountTypeSelect = document.getElementById('discount_type');
        const maxDiscountContainer = document.getElementById('max_discount_container');
        
        function toggleMaxDiscount() {
            if (discountTypeSelect.value === 'percentage') {
                maxDiscountContainer.style.display = 'block';
            } else {
                maxDiscountContainer.style.display = 'none';
                document.getElementById('max_discount').value = '';
            }
        }
        
        // Initial state
        toggleMaxDiscount();
        
        // On change
        discountTypeSelect.addEventListener('change', toggleMaxDiscount);
    });
</script>
