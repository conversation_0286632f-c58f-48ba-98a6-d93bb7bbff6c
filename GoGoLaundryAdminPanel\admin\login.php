<?php
/**
 * Admin Login Page
 *
 * This page allows administrators to log in to the admin dashboard
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/AdminManager.php';

// Check if admin is already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    // Redirect to dashboard
    header('Location: index.php');
    exit;
}

// Initialize admin manager
$adminManager = new AdminManager($pdo);

// Create CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $csrfToken = $adminManager->createCsrfToken();
} else {
    $csrfToken = $_SESSION['csrf_token'];
}

// Log request information for debugging
error_log('Login page accessed from: ' . $_SERVER['REMOTE_ADDR'] . ' - User Agent: ' . $_SERVER['HTTP_USER_AGENT']);
error_log('Session ID: ' . session_id() . ' - Session status: ' . (isset($_SESSION) ? 'Active' : 'Not active'));

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Log login attempt
    error_log('Login attempt from: ' . $_SERVER['REMOTE_ADDR'] . ' - User Agent: ' . $_SERVER['HTTP_USER_AGENT']);

    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !$adminManager->verifyCsrfToken($_POST['csrf_token'])) {
        $error = 'Invalid request. Please try again.';
        error_log('CSRF token verification failed. Provided: ' . (isset($_POST['csrf_token']) ? $_POST['csrf_token'] : 'none') .
                  ', Expected: ' . $_SESSION['csrf_token']);
    } else {
        // Get input data
        $username = isset($_POST['username']) ? trim($_POST['username']) : '';
        $password = isset($_POST['password']) ? $_POST['password'] : '';

        // Log username (but not password for security)
        error_log('Login attempt with username: ' . $username);

        // Validate input
        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
            error_log('Login validation failed: Empty username or password');
        } else {
            // Attempt to login
            $result = $adminManager->login($username, $password);

            if ($result === 'locked') {
                $error = 'Your account has been locked due to too many failed login attempts. Please try again later.';
                error_log('Login failed: Account locked for username: ' . $username);
            } elseif ($result) {
                // Log successful login
                error_log('Login successful for username: ' . $username . ' with role: ' . $result['role']);

                // Set session
                $_SESSION['admin_id'] = $result['id'];
                $_SESSION['admin_username'] = $result['username'];
                $_SESSION['admin_role'] = $result['role'];
                $_SESSION['admin_logged_in'] = true;

                // Log session data
                error_log('Session data set. Session ID: ' . session_id());

                // Redirect to dashboard or requested page
                $redirect = isset($_SESSION['admin_redirect']) ? $_SESSION['admin_redirect'] : 'index.php';
                unset($_SESSION['admin_redirect']);

                // Log redirect
                error_log('Redirecting to: ' . $redirect);

                header('Location: ' . $redirect);
                exit;
            } else {
                $error = 'Invalid username or password.';
                error_log('Login failed: Invalid credentials for username: ' . $username);
            }
        }
    }
}

// Page title
$pageTitle = 'Admin Login';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title><?php echo $pageTitle; ?> - <?php echo APP_NAME; ?></title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/admin.css">
    <style>
        /* Additional mobile-specific styles */
        @media (max-width: 768px) {
            .card {
                border-radius: 10px;
                margin-top: 10vh;
            }
            .input-group-text {
                background-color: #f8f9fa;
            }
            input.form-control {
                height: 45px;
                font-size: 16px; /* Prevents iOS zoom on focus */
            }
            .btn-primary {
                height: 45px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white text-center py-3">
                        <h4 class="mb-0"><?php echo APP_NAME; ?> Admin</h4>
                    </div>
                    <div class="card-body p-4">
                        <h5 class="card-title text-center mb-4">Administrator Login</h5>

                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($_GET['logout']) && $_GET['logout'] === 'success'): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i> You have been successfully logged out.
                            </div>
                        <?php endif; ?>

                        <form method="post" action="login.php" id="loginForm" autocomplete="off">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username"
                                           required autocapitalize="off" autocorrect="off" spellcheck="false">
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="loginButton">
                                    <i class="fas fa-sign-in-alt me-2"></i> Login
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="text-center mt-3 text-muted">
                    <small>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.</small>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Login JS -->
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('#togglePassword').on('click', function() {
                const passwordField = $('#password');
                const passwordFieldType = passwordField.attr('type');
                const toggleIcon = $(this).find('i');

                // Toggle password visibility
                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    toggleIcon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    toggleIcon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });

            // Handle form submission
            $('#loginForm').on('submit', function() {
                // Disable button to prevent double submission
                $('#loginButton').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i> Logging in...');

                // Continue with form submission
                return true;
            });

            // Focus username field on page load
            setTimeout(function() {
                $('#username').focus();
            }, 500);

            // Log session ID for debugging
            console.log('Current session ID: <?php echo session_id(); ?>');
        });
    </script>
</body>
</html>
