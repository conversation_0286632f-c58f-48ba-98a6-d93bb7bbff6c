<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state with enhanced animation -->
    <item android:state_pressed="true">
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:valueTo="0.97"
                android:duration="120"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:valueTo="0.97"
                android:duration="120"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="alpha"
                android:valueTo="0.85"
                android:duration="120"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="translationZ"
                android:valueTo="8dp"
                android:duration="120"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
    <!-- Focused state for accessibility -->
    <item android:state_focused="true">
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:valueTo="1.02"
                android:duration="200"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:valueTo="1.02"
                android:duration="200"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="translationZ"
                android:valueTo="12dp"
                android:duration="200"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
    <!-- Default state with smooth return animation -->
    <item>
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:valueTo="1.0"
                android:duration="250"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:valueTo="1.0"
                android:duration="250"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="alpha"
                android:valueTo="1.0"
                android:duration="250"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="translationZ"
                android:valueTo="6dp"
                android:duration="250"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
</selector>
