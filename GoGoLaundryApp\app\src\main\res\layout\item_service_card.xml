<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/grid_spacing"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="@dimen/service_card_corner_radius"
    app:cardElevation="@dimen/service_card_elevation"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/compact_service_card_padding">

        <!-- Image container -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/compact_service_image_height"
            android:layout_marginBottom="@dimen/margin_small"
            android:background="@drawable/compact_service_image_background">

            <ImageView
                android:id="@+id/service_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"
                android:adjustViewBounds="true"
                android:contentDescription="@string/service_icon"
                tools:src="@drawable/ic_washing_machine" />

        </FrameLayout>

        <!-- Service name -->
        <TextView
            android:id="@+id/service_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:textStyle="normal"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="start"
            android:fontFamily="sans-serif"
            android:lineHeight="18dp"
            tools:text="Shirt" />

        <!-- Service description (optional, can be hidden) -->
        <TextView
            android:id="@+id/service_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:maxLines="2"
            android:ellipsize="end"
            android:gravity="start"
            android:fontFamily="sans-serif"
            android:lineHeight="16dp"
            android:visibility="gone"
            tools:text="Regular cotton shirt for professional cleaning"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.cardview.widget.CardView>