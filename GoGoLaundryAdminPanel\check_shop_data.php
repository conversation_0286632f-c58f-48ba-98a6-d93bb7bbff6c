<?php
/**
 * Check Shop 2 Data
 */

require_once 'config/config.php';
require_once 'config/db.php';

echo "<h2>🔍 Checking Test Shop 2 Data</h2>";

try {
    // Check shop exists
    $shopStmt = $pdo->prepare("SELECT * FROM laundry_shops WHERE id = 2");
    $shopStmt->execute();
    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);

    if ($shop) {
        echo "<h3>✅ Shop Found:</h3>";
        echo "<p><strong>Name:</strong> {$shop['name']}</p>";
        echo "<p><strong>ID:</strong> {$shop['id']}</p>";
        echo "<p><strong>Active:</strong> " . ($shop['is_active'] ? 'Yes' : 'No') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Shop not found!</p>";
        exit;
    }

    // Check services table
    echo "<h3>📋 All Services:</h3>";
    $servicesStmt = $pdo->query("SELECT id, name, is_active FROM services ORDER BY id");
    $services = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($services as $service) {
        $status = $service['is_active'] ? '✅' : '❌';
        echo "<p>$status Service ID {$service['id']}: {$service['name']}</p>";
    }

    // Check items table
    echo "<h3>🛍️ All Items (first 10):</h3>";
    $itemsStmt = $pdo->query("SELECT id, name, service_id, is_active FROM items ORDER BY id LIMIT 10");
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($items as $item) {
        $status = $item['is_active'] ? '✅' : '❌';
        echo "<p>$status Item ID {$item['id']}: {$item['name']} (Service: {$item['service_id']})</p>";
    }

    // Check shop_services
    echo "<h3>🔧 Shop Services for Shop 2:</h3>";
    $shopServicesStmt = $pdo->prepare("
        SELECT ss.*, s.name as service_name
        FROM shop_services ss
        LEFT JOIN services s ON ss.service_id = s.id
        WHERE ss.shop_id = 2
    ");
    $shopServicesStmt->execute();
    $shopServices = $shopServicesStmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($shopServices)) {
        echo "<p style='color: red;'>❌ No services found for Shop 2!</p>";
    } else {
        foreach ($shopServices as $shopService) {
            $status = $shopService['is_available'] ? '✅' : '❌';
            echo "<p>$status Service: {$shopService['service_name']} ({$shopService['estimated_hours']} hours)</p>";
        }
    }

    // Check shop_items
    echo "<h3>🛍️ Shop Items for Shop 2:</h3>";
    $shopItemsStmt = $pdo->prepare("
        SELECT si.*, i.name as item_name
        FROM shop_items si
        LEFT JOIN items i ON si.item_id = i.id
        WHERE si.shop_id = 2
    ");
    $shopItemsStmt->execute();
    $shopItems = $shopItemsStmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($shopItems)) {
        echo "<p style='color: red;'>❌ No items found for Shop 2!</p>";
    } else {
        foreach ($shopItems as $shopItem) {
            $status = $shopItem['is_available'] ? '✅' : '❌';
            $price = $shopItem['custom_price'] ? "৳{$shopItem['custom_price']}" : "Default Price";
            echo "<p>$status Item: {$shopItem['item_name']} ($price, {$shopItem['estimated_hours']} hours)</p>";
        }
    }

    // Check the exact API query
    echo "<h3>🔍 API Query Test:</h3>";

    // Services query
    $apiServicesStmt = $pdo->prepare("
        SELECT
            s.id,
            s.name,
            s.bn_name,
            s.description,
            s.bn_description,
            s.image_url,
            ss.estimated_hours,
            ss.is_available
        FROM shop_services ss
        INNER JOIN services s ON ss.service_id = s.id
        WHERE ss.shop_id = ? AND ss.is_available = 1 AND s.is_active = 1
        ORDER BY s.sort_order ASC, s.name ASC
    ");
    $apiServicesStmt->execute([2]);
    $apiServices = $apiServicesStmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<p><strong>API Services Query Result:</strong> " . count($apiServices) . " services found</p>";
    foreach ($apiServices as $service) {
        echo "<p>- {$service['name']} ({$service['estimated_hours']} hours)</p>";
    }

    // Items query
    $apiItemsStmt = $pdo->prepare("
        SELECT
            i.id,
            i.name,
            i.bn_name,
            i.description,
            i.bn_description,
            i.image_url,
            i.price as default_price,
            si.custom_price,
            si.estimated_hours,
            si.is_available,
            s.id as service_id,
            s.name as service_name,
            s.bn_name as service_bn_name
        FROM shop_items si
        INNER JOIN items i ON si.item_id = i.id
        INNER JOIN services s ON i.service_id = s.id
        WHERE si.shop_id = ? AND si.is_available = 1 AND i.is_active = 1
        ORDER BY s.sort_order ASC, i.name ASC
    ");
    $apiItemsStmt->execute([2]);
    $apiItems = $apiItemsStmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<p><strong>API Items Query Result:</strong> " . count($apiItems) . " items found</p>";
    foreach ($apiItems as $item) {
        $price = $item['custom_price'] ? "৳{$item['custom_price']}" : "৳{$item['default_price']}";
        echo "<p>- {$item['name']} ($price, Service: {$item['service_name']})</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #2c3e50; }
h3 { color: #34495e; margin-top: 20px; }
p { margin: 5px 0; }
</style>
