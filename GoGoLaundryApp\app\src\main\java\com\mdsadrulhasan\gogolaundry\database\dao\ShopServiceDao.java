package com.mdsadrulhasan.gogolaundry.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;

import java.util.List;

/**
 * DAO for ShopService entity
 */
@Dao
public interface ShopServiceDao {

    /**
     * Insert a shop service
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(ShopServiceEntity shopService);

    /**
     * Insert multiple shop services
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<ShopServiceEntity> shopServices);

    /**
     * Update a shop service
     */
    @Update
    void update(ShopServiceEntity shopService);

    /**
     * Delete a shop service
     */
    @Delete
    void delete(ShopServiceEntity shopService);

    /**
     * Get all services for a shop
     */
    @Query("SELECT ss.*, s.name as serviceName, s.bn_name as serviceBnName, s.image_url as serviceImageUrl " +
           "FROM shop_services ss " +
           "INNER JOIN services s ON ss.service_id = s.id " +
           "WHERE ss.shop_id = :shopId AND ss.is_available = 1 AND s.is_active = 1 " +
           "ORDER BY s.sort_order ASC, s.name ASC")
    LiveData<List<ShopServiceEntity>> getServicesByShopId(int shopId);

    /**
     * Get all services for a shop (synchronous)
     */
    @Query("SELECT ss.*, s.name as serviceName, s.bn_name as serviceBnName, s.image_url as serviceImageUrl " +
           "FROM shop_services ss " +
           "INNER JOIN services s ON ss.service_id = s.id " +
           "WHERE ss.shop_id = :shopId AND ss.is_available = 1 AND s.is_active = 1 " +
           "ORDER BY s.sort_order ASC, s.name ASC")
    List<ShopServiceEntity> getServicesByShopIdSync(int shopId);

    /**
     * Get shops that offer a specific service
     */
    @Query("SELECT ss.* FROM shop_services ss " +
           "INNER JOIN laundry_shops ls ON ss.shop_id = ls.id " +
           "WHERE ss.service_id = :serviceId AND ss.is_available = 1 AND ls.is_active = 1")
    LiveData<List<ShopServiceEntity>> getShopsByServiceId(int serviceId);

    /**
     * Check if a shop offers a specific service
     */
    @Query("SELECT COUNT(*) > 0 FROM shop_services " +
           "WHERE shop_id = :shopId AND service_id = :serviceId AND is_available = 1")
    boolean doesShopOfferService(int shopId, int serviceId);

    /**
     * Get shop service by shop and service ID
     */
    @Query("SELECT ss.*, s.name as serviceName, s.bn_name as serviceBnName, s.image_url as serviceImageUrl " +
           "FROM shop_services ss " +
           "INNER JOIN services s ON ss.service_id = s.id " +
           "WHERE ss.shop_id = :shopId AND ss.service_id = :serviceId")
    ShopServiceEntity getShopService(int shopId, int serviceId);

    /**
     * Clear all shop services for a specific shop
     */
    @Query("DELETE FROM shop_services WHERE shop_id = :shopId")
    void clearShopServices(int shopId);

    /**
     * Clear all shop services
     */
    @Query("DELETE FROM shop_services")
    void clearAll();

    /**
     * Get service count for a shop
     */
    @Query("SELECT COUNT(*) FROM shop_services WHERE shop_id = :shopId AND is_available = 1")
    LiveData<Integer> getServiceCountByShop(int shopId);
}
