<?php
require_once 'config/db.php';

// Create promotional_dialogs table
$sql = "
CREATE TABLE IF NOT EXISTS `promotional_dialogs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `subtitle` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `discount_text` varchar(100) NOT NULL,
  `promo_code` varchar(50) DEFAULT NULL,
  `button_text` varchar(50) NOT NULL DEFAULT 'Shop Now',
  `image_path` varchar(500) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `background_color` varchar(7) DEFAULT '#6c757d',
  `text_color` varchar(7) DEFAULT '#ffffff',
  `button_color` varchar(7) DEFAULT '#ffd700',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_dates` (`start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

try {
    $pdo->exec($sql);
    echo "✅ Promotional dialogs table created successfully!\n";
    
    // Check if sample data already exists
    $checkStmt = $pdo->query("SELECT COUNT(*) FROM promotional_dialogs");
    $count = $checkStmt->fetchColumn();
    
    if ($count == 0) {
        // Insert sample data
        $insertSql = "
        INSERT INTO promotional_dialogs (
          title, subtitle, description, discount_text, promo_code, button_text, 
          background_color, text_color, button_color, is_active, start_date, end_date
        ) VALUES (
          'HOT PICKS\nLOW PRICES',
          'Best Deals on Best Prices',
          'Limited time offer on all laundry services. Get the best quality service at unbeatable prices!',
          'UP TO\n60%\nOFF',
          'SAVE60',
          'Shop Now',
          '#6c757d',
          '#ffffff',
          '#ffd700',
          1,
          NOW(),
          DATE_ADD(NOW(), INTERVAL 30 DAY)
        );
        ";
        
        $stmt = $pdo->prepare($insertSql);
        if ($stmt->execute()) {
            echo "✅ Sample promotional dialog inserted!\n";
        } else {
            echo "❌ Error inserting sample data\n";
        }
    } else {
        echo "ℹ️ Sample data already exists ($count records found)\n";
    }
    
    echo "\nYou can now:\n";
    echo "1. Access the admin panel at: http://*************/GoGoLaundry/GoGoLaundryAdminPanel/admin/promo_codes.php\n";
    echo "2. Create, edit, and manage promotional dialogs\n";
    echo "3. The Android app will automatically fetch active dialogs\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
