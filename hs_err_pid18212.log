#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 57392 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=18212, tid=17332
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\6b307627437613cc426e8327b4ca03b6\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\6b307627437613cc426e8327b4ca03b6\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-41862ac6d84858f6271ea5d5661a3ac2-sock

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Thu May 22 03:28:50 2025 Bangladesh Standard Time elapsed time: 0.489396 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000246ae708700):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=17332, stack(0x000000715a400000,0x000000715a500000) (1024K)]


Current CompileTask:
C2:489  681       4       java.util.concurrent.ConcurrentHashMap::get (162 bytes)

Stack: [0x000000715a400000,0x000000715a500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000246f1425410, length=13, elements={
0x000002469a340980, 0x00000246ae6ef8a0, 0x00000246ae6f3a20, 0x00000246ae6f4b80,
0x00000246ae6f5df0, 0x00000246ae6fdad0, 0x00000246ae703890, 0x00000246ae708700,
0x00000246ae708e50, 0x00000246f11c3ff0, 0x00000246f136d2b0, 0x00000246f1347aa0,
0x00000246f11c60c0
}

Java Threads: ( => current thread )
  0x000002469a340980 JavaThread "main"                              [_thread_in_vm, id=8104, stack(0x0000007159a00000,0x0000007159b00000) (1024K)]
  0x00000246ae6ef8a0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=2672, stack(0x0000007159e00000,0x0000007159f00000) (1024K)]
  0x00000246ae6f3a20 JavaThread "Finalizer"                  daemon [_thread_blocked, id=14356, stack(0x0000007159f00000,0x000000715a000000) (1024K)]
  0x00000246ae6f4b80 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=19344, stack(0x000000715a000000,0x000000715a100000) (1024K)]
  0x00000246ae6f5df0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=11440, stack(0x000000715a100000,0x000000715a200000) (1024K)]
  0x00000246ae6fdad0 JavaThread "Service Thread"             daemon [_thread_blocked, id=11388, stack(0x000000715a200000,0x000000715a300000) (1024K)]
  0x00000246ae703890 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=10044, stack(0x000000715a300000,0x000000715a400000) (1024K)]
=>0x00000246ae708700 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=17332, stack(0x000000715a400000,0x000000715a500000) (1024K)]
  0x00000246ae708e50 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=8092, stack(0x000000715a500000,0x000000715a600000) (1024K)]
  0x00000246f11c3ff0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=17556, stack(0x000000715a600000,0x000000715a700000) (1024K)]
  0x00000246f136d2b0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=10452, stack(0x000000715a700000,0x000000715a800000) (1024K)]
  0x00000246f1347aa0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=15596, stack(0x000000715a800000,0x000000715a900000) (1024K)]
  0x00000246f11c60c0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=16588, stack(0x000000715a900000,0x000000715aa00000) (1024K)]
Total: 13

Other Threads:
  0x00000246ae6e2510 VMThread "VM Thread"                           [id=5780, stack(0x0000007159d00000,0x0000007159e00000) (1024K)]
  0x000002469a3ad8d0 WatcherThread "VM Periodic Task Thread"        [id=11248, stack(0x0000007159c00000,0x0000007159d00000) (1024K)]
  0x000002469a35efe0 WorkerThread "GC Thread#0"                     [id=11576, stack(0x0000007159b00000,0x0000007159c00000) (1024K)]
Total: 3

Threads with active compile tasks:
C2 CompilerThread0  508  681       4       java.util.concurrent.ConcurrentHashMap::get (162 bytes)
C2 CompilerThread1  508  641       4       lombok.patcher.scripts.MethodLevelPatchScript::patch (21 bytes)
C2 CompilerThread2  508  671       4       lombok.patcher.PatchScript::classMatches (41 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff82f4dc308] Metaspace_lock - owner thread: 0x000002469a340980

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000246af000000-0x00000246afba0000-0x00000246afba0000), size 12189696, SharedBaseAddress: 0x00000246af000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000246b0000000-0x00000246f0000000, reserved size: 1073741824
Narrow klass base: 0x00000246af000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 14512K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 56% used [0x00000000eab00000,0x00000000eb92c3a0,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 2697K, committed 2880K, reserved 1114112K
  class space    used 277K, committed 384K, reserved 1048576K

Card table byte_map: [0x0000024699d00000,0x0000024699f10000] _byte_map_base: 0x0000024699700000

Marking Bits: (ParMarkBitMap*) 0x00007ff82f4e31f0
 Begin Bits: [0x00000246ac540000, 0x00000246ad540000)
 End Bits:   [0x00000246ad540000, 0x00000246ae540000)

Polling page: 0x0000024698340000

Metaspace:

Usage:
  Non-class:      2.36 MB used.
      Class:    277.85 KB used.
       Both:      2.63 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       2.44 MB (  4%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     384.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       2.81 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.65 MB
       Class:  15.67 MB
        Both:  27.32 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 80.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 45.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 102.
num_chunk_merges: 0.
num_chunk_splits: 71.
num_chunks_enlarged: 46.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=261Kb max_used=261Kb free=119738Kb
 bounds [0x00000246a5010000, 0x00000246a5280000, 0x00000246ac540000]
CodeHeap 'profiled nmethods': size=120000Kb used=988Kb max_used=988Kb free=119011Kb
 bounds [0x000002469d540000, 0x000002469d7b0000, 0x00000246a4a70000]
CodeHeap 'non-nmethods': size=5760Kb used=1182Kb max_used=1200Kb free=4578Kb
 bounds [0x00000246a4a70000, 0x00000246a4ce0000, 0x00000246a5010000]
 total_blobs=1126 nmethods=686 adapters=347
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.462 Thread 0x00000246ae708700 nmethod 659 0x00000246a504ee90 code [0x00000246a504f060, 0x00000246a504f448]
Event: 0.463 Thread 0x00000246ae708e50 nmethod 668 0x000002469d62f290 code [0x000002469d62f480, 0x000002469d62f9f0]
Event: 0.463 Thread 0x00000246ae708e50  669       3       java.util.regex.Pattern::peek (26 bytes)
Event: 0.463 Thread 0x00000246ae708e50 nmethod 669 0x000002469d62fb90 code [0x000002469d62fd40, 0x000002469d62ff98]
Event: 0.465 Thread 0x00000246ae708e50  670       3       java.lang.invoke.LambdaForm$NamedFunction::returnType (11 bytes)
Event: 0.465 Thread 0x00000246ae708e50 nmethod 670 0x000002469d630090 code [0x000002469d6302a0, 0x000002469d6307b8]
Event: 0.468 Thread 0x00000246f1347aa0  671       4       lombok.patcher.PatchScript::classMatches (41 bytes)
Event: 0.469 Thread 0x00000246ae708700  672       4       java.util.Collections$UnmodifiableCollection::iterator (9 bytes)
Event: 0.472 Thread 0x00000246ae708e50  673       3       sun.security.util.DerInputStream::available (10 bytes)
Event: 0.472 Thread 0x00000246ae708e50 nmethod 673 0x000002469d630a10 code [0x000002469d630ba0, 0x000002469d630cb0]
Event: 0.474 Thread 0x00000246ae708e50  674       3       java.util.jar.Manifest$FastInputStream::readLine (296 bytes)
Event: 0.475 Thread 0x00000246ae708e50 nmethod 674 0x000002469d630d10 code [0x000002469d630f40, 0x000002469d6317a8]
Event: 0.475 Thread 0x00000246ae708e50  675       3       java.util.Objects::checkFromIndexSize (8 bytes)
Event: 0.476 Thread 0x00000246ae708e50 nmethod 675 0x000002469d631b10 code [0x000002469d631cc0, 0x000002469d631ec8]
Event: 0.481 Thread 0x00000246ae708e50  678       3       java.lang.Integer::rotateRight (9 bytes)
Event: 0.481 Thread 0x00000246ae708e50 nmethod 678 0x000002469d632010 code [0x000002469d6321a0, 0x000002469d6322a0]
Event: 0.481 Thread 0x00000246ae708e50  679       3       sun.invoke.util.Wrapper::forPrimitiveType (122 bytes)
Event: 0.482 Thread 0x00000246ae708e50 nmethod 679 0x000002469d632310 code [0x000002469d6325e0, 0x000002469d6330c0]
Event: 0.482 Thread 0x00000246ae708700 nmethod 672 0x00000246a5050390 code [0x00000246a50505a0, 0x00000246a5050cb8]
Event: 0.485 Thread 0x00000246ae708700  681       4       java.util.concurrent.ConcurrentHashMap::get (162 bytes)

GC Heap History (0 events):
No events

Dll operation events (8 events):
Event: 0.007 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.066 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.084 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.088 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.090 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.093 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.108 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.182 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 0.282 Thread 0x000002469a340980 DEOPT PACKING pc=0x00000246a501fbf4 sp=0x0000007159afeb70
Event: 0.282 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac3aa2 sp=0x0000007159afead0 mode 2
Event: 0.286 Thread 0x000002469a340980 DEOPT PACKING pc=0x000002469d5890d8 sp=0x0000007159afc6c0
Event: 0.286 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac4242 sp=0x0000007159afbb80 mode 0
Event: 0.290 Thread 0x000002469a340980 DEOPT PACKING pc=0x000002469d5890d8 sp=0x0000007159afae80
Event: 0.290 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac4242 sp=0x0000007159afa340 mode 0
Event: 0.292 Thread 0x000002469a340980 DEOPT PACKING pc=0x000002469d588fff sp=0x0000007159afa500
Event: 0.292 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac4242 sp=0x0000007159af99b0 mode 0
Event: 0.293 Thread 0x000002469a340980 DEOPT PACKING pc=0x000002469d5890d8 sp=0x0000007159afa110
Event: 0.293 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac4242 sp=0x0000007159af95d0 mode 0
Event: 0.294 Thread 0x000002469a340980 DEOPT PACKING pc=0x000002469d5890d8 sp=0x0000007159afa0f0
Event: 0.294 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac4242 sp=0x0000007159af95b0 mode 0
Event: 0.295 Thread 0x000002469a340980 DEOPT PACKING pc=0x000002469d5890d8 sp=0x0000007159afba50
Event: 0.295 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac4242 sp=0x0000007159afaf10 mode 0
Event: 0.301 Thread 0x000002469a340980 DEOPT PACKING pc=0x000002469d5890d8 sp=0x0000007159afaab0
Event: 0.301 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac4242 sp=0x0000007159af9f70 mode 0
Event: 0.431 Thread 0x000002469a340980 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000246a50342f4 relative=0x00000000000000d4
Event: 0.431 Thread 0x000002469a340980 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000246a50342f4 method=java.net.URI.match(CJJ)Z @ 45 c2
Event: 0.431 Thread 0x000002469a340980 DEOPT PACKING pc=0x00000246a50342f4 sp=0x0000007159afcc80
Event: 0.431 Thread 0x000002469a340980 DEOPT UNPACKING pc=0x00000246a4ac3aa2 sp=0x0000007159afcbe8 mode 2

Classes loaded (20 events):
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$NFKDMode done
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$NFCMode
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$NFCMode done
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$NFKCMode
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$NFKCMode done
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$1
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$1 done
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$NFKDModeImpl
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$NFKDModeImpl done
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$ModeImpl
Event: 0.487 Loading class jdk/internal/icu/text/NormalizerBase$ModeImpl done
Event: 0.487 Loading class jdk/internal/icu/text/Normalizer2
Event: 0.487 Loading class jdk/internal/icu/text/Normalizer2 done
Event: 0.487 Loading class jdk/internal/icu/impl/Norm2AllModes
Event: 0.487 Loading class jdk/internal/icu/impl/Norm2AllModes done
Event: 0.487 Loading class jdk/internal/icu/impl/Norm2AllModes$NoopNormalizer2
Event: 0.487 Loading class jdk/internal/icu/impl/Norm2AllModes$NoopNormalizer2 done
Event: 0.487 Loading class jdk/internal/icu/impl/Norm2AllModes$NFKCSingleton
Event: 0.487 Loading class jdk/internal/icu/impl/Norm2AllModes$NFKCSingleton done
Event: 0.488 Loading class jdk/internal/icu/impl/Norm2AllModes$Norm2AllModesSingleton

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (12 events):
Event: 0.062 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ead2a6b8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ead2a6b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.103 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae93838}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eae93838) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.141 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf4bee8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eaf4bee8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.144 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf5e8d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eaf5e8d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.145 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf69d10}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eaf69d10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.147 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf7a808}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eaf7a808) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.149 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf864c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000eaf864c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.150 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf8ae18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eaf8ae18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.150 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf8e9a8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eaf8e9a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.151 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf91e18}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eaf91e18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.208 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb14e140}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000eb14e140) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.401 Thread 0x000002469a340980 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb5d5980}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000eb5d5980) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (4 events):
Event: 0.065 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.065 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.121 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.121 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (13 events):
Event: 0.019 Thread 0x000002469a340980 Thread added: 0x000002469a340980
Event: 0.032 Thread 0x000002469a340980 Thread added: 0x00000246ae6ef8a0
Event: 0.032 Thread 0x000002469a340980 Thread added: 0x00000246ae6f3a20
Event: 0.033 Thread 0x000002469a340980 Thread added: 0x00000246ae6f4b80
Event: 0.033 Thread 0x000002469a340980 Thread added: 0x00000246ae6f5df0
Event: 0.033 Thread 0x000002469a340980 Thread added: 0x00000246ae6fdad0
Event: 0.033 Thread 0x000002469a340980 Thread added: 0x00000246ae703890
Event: 0.033 Thread 0x000002469a340980 Thread added: 0x00000246ae708700
Event: 0.033 Thread 0x000002469a340980 Thread added: 0x00000246ae708e50
Event: 0.055 Thread 0x000002469a340980 Thread added: 0x00000246f11c3ff0
Event: 0.179 Thread 0x00000246ae708700 Thread added: 0x00000246f136d2b0
Event: 0.179 Thread 0x00000246ae708700 Thread added: 0x00000246f1347aa0
Event: 0.277 Thread 0x000002469a340980 Thread added: 0x00000246f11c60c0


Dynamic libraries:
0x00007ff7b73b0000 - 0x00007ff7b73be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8cebf0000 - 0x00007ff8cede8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8cd350000 - 0x00007ff8cd412000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8cc4c0000 - 0x00007ff8cc7b6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8cc850000 - 0x00007ff8cc950000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8a9610000 - 0x00007ff8a962e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8ab3e0000 - 0x00007ff8ab3f8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff8cdf20000 - 0x00007ff8ce0bd000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8ccbe0000 - 0x00007ff8ccc02000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8bcf80000 - 0x00007ff8bd21a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ff8cce60000 - 0x00007ff8ccefe000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8cd320000 - 0x00007ff8cd34b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8ccac0000 - 0x00007ff8ccbd9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8cc950000 - 0x00007ff8cc9ed000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8cd6c0000 - 0x00007ff8cd6ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c8280000 - 0x00007ff8c828c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff89b290000 - 0x00007ff89b31d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ff82e830000 - 0x00007ff82f5c0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8cd6f0000 - 0x00007ff8cd7a1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8ce9c0000 - 0x00007ff8cea5f000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8cd1e0000 - 0x00007ff8cd303000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8cc3e0000 - 0x00007ff8cc407000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8cd550000 - 0x00007ff8cd5bb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8cc0d0000 - 0x00007ff8cc11b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8c0fc0000 - 0x00007ff8c0fe7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8c39b0000 - 0x00007ff8c39ba000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8cc0b0000 - 0x00007ff8cc0c2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8caaf0000 - 0x00007ff8cab02000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c39c0000 - 0x00007ff8c39ca000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff8ca880000 - 0x00007ff8caa81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8baf20000 - 0x00007ff8baf54000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8cc7c0000 - 0x00007ff8cc842000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8b5360000 - 0x00007ff8b536f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8ab9b0000 - 0x00007ff8ab9cf000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff8cd7b0000 - 0x00007ff8cdf1e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8ca0d0000 - 0x00007ff8ca873000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8ce540000 - 0x00007ff8ce893000 	C:\WINDOWS\System32\combase.dll
0x00007ff8cbbe0000 - 0x00007ff8cbc0b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ff8cea80000 - 0x00007ff8ceb4d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8cd4a0000 - 0x00007ff8cd54d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8ceb50000 - 0x00007ff8cebab000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8cc1b0000 - 0x00007ff8cc1d5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff8a66d0000 - 0x00007ff8a66e8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff8bb030000 - 0x00007ff8bb040000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff8c8480000 - 0x00007ff8c858a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8cb940000 - 0x00007ff8cb9aa000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff8a4070000 - 0x00007ff8a4086000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff8b1070000 - 0x00007ff8b1080000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\6b307627437613cc426e8327b4ca03b6\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\6b307627437613cc426e8327b4ca03b6\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-41862ac6d84858f6271ea5d5661a3ac2-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\6b307627437613cc426e8327b4ca03b6\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 7:00 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4453M free)
TotalPageFile size 22476M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 59M, peak: 59M
current process commit charge ("private bytes"): 197M, peak: 198M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
