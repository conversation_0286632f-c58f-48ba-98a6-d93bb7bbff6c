package com.mdsadrulhasan.gogolaundry.utils;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.widget.Toast;


import com.mdsadrulhasan.gogolaundry.R;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import cn.pedant.SweetAlert.SweetAlertDialog;
import retrofit2.Response;

/**
 * Utility class for showing dialogs
 */
public class DialogUtils {
    private static final String TAG = "DialogUtils";

    /**
     * Check if the context is still valid for showing dialogs
     *
     * @param context The context to check
     * @return true if the context is valid, false otherwise
     */
    private static boolean isContextValid(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null");
            return false;
        }

        // Check if context is an Activity and if it's not finishing or destroyed
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            boolean isValid = !activity.isFinishing() && !activity.isDestroyed();

            if (!isValid) {
                Log.e(TAG, "Activity is finishing or destroyed: " + activity.getClass().getSimpleName());
            }

            return isValid;
        }

        return true;
    }

    /**
     * Show error dialog
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     */
    public static void showErrorDialog(Context context, String title, String message) {
        if (!isContextValid(context)) return;

        try {
            new SweetAlertDialog(context, SweetAlertDialog.ERROR_TYPE)
                    .setTitleText(title)
                    .setContentText(message)
                    .setConfirmText(context.getString(android.R.string.ok))
                    .setConfirmClickListener(null)
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing error dialog: " + e.getMessage());
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, message, Toast.LENGTH_LONG).show();
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show error message: " + message);
            }
        }
    }

    /**
     * Show error dialog with default title
     *
     * @param context Context
     * @param message Dialog message
     */
    public static void showErrorDialog(Context context, String message) {
        showErrorDialog(context, context.getString(R.string.error), message);
    }

    /**
     * Show warning dialog
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     */
    public static void showWarningDialog(Context context, String title, String message) {
        if (!isContextValid(context)) return;

        try {
            new SweetAlertDialog(context, SweetAlertDialog.WARNING_TYPE)
                    .setTitleText(title)
                    .setContentText(message)
                    .setConfirmText(context.getString(android.R.string.ok))
                    .setConfirmClickListener(null)
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing warning dialog: " + e.getMessage());
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, message, Toast.LENGTH_LONG).show();
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show warning message: " + message);
            }
        }
    }

    /**
     * Show success dialog
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     */
    public static void showSuccessDialog(Context context, String title, String message) {
        if (!isContextValid(context)) return;

        try {
            new SweetAlertDialog(context, SweetAlertDialog.SUCCESS_TYPE)
                    .setTitleText(title)
                    .setContentText(message)
                    .setConfirmText(context.getString(android.R.string.ok))
                    .setConfirmClickListener(null)
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing success dialog: " + e.getMessage());
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, message, Toast.LENGTH_LONG).show();
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show success message: " + message);
            }
        }
    }

    /**
     * Show success dialog with action
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     * @param confirmText Confirm button text
     * @param confirmListener Confirm button click listener
     */
    public static void showSuccessDialog(Context context, String title, String message,
                                        String confirmText, SweetAlertDialog.OnSweetClickListener confirmListener) {
        if (!isContextValid(context)) return;

        try {
            SweetAlertDialog dialog = new SweetAlertDialog(context, SweetAlertDialog.SUCCESS_TYPE)
                    .setTitleText(title)
                    .setContentText(message)
                    .setConfirmText(confirmText)
                    .setConfirmClickListener(confirmListener);

            // Customize the dialog appearance
            dialog.setCancelable(false); // Prevent dismissal by tapping outside
            dialog.getProgressHelper().setBarColor(context.getResources().getColor(R.color.primary));

            // Show the dialog
            dialog.show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing success dialog with action: " + e.getMessage());
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, message, Toast.LENGTH_LONG).show();

                // If there's a confirm listener, try to execute it after a delay
                if (confirmListener != null) {
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        try {
                            confirmListener.onClick(null);
                        } catch (Exception ex) {
                            Log.e(TAG, "Error executing confirm listener: " + ex.getMessage());
                        }
                    }, 2000); // 2 second delay
                }
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show success message: " + message);
            }
        }
    }

    /**
     * Show order placed success dialog with enhanced styling
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     * @param confirmText Confirm button text
     * @param confirmListener Confirm button click listener
     */
    public static void showOrderPlacedDialog(Context context, String title, String message,
                                           String confirmText, SweetAlertDialog.OnSweetClickListener confirmListener) {
        if (!isContextValid(context)) return;

        try {
            // Create a wrapper for the confirm listener to ensure it works properly
            SweetAlertDialog.OnSweetClickListener wrappedListener = sweetAlertDialog -> {
                try {
                    // Log that the button was clicked
                    Log.d(TAG, "Order placed dialog 'View Orders' button clicked");

                    // Dismiss the dialog first to prevent UI issues
                    sweetAlertDialog.dismissWithAnimation();

                    // Add a small delay before executing the navigation to ensure the dialog is fully dismissed
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                        try {
                            // Execute the original listener
                            if (confirmListener != null) {
                                confirmListener.onClick(null);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error executing confirm listener after delay: " + e.getMessage(), e);
                        }
                    }, 100); // 100ms delay
                } catch (Exception e) {
                    Log.e(TAG, "Error in wrapped confirm listener: " + e.getMessage(), e);

                    // Try to execute the original listener directly as a fallback
                    if (confirmListener != null) {
                        confirmListener.onClick(sweetAlertDialog);
                    }
                }
            };

            // Create the dialog with the wrapped listener
            SweetAlertDialog dialog = new SweetAlertDialog(context, SweetAlertDialog.SUCCESS_TYPE)
                    .setTitleText(title)
                    .setContentText(message)
                    .setConfirmText(confirmText)
                    .setConfirmClickListener(wrappedListener);

            // Customize the dialog appearance for order success
            dialog.setCancelable(false); // Prevent dismissal by tapping outside
            dialog.getProgressHelper().setBarColor(context.getResources().getColor(R.color.success));

            // Set custom button styling
            dialog.setConfirmButtonBackgroundColor(context.getResources().getColor(R.color.primary));

            // Show the dialog
            dialog.show();

            // Log that the dialog was shown
            Log.d(TAG, "Order placed success dialog shown with 'View Orders' button");
        } catch (Exception e) {
            Log.e(TAG, "Error showing order placed dialog: " + e.getMessage(), e);
            // Fall back to regular success dialog
            showSuccessDialog(context, title, message, confirmText, confirmListener);
        }
    }

    /**
     * Show API error dialog
     *
     * @param context Context
     * @param response Retrofit response
     */
    public static void showApiErrorDialog(Context context, Response<?> response) {
        if (!isContextValid(context)) return;

        String errorMessage;

        try {
            if (response.errorBody() != null) {
                String errorBody = response.errorBody().string();
                Log.e(TAG, "API Error: " + errorBody);

                try {
                    JSONObject errorJson = new JSONObject(errorBody);
                    errorMessage = errorJson.optString("message", context.getString(R.string.unknown_error));
                } catch (JSONException e) {
                    errorMessage = errorBody;
                }
            } else {
                errorMessage = context.getString(R.string.unknown_error);
            }
        } catch (IOException e) {
            Log.e(TAG, "Error parsing error body", e);
            errorMessage = context.getString(R.string.unknown_error);
        }

        try {
            // Check for specific error messages and provide more user-friendly messages
            if (errorMessage.contains("not registered")) {
                showWarningDialog(context, context.getString(R.string.error), context.getString(R.string.phone_not_registered));
            } else if (errorMessage.contains("already registered")) {
                showWarningDialog(context, context.getString(R.string.error), context.getString(R.string.phone_already_registered));
            } else if (errorMessage.contains("Invalid or expired OTP")) {
                showErrorDialog(context, context.getString(R.string.error), "The OTP you entered has expired or is invalid. Please request a new OTP and try again.");
            } else {
                showErrorDialog(context, errorMessage);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing API error dialog: " + e.getMessage());
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, errorMessage, Toast.LENGTH_LONG).show();
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show API error message: " + errorMessage);
            }
        }
    }

    /**
     * Show confirmation dialog
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     * @param confirmText Confirm button text
     * @param cancelText Cancel button text
     * @param confirmListener Confirm button click listener
     * @param cancelListener Cancel button click listener
     */
    public static void showConfirmationDialog(
            Context context,
            String title,
            String message,
            String confirmText,
            String cancelText,
            final SweetAlertDialog.OnSweetClickListener confirmListener,
            final SweetAlertDialog.OnSweetClickListener cancelListener) {

        if (!isContextValid(context)) return;

        try {
            new SweetAlertDialog(context, SweetAlertDialog.CUSTOM_IMAGE_TYPE)
                    .setTitleText(title)
                    .setContentText(message)
                    .setConfirmText(confirmText)
                    .setCancelText(cancelText)
                    .setConfirmClickListener(confirmListener)
                    .setCancelClickListener(cancelListener)
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing confirmation dialog: " + e.getMessage());
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, message, Toast.LENGTH_LONG).show();
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show confirmation message: " + message);
            }
        }
    }

    /**
     * Show confirmation dialog with default button text
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     * @param confirmListener Confirm button click listener
     * @param cancelListener Cancel button click listener
     */
    public static void showConfirmationDialog(
            Context context,
            String title,
            String message,
            final SweetAlertDialog.OnSweetClickListener confirmListener,
            final SweetAlertDialog.OnSweetClickListener cancelListener) {

        if (!isContextValid(context)) return;

        try {
            showConfirmationDialog(
                    context,
                    title,
                    message,
                    context.getString(android.R.string.ok),
                    context.getString(android.R.string.cancel),
                    confirmListener,
                    cancelListener);
        } catch (Exception e) {
            Log.e(TAG, "Error showing confirmation dialog with default buttons: " + e.getMessage());
        }
    }

    /**
     * Show loading dialog
     *
     * @param context Context
     * @param message Dialog message
     * @return SweetAlertDialog instance that can be dismissed later
     */
    public static SweetAlertDialog showLoadingDialog(Context context, String message) {
        if (!isContextValid(context)) return null;

        try {
            SweetAlertDialog pDialog = new SweetAlertDialog(context, SweetAlertDialog.PROGRESS_TYPE);
            pDialog.getProgressHelper().setBarColor(Color.parseColor("#A5DC86"));
            pDialog.setTitleText(message);
            pDialog.setCancelable(false);
            pDialog.show();
            return pDialog;
        } catch (Exception e) {
            Log.e(TAG, "Error showing loading dialog: " + e.getMessage());
            return null;
        }
    }

    /**
     * Show info dialog
     *
     * @param context Context
     * @param title Dialog title
     * @param message Dialog message
     */
    public static void showInfoDialog(Context context, String title, String message) {
        if (!isContextValid(context)) return;

        try {
            new SweetAlertDialog(context, SweetAlertDialog.NORMAL_TYPE)
                    .setTitleText(title)
                    .setContentText(message)
                    .setConfirmText(context.getString(android.R.string.ok))
                    .setConfirmClickListener(null)
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing info dialog: " + e.getMessage());
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, message, Toast.LENGTH_LONG).show();
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show info message: " + message);
            }
        }
    }

    /**
     * Show image selection dialog
     *
     * @param context Context
     * @param cameraAction Action to execute when camera is selected
     * @param galleryAction Action to execute when gallery is selected
     */
    public static void showImageSelectionDialog(Context context, Runnable cameraAction, Runnable galleryAction) {
        if (!isContextValid(context)) return;

        try {
            new SweetAlertDialog(context, SweetAlertDialog.NORMAL_TYPE)
                    .setTitleText(context.getString(R.string.select_image_source))
                    .setContentText(context.getString(R.string.choose_image_source))
                    .setConfirmText(context.getString(R.string.camera))
                    .setCancelText(context.getString(R.string.gallery))
                    .setConfirmClickListener(sweetAlertDialog -> {
                        sweetAlertDialog.dismissWithAnimation();
                        if (cameraAction != null) {
                            cameraAction.run();
                        }
                    })
                    .setCancelClickListener(sweetAlertDialog -> {
                        sweetAlertDialog.dismissWithAnimation();
                        if (galleryAction != null) {
                            galleryAction.run();
                        }
                    })
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing image selection dialog: " + e.getMessage());
            // Fallback to direct gallery action if dialog fails
            if (galleryAction != null) {
                galleryAction.run();
            }
        }
    }

    /**
     * Show order status dialog
     *
     * @param context Context
     * @param order Order object
     */
    public static void showOrderStatusDialog(Context context, com.mdsadrulhasan.gogolaundry.model.Order order) {
        if (!isContextValid(context)) return;

        try {
            // Validate order object
            if (order == null) {
                Log.e(TAG, "Cannot show order status dialog: order is null");
                showErrorDialog(context, "Error", "Order information not available");
                return;
            }

            // Log order details for debugging
            Log.d(TAG, "Showing order status dialog for order: " + order.getOrderNumber() +
                  ", Status: " + order.getStatus() +
                  ", Total: " + order.getTotalAmount());

            // Format status message
            String statusMessage = "Order #" + order.getOrderNumber() +
                                  "\nStatus: " + formatOrderStatus(order.getStatus()) +
                                  "\nTotal: ₹" + order.getTotalAmount();

            // Show dialog
            new SweetAlertDialog(context, SweetAlertDialog.NORMAL_TYPE)
                    .setTitleText("Order Status")
                    .setContentText(statusMessage)
                    .setConfirmText("OK")
                    .setConfirmClickListener(SweetAlertDialog::dismissWithAnimation)
                    .show();
        } catch (Exception e) {
            Log.e(TAG, "Error showing order status dialog: " + e.getMessage(), e);
            // Fallback to toast if dialog fails
            try {
                Toast.makeText(context, "Order Status: " +
                              (order != null ? order.getStatus() : "Unknown"),
                              Toast.LENGTH_LONG).show();
            } catch (Exception ignored) {
                // Last resort, just log the error
                Log.e(TAG, "Failed to show order status");
            }
        }
    }

    /**
     * Format order status for display
     *
     * @param status Raw status from API
     * @return Formatted status for display
     */
    private static String formatOrderStatus(String status) {
        if (status == null || status.isEmpty()) {
            return "Unknown";
        }

        // Capitalize first letter of each word
        String[] words = status.split("_");
        StringBuilder result = new StringBuilder();

        for (String word : words) {
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1).toLowerCase())
                      .append(" ");
            }
        }

        return result.toString().trim();
    }
}
