package com.mdsadrulhasan.gogolaundry.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;

import java.util.List;

/**
 * DAO for ShopItem entity
 */
@Dao
public interface ShopItemDao {

    /**
     * Insert a shop item
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(ShopItemEntity shopItem);

    /**
     * Insert multiple shop items
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<ShopItemEntity> shopItems);

    /**
     * Update a shop item
     */
    @Update
    void update(ShopItemEntity shopItem);

    /**
     * Delete a shop item
     */
    @Delete
    void delete(ShopItemEntity shopItem);

    /**
     * Get all items for a shop
     */
    @Query("SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, " +
           "i.price as defaultPrice, s.name as serviceName " +
           "FROM shop_items si " +
           "INNER JOIN items i ON si.item_id = i.id " +
           "INNER JOIN services s ON i.service_id = s.id " +
           "WHERE si.shop_id = :shopId AND si.is_available = 1 AND i.is_active = 1 " +
           "ORDER BY s.sort_order ASC, i.name ASC")
    LiveData<List<ShopItemEntity>> getItemsByShopId(int shopId);

    /**
     * Get all items for a shop (synchronous)
     */
    @Query("SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, " +
           "i.price as defaultPrice, s.name as serviceName " +
           "FROM shop_items si " +
           "INNER JOIN items i ON si.item_id = i.id " +
           "INNER JOIN services s ON i.service_id = s.id " +
           "WHERE si.shop_id = :shopId AND si.is_available = 1 AND i.is_active = 1 " +
           "ORDER BY s.sort_order ASC, i.name ASC")
    List<ShopItemEntity> getItemsByShopIdSync(int shopId);

    /**
     * Get items for a shop filtered by service
     */
    @Query("SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, " +
           "i.price as defaultPrice, s.name as serviceName " +
           "FROM shop_items si " +
           "INNER JOIN items i ON si.item_id = i.id " +
           "INNER JOIN services s ON i.service_id = s.id " +
           "WHERE si.shop_id = :shopId AND i.service_id = :serviceId AND si.is_available = 1 AND i.is_active = 1 " +
           "ORDER BY i.name ASC")
    LiveData<List<ShopItemEntity>> getItemsByShopAndService(int shopId, int serviceId);

    /**
     * Get shops that offer a specific item
     */
    @Query("SELECT si.* FROM shop_items si " +
           "INNER JOIN laundry_shops ls ON si.shop_id = ls.id " +
           "WHERE si.item_id = :itemId AND si.is_available = 1 AND ls.is_active = 1")
    LiveData<List<ShopItemEntity>> getShopsByItemId(int itemId);

    /**
     * Check if a shop offers a specific item
     */
    @Query("SELECT COUNT(*) > 0 FROM shop_items " +
           "WHERE shop_id = :shopId AND item_id = :itemId AND is_available = 1")
    boolean doesShopOfferItem(int shopId, int itemId);

    /**
     * Get shop item by shop and item ID
     */
    @Query("SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, " +
           "i.price as defaultPrice, s.name as serviceName " +
           "FROM shop_items si " +
           "INNER JOIN items i ON si.item_id = i.id " +
           "INNER JOIN services s ON i.service_id = s.id " +
           "WHERE si.shop_id = :shopId AND si.item_id = :itemId")
    ShopItemEntity getShopItem(int shopId, int itemId);

    /**
     * Clear all shop items for a specific shop
     */
    @Query("DELETE FROM shop_items WHERE shop_id = :shopId")
    void clearShopItems(int shopId);

    /**
     * Clear all shop items
     */
    @Query("DELETE FROM shop_items")
    void clearAll();

    /**
     * Get item count for a shop
     */
    @Query("SELECT COUNT(*) FROM shop_items WHERE shop_id = :shopId AND is_available = 1")
    LiveData<Integer> getItemCountByShop(int shopId);

    /**
     * Search items in a shop
     */
    @Query("SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, " +
           "i.price as defaultPrice, s.name as serviceName " +
           "FROM shop_items si " +
           "INNER JOIN items i ON si.item_id = i.id " +
           "INNER JOIN services s ON i.service_id = s.id " +
           "WHERE si.shop_id = :shopId AND si.is_available = 1 AND i.is_active = 1 AND " +
           "(i.name LIKE '%' || :query || '%' OR i.bn_name LIKE '%' || :query || '%') " +
           "ORDER BY i.name ASC")
    LiveData<List<ShopItemEntity>> searchItemsInShop(int shopId, String query);
}
