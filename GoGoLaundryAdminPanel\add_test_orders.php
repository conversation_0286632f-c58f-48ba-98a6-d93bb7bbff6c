<?php
/**
 * Add Test Orders Script
 *
 * This script adds test orders with different statuses and revenue values
 */

// Include required files
require_once 'config/db.php';
require_once 'includes/functions.php';

// Check if the script has already been run
$stmt = $pdo->query("SELECT COUNT(*) FROM orders");
$orderCount = $stmt->fetchColumn();

if ($orderCount > 10) {
    echo "Test orders already exist. There are $orderCount orders in the database.<br>";
    echo "<a href='admin/index.php'>Go to Dashboard</a>";
    exit;
}

// Get a user ID
$stmt = $pdo->query("SELECT id FROM users LIMIT 1");
$userId = $stmt->fetchColumn();

if (!$userId) {
    echo "No users found. Please run add_sample_data.php first.<br>";
    echo "<a href='add_sample_data.php'>Add Sample Data</a>";
    exit;
}

// Check if services exist
$stmt = $pdo->query("SELECT COUNT(*) FROM services");
$serviceCount = $stmt->fetchColumn();

if ($serviceCount == 0) {
    // Add a service if none exists
    $stmt = $pdo->prepare("
        INSERT INTO services (name, bn_name, description, icon, is_active)
        VALUES (?, ?, ?, ?, ?)
    ");
    $stmt->execute(['Wash & Fold', 'ওয়াশ এন্ড ফোল্ড', 'Regular laundry service', 'fas fa-tshirt', 1]);
    $serviceId = $pdo->lastInsertId();
    echo "Added a service.<br>";
} else {
    // Get a service ID
    $stmt = $pdo->query("SELECT id FROM services LIMIT 1");
    $serviceId = $stmt->fetchColumn();
}

// Check if items exist
$stmt = $pdo->query("SELECT COUNT(*) FROM items");
$itemCount = $stmt->fetchColumn();

if ($itemCount == 0) {
    // Add an item if none exists
    $stmt = $pdo->prepare("
        INSERT INTO items (service_id, name, bn_name, description, price, image_url, is_active)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([$serviceId, 'T-Shirt', 'টি-শার্ট', 'Regular T-Shirt', 50.00, 'uploads/items/tshirt.jpg', 1]);
    $itemId = $pdo->lastInsertId();
    echo "Added an item.<br>";
} else {
    // Get an item ID
    $stmt = $pdo->query("SELECT id FROM items LIMIT 1");
    $itemId = $stmt->fetchColumn();
}

// Define order statuses
$statuses = [
    'placed',
    'confirmed',
    'pickup_scheduled',
    'picked_up',
    'processing',
    'ready_for_delivery',
    'out_for_delivery',
    'delivered',
    'cancelled'
];

// Define date ranges
$today = date('Y-m-d');
$yesterday = date('Y-m-d', strtotime('-1 day'));
$lastWeek = date('Y-m-d', strtotime('-7 days'));
$lastMonth = date('Y-m-d', strtotime('-30 days'));

// Create orders with different dates and statuses
$orders = [
    // Today's orders
    [
        'date' => $today,
        'status' => 'placed',
        'total' => 1500.00
    ],
    [
        'date' => $today,
        'status' => 'confirmed',
        'total' => 2500.00
    ],
    [
        'date' => $today,
        'status' => 'processing',
        'total' => 3500.00
    ],
    [
        'date' => $today,
        'status' => 'delivered',
        'total' => 4500.00
    ],

    // Yesterday's orders
    [
        'date' => $yesterday,
        'status' => 'placed',
        'total' => 1200.00
    ],
    [
        'date' => $yesterday,
        'status' => 'processing',
        'total' => 2200.00
    ],
    [
        'date' => $yesterday,
        'status' => 'delivered',
        'total' => 3200.00
    ],

    // Last week's orders
    [
        'date' => $lastWeek,
        'status' => 'delivered',
        'total' => 5000.00
    ],
    [
        'date' => $lastWeek,
        'status' => 'cancelled',
        'total' => 1800.00
    ],

    // Last month's orders
    [
        'date' => $lastMonth,
        'status' => 'delivered',
        'total' => 6000.00
    ]
];

// Insert orders
$orderCount = 0;
foreach ($orders as $order) {
    // Generate order number and tracking number
    $orderNumber = 'ORD' . date('YmdHis') . rand(100, 999);
    $trackingNumber = 'TRK' . date('YmdHis') . rand(100, 999);

    // Insert order
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            order_number, tracking_number, user_id, subtotal, delivery_fee, total,
            payment_method, status, pickup_address, pickup_date, pickup_time_slot,
            delivery_address, created_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");

    $stmt->execute([
        $orderNumber,
        $trackingNumber,
        $userId,
        $order['total'] - 100, // Subtotal
        100, // Delivery fee
        $order['total'],
        'cash',
        $order['status'],
        '123 Test Street',
        date('Y-m-d', strtotime('+1 day')),
        '10:00 AM - 12:00 PM',
        '123 Test Street',
        $order['date'] . ' ' . date('H:i:s')
    ]);

    $orderId = $pdo->lastInsertId();

    // Insert order item
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, item_id, quantity, price, subtotal
        ) VALUES (
            ?, ?, ?, ?, ?
        )
    ");

    $itemPrice = $order['total'] - 100;
    $stmt->execute([
        $orderId,
        $itemId,
        1,
        $itemPrice,
        $itemPrice
    ]);

    $orderCount++;
}

echo "Successfully added $orderCount test orders with different statuses and revenue values.<br>";
echo "<a href='admin/index.php'>Go to Dashboard</a>";
