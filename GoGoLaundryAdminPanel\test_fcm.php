<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FCM Test - GoGoLaundry Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Firebase Cloud Messaging Test</h3>
                    </div>
                    <div class="card-body">
                        <div id="status" class="alert alert-info">
                            Initializing FCM...
                        </div>
                        
                        <div class="mb-3">
                            <label for="fcmToken" class="form-label">FCM Token:</label>
                            <textarea id="fcmToken" class="form-control" rows="3" readonly placeholder="FCM token will appear here..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <button id="requestPermission" class="btn btn-primary">Request Notification Permission</button>
                            <button id="testNotification" class="btn btn-success" disabled>Send Test Notification</button>
                        </div>
                        
                        <div class="mb-3">
                            <h5>Test Notification Form</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" id="testTitle" class="form-control mb-2" placeholder="Notification Title" value="Test Order">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" id="testMessage" class="form-control mb-2" placeholder="Notification Message" value="New order #12345 received">
                                </div>
                            </div>
                            <button id="sendTestFCM" class="btn btn-warning">Send FCM Test</button>
                        </div>
                        
                        <div id="logs" class="mt-4">
                            <h5>Logs:</h5>
                            <div id="logContainer" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                                <!-- Logs will appear here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyASl3UlvsWsfylrHNHLWUOxc2Lcln6PI0g",
            authDomain: "gogolaundry-c4dd1.firebaseapp.com",
            projectId: "gogolaundry-c4dd1",
            storageBucket: "gogolaundry-c4dd1.firebasestorage.app",
            messagingSenderId: "523301621504",
            appId: "1:523301621504:android:59b7b8816b0e2b7604ede5"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // VAPID key
        const vapidKey = "BKxvxhk6f0JTzuykemBWTpCe4kFBmHiMmwOKONgHuOhpeZmZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ";

        // DOM elements
        const statusDiv = document.getElementById('status');
        const fcmTokenTextarea = document.getElementById('fcmToken');
        const requestPermissionBtn = document.getElementById('requestPermission');
        const testNotificationBtn = document.getElementById('testNotification');
        const sendTestFCMBtn = document.getElementById('sendTestFCM');
        const logContainer = document.getElementById('logContainer');

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `text-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'dark'}`;
            logEntry.innerHTML = `<small>[${timestamp}]</small> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[FCM Test] ${message}`);
        }

        // Update status
        function updateStatus(message, type = 'info') {
            statusDiv.className = `alert alert-${type}`;
            statusDiv.textContent = message;
            log(message, type);
        }

        // Request notification permission
        async function requestNotificationPermission() {
            try {
                log('Requesting notification permission...');
                
                const permission = await Notification.requestPermission();
                
                if (permission === 'granted') {
                    log('Notification permission granted!', 'success');
                    
                    // Get FCM token
                    const token = await messaging.getToken({ vapidKey: vapidKey });
                    
                    if (token) {
                        fcmTokenTextarea.value = token;
                        updateStatus('FCM token received successfully!', 'success');
                        testNotificationBtn.disabled = false;
                        
                        // Store token
                        localStorage.setItem('fcm_token', token);
                        
                        log('FCM Token: ' + token.substring(0, 50) + '...', 'success');
                        
                        // Register with server
                        await registerTokenWithServer(token);
                    } else {
                        updateStatus('Failed to get FCM token', 'danger');
                    }
                } else {
                    updateStatus('Notification permission denied', 'warning');
                }
            } catch (error) {
                updateStatus('Error requesting permission: ' + error.message, 'danger');
            }
        }

        // Register token with server
        async function registerTokenWithServer(token) {
            try {
                log('Registering token with server...');
                
                const response = await fetch('api/fcm/register_token.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 1, // Test admin ID
                        token: token,
                        device_id: 'web_test_' + Date.now(),
                        device_type: 'web'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('Token registered with server successfully!', 'success');
                } else {
                    log('Failed to register token with server: ' + result.message, 'error');
                }
            } catch (error) {
                log('Error registering token with server: ' + error.message, 'error');
            }
        }

        // Send test notification
        function sendTestNotification() {
            if (Notification.permission === 'granted') {
                const notification = new Notification('Test Notification', {
                    body: 'This is a test notification from GoGoLaundry Admin Panel',
                    icon: '/GoGoLaundry/GoGoLaundryAdminPanel/assets/images/logo.png',
                    badge: '/GoGoLaundry/GoGoLaundryAdminPanel/assets/images/badge.png'
                });
                
                notification.onclick = function() {
                    window.focus();
                    notification.close();
                };
                
                log('Test notification sent!', 'success');
            } else {
                log('Notification permission not granted', 'error');
            }
        }

        // Send FCM test notification
        async function sendTestFCMNotification() {
            try {
                const title = document.getElementById('testTitle').value || 'Test Notification';
                const message = document.getElementById('testMessage').value || 'This is a test FCM notification';
                
                log('Sending FCM test notification...');
                
                const response = await fetch('api/fcm/send_notification.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        message: message,
                        type: 'system',
                        data: {
                            test: true,
                            timestamp: Date.now()
                        }
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('FCM test notification sent successfully!', 'success');
                } else {
                    log('Failed to send FCM test notification: ' + result.message, 'error');
                }
            } catch (error) {
                log('Error sending FCM test notification: ' + error.message, 'error');
            }
        }

        // Handle foreground messages
        messaging.onMessage((payload) => {
            log('Foreground message received: ' + JSON.stringify(payload), 'success');
            
            const { title, body } = payload.notification || {};
            
            if (title && body) {
                // Show browser notification
                if (Notification.permission === 'granted') {
                    new Notification(title, {
                        body: body,
                        icon: '/GoGoLaundry/GoGoLaundryAdminPanel/assets/images/logo.png'
                    });
                }
            }
        });

        // Event listeners
        requestPermissionBtn.addEventListener('click', requestNotificationPermission);
        testNotificationBtn.addEventListener('click', sendTestNotification);
        sendTestFCMBtn.addEventListener('click', sendTestFCMNotification);

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('FCM Test page loaded');
            updateStatus('Ready to test FCM functionality');
            
            // Check if permission is already granted
            if (Notification.permission === 'granted') {
                log('Notification permission already granted');
                requestNotificationPermission();
            }
        });
    </script>
</body>
</html>
