// Firebase configuration for GoGoLaundry Admin Panel
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyASl3UlvsWsfylrHNHLWUOxc2Lcln6PI0g",
  authDomain: "gogolaundry-c4dd1.firebaseapp.com",
  projectId: "gogolaundry-c4dd1",
  storageBucket: "gogolaundry-c4dd1.firebasestorage.app",
  messagingSenderId: "523301621504",
  appId: "1:523301621504:android:59b7b8816b0e2b7604ede5"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = getMessaging(app);

// VAPID key for web push notifications
const vapidKey = "BKxvxhk6f0JTzuykemBWTpCe4kFBmHiMmwOKONgHuOhpeZmZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ";

/**
 * Request notification permission and get FCM token
 */
export async function requestNotificationPermission() {
  try {
    console.log('Requesting notification permission...');
    
    // Request permission
    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      console.log('Notification permission granted.');
      
      // Get FCM token
      const token = await getToken(messaging, { vapidKey: vapidKey });
      
      if (token) {
        console.log('FCM Token:', token);
        
        // Store token in localStorage
        localStorage.setItem('fcm_token', token);
        
        // Register token with server
        await registerTokenWithServer(token);
        
        return token;
      } else {
        console.log('No registration token available.');
        return null;
      }
    } else {
      console.log('Unable to get permission to notify.');
      return null;
    }
  } catch (error) {
    console.error('An error occurred while retrieving token:', error);
    return null;
  }
}

/**
 * Register FCM token with server
 */
async function registerTokenWithServer(token) {
  try {
    const deviceId = getDeviceId();
    const adminId = getAdminId(); // You'll need to implement this based on your admin session
    
    if (!adminId) {
      console.log('Admin not logged in, skipping token registration');
      return;
    }
    
    const response = await fetch('api/fcm/register_token.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: adminId,
        token: token,
        device_id: deviceId,
        device_type: 'web'
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('FCM token registered successfully with server');
    } else {
      console.error('Failed to register FCM token with server:', result.message);
    }
  } catch (error) {
    console.error('Error registering FCM token with server:', error);
  }
}

/**
 * Get or generate device ID
 */
function getDeviceId() {
  let deviceId = localStorage.getItem('device_id');
  if (!deviceId) {
    deviceId = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    localStorage.setItem('device_id', deviceId);
  }
  return deviceId;
}

/**
 * Get admin ID from session (implement based on your admin session management)
 */
function getAdminId() {
  // This should be implemented based on how you manage admin sessions
  // For example, you might have it in a cookie, localStorage, or session variable
  // Return the admin user ID or null if not logged in
  
  // Example implementation (adjust based on your actual session management):
  const adminData = localStorage.getItem('admin_data');
  if (adminData) {
    try {
      const admin = JSON.parse(adminData);
      return admin.id;
    } catch (e) {
      console.error('Error parsing admin data:', e);
    }
  }
  return null;
}

/**
 * Handle foreground messages
 */
export function setupForegroundMessageHandler() {
  onMessage(messaging, (payload) => {
    console.log('Message received in foreground:', payload);
    
    const { title, body, icon } = payload.notification || {};
    const data = payload.data || {};
    
    // Show notification
    showNotification(title || 'New Notification', body || 'You have a new notification', {
      icon: icon || '/assets/images/logo.png',
      badge: '/assets/images/badge.png',
      data: data,
      requireInteraction: true,
      actions: [
        {
          action: 'view',
          title: 'View Details'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ]
    });
    
    // Play notification sound
    playNotificationSound();
    
    // Handle specific notification types
    handleNotificationData(data);
  });
}

/**
 * Show browser notification
 */
function showNotification(title, body, options = {}) {
  if ('serviceWorker' in navigator && 'Notification' in window) {
    navigator.serviceWorker.ready.then(registration => {
      registration.showNotification(title, {
        body: body,
        icon: options.icon || '/assets/images/logo.png',
        badge: options.badge || '/assets/images/badge.png',
        data: options.data || {},
        requireInteraction: options.requireInteraction || false,
        actions: options.actions || [],
        vibrate: [200, 100, 200],
        tag: 'gogolaundry-notification'
      });
    });
  } else {
    // Fallback for browsers that don't support service workers
    new Notification(title, {
      body: body,
      icon: options.icon || '/assets/images/logo.png'
    });
  }
}

/**
 * Play notification sound
 */
function playNotificationSound() {
  try {
    const audio = new Audio('/assets/sounds/notification.mp3');
    audio.volume = 0.5;
    audio.play().catch(e => {
      console.log('Could not play notification sound:', e);
    });
  } catch (error) {
    console.log('Error playing notification sound:', error);
  }
}

/**
 * Handle notification data based on type
 */
function handleNotificationData(data) {
  const type = data.type;
  
  switch (type) {
    case 'new_order':
      // Handle new order notification
      handleNewOrderNotification(data);
      break;
    case 'order_status':
      // Handle order status update
      handleOrderStatusNotification(data);
      break;
    case 'user_registration':
      // Handle new user registration
      handleUserRegistrationNotification(data);
      break;
    default:
      console.log('Unknown notification type:', type);
  }
}

/**
 * Handle new order notification
 */
function handleNewOrderNotification(data) {
  console.log('New order received:', data);
  
  // Update order count badge if exists
  updateOrderBadge();
  
  // Refresh orders table if on orders page
  if (window.location.pathname.includes('orders') || window.location.pathname.includes('index')) {
    setTimeout(() => {
      location.reload();
    }, 1000);
  }
}

/**
 * Handle order status notification
 */
function handleOrderStatusNotification(data) {
  console.log('Order status updated:', data);
  
  // Refresh orders table if on orders page
  if (window.location.pathname.includes('orders')) {
    setTimeout(() => {
      location.reload();
    }, 1000);
  }
}

/**
 * Handle user registration notification
 */
function handleUserRegistrationNotification(data) {
  console.log('New user registered:', data);
  
  // Update user count badge if exists
  updateUserBadge();
  
  // Refresh users table if on users page
  if (window.location.pathname.includes('users')) {
    setTimeout(() => {
      location.reload();
    }, 1000);
  }
}

/**
 * Update order count badge
 */
function updateOrderBadge() {
  // Implement based on your UI structure
  const badge = document.querySelector('.order-badge');
  if (badge) {
    const currentCount = parseInt(badge.textContent) || 0;
    badge.textContent = currentCount + 1;
  }
}

/**
 * Update user count badge
 */
function updateUserBadge() {
  // Implement based on your UI structure
  const badge = document.querySelector('.user-badge');
  if (badge) {
    const currentCount = parseInt(badge.textContent) || 0;
    badge.textContent = currentCount + 1;
  }
}

// Export messaging instance for use in other files
export { messaging };
