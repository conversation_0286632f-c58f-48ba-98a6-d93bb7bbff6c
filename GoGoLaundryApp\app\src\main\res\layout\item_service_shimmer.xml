<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="8dp">

    <!-- Service Icon Shimmer -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginBottom="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:cardBackgroundColor="@color/shimmer_placeholder"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shimmer_background" />

    </com.google.android.material.card.MaterialCardView>

    <!-- Service Name Shimmer -->
    <View
        android:layout_width="50dp"
        android:layout_height="12dp"
        android:background="@drawable/shimmer_background" />

</LinearLayout>
