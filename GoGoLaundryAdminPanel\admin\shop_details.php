<?php
// Include authentication middleware
require_once 'auth.php';

$shop_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($shop_id <= 0) {
    $_SESSION['error_message'] = "Invalid shop ID.";
    header('Location: shops.php');
    exit();
}

// Get shop details
$sql = "
    SELECT
        ls.*,
        d.name as division_name,
        dist.name as district_name,
        up.name as upazilla_name,
        COUNT(DISTINCT o.id) as total_orders,
        COUNT(DISTINCT CASE WHEN o.status = 'delivered' THEN o.id END) as completed_orders,
        COALESCE(SUM(CASE WHEN o.status = 'delivered' THEN o.total END), 0) as total_revenue,
        COALESCE(AVG(CASE WHEN o.status = 'delivered' THEN o.total END), 0) as avg_order_value
    FROM laundry_shops ls
    LEFT JOIN divisions d ON ls.division_id = d.id
    LEFT JOIN districts dist ON ls.district_id = dist.id
    LEFT JOIN upazillas up ON ls.upazilla_id = up.id
    LEFT JOIN orders o ON ls.id = o.shop_id
    WHERE ls.id = ?
    GROUP BY ls.id
";

$stmt = $pdo->prepare($sql);
$stmt->execute([$shop_id]);
$shop = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$shop) {
    $_SESSION['error_message'] = "Shop not found.";
    header('Location: shops.php');
    exit();
}

// Get shop services
$servicesSql = "
    SELECT s.id, s.name, s.bn_name, s.image_url, ss.estimated_hours, ss.is_available
    FROM shop_services ss
    INNER JOIN services s ON ss.service_id = s.id
    WHERE ss.shop_id = ?
    ORDER BY s.sort_order ASC
";
$servicesStmt = $pdo->prepare($servicesSql);
$servicesStmt->execute([$shop_id]);
$services = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent orders
$ordersSql = "
    SELECT o.*, u.full_name as customer_name
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.id
    WHERE o.shop_id = ?
    ORDER BY o.created_at DESC
    LIMIT 10
";
$ordersStmt = $pdo->prepare($ordersSql);
$ordersStmt->execute([$shop_id]);
$recent_orders = $ordersStmt->fetchAll(PDO::FETCH_ASSOC);

$pageTitle = 'Shop Details - ' . htmlspecialchars($shop['name']);
$currentPage = 'shops';

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <a href="shops.php" class="text-decoration-none me-2">
            <i class="fas fa-arrow-left"></i>
        </a>
        Shop Details
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="edit_shop.php?id=<?= $shop['id'] ?>" class="btn btn-primary me-2">
            <i class="fas fa-edit"></i> Edit Shop
        </a>
        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
            <i class="fas fa-print"></i> Print
        </button>
    </div>
</div>

            <!-- Shop Information -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Shop Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Shop Name:</td>
                                            <td><?= htmlspecialchars($shop['name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Owner Name:</td>
                                            <td><?= htmlspecialchars($shop['owner_name']) ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Phone:</td>
                                            <td>
                                                <a href="tel:<?= htmlspecialchars($shop['phone']) ?>">
                                                    <?= htmlspecialchars($shop['phone']) ?>
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Email:</td>
                                            <td>
                                                <?php if ($shop['email']): ?>
                                                    <a href="mailto:<?= htmlspecialchars($shop['email']) ?>">
                                                        <?= htmlspecialchars($shop['email']) ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">Not provided</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Address:</td>
                                            <td><?= htmlspecialchars($shop['address']) ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold">Location:</td>
                                            <td>
                                                <?= htmlspecialchars($shop['upazilla_name'] ?? 'N/A') ?>,
                                                <?= htmlspecialchars($shop['district_name'] ?? 'N/A') ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Coordinates:</td>
                                            <td>
                                                <?= number_format($shop['latitude'], 6) ?>,
                                                <?= number_format($shop['longitude'], 6) ?>
                                                <a href="https://maps.google.com/?q=<?= $shop['latitude'] ?>,<?= $shop['longitude'] ?>"
                                                   target="_blank" class="ms-2">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Rating:</td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-star"></i> <?= number_format($shop['rating'], 1) ?>
                                                </span>
                                                <small class="text-muted">(<?= $shop['total_reviews'] ?> reviews)</small>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Commission:</td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($shop['commission_percentage'], 1) ?>%</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold">Status:</td>
                                            <td>
                                                <?php if ($shop['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>

                                                <?php if ($shop['is_verified']): ?>
                                                    <span class="badge bg-primary">Verified</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Unverified</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Statistics -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary"><?= number_format($shop['total_orders']) ?></h4>
                                        <small class="text-muted">Total Orders</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success"><?= number_format($shop['completed_orders']) ?></h4>
                                    <small class="text-muted">Completed</small>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-info">৳<?= number_format($shop['total_revenue'], 0) ?></h4>
                                        <small class="text-muted">Total Revenue</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-warning">৳<?= number_format($shop['avg_order_value'], 0) ?></h4>
                                    <small class="text-muted">Avg Order</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Map Preview -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Location Map</h5>
                        </div>
                        <div class="card-body p-0">
                            <div style="height: 200px; background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                                <div class="text-center">
                                    <i class="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                                    <br>
                                    <a href="https://maps.google.com/?q=<?= $shop['latitude'] ?>,<?= $shop['longitude'] ?>"
                                       target="_blank" class="btn btn-sm btn-primary">
                                        View on Google Maps
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Services Offered (<?= count($services) ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($services)): ?>
                        <p class="text-muted">No services configured for this shop.</p>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($services as $service): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="card h-100">
                                        <div class="card-body text-center">
                                            <?php if ($service['image_url']): ?>
                                                <img src="<?= htmlspecialchars($service['image_url']) ?>"
                                                     class="mb-2" width="40" height="40">
                                            <?php endif; ?>
                                            <h6 class="card-title"><?= htmlspecialchars($service['name']) ?></h6>
                                            <small class="text-muted">
                                                Est. <?= $service['estimated_hours'] ?> hours
                                            </small>
                                            <br>
                                            <?php if ($service['is_available']): ?>
                                                <span class="badge bg-success">Available</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Unavailable</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Recent Orders (<?= count($recent_orders) ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recent_orders)): ?>
                        <p class="text-muted">No orders found for this shop.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_orders as $order): ?>
                                        <tr>
                                            <td>#<?= $order['id'] ?></td>
                                            <td><?= htmlspecialchars($order['customer_name'] ?? 'N/A') ?></td>
                                            <td>৳<?= number_format($order['total'], 2) ?></td>
                                            <td>
                                                <span class="badge bg-<?= getStatusColor($order['status']) ?>">
                                                    <?= ucfirst($order['status']) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M j, Y', strtotime($order['created_at'])) ?></td>
                                            <td>
                                                <a href="order_details.php?id=<?= $order['id'] ?>"
                                                   class="btn btn-sm btn-outline-primary">
                                                    View
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <a href="orders.php?shop_id=<?= $shop['id'] ?>" class="btn btn-primary">
                                View All Orders
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>

<?php
function getStatusColor($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'confirmed': return 'info';
        case 'processing': return 'primary';
        case 'ready': return 'success';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}
?>
