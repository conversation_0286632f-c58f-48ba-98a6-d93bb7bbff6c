package com.mdsadrulhasan.gogolaundry.repository;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.mdsadrulhasan.gogolaundry.data.Resource;

/**
 * Repository for handling location services
 */
public class LocationRepository {

    private static final String TAG = "LocationRepository";
    private static LocationRepository instance;
    
    private final Context context;
    private final FusedLocationProviderClient fusedLocationClient;
    private final MutableLiveData<Resource<Location>> currentLocation = new MutableLiveData<>();

    private LocationRepository(Context context) {
        this.context = context.getApplicationContext();
        this.fusedLocationClient = LocationServices.getFusedLocationProviderClient(this.context);
    }

    /**
     * Get singleton instance
     */
    public static synchronized LocationRepository getInstance(Context context) {
        if (instance == null) {
            instance = new LocationRepository(context);
        }
        return instance;
    }

    /**
     * Get current location
     */
    public LiveData<Resource<Location>> getCurrentLocation() {
        getCurrentLocationInternal();
        return currentLocation;
    }

    /**
     * Get current location synchronously (for immediate use)
     */
    public void getCurrentLocationInternal() {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) 
                != PackageManager.PERMISSION_GRANTED && 
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) 
                != PackageManager.PERMISSION_GRANTED) {
            
            currentLocation.setValue(Resource.error("Location permission not granted", null));
            return;
        }

        currentLocation.setValue(Resource.loading(null));

        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        Log.d(TAG, "Location obtained: " + location.getLatitude() + ", " + location.getLongitude());
                        currentLocation.setValue(Resource.success(location));
                    } else {
                        Log.w(TAG, "Location is null, requesting fresh location");
                        requestFreshLocation();
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Failed to get location", e);
                    currentLocation.setValue(Resource.error("Failed to get location: " + e.getMessage(), null));
                });
    }

    /**
     * Request fresh location when last known location is null
     */
    private void requestFreshLocation() {
        // For now, we'll use a default location (Dhaka, Bangladesh)
        // In a production app, you might want to implement location request with callbacks
        Location defaultLocation = new Location("default");
        defaultLocation.setLatitude(23.8103);
        defaultLocation.setLongitude(90.4125);
        
        Log.d(TAG, "Using default location: Dhaka, Bangladesh");
        currentLocation.setValue(Resource.success(defaultLocation));
    }

    /**
     * Calculate distance between two points in kilometers
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // Radius of the earth in km

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = R * c; // Distance in km

        return Math.round(distance * 10.0) / 10.0; // Round to 1 decimal place
    }

    /**
     * Check if location permissions are granted
     */
    public boolean hasLocationPermission() {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) 
                == PackageManager.PERMISSION_GRANTED ||
               ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) 
                == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Get address from coordinates (placeholder for geocoding)
     */
    public String getAddressFromCoordinates(double latitude, double longitude) {
        // This is a placeholder. In a real app, you would use Geocoder
        // or a reverse geocoding API to get the actual address
        return String.format("%.4f, %.4f", latitude, longitude);
    }

    /**
     * Check if a point is within a certain radius of another point
     */
    public static boolean isWithinRadius(double lat1, double lon1, double lat2, double lon2, double radiusKm) {
        double distance = calculateDistance(lat1, lon1, lat2, lon2);
        return distance <= radiusKm;
    }

    /**
     * Get bounding box coordinates for a given center point and radius
     */
    public static class BoundingBox {
        public final double minLat;
        public final double maxLat;
        public final double minLng;
        public final double maxLng;

        public BoundingBox(double minLat, double maxLat, double minLng, double maxLng) {
            this.minLat = minLat;
            this.maxLat = maxLat;
            this.minLng = minLng;
            this.maxLng = maxLng;
        }
    }

    /**
     * Calculate bounding box for a given center point and radius in kilometers
     */
    public static BoundingBox getBoundingBox(double centerLat, double centerLng, double radiusKm) {
        // Approximate conversion: 1 degree ≈ 111 km
        double latDelta = radiusKm / 111.0;
        double lngDelta = radiusKm / (111.0 * Math.cos(Math.toRadians(centerLat)));

        return new BoundingBox(
                centerLat - latDelta,
                centerLat + latDelta,
                centerLng - lngDelta,
                centerLng + lngDelta
        );
    }
}
