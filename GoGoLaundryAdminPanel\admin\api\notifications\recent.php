<?php
/**
 * Get Recent Notifications API
 * 
 * This endpoint returns recent notifications since a given timestamp
 * 
 * Parameters:
 * - since: Timestamp to get notifications since (optional)
 * 
 * Returns:
 * - success: true/false
 * - data: Array of recent notifications
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Include required files
require_once '../../../config/db.php';
require_once '../../../includes/functions.php';

// Start session
session_start();

// Initialize response
$response = [
    'success' => false,
    'message' => 'Unknown error occurred',
    'data' => []
];

try {
    // Check if admin is logged in
    if (!isset($_SESSION['admin_id']) || empty($_SESSION['admin_id'])) {
        $response['message'] = 'Admin not logged in';
        echo json_encode($response);
        exit();
    }
    
    // Get parameters
    $since = isset($_GET['since']) ? intval($_GET['since']) : (time() - 3600); // Default to last hour
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 10;
    
    // Convert timestamp to datetime
    $sinceDate = date('Y-m-d H:i:s', $since / 1000); // Convert from milliseconds
    
    // Get recent notifications
    $stmt = $pdo->prepare("
        SELECT n.*, o.order_number, u.full_name as customer_name
        FROM notifications n
        LEFT JOIN orders o ON n.order_id = o.id
        LEFT JOIN users u ON n.user_id = u.id
        WHERE n.created_at > ?
        AND n.type IN ('new_order', 'order_status', 'user_registration', 'system')
        ORDER BY n.created_at DESC
        LIMIT ?
    ");
    
    $stmt->execute([$sinceDate, $limit]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format notifications for frontend
    $formattedNotifications = [];
    foreach ($notifications as $notification) {
        $formattedNotifications[] = [
            'id' => $notification['id'],
            'title' => $notification['title'],
            'message' => $notification['message'],
            'type' => $notification['type'],
            'order_id' => $notification['order_id'],
            'order_number' => $notification['order_number'],
            'customer_name' => $notification['customer_name'],
            'created_at' => $notification['created_at'],
            'is_read' => $notification['is_read']
        ];
    }
    
    $response['success'] = true;
    $response['message'] = 'Recent notifications retrieved successfully';
    $response['data'] = $formattedNotifications;
    
} catch (Exception $e) {
    $response['message'] = 'Server error: ' . $e->getMessage();
    error_log('Get Recent Notifications Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
?>
