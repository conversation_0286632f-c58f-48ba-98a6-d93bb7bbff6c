package com.mdsadrulhasan.gogolaundry.repository;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.GoGoLaundryApp;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.database.dao.ServiceDao;
import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;
import com.mdsadrulhasan.gogolaundry.model.Service;
import com.mdsadrulhasan.gogolaundry.utils.AppExecutors;
import com.mdsadrulhasan.gogolaundry.utils.Resource;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Repository for service-related operations
 */
public class ServiceRepository {
    private static final String TAG = "ServiceRepository";

    private static ServiceRepository instance;

    private final ServiceDao serviceDao;
    private final ApiService apiService;
    private final AppExecutors executors;

    private ServiceRepository() {
        serviceDao = GoGoLaundryApp.getInstance().getDatabase().serviceDao();
        apiService = ApiClient.getApiService(GoGoLaundryApp.getInstance());
        executors = AppExecutors.getInstance();
    }

    /**
     * Get repository instance
     *
     * @return Repository instance
     */
    public static synchronized ServiceRepository getInstance() {
        if (instance == null) {
            instance = new ServiceRepository();
        }
        return instance;
    }

    /**
     * Get all services
     *
     * @param forceRefresh Whether to force refresh from network
     * @return LiveData of services
     */
    public LiveData<Resource<List<ServiceEntity>>> getAllServices(boolean forceRefresh) {
        return getAllServices(forceRefresh, false);
    }

    /**
     * Get all active services
     *
     * @param forceRefresh Whether to force refresh from network
     * @return LiveData of active services
     */
    public LiveData<Resource<List<ServiceEntity>>> getAllActiveServices(boolean forceRefresh) {
        return getAllServices(forceRefresh, true);
    }

    /**
     * Get all services
     *
     * @param forceRefresh Whether to force refresh from network
     * @param activeOnly Whether to get only active services
     * @return LiveData of services
     */
    private LiveData<Resource<List<ServiceEntity>>> getAllServices(boolean forceRefresh, boolean activeOnly) {
        MutableLiveData<Resource<List<ServiceEntity>>> result = new MutableLiveData<>();

        // First, load from database
        executors.diskIO().execute(() -> {
            List<ServiceEntity> services = activeOnly ?
                    serviceDao.getAllActiveServices() :
                    serviceDao.getAllServices();

            executors.mainThread().execute(() -> {
                if (services.isEmpty() || forceRefresh) {
                    // If database is empty or force refresh, load from network
                    fetchServicesFromNetwork(result, activeOnly);
                } else {
                    // Otherwise, return data from database
                    result.setValue(Resource.success(services));
                }
            });
        });

        return result;
    }

    /**
     * Fetch services from network
     *
     * @param result Result LiveData
     * @param activeOnly Whether to get only active services
     */
    private void fetchServicesFromNetwork(MutableLiveData<Resource<List<ServiceEntity>>> result, boolean activeOnly) {
        result.setValue(Resource.loading(null));

        // Make API call to get services
        Call<ApiResponse<List<Service>>> call = apiService.getServices(activeOnly ? 1 : 0);

        call.enqueue(new Callback<ApiResponse<List<Service>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<Service>>> call, Response<ApiResponse<List<Service>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<Service> serviceModels = response.body().getData();

                    if (serviceModels != null && !serviceModels.isEmpty()) {
                        // Convert API models to database entities
                        List<ServiceEntity> serviceEntities = convertToServiceEntities(serviceModels);

                        // Save to database
                        executors.diskIO().execute(() -> {
                            serviceDao.insertAll(serviceEntities);

                            executors.mainThread().execute(() -> {
                                result.setValue(Resource.success(serviceEntities));
                            });
                        });
                    } else {
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.success(new ArrayList<>()));
                        });
                    }
                } else {
                    String errorMsg = "Failed to load services";
                    if (response.body() != null) {
                        errorMsg = response.body().getMessage();
                    }
                    final String finalErrorMsg = errorMsg;
                    executors.mainThread().execute(() -> {
                        result.setValue(Resource.error(finalErrorMsg, null));
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<Service>>> call, Throwable t) {
                executors.mainThread().execute(() -> {
                    result.setValue(Resource.error("Network error: " + t.getMessage(), null));
                });
            }
        });
    }

    /**
     * Convert Service models to ServiceEntity objects
     *
     * @param services List of Service models from API
     * @return List of ServiceEntity objects for database
     */
    private List<ServiceEntity> convertToServiceEntities(List<Service> services) {
        List<ServiceEntity> serviceEntities = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);

        for (Service service : services) {
            ServiceEntity entity = new ServiceEntity();
            entity.setId(service.getId());
            entity.setName(service.getName());
            entity.setDescription(service.getDescription());
            entity.setImageUrl(service.getIconUrl());

            // Handle is_active which might be a number (1/0) or boolean
            if (service.getIsActiveRaw() != null) {
                // If we have the raw value (which could be a number), use it
                entity.setActive(service.getIsActiveRaw() == 1);
            } else {
                // Fall back to the boolean method
                entity.setActive(service.isActive());
            }

            entity.setSortOrder(0); // Default sort order

            // Parse dates if available
            try {
                if (service.getCreatedAt() != null && !service.getCreatedAt().isEmpty()) {
                    entity.setCreatedAt(dateFormat.parse(service.getCreatedAt()));
                } else {
                    entity.setCreatedAt(new Date());
                }

                if (service.getUpdatedAt() != null && !service.getUpdatedAt().isEmpty()) {
                    entity.setUpdatedAt(dateFormat.parse(service.getUpdatedAt()));
                } else {
                    entity.setUpdatedAt(new Date());
                }
            } catch (ParseException e) {
                // Use current date if parsing fails
                entity.setCreatedAt(new Date());
                entity.setUpdatedAt(new Date());
            }

            serviceEntities.add(entity);
        }

        return serviceEntities;
    }
}
