package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mdsadrulhasan.gogolaundry.GoGoLaundryApp;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.OrderTrackingResponse;
import com.mdsadrulhasan.gogolaundry.api.deserializer.OrderDeserializer;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.model.OrderStatusHistory;
import com.mdsadrulhasan.gogolaundry.repository.OrderRepository;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Manager class for order tracking functionality
 */
public class OrderTrackingManager {
    private static final String TAG = "OrderTrackingManager";

    private static OrderTrackingManager instance;
    private final ApiService apiService;
    private final OrderRepository orderRepository;
    private final AppExecutors executors;

    private OrderTrackingManager() {
        // Create a custom Gson instance with our Order deserializer
        Gson gson = new GsonBuilder()
                .registerTypeAdapter(Order.class, new OrderDeserializer())
                .setLenient()
                .create();

        // Create a custom Retrofit instance with our Gson
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(ApiClient.getBaseUrl())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .client(ApiClient.getOkHttpClient(GoGoLaundryApp.getInstance()))
                .build();

        // Create API service with our custom Retrofit
        apiService = retrofit.create(ApiService.class);

        // Initialize other dependencies
        orderRepository = OrderRepository.getInstance();
        executors = AppExecutors.getInstance();

        Log.d(TAG, "OrderTrackingManager initialized with custom Order deserializer");
    }

    /**
     * Get instance of OrderTrackingManager
     *
     * @return OrderTrackingManager instance
     */
    public static synchronized OrderTrackingManager getInstance() {
        if (instance == null) {
            instance = new OrderTrackingManager();
        }
        return instance;
    }

    /**
     * Get order status
     *
     * @param orderId Order ID
     * @return LiveData of order status resource
     */
    public LiveData<Resource<OrderEntity>> getOrderStatus(int orderId) {
        return orderRepository.getOrderById(orderId, true);
    }

    /**
     * Get order status history
     *
     * @param orderId Order ID
     * @return LiveData of order status history resource
     */
    public LiveData<Resource<List<OrderStatusHistory>>> getOrderStatusHistory(int orderId) {
        MutableLiveData<Resource<List<OrderStatusHistory>>> result = new MutableLiveData<>();
        result.setValue(Resource.loading(null));

        // Make API call to get order status history
        Call<ApiResponse<List<OrderStatusHistory>>> call = apiService.getOrderStatusHistory(orderId);
        call.enqueue(new Callback<ApiResponse<List<OrderStatusHistory>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<OrderStatusHistory>>> call,
                                  Response<ApiResponse<List<OrderStatusHistory>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<OrderStatusHistory> history = response.body().getData();
                    result.setValue(Resource.success(history));

                    // Show success toast
                    ToastUtils.showInfoToast(getContext(), "Order tracking history loaded");
                } else {
                    // API error
                    String errorMessage = "Failed to load order history";
                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    }
                    result.setValue(Resource.error(errorMessage, null));

                    // Show error toast
                    ToastUtils.showErrorToast(getContext(), errorMessage);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<OrderStatusHistory>>> call, Throwable t) {
                // Network error
                String errorMessage = "Network error: " + t.getMessage();

                // Simplify error message for common network issues
                if (t.getMessage() != null && t.getMessage().contains("Unable to resolve host")) {
                    errorMessage = "No internet connection. Please check your network settings.";
                }

                result.setValue(Resource.error(errorMessage, null));

                // Show error toast
                ToastUtils.showErrorToast(getContext(), errorMessage);
            }
        });

        return result;
    }

    /**
     * Track order by tracking number
     *
     * @param trackingNumber Order tracking number
     * @return LiveData of order resource
     */
    public LiveData<Resource<Order>> trackOrderByTrackingNumber(String trackingNumber) {
        MutableLiveData<Resource<Order>> result = new MutableLiveData<>();
        result.setValue(Resource.loading(null));

        // Make API call to track order
        Call<ApiResponse<OrderTrackingResponse>> call = apiService.trackOrder(trackingNumber);
        call.enqueue(new Callback<ApiResponse<OrderTrackingResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<OrderTrackingResponse>> call, Response<ApiResponse<OrderTrackingResponse>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    OrderTrackingResponse trackingResponse = response.body().getData();

                    if (trackingResponse != null && trackingResponse.getOrder() != null) {
                        Order order = trackingResponse.getOrder();

                        // Log the order details for debugging
                        Log.d(TAG, "Order tracking successful: " + order.getOrderNumber() + ", Status: " + order.getStatus());

                        result.setValue(Resource.success(order));

                        // Show success toast with status
                        String status = order.getStatus();
                        String orderNumber = order.getOrderNumber();
                        ToastUtils.showOrderStatusToast(getContext(), status, orderNumber);
                    } else {
                        // No order found in response
                        Log.e(TAG, "Order tracking response is missing order data");
                        result.setValue(Resource.error("Order tracking response is missing order data", null));
                        ToastUtils.showWarningToast(getContext(), "Invalid order data received");
                    }
                } else {
                    // API error
                    String errorMessage = "Failed to track order";
                    int statusCode = response.code();

                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    }

                    // Special handling for 404 Not Found
                    if (statusCode == 404 || (errorMessage != null && errorMessage.contains("not found"))) {
                        errorMessage = "Order with tracking number " + trackingNumber + " not found";

                        // Show a more user-friendly toast
                        ToastUtils.showWarningToast(getContext(),
                            "Order not found. Please check the tracking number.");

                        // Try to find the order in local database
                        fallbackToLocalData(result, trackingNumber);
                    } else {
                        result.setValue(Resource.error(errorMessage, null));

                        // Show error toast
                        ToastUtils.showErrorToast(getContext(), errorMessage);
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<OrderTrackingResponse>> call, Throwable t) {
                // Network error
                String errorMessage = "Network error: " + t.getMessage();

                // Simplify error message for common network issues
                if (t.getMessage() != null && t.getMessage().contains("Unable to resolve host")) {
                    errorMessage = "No internet connection. Please check your network settings.";
                }

                // Log the error for debugging
                Log.e(TAG, "API Error: " + errorMessage, t);

                // Fallback to local data
                fallbackToLocalData(result, trackingNumber);
            }

            /**
             * Fallback to local data when API call fails
             *
             * @param result LiveData result to update
             * @param trackingNumber Order tracking number
             */
            private void fallbackToLocalData(MutableLiveData<Resource<Order>> result, String trackingNumber) {
                // Show info toast about using local data
                ToastUtils.showInfoToast(getContext(), "Using local data for tracking");

                // Get order from local database using OrderRepository
                executors.diskIO().execute(() -> {
                    // Try to find order by tracking number in local database
                    OrderEntity orderEntity = orderRepository.getOrderByTrackingNumber(trackingNumber);

                    if (orderEntity != null) {
                        // Convert entity to model
                        Order order = convertToOrderModel(orderEntity);

                        executors.mainThread().execute(() -> {
                            // Update result with local data
                            result.setValue(Resource.success(order));

                            // Show status toast
                            ToastUtils.showOrderStatusToast(getContext(),
                                order.getStatus(),
                                order.getOrderNumber());
                        });
                    } else {
                        // If not found by tracking number, try to find by order number
                        // (in case tracking number is actually an order number)
                        OrderEntity orderByNumber = orderRepository.getOrderByOrderNumber(trackingNumber);

                        if (orderByNumber != null) {
                            // Convert entity to model
                            Order order = convertToOrderModel(orderByNumber);

                            executors.mainThread().execute(() -> {
                                // Update result with local data
                                result.setValue(Resource.success(order));

                                // Show status toast
                                ToastUtils.showOrderStatusToast(getContext(),
                                    order.getStatus(),
                                    order.getOrderNumber());
                            });
                        } else {
                            // No local data found
                            executors.mainThread().execute(() -> {
                                result.setValue(Resource.error("Order not found", null));
                                ToastUtils.showErrorToast(getContext(), "Order not found in local database");
                            });
                        }
                    }
                });
            }
        });

        return result;
    }

    // Get context for toast notifications
    private Context getContext() {
        return GoGoLaundryApp.getInstance().getApplicationContext();
    }

    /**
     * Convert OrderEntity to Order model
     *
     * @param entity OrderEntity from database
     * @return Order model for UI
     */
    private Order convertToOrderModel(OrderEntity entity) {
        if (entity == null) return null;

        Order order = new Order();
        order.setId(entity.getId());
        order.setOrderNumber(entity.getOrderNumber());
        order.setTrackingNumber(entity.getTrackingNumber());
        order.setUserId(entity.getUserId());
        order.setTotalAmount(entity.getTotal());
        order.setStatus(entity.getStatus());
        order.setPaymentStatus(entity.getPaymentStatus());
        order.setPaymentMethod(entity.getPaymentMethod());

        // Format dates
        if (entity.getCreatedAt() != null) {
            order.setCreatedAt(entity.getCreatedAt().toString());
        }
        if (entity.getUpdatedAt() != null) {
            order.setUpdatedAt(entity.getUpdatedAt().toString());
        }

        // Set pickup and delivery dates
        if (entity.getPickupDate() != null) {
            order.setPickupDate(entity.getPickupDate().toString());
            order.setPickupTime(entity.getPickupTimeSlot());
        }
        if (entity.getDeliveryDate() != null) {
            order.setDeliveryDate(entity.getDeliveryDate().toString());
            order.setDeliveryTime(entity.getDeliveryTimeSlot());
        }

        // Set address
        order.setAddress(entity.getPickupAddress());
        order.setNotes(entity.getNotes());

        return order;
    }
}
