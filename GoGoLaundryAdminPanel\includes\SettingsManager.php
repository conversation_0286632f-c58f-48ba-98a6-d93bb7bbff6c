<?php
/**
 * Settings Manager
 *
 * This class handles system settings
 */
class SettingsManager {
    private $pdo;
    private $settings = [];
    private $loaded = false;

    /**
     * Constructor
     *
     * @param PDO $pdo PDO instance
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Load all settings from database
     *
     * @return array Settings
     */
    public function loadSettings() {
        if ($this->loaded) {
            return $this->settings;
        }

        $stmt = $this->pdo->prepare("SELECT setting_key, setting_value FROM settings");
        $stmt->execute();

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($results as $row) {
            $this->settings[$row['setting_key']] = $row['setting_value'];
        }

        $this->loaded = true;

        return $this->settings;
    }

    /**
     * Get a setting value
     *
     * @param string $key Setting key
     * @param mixed $default Default value if setting not found
     * @return mixed Setting value
     */
    public function getSetting($key, $default = null) {
        if (!$this->loaded) {
            $this->loadSettings();
        }

        return isset($this->settings[$key]) ? $this->settings[$key] : $default;
    }

    /**
     * Update a setting
     *
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool Success
     */
    public function updateSetting($key, $value) {
        // Convert value to string for storage
        $valueStr = (string)$value;

        // Log the update attempt
        error_log("Updating setting: $key with value: $valueStr");

        $stmt = $this->pdo->prepare("
            INSERT INTO settings (setting_key, setting_value)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE setting_value = ?
        ");

        $result = $stmt->execute([$key, $valueStr, $valueStr]);

        if ($result) {
            $this->settings[$key] = $valueStr;
            error_log("Setting updated successfully: $key = $valueStr");
        } else {
            error_log("Failed to update setting: $key. Error: " . implode(', ', $stmt->errorInfo()));
        }

        return $result;
    }

    /**
     * Check if OTP verification is enabled
     *
     * @return bool True if enabled, false otherwise
     */
    public function isOtpEnabled() {
        return (bool) $this->getSetting('otp_enabled', false);
    }

    /**
     * Get all settings
     *
     * @return array All settings
     */
    public function getAllSettings() {
        if (!$this->loaded) {
            $this->loadSettings();
        }

        return $this->settings;
    }
}
