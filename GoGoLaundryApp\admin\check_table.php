<?php
require_once 'includes/config.php';

echo "Checking promotional_dialogs table:\n";
$result = $conn->query('DESCRIBE promotional_dialogs');
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
} else {
    echo 'Error: ' . $conn->error . "\n";
}

echo "\nSample data:\n";
$result = $conn->query('SELECT id, title, discount_text, promo_code, is_active FROM promotional_dialogs LIMIT 1');
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    echo 'ID: ' . $row['id'] . "\n";
    echo 'Title: ' . $row['title'] . "\n";
    echo 'Discount: ' . $row['discount_text'] . "\n";
    echo 'Promo Code: ' . $row['promo_code'] . "\n";
    echo 'Active: ' . ($row['is_active'] ? 'Yes' : 'No') . "\n";
} else {
    echo 'No sample data found or error: ' . $conn->error . "\n";
}

$conn->close();
?>
