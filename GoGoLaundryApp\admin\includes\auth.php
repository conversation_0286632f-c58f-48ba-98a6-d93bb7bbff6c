<?php
/**
 * Authentication helper functions for admin panel
 */

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'admin';
}

/**
 * Get current admin user ID
 */
function getAdminUserId() {
    return $_SESSION['admin_user_id'] ?? null;
}

/**
 * Get current admin username
 */
function getAdminUsername() {
    return $_SESSION['admin_username'] ?? null;
}

/**
 * Login admin user
 */
function loginAdmin($userId, $username, $role = 'admin') {
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_user_id'] = $userId;
    $_SESSION['admin_username'] = $username;
    $_SESSION['admin_role'] = $role;
}

/**
 * Logout admin user
 */
function logoutAdmin() {
    unset($_SESSION['admin_logged_in']);
    unset($_SESSION['admin_user_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_role']);
}

/**
 * Require admin authentication
 */
function requireAdmin() {
    if (!isLoggedIn() || !isAdmin()) {
        header('Location: login.php');
        exit();
    }
}
?>
