package com.mdsadrulhasan.gogolaundry.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import com.google.gson.annotations.SerializedName;

/**
 * Model class for laundry items
 */
@Entity(tableName = "items")
public class Item {

    @PrimaryKey
    @SerializedName("id")
    private int id;

    @SerializedName("service_id")
    private int serviceId;

    @SerializedName("name")
    private String name;

    @SerializedName("bn_name")
    private String bnName;

    @SerializedName("description")
    private String description;

    @SerializedName("bn_description")
    private String bnDescription;

    @SerializedName("price")
    private double price;

    @SerializedName("image_url")
    private String imageUrl;

    @SerializedName("is_active")
    private Integer isActiveRaw;

    // This field is not directly mapped from JSON but set programmatically
    private boolean isActive;

    @SerializedName("in_stock")
    private Integer inStockRaw;

    // This field is not directly mapped from JSON but set programmatically
    private boolean inStock;

    @SerializedName("service_name")
    private String serviceName;

    @SerializedName("created_at")
    private String createdAt;

    @SerializedName("updated_at")
    private String updatedAt;

    // Default constructor required by Room
    public Item() {
    }

    // Constructor with all fields
    public Item(int id, int serviceId, String name, String bnName, String description,
                String bnDescription, double price, String imageUrl, boolean isActive,
                boolean inStock, String serviceName, String createdAt, String updatedAt) {
        this.id = id;
        this.serviceId = serviceId;
        this.name = name;
        this.bnName = bnName;
        this.description = description;
        this.bnDescription = bnDescription;
        this.price = price;
        this.imageUrl = imageUrl;
        this.isActive = isActive;
        this.isActiveRaw = isActive ? 1 : 0;
        this.inStock = inStock;
        this.inStockRaw = inStock ? 1 : 0;
        this.serviceName = serviceName;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getServiceId() {
        return serviceId;
    }

    public void setServiceId(int serviceId) {
        this.serviceId = serviceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBnName() {
        return bnName;
    }

    public void setBnName(String bnName) {
        this.bnName = bnName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBnDescription() {
        return bnDescription;
    }

    public void setBnDescription(String bnDescription) {
        this.bnDescription = bnDescription;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean isActive() {
        if (isActiveRaw != null) {
            return isActiveRaw == 1;
        }
        return isActive;
    }

    public void setActive(boolean active) {
        this.isActive = active;
        this.isActiveRaw = active ? 1 : 0;
    }

    public Integer getIsActiveRaw() {
        return isActiveRaw;
    }

    public void setIsActiveRaw(Integer isActiveRaw) {
        this.isActiveRaw = isActiveRaw;
        if (isActiveRaw != null) {
            this.isActive = (isActiveRaw == 1);
        }
    }

    public boolean isInStock() {
        if (inStockRaw != null) {
            return inStockRaw == 1;
        }
        return inStock;
    }

    public void setInStock(boolean inStock) {
        this.inStock = inStock;
        this.inStockRaw = inStock ? 1 : 0;
    }

    public Integer getInStockRaw() {
        return inStockRaw;
    }

    public void setInStockRaw(Integer inStockRaw) {
        this.inStockRaw = inStockRaw;
        if (inStockRaw != null) {
            this.inStock = (inStockRaw == 1);
        }
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper method to format price
    public String getFormattedPrice() {
        // Format price with 2 decimal places if needed
        if (price == (int) price) {
            // If price is a whole number, show without decimal
            return "৳" + (int) price;
        } else {
            // If price has decimal part, show with 2 decimal places
            return String.format("৳%.2f", price);
        }
    }
}
