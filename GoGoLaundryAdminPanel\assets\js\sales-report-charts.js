// Set new default font family and font color to mimic Bootstrap's default styling
Chart.defaults.global.defaultFontFamily = 'Nunito', '-apple-system,system-ui,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif';
Chart.defaults.global.defaultFontColor = '#858796';

// Function to format numbers with commas
function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * Initialize sales chart
 * 
 * @param {Array} labels Chart labels
 * @param {Array} data Chart data
 */
function initSalesChart(labels, data) {
    var ctx = document.getElementById("salesChart");
    
    // Extract revenue and orders data
    var revenueData = data.map(function(item) {
        return item.revenue;
    });
    
    var ordersData = data.map(function(item) {
        return item.orders;
    });
    
    var salesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: "Revenue",
                    backgroundColor: "rgba(78, 115, 223, 0.2)",
                    borderColor: "rgba(78, 115, 223, 1)",
                    borderWidth: 1,
                    data: revenueData,
                    yAxisID: 'y-axis-1',
                },
                {
                    label: "Orders",
                    backgroundColor: "rgba(28, 200, 138, 0.2)",
                    borderColor: "rgba(28, 200, 138, 1)",
                    borderWidth: 1,
                    data: ordersData,
                    yAxisID: 'y-axis-2',
                    type: 'line',
                }
            ],
        },
        options: {
            maintainAspectRatio: false,
            layout: {
                padding: {
                    left: 10,
                    right: 25,
                    top: 25,
                    bottom: 0
                }
            },
            scales: {
                xAxes: [{
                    time: {
                        unit: 'date'
                    },
                    gridLines: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        maxTicksLimit: 7
                    }
                }],
                yAxes: [
                    {
                        id: 'y-axis-1',
                        type: 'linear',
                        position: 'left',
                        ticks: {
                            maxTicksLimit: 5,
                            padding: 10,
                            // Include a BDT sign in the ticks
                            callback: function(value, index, values) {
                                return numberWithCommas(value) + ' BDT';
                            }
                        },
                        gridLines: {
                            color: "rgb(234, 236, 244)",
                            zeroLineColor: "rgb(234, 236, 244)",
                            drawBorder: false,
                            borderDash: [2],
                            zeroLineBorderDash: [2]
                        }
                    },
                    {
                        id: 'y-axis-2',
                        type: 'linear',
                        position: 'right',
                        ticks: {
                            maxTicksLimit: 5,
                            padding: 10,
                            callback: function(value, index, values) {
                                return numberWithCommas(value);
                            }
                        },
                        gridLines: {
                            display: false
                        }
                    }
                ],
            },
            legend: {
                display: true
            },
            tooltips: {
                backgroundColor: "rgb(255,255,255)",
                bodyFontColor: "#858796",
                titleMarginBottom: 10,
                titleFontColor: '#6e707e',
                titleFontSize: 14,
                borderColor: '#dddfeb',
                borderWidth: 1,
                xPadding: 15,
                yPadding: 15,
                displayColors: false,
                intersect: false,
                mode: 'index',
                caretPadding: 10,
                callbacks: {
                    label: function(tooltipItem, chart) {
                        var datasetLabel = chart.datasets[tooltipItem.datasetIndex].label || '';
                        if (datasetLabel === "Revenue") {
                            return datasetLabel + ': ' + numberWithCommas(tooltipItem.yLabel) + ' BDT';
                        } else {
                            return datasetLabel + ': ' + numberWithCommas(tooltipItem.yLabel);
                        }
                    }
                }
            }
        }
    });
}

/**
 * Initialize payment methods chart
 * 
 * @param {Array} labels Chart labels
 * @param {Array} data Chart data
 * @param {Array} colors Chart colors
 */
function initPaymentMethodsChart(labels, data, colors) {
    var ctx = document.getElementById("paymentMethodsChart");
    var paymentMethodsChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                hoverBackgroundColor: colors.map(function(color) {
                    return darkenColor(color, 10);
                }),
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            maintainAspectRatio: false,
            tooltips: {
                backgroundColor: "rgb(255,255,255)",
                bodyFontColor: "#858796",
                borderColor: '#dddfeb',
                borderWidth: 1,
                xPadding: 15,
                yPadding: 15,
                displayColors: false,
                caretPadding: 10,
            },
            legend: {
                display: false
            },
            cutoutPercentage: 80,
        },
    });
}

/**
 * Darken a hex color
 * 
 * @param {string} hex Hex color code
 * @param {number} percent Percentage to darken
 * @return {string} Darkened hex color code
 */
function darkenColor(hex, percent) {
    // Remove # if present
    hex = hex.replace('#', '');
    
    // Convert to RGB
    var r = parseInt(hex.substring(0, 2), 16);
    var g = parseInt(hex.substring(2, 4), 16);
    var b = parseInt(hex.substring(4, 6), 16);
    
    // Darken
    r = Math.max(0, Math.floor(r * (100 - percent) / 100));
    g = Math.max(0, Math.floor(g * (100 - percent) / 100));
    b = Math.max(0, Math.floor(b * (100 - percent) / 100));
    
    // Convert back to hex
    return '#' + (r.toString(16).padStart(2, '0') + 
                  g.toString(16).padStart(2, '0') + 
                  b.toString(16).padStart(2, '0'));
}

// Initialize DataTables
$(document).ready(function() {
    $('#salesTable').DataTable({
        order: [[0, 'asc']],
        pageLength: 10,
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });
});
