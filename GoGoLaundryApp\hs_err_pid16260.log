#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 437728 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=16260, tid=9984
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Thu May 22 03:28:23 2025 Bangladesh Standard Time elapsed time: 2.440767 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x000001d0ee1de6a0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=9984, stack(0x0000007453c00000,0x0000007453d00000) (1024K)]


Current CompileTask:
C2:   2440 2442   !   4       java.util.zip.InflaterInputStream::read (129 bytes)

Stack: [0x0000007453c00000,0x0000007453d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0xc3f3d]
V  [jvm.dll+0xc4473]
V  [jvm.dll+0x3b5c2c]
V  [jvm.dll+0x382855]
V  [jvm.dll+0x381cca]
V  [jvm.dll+0x249bd0]
V  [jvm.dll+0x2491b1]
V  [jvm.dll+0x1c9634]
V  [jvm.dll+0x258859]
V  [jvm.dll+0x256e3a]
V  [jvm.dll+0x3ef6c6]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d0f4205af0, length=22, elements={
0x000001d0d17cc560, 0x000001d0ee1ce110, 0x000001d0ee1cefe0, 0x000001d0ee1d1e00,
0x000001d0ee1d3ca0, 0x000001d0ee1d62a0, 0x000001d0ee1d7650, 0x000001d0ee1de6a0,
0x000001d0ee1ee200, 0x000001d0ee1d6fc0, 0x000001d0ee1d7ce0, 0x000001d0f406fbe0,
0x000001d0ee1d8370, 0x000001d0ee1d4ef0, 0x000001d0ee1d5580, 0x000001d0f414ebd0,
0x000001d0f41482d0, 0x000001d0f414c470, 0x000001d0f414deb0, 0x000001d0f414b0c0,
0x000001d0f414a3a0, 0x000001d0f4147c40
}

Java Threads: ( => current thread )
  0x000001d0d17cc560 JavaThread "main"                              [_thread_blocked, id=5852, stack(0x0000007452e00000,0x0000007452f00000) (1024K)]
  0x000001d0ee1ce110 JavaThread "Reference Handler"          daemon [_thread_blocked, id=19260, stack(0x0000007453600000,0x0000007453700000) (1024K)]
  0x000001d0ee1cefe0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=17128, stack(0x0000007453700000,0x0000007453800000) (1024K)]
  0x000001d0ee1d1e00 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=13292, stack(0x0000007453800000,0x0000007453900000) (1024K)]
  0x000001d0ee1d3ca0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=304, stack(0x0000007453900000,0x0000007453a00000) (1024K)]
  0x000001d0ee1d62a0 JavaThread "Service Thread"             daemon [_thread_blocked, id=2668, stack(0x0000007453a00000,0x0000007453b00000) (1024K)]
  0x000001d0ee1d7650 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=14272, stack(0x0000007453b00000,0x0000007453c00000) (1024K)]
=>0x000001d0ee1de6a0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=9984, stack(0x0000007453c00000,0x0000007453d00000) (1024K)]
  0x000001d0ee1ee200 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=19212, stack(0x0000007453d00000,0x0000007453e00000) (1024K)]
  0x000001d0ee1d6fc0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=5816, stack(0x0000007453e00000,0x0000007453f00000) (1024K)]
  0x000001d0ee1d7ce0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=12260, stack(0x0000007453f00000,0x0000007454000000) (1024K)]
  0x000001d0f406fbe0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=11676, stack(0x0000007454000000,0x0000007454100000) (1024K)]
  0x000001d0ee1d8370 JavaThread "Daemon health stats"               [_thread_blocked, id=14256, stack(0x0000007454100000,0x0000007454200000) (1024K)]
  0x000001d0ee1d4ef0 JavaThread "Incoming local TCP Connector on port 55545"        [_thread_in_native, id=9016, stack(0x0000007454900000,0x0000007454a00000) (1024K)]
  0x000001d0ee1d5580 JavaThread "Daemon periodic checks"            [_thread_blocked, id=18536, stack(0x0000007454a00000,0x0000007454b00000) (1024K)]
  0x000001d0f414ebd0 JavaThread "Daemon"                            [_thread_blocked, id=12220, stack(0x0000007454b00000,0x0000007454c00000) (1024K)]
  0x000001d0f41482d0 JavaThread "Handler for socket connection from /127.0.0.1:55545 to /127.0.0.1:55546"        [_thread_in_native, id=6084, stack(0x0000007454c00000,0x0000007454d00000) (1024K)]
  0x000001d0f414c470 JavaThread "Cancel handler"                    [_thread_blocked, id=2740, stack(0x0000007454d00000,0x0000007454e00000) (1024K)]
  0x000001d0f414deb0 JavaThread "Daemon worker"                     [_thread_in_vm, id=18012, stack(0x0000007454e00000,0x0000007454f00000) (1024K)]
  0x000001d0f414b0c0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:55545 to /127.0.0.1:55546"        [_thread_blocked, id=17256, stack(0x0000007454f00000,0x0000007455000000) (1024K)]
  0x000001d0f414a3a0 JavaThread "Stdin handler"                     [_thread_blocked, id=12388, stack(0x0000007455000000,0x0000007455100000) (1024K)]
  0x000001d0f4147c40 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=15796, stack(0x0000007455100000,0x0000007455200000) (1024K)]
Total: 22

Other Threads:
  0x000001d0ee1b2570 VMThread "VM Thread"                           [id=16032, stack(0x0000007453500000,0x0000007453600000) (1024K)]
  0x000001d0ee19e2d0 WatcherThread "VM Periodic Task Thread"        [id=10372, stack(0x0000007453400000,0x0000007453500000) (1024K)]
  0x000001d0cf60dfc0 WorkerThread "GC Thread#0"                     [id=13616, stack(0x0000007452f00000,0x0000007453000000) (1024K)]
  0x000001d0ee5ec440 WorkerThread "GC Thread#1"                     [id=17952, stack(0x0000007454200000,0x0000007454300000) (1024K)]
  0x000001d0eeb78a20 WorkerThread "GC Thread#2"                     [id=13420, stack(0x0000007454300000,0x0000007454400000) (1024K)]
  0x000001d0ee8d2540 WorkerThread "GC Thread#3"                     [id=268, stack(0x0000007454400000,0x0000007454500000) (1024K)]
  0x000001d0ee8afb00 WorkerThread "GC Thread#4"                     [id=18448, stack(0x0000007454500000,0x0000007454600000) (1024K)]
  0x000001d0ee8afea0 WorkerThread "GC Thread#5"                     [id=19008, stack(0x0000007454600000,0x0000007454700000) (1024K)]
  0x000001d0eea21e40 WorkerThread "GC Thread#6"                     [id=14696, stack(0x0000007454700000,0x0000007454800000) (1024K)]
  0x000001d0eea221e0 WorkerThread "GC Thread#7"                     [id=9588, stack(0x0000007454800000,0x0000007454900000) (1024K)]
  0x000001d0d1830ad0 ConcurrentGCThread "G1 Main Marker"            [id=2220, stack(0x0000007453000000,0x0000007453100000) (1024K)]
  0x000001d0d1832b90 WorkerThread "G1 Conc#0"                       [id=11132, stack(0x0000007453100000,0x0000007453200000) (1024K)]
  0x000001d0d188ea00 ConcurrentGCThread "G1 Refine#0"               [id=17708, stack(0x0000007453200000,0x0000007453300000) (1024K)]
  0x000001d0ee0f07c0 ConcurrentGCThread "G1 Service"                [id=7636, stack(0x0000007453300000,0x0000007453400000) (1024K)]
Total: 14

Threads with active compile tasks:
C2 CompilerThread0     2473 2442   !   4       java.util.zip.InflaterInputStream::read (129 bytes)
C1 CompilerThread0     2474 2471       3       java.lang.invoke.MethodHandleImpl::makePairwiseConvertByEditor (617 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001d080000000-0x000001d080c90000-0x000001d080c90000), size 13172736, SharedBaseAddress: 0x000001d080000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001d081000000-0x000001d0c1000000, reserved size: 1073741824
Narrow klass base: 0x000001d080000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 360448K, used 50740K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 39 young (39936K), 5 survivors (5120K)
 Metaspace       used 13195K, committed 13632K, reserved 1114112K
  class space    used 1766K, committed 1984K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080800000, 0x0000000080900000|  0%| F|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080caa9f8, 0x0000000080d00000| 66%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080d00000, 0x0000000080e00000|  0%| F|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080e00000, 0x0000000080f00000|  0%| F|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000080f00000, 0x0000000081000000|  0%| F|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081000000, 0x0000000081100000|  0%| F|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081100000, 0x0000000081200000|  0%| F|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081200000, 0x0000000081300000|  0%| F|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081300000, 0x0000000081400000|  0%| F|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081400000, 0x0000000081500000|  0%| F|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081500000, 0x0000000081600000|  0%| F|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081600000, 0x0000000081700000|  0%| F|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081700000, 0x0000000081800000|  0%| F|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081800000, 0x0000000081900000|  0%| F|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081900000, 0x0000000081a00000|  0%| F|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081a00000, 0x0000000081b00000|  0%| F|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081b00000, 0x0000000081c00000|  0%| F|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081c00000, 0x0000000081d00000|  0%| F|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081d00000, 0x0000000081e00000|  0%| F|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081e00000, 0x0000000081f00000|  0%| F|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000081f00000, 0x0000000082000000|  0%| F|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082000000, 0x0000000082100000|  0%| F|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082100000, 0x0000000082200000|  0%| F|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082200000, 0x0000000082300000|  0%| F|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082300000, 0x0000000082400000|  0%| F|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082400000, 0x0000000082500000|  0%| F|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082500000, 0x0000000082600000|  0%| F|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082600000, 0x0000000082700000|  0%| F|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082700000, 0x0000000082800000|  0%| F|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082800000, 0x0000000082900000|  0%| F|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082900000, 0x0000000082a00000|  0%| F|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082a00000, 0x0000000082b00000|  0%| F|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082b00000, 0x0000000082c00000|  0%| F|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082c00000, 0x0000000082d00000|  0%| F|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082d00000, 0x0000000082e00000|  0%| F|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082e00000, 0x0000000082f00000|  0%| F|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000082f00000, 0x0000000083000000|  0%| F|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083000000, 0x0000000083100000|  0%| F|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083100000, 0x0000000083200000|  0%| F|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083300000, 0x0000000083400000|  0%| F|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083400000, 0x0000000083500000|  0%| F|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083600000, 0x0000000083700000|  0%| F|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083800000, 0x0000000083900000|  0%| F|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083900000, 0x0000000083a00000|  0%| F|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083a00000, 0x0000000083b00000|  0%| F|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083b00000, 0x0000000083c00000|  0%| F|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083c00000, 0x0000000083d00000|  0%| F|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083d00000, 0x0000000083e00000|  0%| F|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083e00000, 0x0000000083f00000|  0%| F|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083f00000, 0x0000000084000000|  0%| F|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084000000, 0x0000000084100000|  0%| F|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084100000, 0x0000000084200000|  0%| F|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084200000, 0x0000000084300000|  0%| F|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084300000, 0x0000000084400000|  0%| F|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084400000, 0x0000000084500000|  0%| F|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084500000, 0x0000000084600000|  0%| F|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084600000, 0x0000000084700000|  0%| F|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084800000, 0x0000000084900000|  0%| F|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085400000, 0x0000000085500000|  0%| F|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085500000, 0x0000000085600000|  0%| F|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085600000, 0x0000000085700000|  0%| F|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085700000, 0x0000000085800000|  0%| F|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085800000, 0x0000000085900000|  0%| F|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086d00000, 0x0000000086e00000|  0%| F|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086e00000, 0x0000000086f00000|  0%| F|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000086f00000, 0x0000000087000000|  0%| F|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087000000, 0x0000000087100000|  0%| F|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087100000, 0x0000000087200000|  0%| F|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087200000, 0x0000000087300000|  0%| F|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087300000, 0x0000000087400000|  0%| F|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087400000, 0x0000000087500000|  0%| F|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087500000, 0x0000000087600000|  0%| F|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087600000, 0x0000000087700000|  0%| F|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087900000, 0x0000000087a00000|  0%| F|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087a00000, 0x0000000087b00000|  0%| F|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087b00000, 0x0000000087c00000|  0%| F|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087c00000, 0x0000000087d00000|  0%| F|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087d00000, 0x0000000087e00000|  0%| F|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087e00000, 0x0000000087f00000|  0%| F|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000087f00000, 0x0000000088000000|  0%| F|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088000000, 0x0000000088100000|  0%| F|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088200000, 0x0000000088300000|  0%| F|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088300000, 0x0000000088400000|  0%| F|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088400000, 0x0000000088500000|  0%| F|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088500000, 0x0000000088600000|  0%| F|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088600000, 0x0000000088700000|  0%| F|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a00000, 0x0000000088b00000|  0%| F|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088b00000, 0x0000000088c00000|  0%| F|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090400000, 0x0000000090500000|  0%| F|  |TAMS 0x0000000090400000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090500000, 0x0000000090600000|  0%| F|  |TAMS 0x0000000090500000| PB 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090800000, 0x0000000090900000|  0%| F|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090d00000, 0x0000000090e00000|  0%| F|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090e00000, 0x0000000090f00000|  0%| F|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000090f00000, 0x0000000091000000|  0%| F|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091b00000, 0x0000000091c00000|  0%| F|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091d00000, 0x0000000091e00000|  0%| F|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000091f00000, 0x0000000092000000|  0%| F|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092400000, 0x0000000092500000|  0%| F|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x00000000928e2938, 0x0000000092900000| 88%| S|CS|TAMS 0x0000000092800000| PB 0x0000000092800000| Complete 
| 297|0x0000000092900000, 0x0000000092a00000, 0x0000000092a00000|100%| S|CS|TAMS 0x0000000092900000| PB 0x0000000092900000| Complete 
| 298|0x0000000092a00000, 0x0000000092b00000, 0x0000000092b00000|100%| S|CS|TAMS 0x0000000092a00000| PB 0x0000000092a00000| Complete 
| 299|0x0000000092b00000, 0x0000000092c00000, 0x0000000092c00000|100%| S|CS|TAMS 0x0000000092b00000| PB 0x0000000092b00000| Complete 
| 300|0x0000000092c00000, 0x0000000092d00000, 0x0000000092d00000|100%| S|CS|TAMS 0x0000000092c00000| PB 0x0000000092c00000| Complete 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092e00000, 0x0000000092f00000|  0%| F|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000092f00000, 0x0000000093000000|  0%| F|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093100000, 0x0000000093200000|  0%| F|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093200000, 0x0000000093300000|  0%| F|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093300000, 0x0000000093400000|  0%| F|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093400000, 0x0000000093500000|  0%| F|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093500000, 0x0000000093600000|  0%| F|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093600000, 0x0000000093700000|  0%| F|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093f00000, 0x0000000093f00000|100%| E|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Complete 
| 319|0x0000000093f00000, 0x0000000094000000, 0x0000000094000000|100%| E|CS|TAMS 0x0000000093f00000| PB 0x0000000093f00000| Complete 
| 320|0x0000000094000000, 0x0000000094100000, 0x0000000094100000|100%| E|CS|TAMS 0x0000000094000000| PB 0x0000000094000000| Complete 
| 321|0x0000000094100000, 0x0000000094200000, 0x0000000094200000|100%| E|CS|TAMS 0x0000000094100000| PB 0x0000000094100000| Complete 
| 322|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| E|CS|TAMS 0x0000000094200000| PB 0x0000000094200000| Complete 
| 323|0x0000000094300000, 0x0000000094400000, 0x0000000094400000|100%| E|CS|TAMS 0x0000000094300000| PB 0x0000000094300000| Complete 
| 324|0x0000000094400000, 0x0000000094500000, 0x0000000094500000|100%| E|CS|TAMS 0x0000000094400000| PB 0x0000000094400000| Complete 
| 325|0x0000000094500000, 0x0000000094600000, 0x0000000094600000|100%| E|CS|TAMS 0x0000000094500000| PB 0x0000000094500000| Complete 
| 326|0x0000000094600000, 0x0000000094700000, 0x0000000094700000|100%| E|CS|TAMS 0x0000000094600000| PB 0x0000000094600000| Complete 
| 327|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%| E|CS|TAMS 0x0000000094700000| PB 0x0000000094700000| Complete 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| E|CS|TAMS 0x0000000094800000| PB 0x0000000094800000| Complete 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| E|CS|TAMS 0x0000000094900000| PB 0x0000000094900000| Complete 
| 330|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| E|CS|TAMS 0x0000000094a00000| PB 0x0000000094a00000| Complete 
| 331|0x0000000094b00000, 0x0000000094c00000, 0x0000000094c00000|100%| E|CS|TAMS 0x0000000094b00000| PB 0x0000000094b00000| Complete 
| 332|0x0000000094c00000, 0x0000000094d00000, 0x0000000094d00000|100%| E|CS|TAMS 0x0000000094c00000| PB 0x0000000094c00000| Complete 
| 333|0x0000000094d00000, 0x0000000094e00000, 0x0000000094e00000|100%| E|CS|TAMS 0x0000000094d00000| PB 0x0000000094d00000| Complete 
| 334|0x0000000094e00000, 0x0000000094f00000, 0x0000000094f00000|100%| E|CS|TAMS 0x0000000094e00000| PB 0x0000000094e00000| Complete 
| 335|0x0000000094f00000, 0x0000000095000000, 0x0000000095000000|100%| E|CS|TAMS 0x0000000094f00000| PB 0x0000000094f00000| Complete 
| 336|0x0000000095000000, 0x0000000095100000, 0x0000000095100000|100%| E|CS|TAMS 0x0000000095000000| PB 0x0000000095000000| Complete 
| 337|0x0000000095100000, 0x0000000095200000, 0x0000000095200000|100%| E|CS|TAMS 0x0000000095100000| PB 0x0000000095100000| Complete 
| 338|0x0000000095200000, 0x0000000095300000, 0x0000000095300000|100%| E|CS|TAMS 0x0000000095200000| PB 0x0000000095200000| Complete 
| 339|0x0000000095300000, 0x0000000095400000, 0x0000000095400000|100%| E|CS|TAMS 0x0000000095300000| PB 0x0000000095300000| Complete 
| 340|0x0000000095400000, 0x0000000095500000, 0x0000000095500000|100%| E|CS|TAMS 0x0000000095400000| PB 0x0000000095400000| Complete 
| 341|0x0000000095500000, 0x0000000095600000, 0x0000000095600000|100%| E|CS|TAMS 0x0000000095500000| PB 0x0000000095500000| Complete 
| 342|0x0000000095600000, 0x0000000095700000, 0x0000000095700000|100%| E|CS|TAMS 0x0000000095600000| PB 0x0000000095600000| Complete 
| 343|0x0000000095700000, 0x0000000095800000, 0x0000000095800000|100%| E|CS|TAMS 0x0000000095700000| PB 0x0000000095700000| Complete 
| 344|0x0000000095800000, 0x0000000095900000, 0x0000000095900000|100%| E|CS|TAMS 0x0000000095800000| PB 0x0000000095800000| Complete 
| 345|0x0000000095900000, 0x0000000095a00000, 0x0000000095a00000|100%| E|CS|TAMS 0x0000000095900000| PB 0x0000000095900000| Complete 
| 346|0x0000000095a00000, 0x0000000095b00000, 0x0000000095b00000|100%| E|CS|TAMS 0x0000000095a00000| PB 0x0000000095a00000| Complete 
| 347|0x0000000095b00000, 0x0000000095c00000, 0x0000000095c00000|100%| E|CS|TAMS 0x0000000095b00000| PB 0x0000000095b00000| Complete 
| 348|0x0000000095c00000, 0x0000000095d00000, 0x0000000095d00000|100%| E|CS|TAMS 0x0000000095c00000| PB 0x0000000095c00000| Complete 
| 349|0x0000000095d00000, 0x0000000095e00000, 0x0000000095e00000|100%| E|CS|TAMS 0x0000000095d00000| PB 0x0000000095d00000| Complete 
| 350|0x0000000095e00000, 0x0000000095f00000, 0x0000000095f00000|100%| E|CS|TAMS 0x0000000095e00000| PB 0x0000000095e00000| Complete 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| E|CS|TAMS 0x0000000095f00000| PB 0x0000000095f00000| Complete 

Card table byte_map: [0x000001d0e9360000,0x000001d0e9760000] _byte_map_base: 0x000001d0e8f60000

Marking Bits: (CMBitMap*) 0x000001d0cf60e5c0
 Bits: [0x000001d0e9760000, 0x000001d0eb760000)

Polling page: 0x000001d0cf7a0000

Metaspace:

Usage:
  Non-class:     11.31 MB used.
      Class:      1.75 MB used.
       Both:     13.06 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      11.56 MB ( 18%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      13.50 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  4.30 MB
       Class:  14.07 MB
        Both:  18.36 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 432.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 216.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 848.
num_chunk_merges: 0.
num_chunk_splits: 549.
num_chunks_enlarged: 397.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1158Kb max_used=1158Kb free=118842Kb
 bounds [0x000001d0e1700000, 0x000001d0e1970000, 0x000001d0e8c30000]
CodeHeap 'profiled nmethods': size=120000Kb used=4994Kb max_used=4994Kb free=115005Kb
 bounds [0x000001d0d9c30000, 0x000001d0da120000, 0x000001d0e1160000]
CodeHeap 'non-nmethods': size=5760Kb used=1458Kb max_used=1466Kb free=4301Kb
 bounds [0x000001d0e1160000, 0x000001d0e13d0000, 0x000001d0e1700000]
 total_blobs=3026 nmethods=2483 adapters=447
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2.395 Thread 0x000001d0ee1ee200 nmethod 2436 0x000001d0da0ef910 code [0x000001d0da0efac0, 0x000001d0da0efc88]
Event: 2.395 Thread 0x000001d0ee1ee200 2437       3       java.util.stream.PipelineHelper::<init> (5 bytes)
Event: 2.395 Thread 0x000001d0ee1ee200 nmethod 2437 0x000001d0da0efd90 code [0x000001d0da0eff40, 0x000001d0da0f00a8]
Event: 2.398 Thread 0x000001d0ee1ee200 2439       1       java.util.stream.AbstractPipeline::getStreamAndOpFlags (5 bytes)
Event: 2.398 Thread 0x000001d0ee1ee200 nmethod 2439 0x000001d0e181bc10 code [0x000001d0e181bda0, 0x000001d0e181be68]
Event: 2.398 Thread 0x000001d0ee1ee200 2440       3       java.lang.invoke.Invokers$Holder::linkToTargetMethod (9 bytes)
Event: 2.398 Thread 0x000001d0ee1ee200 nmethod 2440 0x000001d0da0f0190 code [0x000001d0da0f0360, 0x000001d0da0f0720]
Event: 2.402 Thread 0x000001d0ee1ee200 2441       3       java.util.concurrent.ConcurrentHashMap::get (162 bytes)
Event: 2.403 Thread 0x000001d0ee1ee200 nmethod 2441 0x000001d0da0f0810 code [0x000001d0da0f0a80, 0x000001d0da0f1520]
Event: 2.410 Thread 0x000001d0ee1de6a0 nmethod 2429 0x000001d0e181bf10 code [0x000001d0e181c160, 0x000001d0e181d100]
Event: 2.420 Thread 0x000001d0ee1de6a0 2442   !   4       java.util.zip.InflaterInputStream::read (129 bytes)
Event: 2.422 Thread 0x000001d0f406fbe0 2443       4       java.util.jar.JarFile::maybeInstantiateVerifier (42 bytes)
Event: 2.422 Thread 0x000001d0f406fbe0 nmethod 2443 0x000001d0e181d810 code [0x000001d0e181d9a0, 0x000001d0e181daa8]
Event: 2.422 Thread 0x000001d0f406fbe0 2444       4       java.util.WeakHashMap::remove (131 bytes)
Event: 2.428 Thread 0x000001d0f406fbe0 nmethod 2444 0x000001d0e181db90 code [0x000001d0e181dd40, 0x000001d0e181e118]
Event: 2.436 Thread 0x000001d0ee1ee200 2445       3       java.lang.invoke.MethodHandles$Lookup::getDirectConstructorCommon (69 bytes)
Event: 2.436 Thread 0x000001d0ee1ee200 nmethod 2445 0x000001d0da0f1810 code [0x000001d0da0f1a20, 0x000001d0da0f1f50]
Event: 2.436 Thread 0x000001d0ee1ee200 2446       3       java.lang.invoke.MemberName::asConstructor (58 bytes)
Event: 2.436 Thread 0x000001d0ee1ee200 nmethod 2446 0x000001d0da0f2190 code [0x000001d0da0f23c0, 0x000001d0da0f29d0]
Event: 2.437 Thread 0x000001d0f406fbe0 2447       4       java.lang.Class::getName (18 bytes)

GC Heap History (6 events):
Event: 0.412 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 360448K, used 22528K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 0 survivors (0K)
 Metaspace       used 1068K, committed 1216K, reserved 1114112K
  class space    used 77K, committed 128K, reserved 1048576K
}
Event: 0.414 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 360448K, used 3508K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 1068K, committed 1216K, reserved 1114112K
  class space    used 77K, committed 128K, reserved 1048576K
}
Event: 1.009 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 360448K, used 40372K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 36 young (36864K), 3 survivors (3072K)
 Metaspace       used 4376K, committed 4544K, reserved 1114112K
  class space    used 542K, committed 640K, reserved 1048576K
}
Event: 1.012 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 360448K, used 11150K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 4376K, committed 4544K, reserved 1114112K
  class space    used 542K, committed 640K, reserved 1048576K
}
Event: 1.640 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 360448K, used 60302K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 51 young (52224K), 5 survivors (5120K)
 Metaspace       used 5416K, committed 5696K, reserved 1114112K
  class space    used 706K, committed 832K, reserved 1048576K
}
Event: 1.643 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 360448K, used 16948K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 5416K, committed 5696K, reserved 1114112K
  class space    used 706K, committed 832K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.008 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.038 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.079 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.082 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.085 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.087 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.090 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.333 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.465 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.586 Loaded shared library C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 0.590 Loaded shared library C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 1.778 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.781 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.993 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 2.116 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 2.217 Thread 0x000001d0f414deb0 Uncommon trap: trap_request=0xffffff6e fr.pc=0x000001d0e17b79ac relative=0x00000000000003ec
Event: 2.217 Thread 0x000001d0f414deb0 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x000001d0e17b79ac method=java.lang.StringLatin1.indexOf([BI[BII)I @ 37 c2
Event: 2.217 Thread 0x000001d0f414deb0 DEOPT PACKING pc=0x000001d0e17b79ac sp=0x0000007454efefd0
Event: 2.217 Thread 0x000001d0f414deb0 DEOPT UNPACKING pc=0x000001d0e11b46a2 sp=0x0000007454efef30 mode 2
Event: 2.381 Thread 0x000001d0f414deb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d0e17acfc0 relative=0x00000000000001e0
Event: 2.381 Thread 0x000001d0f414deb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d0e17acfc0 method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 66 c2
Event: 2.381 Thread 0x000001d0f414deb0 DEOPT PACKING pc=0x000001d0e17acfc0 sp=0x0000007454efc9e0
Event: 2.381 Thread 0x000001d0f414deb0 DEOPT UNPACKING pc=0x000001d0e11b46a2 sp=0x0000007454efc900 mode 2
Event: 2.381 Thread 0x000001d0f414deb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d0e180fad4 relative=0x0000000000000794
Event: 2.381 Thread 0x000001d0f414deb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d0e180fad4 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 94 c2
Event: 2.381 Thread 0x000001d0f414deb0 DEOPT PACKING pc=0x000001d0e180fad4 sp=0x0000007454efc950
Event: 2.381 Thread 0x000001d0f414deb0 DEOPT UNPACKING pc=0x000001d0e11b46a2 sp=0x0000007454efc8e8 mode 2
Event: 2.382 Thread 0x000001d0f414deb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d0e17465fc relative=0x000000000000019c
Event: 2.382 Thread 0x000001d0f414deb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d0e17465fc method=java.util.HashMap.getNode(Ljava/lang/Object;)Ljava/util/HashMap$Node; @ 66 c2
Event: 2.382 Thread 0x000001d0f414deb0 DEOPT PACKING pc=0x000001d0e17465fc sp=0x0000007454efc980
Event: 2.382 Thread 0x000001d0f414deb0 DEOPT UNPACKING pc=0x000001d0e11b46a2 sp=0x0000007454efc900 mode 2
Event: 2.392 Thread 0x000001d0f414deb0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d0e1791e18 relative=0x00000000000002d8
Event: 2.392 Thread 0x000001d0f414deb0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d0e1791e18 method=java.util.concurrent.ConcurrentHashMap.get(Ljava/lang/Object;)Ljava/lang/Object; @ 152 c2
Event: 2.392 Thread 0x000001d0f414deb0 DEOPT PACKING pc=0x000001d0e1791e18 sp=0x0000007454efbf30
Event: 2.392 Thread 0x000001d0f414deb0 DEOPT UNPACKING pc=0x000001d0e11b46a2 sp=0x0000007454efbea8 mode 2

Classes loaded (20 events):
Event: 2.386 Loading class sun/reflect/generics/tree/Wildcard
Event: 2.386 Loading class sun/reflect/generics/tree/Wildcard done
Event: 2.386 Loading class sun/reflect/generics/reflectiveObjects/WildcardTypeImpl
Event: 2.387 Loading class sun/reflect/generics/reflectiveObjects/WildcardTypeImpl done
Event: 2.391 Loading class java/lang/Deprecated
Event: 2.391 Loading class java/lang/Deprecated done
Event: 2.392 Loading class jdk/internal/vm/annotation/IntrinsicCandidate
Event: 2.392 Loading class jdk/internal/vm/annotation/IntrinsicCandidate done
Event: 2.394 Loading class java/util/stream/MatchOps$MatchKind
Event: 2.394 Loading class java/util/stream/MatchOps$MatchKind done
Event: 2.394 Loading class java/util/stream/MatchOps
Event: 2.394 Loading class java/util/stream/MatchOps done
Event: 2.394 Loading class java/util/stream/MatchOps$MatchOp
Event: 2.394 Loading class java/util/stream/MatchOps$MatchOp done
Event: 2.394 Loading class java/util/stream/MatchOps$BooleanTerminalSink
Event: 2.394 Loading class java/util/stream/MatchOps$BooleanTerminalSink done
Event: 2.395 Loading class java/util/stream/MatchOps$1MatchSink
Event: 2.395 Loading class java/util/stream/MatchOps$1MatchSink done
Event: 2.417 Loading class java/lang/TypeNotPresentException
Event: 2.417 Loading class java/lang/TypeNotPresentException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1.816 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095bc8958}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000095bc8958) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.818 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095bdb8a8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000095bdb8a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.838 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095a69640}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000095a69640) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.846 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095933448}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000095933448) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.848 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x00000000959525e0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000959525e0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.850 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095972710}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000095972710) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.852 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095994ff0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000095994ff0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp
Event: 1.903 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x00000000957f44f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000957f44f0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.912 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x000000009567bb08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000009567bb08) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.961 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x00000000954145f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x00000000954145f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.977 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x00000000954849b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x00000000954849b8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.090 Thread 0x000001d0d17cc560 Exception <a 'java/io/IOException'{0x00000000951fd0e8}> (0x00000000951fd0e8) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.096 Thread 0x000001d0d17cc560 Implicit null exception at 0x000001d0e175afa0 to 0x000001d0e175b53c
Event: 2.101 Thread 0x000001d0d17cc560 Exception <a 'java/lang/NoSuchMethodError'{0x000000009505e810}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000009505e810) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.213 Thread 0x000001d0f414ebd0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000094ee3eb8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000094ee3eb8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.285 Thread 0x000001d0f414deb0 Exception <a 'java/lang/NoSuchMethodError'{0x000000009486d438}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000009486d438) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.354 Thread 0x000001d0f414deb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000944b77c8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000944b77c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.360 Thread 0x000001d0f414deb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000944f9530}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000944f9530) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.361 Thread 0x000001d0f414deb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000944fe4a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000944fe4a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.397 Thread 0x000001d0f414deb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000942e7ca0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000942e7ca0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 1.801 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.801 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.827 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.827 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.830 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.830 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.831 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.831 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.872 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.872 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.901 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.901 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.936 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.936 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.971 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.971 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.120 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.120 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.220 Executing VM operation: ICBufferFull
Event: 2.220 Executing VM operation: ICBufferFull done

Events (20 events):
Event: 0.037 Thread 0x000001d0ee1d7650 Thread added: 0x000001d0ee1d7650
Event: 0.037 Thread 0x000001d0ee1de6a0 Thread added: 0x000001d0ee1de6a0
Event: 0.037 Thread 0x000001d0ee1ee200 Thread added: 0x000001d0ee1ee200
Event: 0.057 Thread 0x000001d0ee1d6fc0 Thread added: 0x000001d0ee1d6fc0
Event: 0.102 Thread 0x000001d0ee1d7ce0 Thread added: 0x000001d0ee1d7ce0
Event: 0.329 Thread 0x000001d0ee5c53f0 Thread added: 0x000001d0ee5c53f0
Event: 0.335 Thread 0x000001d0ee566430 Thread added: 0x000001d0ee566430
Event: 1.046 Thread 0x000001d0ee566430 Thread exited: 0x000001d0ee566430
Event: 1.086 Thread 0x000001d0ee5c53f0 Thread exited: 0x000001d0ee5c53f0
Event: 1.628 Thread 0x000001d0f406fbe0 Thread added: 0x000001d0f406fbe0
Event: 1.831 Thread 0x000001d0ee1d8370 Thread added: 0x000001d0ee1d8370
Event: 2.031 Thread 0x000001d0ee1d4ef0 Thread added: 0x000001d0ee1d4ef0
Event: 2.106 Thread 0x000001d0ee1d5580 Thread added: 0x000001d0ee1d5580
Event: 2.160 Thread 0x000001d0f414ebd0 Thread added: 0x000001d0f414ebd0
Event: 2.164 Thread 0x000001d0f41482d0 Thread added: 0x000001d0f41482d0
Event: 2.206 Thread 0x000001d0f414c470 Thread added: 0x000001d0f414c470
Event: 2.213 Thread 0x000001d0f414deb0 Thread added: 0x000001d0f414deb0
Event: 2.228 Thread 0x000001d0f414b0c0 Thread added: 0x000001d0f414b0c0
Event: 2.232 Thread 0x000001d0f414a3a0 Thread added: 0x000001d0f414a3a0
Event: 2.235 Thread 0x000001d0f4147c40 Thread added: 0x000001d0f4147c40


Dynamic libraries:
0x00007ff654c80000 - 0x00007ff654c90000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ff8cebf0000 - 0x00007ff8cede8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8cd350000 - 0x00007ff8cd412000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8cc4c0000 - 0x00007ff8cc7b6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8cc850000 - 0x00007ff8cc950000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8b10a0000 - 0x00007ff8b10bb000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ff8b1080000 - 0x00007ff8b1099000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ff8cd6f0000 - 0x00007ff8cd7a1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8cce60000 - 0x00007ff8ccefe000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8ce9c0000 - 0x00007ff8cea5f000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8cd1e0000 - 0x00007ff8cd303000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8cc3e0000 - 0x00007ff8cc407000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8cdf20000 - 0x00007ff8ce0bd000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8ccbe0000 - 0x00007ff8ccc02000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8bcf80000 - 0x00007ff8bd21a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ff8cd320000 - 0x00007ff8cd34b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8ccac0000 - 0x00007ff8ccbd9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8cc950000 - 0x00007ff8cc9ed000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8c39b0000 - 0x00007ff8c39ba000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8cd6c0000 - 0x00007ff8cd6ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c9640000 - 0x00007ff8c964c000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ff89d620000 - 0x00007ff89d6ae000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ff863b60000 - 0x00007ff864877000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ff8cd550000 - 0x00007ff8cd5bb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8cc0d0000 - 0x00007ff8cc11b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8c0fc0000 - 0x00007ff8c0fe7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8cc0b0000 - 0x00007ff8cc0c2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8caaf0000 - 0x00007ff8cab02000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c4280000 - 0x00007ff8c428a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ff8ca880000 - 0x00007ff8caa81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8baf20000 - 0x00007ff8baf54000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8cc7c0000 - 0x00007ff8cc842000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8a3a80000 - 0x00007ff8a3a8f000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007ff8b00f0000 - 0x00007ff8b010f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ff8cd7b0000 - 0x00007ff8cdf1e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8ca0d0000 - 0x00007ff8ca873000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8ce540000 - 0x00007ff8ce893000 	C:\WINDOWS\System32\combase.dll
0x00007ff8cbbe0000 - 0x00007ff8cbc0b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ff8cea80000 - 0x00007ff8ceb4d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8cd4a0000 - 0x00007ff8cd54d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8ceb50000 - 0x00007ff8cebab000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8cc1b0000 - 0x00007ff8cc1d5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff88db00000 - 0x00007ff88dbd7000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007ff8a4030000 - 0x00007ff8a4048000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ff8af9b0000 - 0x00007ff8af9c0000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ff8c8480000 - 0x00007ff8c858a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ff8cb940000 - 0x00007ff8cb9aa000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff8af990000 - 0x00007ff8af9a6000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ff8ab240000 - 0x00007ff8ab250000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007ff89e060000 - 0x00007ff89e087000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ff88b880000 - 0x00007ff88b9c4000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ff8ab1d0000 - 0x00007ff8ab1da000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ff8a3be0000 - 0x00007ff8a3beb000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ff8ce0c0000 - 0x00007ff8ce0c8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ff8cbb30000 - 0x00007ff8cbb48000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ff8cb260000 - 0x00007ff8cb298000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ff8cc130000 - 0x00007ff8cc15e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ff8cbb50000 - 0x00007ff8cbb5c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ff8cb620000 - 0x00007ff8cb65b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ff8cd650000 - 0x00007ff8cd658000 	C:\WINDOWS\System32\NSI.dll
0x00007ff8a3bb0000 - 0x00007ff8a3bb9000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007ff8a3970000 - 0x00007ff8a397e000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007ff8cc280000 - 0x00007ff8cc3dd000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ff8cbc50000 - 0x00007ff8cbc77000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ff8cbc10000 - 0x00007ff8cbc4b000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ff8bb020000 - 0x00007ff8bb027000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\agents\gradle-instrumentation-agent-8.11.1.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.11.1
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.11.1-bin\bpt9gzteqjrbo1mjrsomdt32c\gradle-8.11.1\lib\gradle-daemon-main-8.11.1.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
USERNAME=ntc
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 6:59 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4435M free)
TotalPageFile size 22476M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 151M, peak: 151M
current process commit charge ("private bytes"): 486M, peak: 486M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
