<?php
// API configuration endpoint

// Include database connection
require_once __DIR__ . "/../config/db.php";

// Set headers
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Get app version from request
$app_version = isset($_GET["app_version"]) ? $_GET["app_version"] : "1.0.0";

// Get settings from database
$settings = array();
$stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $settings[$row['setting_key']] = $row['setting_value'];
}

// Create response data
$data = array(
    "app_name" => "OTP Management System",
    "app_version" => "1.0.0",
    "otp_enabled" => 0, // Set dynamically from database
    "otp_length" => 6,
    "otp_expiry" => 600,
    "otp_max_attempts" => 3,
    "min_password_length" => 6,
    "admin_whatsapp" => "+8801303565075",
    "server_time" => date("Y-m-d H:i:s")
);

// Create response
$response = array(
    "success" => true,
    "message" => "Configuration retrieved successfully",
    "data" => $data
);

// Return response
echo json_encode($response);
