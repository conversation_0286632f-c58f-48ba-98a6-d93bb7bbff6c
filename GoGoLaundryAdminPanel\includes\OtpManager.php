<?php
/**
 * OTP Manager Class
 *
 * Handles OTP generation, validation, and management
 */
class OtpManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo Database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Generate a new OTP
     *
     * @param string $phone Phone number
     * @param string $email Email (optional)
     * @param string $purpose Purpose of OTP (registration, login, reset_password)
     * @return string|bool Generated OTP or false on failure
     */
    public function generateOtp($phone, $email = null, $purpose = 'registration') {
        // Format phone number
        $phone = formatPhone($phone);
        error_log("OtpManager::generateOtp - Formatted phone: {$phone}, Purpose: {$purpose}");

        // Check if rate limited
        if (isRateLimited($phone, getClientIp(), $this->pdo, $purpose)) {
            error_log("OtpManager::generateOtp - Rate limited for phone: {$phone}");
            return false;
        }

        // Generate random OTP
        $otp = '';
        for ($i = 0; $i < OTP_LENGTH; $i++) {
            $otp .= mt_rand(0, 9);
        }
        error_log("OtpManager::generateOtp - Generated OTP: {$otp}");

        // Calculate expiry time using database time to avoid timezone issues
        $stmt = $this->pdo->prepare("SELECT DATE_ADD(NOW(), INTERVAL ? SECOND) as expiry_time");
        $stmt->execute([OTP_EXPIRY]);
        $expiryTime = $stmt->fetch(PDO::FETCH_ASSOC)['expiry_time'];
        error_log("OtpManager::generateOtp - Expiry time: {$expiryTime}");

        try {
            // Delete any existing OTPs for this phone and purpose
            $stmt = $this->pdo->prepare("
                DELETE FROM otps
                WHERE phone = ? AND purpose = ?
            ");
            $stmt->execute([$phone, $purpose]);
            error_log("OtpManager::generateOtp - Deleted existing OTPs");

            // Insert new OTP
            $stmt = $this->pdo->prepare("
                INSERT INTO otps (phone, email, otp, purpose, expires_at)
                VALUES (?, ?, ?, ?, ?)
            ");

            $result = $stmt->execute([$phone, $email, $otp, $purpose, $expiryTime]);

            if ($result) {
                // Log OTP activity
                logOtpActivity($phone, getClientIp(), 'sent', $this->pdo);
                error_log("OtpManager::generateOtp - OTP saved successfully");
                return $otp;
            } else {
                error_log("OtpManager::generateOtp - Failed to insert OTP: " . json_encode($stmt->errorInfo()));
            }
        } catch (PDOException $e) {
            error_log("OtpManager::generateOtp - Database error: " . $e->getMessage());
        }

        return false;
    }

    /**
     * Verify an OTP
     *
     * @param string $phone Phone number
     * @param string $otp OTP to verify
     * @param string $purpose Purpose of OTP
     * @param bool $checkRecentlyUsed Whether to also check recently used OTPs
     * @return bool True if valid, false otherwise
     */
    public function verifyOtp($phone, $otp, $purpose = 'registration', $checkRecentlyUsed = false) {
        // For reset_password purpose, we don't want to mark the OTP as used
        // because we need to use it again in the reset_password.php endpoint
        if ($purpose === 'reset_password') {
            return $this->verifyOtpWithoutMarking($phone, $otp, $purpose);
        }

        // If we should check recently used OTPs (for registration after verification)
        if ($checkRecentlyUsed) {
            // First try to verify an unused OTP
            if ($this->verifyOtpInternal($phone, $otp, $purpose, false)) {
                return true;
            }

            // If that fails, check for a recently used OTP
            return $this->checkRecentlyUsedOtp($phone, $otp, $purpose);
        }

        return $this->verifyOtpInternal($phone, $otp, $purpose, true);
    }

    /**
     * Internal method to verify an OTP
     *
     * @param string $phone Phone number
     * @param string $otp OTP to verify
     * @param string $purpose Purpose of OTP
     * @param bool $markAsUsed Whether to mark the OTP as used if valid
     * @return bool True if valid, false otherwise
     */
    private function verifyOtpInternal($phone, $otp, $purpose = 'registration', $markAsUsed = true) {
        // Format phone number
        $phone = formatPhone($phone);
        error_log("OtpManager::verifyOtpInternal - Formatted phone: {$phone}, OTP: {$otp}, Purpose: {$purpose}");

        // Get OTP record - debug the query
        $query = "
            SELECT * FROM otps
            WHERE phone = ?
            AND purpose = ?
            AND is_used = 0
            AND attempt_count < ?
            AND expires_at > NOW()
            ORDER BY created_at DESC
            LIMIT 1
        ";
        error_log("OtpManager::verifyOtpInternal - SQL Query: " . $query);
        error_log("OtpManager::verifyOtpInternal - Parameters: " . json_encode([$phone, $purpose, OTP_MAX_ATTEMPTS]));

        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$phone, $purpose, OTP_MAX_ATTEMPTS]);

        $otpRecord = $stmt->fetch();

        if (!$otpRecord) {
            // No valid OTP found
            error_log("OtpManager::verifyOtpInternal - No valid OTP found for phone: {$phone}, purpose: {$purpose}");
            logOtpActivity($phone, getClientIp(), 'expired', $this->pdo);
            return false;
        }

        error_log("OtpManager::verifyOtpInternal - Found OTP record: " . json_encode($otpRecord));

        // Increment attempt count
        $stmt = $this->pdo->prepare("
            UPDATE otps
            SET attempt_count = attempt_count + 1
            WHERE id = ?
        ");
        $stmt->execute([$otpRecord['id']]);
        error_log("OtpManager::verifyOtpInternal - Incremented attempt count for OTP ID: {$otpRecord['id']}");

        // Check if OTP matches
        if ($otpRecord['otp'] === $otp) {
            if ($markAsUsed) {
                error_log("OtpManager::verifyOtpInternal - OTP matches! Marking as used.");
                // Mark OTP as used
                $stmt = $this->pdo->prepare("
                    UPDATE otps
                    SET is_used = 1
                    WHERE id = ?
                ");
                $stmt->execute([$otpRecord['id']]);
            } else {
                error_log("OtpManager::verifyOtpInternal - OTP matches! Not marking as used.");
            }

            // Log successful verification
            logOtpActivity($phone, getClientIp(), 'verified', $this->pdo);

            return true;
        }

        error_log("OtpManager::verifyOtpInternal - OTP does not match. Stored: {$otpRecord['otp']}, Provided: {$otp}");
        return false;
    }

    /**
     * Verify an OTP without marking it as used
     *
     * @param string $phone Phone number
     * @param string $otp OTP to verify
     * @param string $purpose Purpose of OTP
     * @return bool True if valid, false otherwise
     */
    public function verifyOtpWithoutMarking($phone, $otp, $purpose = 'registration') {
        // Format phone number
        $phone = formatPhone($phone);
        error_log("OtpManager::verifyOtpWithoutMarking - Formatted phone: {$phone}, OTP: {$otp}, Purpose: {$purpose}");

        // Get OTP record
        $stmt = $this->pdo->prepare("
            SELECT * FROM otps
            WHERE phone = ?
            AND purpose = ?
            AND is_used = 0
            AND attempt_count < ?
            AND expires_at > NOW()
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$phone, $purpose, OTP_MAX_ATTEMPTS]);

        $otpRecord = $stmt->fetch();

        if (!$otpRecord) {
            // No valid OTP found
            error_log("OtpManager::verifyOtpWithoutMarking - No valid OTP found for phone: {$phone}, purpose: {$purpose}");
            return false;
        }

        error_log("OtpManager::verifyOtpWithoutMarking - Found OTP record: " . json_encode($otpRecord));

        // Increment attempt count
        $stmt = $this->pdo->prepare("
            UPDATE otps
            SET attempt_count = attempt_count + 1
            WHERE id = ?
        ");
        $stmt->execute([$otpRecord['id']]);
        error_log("OtpManager::verifyOtpWithoutMarking - Incremented attempt count for OTP ID: {$otpRecord['id']}");

        // Check if OTP matches
        if ($otpRecord['otp'] === $otp) {
            error_log("OtpManager::verifyOtpWithoutMarking - OTP matches! Not marking as used.");
            // Log successful verification
            logOtpActivity($phone, getClientIp(), 'verified', $this->pdo);

            return true;
        }

        error_log("OtpManager::verifyOtpWithoutMarking - OTP does not match. Stored: {$otpRecord['otp']}, Provided: {$otp}");
        return false;
    }

    /**
     * Check if an OTP exists and is valid
     *
     * @param string $phone Phone number
     * @param string $purpose Purpose of OTP
     * @return bool True if valid OTP exists, false otherwise
     */
    public function hasValidOtp($phone, $purpose = 'registration') {
        // Format phone number
        $phone = formatPhone($phone);

        // Check for valid OTP
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM otps
            WHERE phone = ?
            AND purpose = ?
            AND is_used = 0
            AND attempt_count < ?
            AND expires_at > NOW()
        ");
        $stmt->execute([$phone, $purpose, OTP_MAX_ATTEMPTS]);

        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    /**
     * Check if a recently used OTP is valid
     *
     * This is used for registration after OTP verification
     *
     * @param string $phone Phone number
     * @param string $otp OTP to verify
     * @param string $purpose Purpose of OTP
     * @return bool True if valid, false otherwise
     */
    public function checkRecentlyUsedOtp($phone, $otp, $purpose = 'registration') {
        // Format phone number
        $phone = formatPhone($phone);
        error_log("OtpManager::checkRecentlyUsedOtp - Formatted phone: {$phone}, OTP: {$otp}, Purpose: {$purpose}");

        // Get recently used OTP record (used within the last 10 minutes)
        $query = "
            SELECT * FROM otps
            WHERE phone = ?
            AND purpose = ?
            AND otp = ?
            AND is_used = 1
            AND created_at > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
            ORDER BY created_at DESC
            LIMIT 1
        ";
        error_log("OtpManager::checkRecentlyUsedOtp - SQL Query: " . $query);
        error_log("OtpManager::checkRecentlyUsedOtp - Parameters: " . json_encode([$phone, $purpose, $otp]));

        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$phone, $purpose, $otp]);

        $otpRecord = $stmt->fetch();

        if (!$otpRecord) {
            error_log("OtpManager::checkRecentlyUsedOtp - No recently used OTP found");
            return false;
        }

        error_log("OtpManager::checkRecentlyUsedOtp - Found recently used OTP: " . json_encode($otpRecord));
        return true;
    }

    /**
     * Get remaining time for OTP in seconds
     *
     * @param string $phone Phone number
     * @param string $purpose Purpose of OTP
     * @return int Remaining time in seconds or 0 if no valid OTP
     */
    public function getRemainingTime($phone, $purpose = 'registration') {
        // Format phone number
        $phone = formatPhone($phone);

        // Get OTP record
        $stmt = $this->pdo->prepare("
            SELECT expires_at
            FROM otps
            WHERE phone = ?
            AND purpose = ?
            AND is_used = 0
            AND attempt_count < ?
            AND expires_at > NOW()
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$phone, $purpose, OTP_MAX_ATTEMPTS]);

        $otpRecord = $stmt->fetch();

        if (!$otpRecord) {
            return 0;
        }

        $expiryTime = strtotime($otpRecord['expires_at']);
        $currentTime = time();

        return max(0, $expiryTime - $currentTime);
    }
}
