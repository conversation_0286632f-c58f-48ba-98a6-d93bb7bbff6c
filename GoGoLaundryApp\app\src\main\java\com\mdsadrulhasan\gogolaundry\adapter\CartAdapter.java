package com.mdsadrulhasan.gogolaundry.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.CartItem;

import java.util.ArrayList;
import java.util.List;

/**
 * Adapter for displaying cart items in a RecyclerView
 */
public class CartAdapter extends RecyclerView.Adapter<CartAdapter.CartItemViewHolder> {

    private List<CartItem> cartItems;
    private final CartItemActionListener listener;

    /**
     * Interface for handling cart item actions
     */
    public interface CartItemActionListener {
        void onRemoveItem(CartItem item, int position);
        void onIncreaseQuantity(CartItem item, int position);
        void onDecreaseQuantity(CartItem item, int position);
    }

    /**
     * Constructor
     *
     * @param cartItems List of cart items
     * @param listener Action listener
     */
    public CartAdapter(List<CartItem> cartItems, CartItemActionListener listener) {
        this.cartItems = cartItems;
        this.listener = listener;
    }

    /**
     * Update cart items
     *
     * @param newItems New list of cart items
     */
    public void updateCartItems(List<CartItem> newItems) {
        this.cartItems = new ArrayList<>(newItems);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public CartItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_cart, parent, false);
        return new CartItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CartItemViewHolder holder, int position) {
        CartItem item = cartItems.get(position);
        holder.bind(item, position);
    }

    @Override
    public int getItemCount() {
        return cartItems.size();
    }

    /**
     * ViewHolder for cart items
     */
    class CartItemViewHolder extends RecyclerView.ViewHolder {

        private final ImageView itemImage;
        private final TextView itemName;
        private final TextView serviceName;
        private final TextView itemPrice;
        private final TextView itemQuantity;
        private final TextView itemSubtotal;
        private final FrameLayout removeButton; // Changed from ImageButton to FrameLayout
        private final FrameLayout increaseButton; // Changed from ImageButton to FrameLayout
        private final FrameLayout decreaseButton; // Changed from ImageButton to FrameLayout

        public CartItemViewHolder(@NonNull View itemView) {
            super(itemView);
            itemImage = itemView.findViewById(R.id.cart_item_image);
            itemName = itemView.findViewById(R.id.cart_item_name);
            serviceName = itemView.findViewById(R.id.cart_service_name);
            itemPrice = itemView.findViewById(R.id.cart_item_price);
            itemQuantity = itemView.findViewById(R.id.cart_item_quantity);
            itemSubtotal = itemView.findViewById(R.id.cart_item_subtotal);
            removeButton = itemView.findViewById(R.id.cart_remove_button);
            increaseButton = itemView.findViewById(R.id.cart_increase_button);
            decreaseButton = itemView.findViewById(R.id.cart_decrease_button);
        }

        /**
         * Bind cart item data to views
         *
         * @param item Cart item to bind
         * @param position Position in adapter
         */
        public void bind(final CartItem item, final int position) {
            // Set item name
            itemName.setText(item.getName());

            // Set service name if available
            if (item.getServiceName() != null && !item.getServiceName().isEmpty()) {
                serviceName.setText(item.getServiceName());
                serviceName.setVisibility(View.VISIBLE);
            } else {
                serviceName.setVisibility(View.GONE);
            }

            // Set price
            itemPrice.setText(item.getFormattedPrice());

            // Set quantity
            itemQuantity.setText(String.valueOf(item.getQuantity()));

            // Set subtotal
            itemSubtotal.setText(item.getFormattedSubtotal());

            // Load image if available
            if (item.getImageUrl() != null && !item.getImageUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(item.getImageUrl())
                        .apply(new RequestOptions()
                                .placeholder(R.drawable.placeholder_image)
                                .error(R.drawable.placeholder_image))
                        .into(itemImage);
                itemImage.setVisibility(View.VISIBLE);
            } else {
                itemImage.setVisibility(View.GONE);
            }

            // Set click listeners
            removeButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onRemoveItem(item, position);
                }
            });

            increaseButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onIncreaseQuantity(item, position);
                }
            });

            decreaseButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onDecreaseQuantity(item, position);
                }
            });
        }
    }
}
