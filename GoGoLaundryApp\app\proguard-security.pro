# ===============================================================================
# ADDITIONAL SECURITY CONFIGURATION FOR GOGOLAUNDRY
# ===============================================================================
# This file contains extra security measures and anti-tampering rules
# ===============================================================================

# ===============================================================================
# ANTI-DEBUGGING & ANTI-TAMPERING
# ===============================================================================

# Remove all debug information completely
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    public static *** i(...);
    public static *** w(...);
    public static *** e(...);
    public static *** wtf(...);
}

# Remove BuildConfig debug information
-assumenosideeffects class **.BuildConfig {
    public static final boolean DEBUG return false;
}

# ===============================================================================
# STRING ENCRYPTION & OBFUSCATION
# ===============================================================================

# Obfuscate all string constants (API URLs, keys, etc.)
-adaptclassstrings com.mdsadrulhasan.gogolaundry.**

# Rename all resource files to make them harder to identify
-adaptresourcefilenames **.xml,**.png,**.jpg,**.jpeg,**.gif,**.webp
-adaptresourcefilecontents **.xml,**.html,**.js,**.css

# ===============================================================================
# PACKAGE & CLASS NAME OBFUSCATION
# ===============================================================================

# Flatten package hierarchy completely
-repackageclasses 'a'
-flattenpackagehierarchy 'a'

# Use short, meaningless names for obfuscated classes
-obfuscationdictionary obfuscation-dictionary.txt
-classobfuscationdictionary obfuscation-dictionary.txt
-packageobfuscationdictionary obfuscation-dictionary.txt

# ===============================================================================
# METHOD & FIELD OBFUSCATION
# ===============================================================================

# Aggressively obfuscate method and field names
-overloadaggressively
-useuniqueclassmembernames
-allowaccessmodification

# ===============================================================================
# REFLECTION BLOCKING
# ===============================================================================

# Block common reflection attacks
-keepattributes !LocalVariableTable,!LocalVariableTypeTable

# ===============================================================================
# ENHANCED ANTI-STATIC ANALYSIS
# ===============================================================================

# Maximum obfuscation and optimization (corrected conflicting rules)
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*,!method/inlining/*
-optimizationpasses 7

# ===============================================================================
# ENHANCED RESOURCE PROTECTION
# ===============================================================================

# Obfuscate resource names with specific patterns
-adaptresourcefilenames **.xml,**.png,**.jpg,**.jpeg,**.gif,**.webp,**.json,**.properties
-adaptresourcefilecontents **.xml,**.json,**.properties,**.html

# ===============================================================================
# NATIVE CODE PROTECTION
# ===============================================================================

# If you have native libraries, protect them too
-keepclasseswithmembernames class * {
    native <methods>;
}

# ===============================================================================
# ADVANCED ANTI-REVERSE ENGINEERING
# ===============================================================================

# Remove reflection helpers that aid reverse engineering
-assumenosideeffects class java.lang.Class {
    public java.lang.String getName();
    public java.lang.String getSimpleName();
    public java.lang.String getCanonicalName();
    public java.lang.Package getPackage();
}

# Remove stack trace information
-assumenosideeffects class java.lang.Throwable {
    public void printStackTrace();
    public void printStackTrace(java.io.PrintStream);
    public void printStackTrace(java.io.PrintWriter);
    public java.lang.String getMessage();
    public java.lang.String getLocalizedMessage();
}

# Remove System.out calls
-assumenosideeffects class java.lang.System {
    public static void out.println(...);
    public static void out.print(...);
    public static void err.println(...);
    public static void err.print(...);
}

# ===============================================================================
# FINAL CLEANUP & HARDENING
# ===============================================================================

# Remove all unnecessary metadata that could help attackers
-keepattributes !SourceFile,!LineNumberTable,!LocalVariableTable,!LocalVariableTypeTable,!Deprecated,!SourceDir,!Synthetic

# Enable aggressive optimization and shrinking
-optimizationpasses 7
-allowaccessmodification

# Generate mapping files for debugging (keep these secure!)
-printmapping mapping.txt
-printseeds seeds.txt
-printusage usage.txt
