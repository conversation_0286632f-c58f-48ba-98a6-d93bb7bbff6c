<?php
/**
 * Admin Logout Page
 *
 * This page logs out the admin user
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/AdminManager.php';

// Check if admin is logged in
if (isset($_SESSION['admin_id']) && isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    // Initialize admin manager
    $adminManager = new AdminManager($pdo);

    // Log logout action
    $adminManager->logAdminAction($_SESSION['admin_id'], 'logout', 'Admin logged out', getClientIp());
}

// Clear session
session_unset();
session_destroy();

// Redirect to login page with success message
header('Location: login.php?logout=success');
exit;
