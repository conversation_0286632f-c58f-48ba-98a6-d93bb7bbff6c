package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.pdf.PdfDocument;
import android.net.Uri;
import android.os.Bundle;
import android.os.CancellationSignal;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.ParcelFileDescriptor;
import android.print.PageRange;
import android.print.PrintAttributes;
import android.print.PrintDocumentAdapter;
import android.print.PrintDocumentInfo;
import android.print.PrintManager;
import android.print.pdf.PrintedPdfDocument;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.InvoiceItemAdapter;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.utils.DateUtils;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.OrdersViewModel;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Fragment for displaying an invoice for an order
 */
public class InvoiceFragment extends Fragment {

    private static final String TAG = "InvoiceFragment";
    private static final String ARG_ORDER_ID = "order_id";
    private static final String ARG_ORDER_NUMBER = "order_number";
    private static final String ARG_TRACKING_NUMBER = "tracking_number";

    private int orderId;
    private String orderNumber;
    private String trackingNumber;

    // Views
    private TextView customerName;
    private TextView customerPhone;
    private TextView customerAddress;
    private TextView invoiceNumber;
    private TextView orderNumberView;
    private TextView invoiceDate;
    private RecyclerView itemsRecyclerView;
    private TextView subtotalView;
    private TextView discountView;
    private TextView deliveryFeeView;
    private TextView totalView;
    private MaterialButton shareButton;
    private MaterialButton printButton;
    private View rootView;

    // ViewModel
    private OrdersViewModel viewModel;

    // Adapter
    private InvoiceItemAdapter adapter;

    // Current order
    private OrderEntity currentOrder;

    // Session manager
    private SessionManager sessionManager;
    private User currentUser;

    /**
     * Create a new instance of InvoiceFragment
     *
     * @param orderId Order ID
     * @param orderNumber Order number
     * @param trackingNumber Tracking number
     * @return New instance of InvoiceFragment
     */
    public static InvoiceFragment newInstance(int orderId, String orderNumber, String trackingNumber) {
        InvoiceFragment fragment = new InvoiceFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_ORDER_ID, orderId);
        args.putString(ARG_ORDER_NUMBER, orderNumber);
        args.putString(ARG_TRACKING_NUMBER, trackingNumber);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            orderId = getArguments().getInt(ARG_ORDER_ID);
            orderNumber = getArguments().getString(ARG_ORDER_NUMBER);
            trackingNumber = getArguments().getString(ARG_TRACKING_NUMBER);
        }

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(OrdersViewModel.class);

        // Initialize session manager
        sessionManager = new SessionManager(requireContext());
        currentUser = sessionManager.getUser();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.fragment_invoice, container, false);

        // Initialize views
        initViews();

        // Set up recycler view
        setupRecyclerView();

        // Load data
        loadOrderData();

        // Set up click listeners
        setupClickListeners();

        return rootView;
    }

    /**
     * Initialize views
     */
    private void initViews() {
        customerName = rootView.findViewById(R.id.customer_name);
        customerPhone = rootView.findViewById(R.id.customer_phone);
        customerAddress = rootView.findViewById(R.id.customer_address);
        invoiceNumber = rootView.findViewById(R.id.invoice_number);
        orderNumberView = rootView.findViewById(R.id.order_number);
        invoiceDate = rootView.findViewById(R.id.invoice_date);
        itemsRecyclerView = rootView.findViewById(R.id.items_recycler_view);
        subtotalView = rootView.findViewById(R.id.subtotal);
        discountView = rootView.findViewById(R.id.discount);
        deliveryFeeView = rootView.findViewById(R.id.delivery_fee);
        totalView = rootView.findViewById(R.id.total);
        shareButton = rootView.findViewById(R.id.share_button);
        printButton = rootView.findViewById(R.id.print_button);
    }

    /**
     * Set up recycler view
     */
    private void setupRecyclerView() {
        adapter = new InvoiceItemAdapter(new ArrayList<>());
        itemsRecyclerView.setAdapter(adapter);
    }

    /**
     * Load order data
     */
    private void loadOrderData() {
        // Load order by ID
        viewModel.getOrderById(orderId).observe(getViewLifecycleOwner(), resource -> {
            if (resource.isSuccess()) {
                currentOrder = resource.getData();
                if (currentOrder != null) {
                    updateUI(currentOrder);
                    loadOrderItems(currentOrder.getId());
                } else {
                    ToastUtils.showErrorToast(requireContext(), "Order not found");
                }
            } else if (resource.isError()) {
                ToastUtils.showErrorToast(requireContext(), "Error loading order: " + resource.getMessage());
            }
        });
    }

    /**
     * Load order items
     *
     * @param orderId Order ID
     */
    private void loadOrderItems(int orderId) {
        // Load order items by order ID
        viewModel.getOrderItemsByOrderId(orderId).observe(getViewLifecycleOwner(), resource -> {
            if (resource.isSuccess()) {
                List<OrderItemEntity> orderItems = resource.getData();
                if (orderItems != null && !orderItems.isEmpty()) {
                    adapter.updateItems(orderItems);
                } else {
                    // If no items found, use dummy data
                    adapter.updateItems(getDummyOrderItems());
                }
            } else if (resource.isError()) {
                // If error loading items, use dummy data
                adapter.updateItems(getDummyOrderItems());
                ToastUtils.showErrorToast(requireContext(), "Error loading order items: " + resource.getMessage());
            }
        });
    }

    /**
     * Update UI with order data
     *
     * @param order Order data
     */
    private void updateUI(OrderEntity order) {
        // Set customer info
        if (order.getCustomerName() != null) {
            customerName.setText(order.getCustomerName());
        } else if (currentUser != null) {
            customerName.setText(currentUser.getFullName());
        } else {
            customerName.setText("Customer");
        }

        if (order.getCustomerPhone() != null) {
            customerPhone.setText(order.getCustomerPhone());
        } else if (currentUser != null) {
            customerPhone.setText(currentUser.getPhone());
        } else {
            customerPhone.setText("N/A");
        }

        // Set address
        String address = order.getDeliveryAddress();
        if (address != null && !address.isEmpty()) {
            customerAddress.setText(address);
        } else if (currentUser != null && currentUser.getAddress() != null) {
            customerAddress.setText(currentUser.getAddress());
        } else {
            customerAddress.setText("N/A");
        }

        // Set invoice details
        invoiceNumber.setText("Invoice #: INV-" + order.getId());
        orderNumberView.setText("Order #: " + order.getOrderNumber());

        // Format date
        String dateStr = "Date: N/A";
        if (order.getCreatedAt() != null) {
            dateStr = "Date: " + DateUtils.formatDate(order.getCreatedAt(), "MMM dd, yyyy");
        }
        invoiceDate.setText(dateStr);

        // Set summary
        subtotalView.setText(String.format("৳ %.2f", order.getSubtotal()));
        discountView.setText(String.format("৳ %.2f", order.getDiscount()));
        deliveryFeeView.setText(String.format("৳ %.2f", order.getDeliveryFee()));
        totalView.setText(String.format("৳ %.2f", order.getTotal()));
    }

    /**
     * Set up click listeners
     */
    private void setupClickListeners() {
        Log.d(TAG, "Setting up click listeners");

        // Share button
        shareButton.setOnClickListener(v -> {
            Log.d(TAG, "Share button clicked");
            shareInvoice();
        });

        // Print button
        printButton.setOnClickListener(v -> {
            Log.d(TAG, "Print button clicked");
            printInvoice();
        });

        Log.d(TAG, "Click listeners set up successfully");
    }

    /**
     * Share invoice as image
     */
    private void shareInvoice() {
        try {
            // Create bitmap from view
            Bitmap bitmap = getBitmapFromView(rootView);

            // Save bitmap to file
            File cachePath = new File(requireContext().getExternalFilesDir(Environment.DIRECTORY_PICTURES), "invoice_" + System.currentTimeMillis() + ".jpg");
            FileOutputStream stream = new FileOutputStream(cachePath);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
            stream.close();

            // Get URI from file
            Uri uri = FileProvider.getUriForFile(requireContext(), "com.mdsadrulhasan.gogolaundry.fileprovider", cachePath);

            // Create share intent
            Intent intent = new Intent(Intent.ACTION_SEND);
            intent.setType("image/jpeg");
            intent.putExtra(Intent.EXTRA_STREAM, uri);
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            // Start share activity
            startActivity(Intent.createChooser(intent, "Share Invoice"));
        } catch (Exception e) {
            ToastUtils.showErrorToast(requireContext(), "Error sharing invoice: " + e.getMessage());
        }
    }

    /**
     * Print invoice using MainActivity's print method
     */
    private void printInvoice() {
        Log.d(TAG, "printInvoice() called");

        // Check if fragment is attached to MainActivity
        if (getActivity() == null || !(getActivity() instanceof com.mdsadrulhasan.gogolaundry.MainActivity)) {
            Log.e(TAG, "Fragment not attached to MainActivity");
            ToastUtils.showErrorToast(requireContext(), "Cannot print: Activity not available");
            return;
        }

        try {
            // Get MainActivity instance
            com.mdsadrulhasan.gogolaundry.MainActivity mainActivity =
                (com.mdsadrulhasan.gogolaundry.MainActivity) getActivity();

            Log.d(TAG, "Got MainActivity instance: " + mainActivity.getClass().getName());

            // Set job name
            String jobName = getString(R.string.app_name) + " - Invoice #" + (currentOrder != null ? currentOrder.getId() : "");
            Log.d(TAG, "Print job name: " + jobName);

            // Create print adapter with MainActivity context
            InvoicePrintDocumentAdapter printAdapter = new InvoicePrintDocumentAdapter(mainActivity);
            Log.d(TAG, "Created print adapter with MainActivity context");

            // Call MainActivity's print method
            Log.d(TAG, "About to call MainActivity.printDocument()");
            Log.d(TAG, "MainActivity instance: " + mainActivity.toString());
            Log.d(TAG, "Job name: " + jobName);
            Log.d(TAG, "Print adapter: " + printAdapter.toString());

            mainActivity.printDocument(jobName, printAdapter);

            Log.d(TAG, "MainActivity.printDocument() method call completed");
            Log.d(TAG, "Waiting for print operation to complete...");

        } catch (Exception e) {
            Log.e(TAG, "Exception in printInvoice", e);
            ToastUtils.showErrorToast(requireContext(), "Error starting print job: " + e.getMessage());
        }
    }

    /**
     * Get bitmap from view
     *
     * @param view View to convert to bitmap
     * @return Bitmap of view
     */
    private Bitmap getBitmapFromView(View view) {
        Bitmap bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        view.draw(canvas);
        return bitmap;
    }

    /**
     * Get dummy order items for testing
     *
     * @return List of dummy order items
     */
    private List<OrderItemEntity> getDummyOrderItems() {
        List<OrderItemEntity> items = new ArrayList<>();

        // Create dummy items
        OrderItemEntity item1 = new OrderItemEntity();
        item1.setId(1);
        item1.setOrderId(orderId);
        item1.setItemId(1);
        item1.setItemName("Shirt");
        item1.setServiceName("Wash & Iron");
        item1.setQuantity(2);
        item1.setPrice(50.0);
        item1.setSubtotal(100.0);
        items.add(item1);

        OrderItemEntity item2 = new OrderItemEntity();
        item2.setId(2);
        item2.setOrderId(orderId);
        item2.setItemId(2);
        item2.setItemName("Pants");
        item2.setServiceName("Dry Clean");
        item2.setQuantity(1);
        item2.setPrice(80.0);
        item2.setSubtotal(80.0);
        items.add(item2);

        OrderItemEntity item3 = new OrderItemEntity();
        item3.setId(3);
        item3.setOrderId(orderId);
        item3.setItemId(3);
        item3.setItemName("Bedsheet");
        item3.setServiceName("Wash & Fold");
        item3.setQuantity(1);
        item3.setPrice(120.0);
        item3.setSubtotal(120.0);
        items.add(item3);

        return items;
    }

    /**
     * Custom PrintDocumentAdapter for printing invoices
     */
    private class InvoicePrintDocumentAdapter extends PrintDocumentAdapter {
        private Context context;
        private PrintedPdfDocument pdfDocument;
        private int pageCount = 1; // Default to 1 page for invoice

        /**
         * Constructor
         *
         * @param context Context
         */
        public InvoicePrintDocumentAdapter(Context context) {
            this.context = context;
            Log.d(TAG, "InvoicePrintDocumentAdapter created with context: " + (context != null ? context.getClass().getSimpleName() : "null"));
        }

        @Override
        public void onLayout(PrintAttributes oldAttributes,
                            PrintAttributes newAttributes,
                            CancellationSignal cancellationSignal,
                            LayoutResultCallback callback,
                            Bundle extras) {

            Log.d(TAG, "onLayout() called in PrintDocumentAdapter");

            try {
                // Create a new PDF document with the requested page attributes
                Log.d(TAG, "Creating PrintedPdfDocument with context: " + (context != null ? context.getClass().getSimpleName() : "null"));
                pdfDocument = new PrintedPdfDocument(context, newAttributes);
                Log.d(TAG, "PrintedPdfDocument created successfully");

                // Check for cancellation
                if (cancellationSignal.isCanceled()) {
                    Log.d(TAG, "Print operation was cancelled");
                    callback.onLayoutCancelled();
                    return;
                }

                // For invoices, we'll use a fixed page count of 1 for simplicity
                // In a real app, you might calculate based on content length
                pageCount = 1;
                Log.d(TAG, "Page count set to: " + pageCount);

                // Create print document info
                String fileName = "invoice_" + (currentOrder != null ? currentOrder.getId() : "unknown") + ".pdf";
                Log.d(TAG, "Creating PrintDocumentInfo with filename: " + fileName);

                PrintDocumentInfo info = new PrintDocumentInfo
                        .Builder(fileName)
                        .setContentType(PrintDocumentInfo.CONTENT_TYPE_DOCUMENT)
                        .setPageCount(pageCount)
                        .build();

                Log.d(TAG, "PrintDocumentInfo created, calling onLayoutFinished");
                // Content layout is complete
                callback.onLayoutFinished(info, true);
                Log.d(TAG, "onLayoutFinished called successfully");

            } catch (Exception e) {
                Log.e(TAG, "Exception in onLayout", e);
                callback.onLayoutFailed("Layout failed: " + e.getMessage());
            }
        }

        @Override
        public void onWrite(PageRange[] pageRanges,
                           ParcelFileDescriptor destination,
                           CancellationSignal cancellationSignal,
                           WriteResultCallback callback) {

            Log.d(TAG, "onWrite() called in PrintDocumentAdapter");

            try {
                // Check for cancellation
                if (cancellationSignal.isCanceled()) {
                    Log.d(TAG, "Print operation was cancelled in onWrite");
                    callback.onWriteCancelled();
                    return;
                }

                Log.d(TAG, "Starting PDF page creation");
                // Start a page
                PdfDocument.Page page = pdfDocument.startPage(0);
                Log.d(TAG, "PDF page created successfully");

                // Draw the invoice content
                Log.d(TAG, "Drawing invoice content on canvas");
                drawInvoice(page.getCanvas());
                Log.d(TAG, "Invoice content drawn successfully");

                // Finish the page
                pdfDocument.finishPage(page);
                Log.d(TAG, "PDF page finished");

                // Write the document to file
                Log.d(TAG, "Writing PDF document to file");
                pdfDocument.writeTo(new FileOutputStream(destination.getFileDescriptor()));
                Log.d(TAG, "PDF document written successfully");

                // Signal the print framework that the document is complete
                Log.d(TAG, "Calling onWriteFinished");
                callback.onWriteFinished(new PageRange[]{PageRange.ALL_PAGES});
                Log.d(TAG, "onWriteFinished called successfully");

            } catch (IOException e) {
                Log.e(TAG, "IOException in onWrite", e);
                // Report error
                callback.onWriteFailed(e.toString());
            } catch (Exception e) {
                Log.e(TAG, "Exception in onWrite", e);
                callback.onWriteFailed("Write failed: " + e.getMessage());
            } finally {
                // Close the document
                if (pdfDocument != null) {
                    Log.d(TAG, "Closing PDF document");
                    pdfDocument.close();
                }
            }
        }

        @Override
        public void onFinish() {
            Log.d(TAG, "onFinish() called in PrintDocumentAdapter");
            // Clean up resources if needed
        }

        /**
         * Draw invoice content on the canvas
         *
         * @param canvas Canvas to draw on
         */
        private void drawInvoice(Canvas canvas) {
            // Initialize paint for drawing
            Paint paint = new Paint();

            // Set page dimensions (in points, 1/72 inch)
            int pageWidth = canvas.getWidth();
            int pageHeight = canvas.getHeight();

            // Margins
            int leftMargin = 50;
            int topMargin = 50;
            int rightMargin = pageWidth - 50;

            // Current Y position for drawing
            int currentY = topMargin;

            // Draw company logo/header
            paint.setColor(Color.rgb(33, 150, 243)); // Primary color
            paint.setTextSize(24);
            paint.setFakeBoldText(true);
            canvas.drawText("GoGo Laundry", leftMargin, currentY, paint);

            // Draw invoice title
            paint.setTextSize(18);
            canvas.drawText("INVOICE", rightMargin - 80, currentY, paint);
            currentY += 40;

            // Draw separator line
            paint.setColor(Color.LTGRAY);
            canvas.drawRect(leftMargin, currentY, rightMargin, currentY + 1, paint);
            currentY += 20;

            // Draw customer info
            paint.setColor(Color.BLACK);
            paint.setTextSize(14);
            paint.setFakeBoldText(true);
            canvas.drawText("Invoice To:", leftMargin, currentY, paint);
            currentY += 20;

            paint.setFakeBoldText(false);
            paint.setTextSize(12);

            // Customer name
            String customerName = "Customer";
            if (currentOrder != null && currentOrder.getCustomerName() != null) {
                customerName = currentOrder.getCustomerName();
            } else if (currentUser != null) {
                customerName = currentUser.getFullName();
            }
            canvas.drawText(customerName, leftMargin, currentY, paint);
            currentY += 15;

            // Customer phone
            String customerPhone = "N/A";
            if (currentOrder != null && currentOrder.getCustomerPhone() != null) {
                customerPhone = currentOrder.getCustomerPhone();
            } else if (currentUser != null) {
                customerPhone = currentUser.getPhone();
            }
            canvas.drawText(customerPhone, leftMargin, currentY, paint);
            currentY += 15;

            // Customer address
            String address = "N/A";
            if (currentOrder != null && currentOrder.getDeliveryAddress() != null) {
                address = currentOrder.getDeliveryAddress();
            } else if (currentUser != null && currentUser.getAddress() != null) {
                address = currentUser.getAddress();
            }
            canvas.drawText(address, leftMargin, currentY, paint);

            // Draw invoice details on the right side
            int detailsX = pageWidth / 2 + 50;
            currentY = topMargin + 60; // Reset Y position for invoice details

            paint.setFakeBoldText(true);
            canvas.drawText("Invoice Details:", detailsX, currentY, paint);
            currentY += 20;

            paint.setFakeBoldText(false);

            // Invoice number
            String invoiceNumber = "Invoice #: INV-" + (currentOrder != null ? currentOrder.getId() : "N/A");
            canvas.drawText(invoiceNumber, detailsX, currentY, paint);
            currentY += 15;

            // Order number
            String orderNum = "Order #: " + (currentOrder != null ? currentOrder.getOrderNumber() : "N/A");
            canvas.drawText(orderNum, detailsX, currentY, paint);
            currentY += 15;

            // Date
            String dateStr = "Date: ";
            if (currentOrder != null && currentOrder.getCreatedAt() != null) {
                dateStr += DateUtils.formatDate(currentOrder.getCreatedAt(), "MMM dd, yyyy");
            } else {
                dateStr += "N/A";
            }
            canvas.drawText(dateStr, detailsX, currentY, paint);
            currentY = topMargin + 160; // Reset Y position for items table

            // Draw items header
            paint.setColor(Color.rgb(33, 150, 243)); // Primary color
            paint.setFakeBoldText(true);

            // Table header background
            paint.setStyle(Paint.Style.FILL);
            canvas.drawRect(leftMargin, currentY - 15, rightMargin, currentY + 5, paint);

            // Table header text
            paint.setColor(Color.WHITE);
            canvas.drawText("Item", leftMargin + 10, currentY, paint);
            canvas.drawText("Qty", leftMargin + 230, currentY, paint);
            canvas.drawText("Price", leftMargin + 280, currentY, paint);
            canvas.drawText("Total", leftMargin + 350, currentY, paint);
            currentY += 25;

            // Draw items
            paint.setColor(Color.BLACK);
            paint.setFakeBoldText(false);

            List<OrderItemEntity> items = adapter.getItems();
            for (OrderItemEntity item : items) {
                // Item name with service
                String itemText = item.getItemName();
                if (item.getServiceName() != null && !item.getServiceName().isEmpty()) {
                    itemText += " (" + item.getServiceName() + ")";
                }
                canvas.drawText(itemText, leftMargin + 10, currentY, paint);

                // Quantity
                canvas.drawText(String.valueOf(item.getQuantity()), leftMargin + 230, currentY, paint);

                // Price
                canvas.drawText(String.format("৳ %.2f", item.getPrice()), leftMargin + 280, currentY, paint);

                // Total
                canvas.drawText(String.format("৳ %.2f", item.getSubtotal()), leftMargin + 350, currentY, paint);

                currentY += 20;
            }

            // Draw separator line
            paint.setColor(Color.LTGRAY);
            currentY += 10;
            canvas.drawRect(leftMargin, currentY, rightMargin, currentY + 1, paint);
            currentY += 20;

            // Draw summary
            paint.setColor(Color.BLACK);

            // Subtotal
            canvas.drawText("Subtotal:", leftMargin + 280, currentY, paint);
            double subtotal = currentOrder != null ? currentOrder.getSubtotal() : 0.0;
            canvas.drawText(String.format("৳ %.2f", subtotal), leftMargin + 350, currentY, paint);
            currentY += 20;

            // Discount
            canvas.drawText("Discount:", leftMargin + 280, currentY, paint);
            double discount = currentOrder != null ? currentOrder.getDiscount() : 0.0;
            canvas.drawText(String.format("৳ %.2f", discount), leftMargin + 350, currentY, paint);
            currentY += 20;

            // Delivery fee
            canvas.drawText("Delivery Fee:", leftMargin + 280, currentY, paint);
            double deliveryFee = currentOrder != null ? currentOrder.getDeliveryFee() : 0.0;
            canvas.drawText(String.format("৳ %.2f", deliveryFee), leftMargin + 350, currentY, paint);
            currentY += 20;

            // Draw separator line
            paint.setColor(Color.LTGRAY);
            canvas.drawRect(leftMargin, currentY, rightMargin, currentY + 1, paint);
            currentY += 20;

            // Total
            paint.setColor(Color.rgb(33, 150, 243)); // Primary color
            paint.setFakeBoldText(true);
            canvas.drawText("Total:", leftMargin + 280, currentY, paint);
            double total = currentOrder != null ? currentOrder.getTotal() : 0.0;
            canvas.drawText(String.format("৳ %.2f", total), leftMargin + 350, currentY, paint);

            // Draw footer
            currentY = pageHeight - 100;
            paint.setColor(Color.LTGRAY);
            canvas.drawRect(leftMargin, currentY, rightMargin, currentY + 1, paint);
            currentY += 20;

            paint.setColor(Color.BLACK);
            paint.setFakeBoldText(true);
            paint.setTextAlign(Paint.Align.CENTER);
            canvas.drawText("Thank you for your business!", pageWidth / 2, currentY, paint);
            currentY += 20;

            paint.setFakeBoldText(false);
            paint.setTextSize(10);
            canvas.drawText("For any questions, please contact us at:", pageWidth / 2, currentY, paint);
            currentY += 15;
            canvas.drawText("+880 1234567890 | <EMAIL>", pageWidth / 2, currentY, paint);
        }
    }
}
