package com.mdsadrulhasan.gogolaundry;

import android.app.Activity;
import android.app.Application;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import androidx.room.Room;

import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.FirebaseMessaging;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.database.AppDatabase;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.fcm.FCMTokenRequest;
import com.mdsadrulhasan.gogolaundry.utils.AppExecutors;
import com.mdsadrulhasan.gogolaundry.utils.ConfigUpdateManager;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import java.util.List;

/**
 * Application class for GoGoLaundry
 * Initializes app-wide components like database, session manager, etc.
 */
public class GoGoLaundryApp extends Application {
    private static final String TAG = "GoGoLaundryApp";

    // Singleton instance
    private static GoGoLaundryApp instance;

    // Database instance
    private AppDatabase database;

    // Session manager
    private SessionManager sessionManager;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Application onCreate");

        // Set instance
        instance = this;

        // Initialize database
        database = Room.databaseBuilder(this, AppDatabase.class, "gogolaundry-db")
                .fallbackToDestructiveMigration() // For simplicity during development
                .build();

        // Initialize session manager
        sessionManager = new SessionManager(this);

        // Initialize config update manager
        ConfigUpdateManager.getInstance(this);

        // Initialize Firebase
        try {
            FirebaseApp.initializeApp(this);
            Log.d(TAG, "Firebase initialized successfully");

            // Initialize FCM
            initializeFCM();
        } catch (Exception e) {
            Log.e(TAG, "Error initializing Firebase: " + e.getMessage(), e);
        }

        // Clear any dummy data from the database
        clearDummyData();
    }

    /**
     * Initialize Firebase Cloud Messaging
     */
    private void initializeFCM() {
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(task -> {
                    if (!task.isSuccessful()) {
                        Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                        return;
                    }

                    // Get new FCM registration token
                    String token = task.getResult();
                    Log.d(TAG, "FCM Token: " + token);

                    // Save token locally
                    sessionManager.saveFCMToken(token);

                    // Register token with server if user is logged in
                    if (sessionManager.isLoggedIn()) {
                        registerFCMTokenWithServer(token);
                    }
                });
    }

    /**
     * Register FCM token with server
     */
    private void registerFCMTokenWithServer(String token) {
        try {
            int userId = sessionManager.getUserId();
            String deviceId = sessionManager.getDeviceId();

            if (userId == 0) {
                Log.d(TAG, "User not logged in, skipping FCM token registration");
                return;
            }

            ApiService apiService = ApiClient.getClient(this).create(ApiService.class);
            FCMTokenRequest request = new FCMTokenRequest(userId, token, deviceId, "android");

            Call<Object> call = apiService.registerFCMToken(request);
            call.enqueue(new Callback<Object>() {
                @Override
                public void onResponse(Call<Object> call, Response<Object> response) {
                    if (response.isSuccessful()) {
                        Log.d(TAG, "FCM token registered successfully with server");
                    } else {
                        Log.e(TAG, "Failed to register FCM token with server: " + response.message());
                    }
                }

                @Override
                public void onFailure(Call<Object> call, Throwable t) {
                    Log.e(TAG, "Error registering FCM token with server: " + t.getMessage());
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception while registering FCM token: " + e.getMessage(), e);
        }
    }

    /**
     * Clear dummy data from the database
     */
    private void clearDummyData() {
        // We're not clearing any data now, as we want to show all orders
        Log.d(TAG, "Not clearing any data, showing all orders");
    }

    /**
     * Get application instance
     *
     * @return Application instance
     */
    public static GoGoLaundryApp getInstance() {
        return instance;
    }

    /**
     * Get database instance
     *
     * @return Database instance
     */
    public AppDatabase getDatabase() {
        return database;
    }

    /**
     * Get session manager
     *
     * @return Session manager
     */
    public SessionManager getSessionManager() {
        return sessionManager;
    }

    /**
     * Register FCM token with server (public method)
     * This can be called after user login to register the FCM token
     */
    public void registerFCMToken() {
        String token = sessionManager.getFCMToken();
        if (token != null && sessionManager.isLoggedIn()) {
            registerFCMTokenWithServer(token);
        } else {
            Log.d(TAG, "No FCM token available or user not logged in");
        }
    }

    // Current foreground activity
    private Activity currentActivity;

    /**
     * Set the current foreground activity
     * This should be called in onResume of each activity
     *
     * @param activity Current activity
     */
    public void setCurrentActivity(Activity activity) {
        this.currentActivity = activity;
        Log.d(TAG, "Current activity set to: " + (activity != null ? activity.getClass().getSimpleName() : "null"));
    }

    /**
     * Get the current foreground activity
     *
     * @return Current activity or null if none
     */
    public Activity getCurrentActivity() {
        return currentActivity;
    }

    /**
     * Navigate to the Orders screen
     * This method can be called from anywhere in the app to navigate to the Orders screen
     */
    public void navigateToOrders() {
        try {
            Log.d(TAG, "Attempting to navigate to Orders screen");

            // Get the current activity
            Activity activity = getCurrentActivity();

            if (activity != null) {
                Log.d(TAG, "Current activity is: " + activity.getClass().getSimpleName());

                if (activity instanceof MainActivity) {
                    // If we're in MainActivity, use its method
                    Log.d(TAG, "Using MainActivity.navigateToOrders()");
                    ((MainActivity) activity).navigateToOrders();
                } else {
                    // Otherwise, start a new MainActivity with intent to show orders
                    Log.d(TAG, "Starting new MainActivity with intent to show orders");
                    Intent intent = new Intent(activity, MainActivity.class);
                    intent.putExtra("navigate_to", "orders");
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    activity.startActivity(intent);
                }
            } else {
                // If no activity is available, create a new intent with new task flag
                Log.d(TAG, "No current activity, creating new task intent");
                Intent intent = new Intent(this, MainActivity.class);
                intent.putExtra("navigate_to", "orders");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to Orders screen: " + e.getMessage(), e);

            // Show toast on main thread
            new Handler(Looper.getMainLooper()).post(() -> {
                Toast.makeText(this, "Error navigating to Orders screen", Toast.LENGTH_SHORT).show();
            });
        }
    }
}
