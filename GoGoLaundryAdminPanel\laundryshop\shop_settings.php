<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'Shop Settings';
$currentPage = 'shop_settings';

// Get shop details
try {
    $shopStmt = $pdo->prepare("SELECT * FROM laundry_shops WHERE id = ?");
    $shopStmt->execute([$shopOwnerData['shop_id']]);
    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log('Shop settings fetch error: ' . $e->getMessage());
    $shop = null;
}

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'update_operating_hours':
            // Handle both structured and text format
            if (isset($_POST['operating_hours_type']) && $_POST['operating_hours_type'] === 'structured') {
                // Handle structured format (day-wise time settings)
                $operatingHoursData = [];
                $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

                foreach ($days as $day) {
                    $isOpen = isset($_POST[$day . '_is_open']) ? 1 : 0;
                    $openTime = sanitize($_POST[$day . '_open'] ?? '09:00');
                    $closeTime = sanitize($_POST[$day . '_close'] ?? '18:00');

                    $operatingHoursData[$day] = [
                        'is_open' => $isOpen,
                        'open_time' => $openTime,
                        'close_time' => $closeTime
                    ];
                }

                // Validate structured data
                $validation = validateOperatingHours($operatingHoursData);
                if (!$validation['valid']) {
                    $_SESSION['shop_error_message'] = 'Validation errors: ' . implode(', ', $validation['errors']);
                    break;
                }

                $operatingHours = json_encode($operatingHoursData);
            } else {
                // Handle text format (backward compatibility)
                $textOperatingHours = sanitize($_POST['operating_hours']);

                // Validate that it's not empty
                if (empty(trim($textOperatingHours))) {
                    $_SESSION['shop_error_message'] = 'Operating hours cannot be empty.';
                    break;
                }

                // Convert text to a simple JSON structure for database compatibility
                // Since the database expects JSON format, we'll store the text as a simple object
                $operatingHours = json_encode([
                    'format' => 'text',
                    'text' => $textOperatingHours,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            try {
                $stmt = $pdo->prepare("UPDATE laundry_shops SET operating_hours = ?, updated_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$operatingHours, $shopOwnerData['shop_id']]);

                if ($result && $stmt->rowCount() > 0) {
                    $_SESSION['shop_success_message'] = 'Operating hours updated successfully!';
                    // Refresh shop data
                    $shopStmt->execute([$shopOwnerData['shop_id']]);
                    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
                } else {
                    // Check if the shop exists
                    $checkStmt = $pdo->prepare("SELECT id FROM laundry_shops WHERE id = ?");
                    $checkStmt->execute([$shopOwnerData['shop_id']]);
                    if (!$checkStmt->fetch()) {
                        $_SESSION['shop_error_message'] = 'Shop not found.';
                    } else {
                        $_SESSION['shop_error_message'] = 'No changes were made to operating hours.';
                    }
                }
            } catch (PDOException $e) {
                error_log('Operating hours update error: ' . $e->getMessage());
                error_log('Shop ID: ' . $shopOwnerData['shop_id']);
                error_log('Operating hours data: ' . $operatingHours);
                $_SESSION['shop_error_message'] = 'Database error: ' . $e->getMessage();
            }
            break;

        case 'update_location':
            $latitude = floatval($_POST['latitude']);
            $longitude = floatval($_POST['longitude']);
            $address = sanitize($_POST['address']);

            if ($latitude != 0 && $longitude != 0) {
                try {
                    $stmt = $pdo->prepare("UPDATE laundry_shops SET latitude = ?, longitude = ?, address = ?, updated_at = NOW() WHERE id = ?");
                    if ($stmt->execute([$latitude, $longitude, $address, $shopOwnerData['shop_id']])) {
                        $_SESSION['shop_success_message'] = 'Location updated successfully!';
                        // Refresh shop data
                        $shopStmt->execute([$shopOwnerData['shop_id']]);
                        $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $_SESSION['shop_error_message'] = 'Failed to update location.';
                    }
                } catch (PDOException $e) {
                    error_log('Location update error: ' . $e->getMessage());
                    $_SESSION['shop_error_message'] = 'Failed to update location.';
                }
            } else {
                $_SESSION['shop_error_message'] = 'Please select a valid location on the map.';
            }
            break;
    }
}

if (!$shop) {
    $_SESSION['shop_error_message'] = 'Shop not found.';
    header('Location: index.php');
    exit;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Shop Settings</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shop['name']); ?>
        </span>
    </div>
</div>

<div class="row">
    <!-- Operating Hours -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Operating Hours
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="operatingHoursForm">
                    <input type="hidden" name="action" value="update_operating_hours">
                    <input type="hidden" name="operating_hours_type" id="operating_hours_type" value="text">

                    <!-- Format Toggle -->
                    <div class="mb-3">
                        <label class="form-label">Input Format</label>
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="format_toggle" id="format_text" value="text" checked>
                            <label class="btn btn-outline-primary" for="format_text">
                                <i class="fas fa-align-left me-1"></i>Text Format
                            </label>

                            <input type="radio" class="btn-check" name="format_toggle" id="format_structured" value="structured">
                            <label class="btn btn-outline-primary" for="format_structured">
                                <i class="fas fa-clock me-1"></i>Time Picker
                            </label>
                        </div>
                    </div>

                    <!-- Text Format Input -->
                    <div id="text_format_section" class="mb-3">
                        <label for="operating_hours" class="form-label">Operating Hours</label>
                        <textarea class="form-control" id="operating_hours" name="operating_hours" rows="4"
                                  placeholder="e.g., Monday - Friday: 8:00 AM - 8:00 PM&#10;Saturday: 9:00 AM - 6:00 PM&#10;Sunday: Closed"><?php
                        // Display existing operating hours in text format
                        $displayText = '';
                        if (!empty($shop['operating_hours'])) {
                            $decoded = json_decode($shop['operating_hours'], true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                if (isset($decoded['format']) && $decoded['format'] === 'text') {
                                    // Text format stored in JSON
                                    $displayText = $decoded['text'] ?? '';
                                } else {
                                    // Structured format - convert to readable text
                                    $displayText = formatOperatingHours($shop['operating_hours']);
                                }
                            } else {
                                // Fallback for non-JSON data
                                $displayText = $shop['operating_hours'];
                            }
                        }
                        echo htmlspecialchars($displayText);
                        ?></textarea>
                        <div class="form-text">Enter your shop's operating hours. This will be displayed to customers.</div>
                    </div>

                    <!-- Structured Format Input -->
                    <div id="structured_format_section" class="mb-3" style="display: none;">
                        <label class="form-label">Weekly Schedule</label>
                        <div class="row">
                            <?php
                            $days = [
                                'monday' => 'Monday',
                                'tuesday' => 'Tuesday',
                                'wednesday' => 'Wednesday',
                                'thursday' => 'Thursday',
                                'friday' => 'Friday',
                                'saturday' => 'Saturday',
                                'sunday' => 'Sunday'
                            ];

                            // Try to parse existing operating hours if it's JSON
                            $existingHours = [];
                            if (!empty($shop['operating_hours'])) {
                                $decoded = json_decode($shop['operating_hours'], true);
                                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                    $existingHours = $decoded;
                                }
                            }

                            foreach ($days as $dayKey => $dayName):
                                $isOpen = isset($existingHours[$dayKey]['is_open']) ? $existingHours[$dayKey]['is_open'] : true;
                                $openTime = isset($existingHours[$dayKey]['open_time']) ? $existingHours[$dayKey]['open_time'] : '09:00';
                                $closeTime = isset($existingHours[$dayKey]['close_time']) ? $existingHours[$dayKey]['close_time'] : '18:00';
                            ?>
                            <div class="col-12 mb-3">
                                <div class="card">
                                    <div class="card-body py-2">
                                        <div class="row align-items-center">
                                            <div class="col-md-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           id="<?php echo $dayKey; ?>_is_open"
                                                           name="<?php echo $dayKey; ?>_is_open"
                                                           <?php echo $isOpen ? 'checked' : ''; ?>>
                                                    <label class="form-check-label fw-bold" for="<?php echo $dayKey; ?>_is_open">
                                                        <?php echo $dayName; ?>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label small">Open Time</label>
                                                <input type="time" class="form-control form-control-sm"
                                                       name="<?php echo $dayKey; ?>_open"
                                                       value="<?php echo $openTime; ?>"
                                                       id="<?php echo $dayKey; ?>_open">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label small">Close Time</label>
                                                <input type="time" class="form-control form-control-sm"
                                                       name="<?php echo $dayKey; ?>_close"
                                                       value="<?php echo $closeTime; ?>"
                                                       id="<?php echo $dayKey; ?>_close">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="form-text">Toggle days on/off and set opening hours for each day.</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Operating Hours
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Location Settings -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>Location Settings
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_location">

                    <div class="mb-3">
                        <label for="address" class="form-label">Address</label>
                        <textarea class="form-control" id="address" name="address" rows="2" required><?php echo htmlspecialchars($shop['address']); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Shop Location</label>
                        <div id="map" style="height: 300px; border-radius: 8px;"></div>
                        <small class="text-muted">Click on the map to update your shop location</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" class="form-control" id="latitude" name="latitude"
                                       step="any" value="<?php echo $shop['latitude']; ?>" required readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" class="form-control" id="longitude" name="longitude"
                                       step="any" value="<?php echo $shop['longitude']; ?>" required readonly>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Location
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Shop Information Display -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Shop Information
                </h5>
                <small class="text-muted">Read-only information managed by admin</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Shop ID</label>
                            <div class="form-control-plaintext">#<?php echo $shop['id']; ?></div>
                        </div>

                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Commission Rate</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-secondary"><?php echo number_format($shop['commission_percentage'], 1); ?>%</span>
                            </div>
                        </div>

                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Verification Status</label>
                            <div class="form-control-plaintext">
                                <?php if ($shop['is_verified']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Verified
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock me-1"></i>Pending Verification
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Shop Status</label>
                            <div class="form-control-plaintext">
                                <?php if ($shop['is_active']): ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>Active
                                    </span>
                                <?php else: ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times-circle me-1"></i>Inactive
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Rating</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i><?php echo number_format($shop['rating'], 1); ?>
                                </span>
                                <small class="text-muted ms-2">(<?php echo number_format($shop['total_reviews']); ?> reviews)</small>
                            </div>
                        </div>

                        <div class="info-group mb-3">
                            <label class="form-label text-muted">Member Since</label>
                            <div class="form-control-plaintext">
                                <?php echo date('M j, Y', strtotime($shop['created_at'])); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> To update shop name, description, contact details, or other basic information,
                    please visit the <a href="profile.php" class="alert-link">Shop Profile</a> page.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

<style>
.info-group {
    border-bottom: 1px solid #e3e6f0;
    padding-bottom: 10px;
}

.info-group:last-child {
    border-bottom: none;
}

.form-control-plaintext {
    font-weight: 500;
}
</style>

<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let map;
let marker;

// Initialize map and operating hours functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
    initializeOperatingHours();
});

function initializeMap() {
    const currentLat = <?php echo $shop['latitude']; ?>;
    const currentLng = <?php echo $shop['longitude']; ?>;

    // Initialize map with current shop location
    map = L.map('map').setView([currentLat, currentLng], 15);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Add current location marker
    marker = L.marker([currentLat, currentLng]).addTo(map);

    // Add click listener to map
    map.on('click', function(e) {
        setMapLocation(e.latlng.lat, e.latlng.lng);
    });
}

function setMapLocation(lat, lng) {
    // Remove existing marker
    if (marker) {
        map.removeLayer(marker);
    }

    // Add new marker
    marker = L.marker([lat, lng]).addTo(map);

    // Update coordinate inputs
    document.getElementById('latitude').value = lat.toFixed(8);
    document.getElementById('longitude').value = lng.toFixed(8);

    // Center map on location
    map.setView([lat, lng], map.getZoom());
}

function initializeOperatingHours() {
    // Handle format toggle
    const formatToggle = document.querySelectorAll('input[name="format_toggle"]');
    const textSection = document.getElementById('text_format_section');
    const structuredSection = document.getElementById('structured_format_section');
    const operatingHoursType = document.getElementById('operating_hours_type');

    formatToggle.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'text') {
                textSection.style.display = 'block';
                structuredSection.style.display = 'none';
                operatingHoursType.value = 'text';
            } else {
                textSection.style.display = 'none';
                structuredSection.style.display = 'block';
                operatingHoursType.value = 'structured';
            }
        });
    });

    // Handle day checkbox changes
    const dayCheckboxes = document.querySelectorAll('input[type="checkbox"][id$="_is_open"]');
    dayCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const day = this.id.replace('_is_open', '');
            const openInput = document.getElementById(day + '_open');
            const closeInput = document.getElementById(day + '_close');

            if (this.checked) {
                openInput.disabled = false;
                closeInput.disabled = false;
                openInput.parentElement.style.opacity = '1';
                closeInput.parentElement.style.opacity = '1';
            } else {
                openInput.disabled = true;
                closeInput.disabled = true;
                openInput.parentElement.style.opacity = '0.5';
                closeInput.parentElement.style.opacity = '0.5';
            }
        });

        // Trigger change event to set initial state
        checkbox.dispatchEvent(new Event('change'));
    });

    // Form validation
    const form = document.getElementById('operatingHoursForm');
    form.addEventListener('submit', function(e) {
        const formatType = operatingHoursType.value;

        if (formatType === 'text') {
            const textArea = document.getElementById('operating_hours');
            if (!textArea.value.trim()) {
                e.preventDefault();
                alert('Please enter operating hours or switch to Time Picker format.');
                textArea.focus();
                return false;
            }
        } else {
            // Check if at least one day is open
            const anyDayOpen = Array.from(dayCheckboxes).some(cb => cb.checked);
            if (!anyDayOpen) {
                e.preventDefault();
                alert('Please select at least one day when your shop is open.');
                return false;
            }

            // Validate time inputs for open days
            let hasError = false;
            dayCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const day = checkbox.id.replace('_is_open', '');
                    const openTime = document.getElementById(day + '_open').value;
                    const closeTime = document.getElementById(day + '_close').value;

                    if (!openTime || !closeTime) {
                        hasError = true;
                        return;
                    }

                    // Check if close time is after open time (same day)
                    if (openTime >= closeTime) {
                        // Allow overnight hours, but warn user
                        if (!confirm(`${day.charAt(0).toUpperCase() + day.slice(1)}: Close time (${closeTime}) is before or same as open time (${openTime}). This will be treated as overnight hours. Continue?`)) {
                            hasError = true;
                            return;
                        }
                    }
                }
            });

            if (hasError) {
                e.preventDefault();
                alert('Please check the time inputs for all open days.');
                return false;
            }
        }
    });
}
</script>

<?php include 'includes/footer.php'; ?>
