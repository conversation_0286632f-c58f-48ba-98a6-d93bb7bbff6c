<?php
/**
 * Get detailed information about a specific laundry shop
 *
 * Parameters:
 * - shop_id (required): ID of the shop
 * - include_services (optional): Include services data (default: true)
 * - include_items (optional): Include items data (default: true)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../../includes/functions.php';

try {
    // Get parameters
    $shopId = isset($_GET['shop_id']) ? intval($_GET['shop_id']) : null;
    $includeServices = isset($_GET['include_services']) ? boolval($_GET['include_services']) : true;
    $includeItems = isset($_GET['include_items']) ? boolval($_GET['include_items']) : true;

    // Validate required parameters
    if (!$shopId) {
        throw new Exception('Shop ID is required');
    }

    // Get shop details
    $sql = "
        SELECT
            ls.*,
            d.name as division_name,
            dist.name as district_name,
            up.name as upazilla_name
        FROM laundry_shops ls
        LEFT JOIN divisions d ON ls.division_id = d.id
        LEFT JOIN districts dist ON ls.district_id = dist.id
        LEFT JOIN upazillas up ON ls.upazilla_id = up.id
        WHERE ls.id = ? AND ls.is_active = 1
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$shopId]);
    $shop = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$shop) {
        throw new Exception('Shop not found or inactive');
    }

    // Format shop data
    $formattedShop = [
        'id' => intval($shop['id']),
        'name' => $shop['name'],
        'bn_name' => $shop['bn_name'],
        'description' => $shop['description'],
        'bn_description' => $shop['bn_description'],
        'owner_name' => $shop['owner_name'],
        'phone' => $shop['phone'],
        'email' => $shop['email'],
        'address' => $shop['address'],
        'division_id' => $shop['division_id'] ? intval($shop['division_id']) : null,
        'district_id' => $shop['district_id'] ? intval($shop['district_id']) : null,
        'upazilla_id' => $shop['upazilla_id'] ? intval($shop['upazilla_id']) : null,
        'division_name' => $shop['division_name'],
        'district_name' => $shop['district_name'],
        'upazilla_name' => $shop['upazilla_name'],
        'latitude' => floatval($shop['latitude']),
        'longitude' => floatval($shop['longitude']),
        'operating_hours' => $shop['operating_hours'] ? json_decode($shop['operating_hours'], true) : null,
        'rating' => floatval($shop['rating']),
        'total_reviews' => intval($shop['total_reviews']),
        'commission_percentage' => floatval($shop['commission_percentage']),
        'is_active' => boolval($shop['is_active']),
        'is_verified' => boolval($shop['is_verified']),
        'profile_image_url' => $shop['profile_image_url'],
        'cover_image_url' => $shop['cover_image_url'],
        'created_at' => $shop['created_at'],
        'updated_at' => $shop['updated_at']
    ];

    // Check if shop is currently open
    $formattedShop['is_open'] = isShopOpen($shop['operating_hours']);
    $formattedShop['status'] = $formattedShop['is_open'] ? 'open' : 'closed';

    // Get services if requested
    if ($includeServices) {
        $servicesSql = "
            SELECT
                s.id,
                s.name,
                s.bn_name,
                s.description,
                s.bn_description,
                s.image_url,
                ss.estimated_hours,
                ss.is_available
            FROM shop_services ss
            INNER JOIN services s ON ss.service_id = s.id
            WHERE ss.shop_id = ? AND ss.is_available = 1 AND s.is_active = 1
            ORDER BY s.sort_order ASC, s.name ASC
        ";
        $servicesStmt = $pdo->prepare($servicesSql);
        $servicesStmt->execute([$shopId]);
        $services = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);

        // Format services
        $formattedServices = [];
        foreach ($services as $service) {
            $formattedServices[] = [
                'id' => intval($service['id']),
                'name' => $service['name'],
                'bn_name' => $service['bn_name'],
                'description' => $service['description'],
                'bn_description' => $service['bn_description'],
                'image_url' => $service['image_url'],
                'estimated_hours' => intval($service['estimated_hours']),
                'is_available' => boolval($service['is_available'])
            ];
        }
        $formattedShop['services'] = $formattedServices;
    }

    // Get items if requested
    if ($includeItems) {
        $itemsSql = "
            SELECT
                i.id,
                i.name,
                i.bn_name,
                i.description,
                i.bn_description,
                i.image_url,
                i.price as default_price,
                si.custom_price,
                si.estimated_hours,
                si.is_available,
                s.id as service_id,
                s.name as service_name,
                s.bn_name as service_bn_name
            FROM shop_items si
            INNER JOIN items i ON si.item_id = i.id
            INNER JOIN services s ON i.service_id = s.id
            WHERE si.shop_id = ? AND si.is_available = 1 AND i.is_active = 1
            ORDER BY s.sort_order ASC, i.name ASC
        ";
        $itemsStmt = $pdo->prepare($itemsSql);
        $itemsStmt->execute([$shopId]);
        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Format items
        $formattedItems = [];
        foreach ($items as $item) {
            $effectivePrice = $item['custom_price'] !== null ? floatval($item['custom_price']) : floatval($item['default_price']);

            $formattedItems[] = [
                'id' => intval($item['id']),
                'name' => $item['name'],
                'bn_name' => $item['bn_name'],
                'description' => $item['description'],
                'bn_description' => $item['bn_description'],
                'image_url' => $item['image_url'],
                'default_price' => floatval($item['default_price']),
                'custom_price' => $item['custom_price'] ? floatval($item['custom_price']) : null,
                'effective_price' => $effectivePrice,
                'estimated_hours' => intval($item['estimated_hours']),
                'is_available' => boolval($item['is_available']),
                'service' => [
                    'id' => intval($item['service_id']),
                    'name' => $item['service_name'],
                    'bn_name' => $item['service_bn_name']
                ]
            ];
        }
        $formattedShop['items'] = $formattedItems;
    }

    // Get delivery zones
    $zonesSql = "
        SELECT
            dz.*,
            d.name as division_name,
            dist.name as district_name,
            up.name as upazilla_name
        FROM delivery_zones dz
        LEFT JOIN divisions d ON dz.division_id = d.id
        LEFT JOIN districts dist ON dz.district_id = dist.id
        LEFT JOIN upazillas up ON dz.upazilla_id = up.id
        WHERE dz.shop_id = ? AND dz.is_active = 1
        ORDER BY dz.delivery_fee ASC
    ";
    $zonesStmt = $pdo->prepare($zonesSql);
    $zonesStmt->execute([$shopId]);
    $zones = $zonesStmt->fetchAll(PDO::FETCH_ASSOC);

    $formattedZones = [];
    foreach ($zones as $zone) {
        $formattedZones[] = [
            'id' => intval($zone['id']),
            'division_id' => $zone['division_id'] ? intval($zone['division_id']) : null,
            'district_id' => $zone['district_id'] ? intval($zone['district_id']) : null,
            'upazilla_id' => $zone['upazilla_id'] ? intval($zone['upazilla_id']) : null,
            'division_name' => $zone['division_name'],
            'district_name' => $zone['district_name'],
            'upazilla_name' => $zone['upazilla_name'],
            'delivery_fee' => floatval($zone['delivery_fee']),
            'min_order_amount' => floatval($zone['min_order_amount']),
            'estimated_delivery_hours' => intval($zone['estimated_delivery_hours']),
            'is_active' => boolval($zone['is_active'])
        ];
    }
    $formattedShop['delivery_zones'] = $formattedZones;

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Shop details retrieved successfully',
        'data' => $formattedShop
    ]);

} catch (Exception $e) {
    // Return error response
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}


?>
