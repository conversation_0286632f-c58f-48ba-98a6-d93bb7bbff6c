<?php
/**
 * Test Order Insert Script
 * 
 * This script tests inserting an order directly into the database
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'GoGoLaundryAdminPanel/config/db.php';

echo "<h2>Test Order Insert</h2>";

// Start a transaction
$pdo->beginTransaction();

try {
    // Generate unique order number and tracking number
    $orderNumber = 'ORD' . date('ymd') . str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
    $trackingNumber = 'TRK' . date('ymd') . str_pad(mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
    
    // User ID (use an existing user)
    $userId = 19; // From the logs
    
    // Insert the order
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            order_number,
            tracking_number,
            user_id,
            subtotal,
            discount,
            delivery_fee,
            total,
            payment_method,
            payment_status,
            transaction_id,
            payment_provider,
            status,
            pickup_address,
            pickup_division_id,
            pickup_district_id,
            pickup_upazilla_id,
            pickup_date,
            pickup_time_slot,
            delivery_address,
            notes
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");
    
    $stmt->execute([
        $orderNumber,
        $trackingNumber,
        $userId,
        44.00, // subtotal
        0.00, // discount
        50.00, // delivery_fee
        94.00, // total
        'bKash', // payment_method
        'pending', // payment_status
        'TEST123456', // transaction_id
        'bKash', // payment_provider
        'placed', // status
        'test address', // pickup_address
        6, // pickup_division_id
        41, // pickup_district_id
        325, // pickup_upazilla_id
        date('Y-m-d'), // pickup_date
        '12:00 PM - 3:00 PM', // pickup_time_slot
        'test address', // delivery_address
        'Test order from script' // notes
    ]);
    
    $orderId = $pdo->lastInsertId();
    
    echo "Order created with ID: $orderId<br>";
    
    // Insert order items
    $items = [
        [
            'item_id' => 1,
            'quantity' => 1,
            'price' => 22.00,
            'subtotal' => 22.00
        ],
        [
            'item_id' => 2,
            'quantity' => 1,
            'price' => 22.00,
            'subtotal' => 22.00
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id,
            item_id,
            quantity,
            price,
            subtotal,
            notes
        ) VALUES (
            ?, ?, ?, ?, ?, ?
        )
    ");
    
    foreach ($items as $item) {
        try {
            $stmt->execute([
                $orderId,
                $item['item_id'],
                $item['quantity'],
                $item['price'],
                $item['subtotal'],
                'Test item'
            ]);
            
            echo "Added item ID {$item['item_id']} to order<br>";
        } catch (PDOException $e) {
            echo "Error adding item ID {$item['item_id']}: " . $e->getMessage() . "<br>";
            
            // Check if the item exists
            $checkStmt = $pdo->prepare("SELECT * FROM items WHERE id = ?");
            $checkStmt->execute([$item['item_id']]);
            $itemExists = $checkStmt->fetch(PDO::FETCH_ASSOC);
            
            if ($itemExists) {
                echo "Item ID {$item['item_id']} exists in the database:<br>";
                echo "<pre>" . print_r($itemExists, true) . "</pre>";
            } else {
                echo "Item ID {$item['item_id']} does NOT exist in the database<br>";
            }
        }
    }
    
    // Commit the transaction
    $pdo->commit();
    
    echo "Order and items created successfully<br>";
    
    // Verify the order was created
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Order Details:</h3>";
    echo "<pre>" . print_r($order, true) . "</pre>";
    
    // Verify the order items were created
    $stmt = $pdo->prepare("SELECT * FROM order_items WHERE order_id = ?");
    $stmt->execute([$orderId]);
    $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Order Items:</h3>";
    echo "<pre>" . print_r($orderItems, true) . "</pre>";
    
} catch (PDOException $e) {
    // Rollback the transaction
    $pdo->rollBack();
    
    echo "Error: " . $e->getMessage() . "<br>";
    echo "Error Code: " . $e->getCode() . "<br>";
    
    // Get more detailed error information
    $errorInfo = $e->errorInfo;
    if (isset($errorInfo[2])) {
        echo "SQL Error: " . $errorInfo[2] . "<br>";
    }
}

echo "<br><a href='GoGoLaundryAdminPanel/admin/index.php'>Go to Admin Panel</a>";
?>
