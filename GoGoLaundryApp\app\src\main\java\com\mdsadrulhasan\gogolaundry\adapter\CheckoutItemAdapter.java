package com.mdsadrulhasan.gogolaundry.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.model.CartItem;

import java.util.List;

/**
 * Adapter for displaying cart items in checkout screen
 */
public class CheckoutItemAdapter extends RecyclerView.Adapter<CheckoutItemAdapter.ViewHolder> {

    private List<CartItem> items;

    /**
     * Constructor
     *
     * @param items List of cart items
     */
    public CheckoutItemAdapter(List<CartItem> items) {
        this.items = items;
    }

    /**
     * Update items list and refresh adapter
     *
     * @param items New list of items
     */
    public void updateItems(List<CartItem> items) {
        this.items = items;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_checkout, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CartItem item = items.get(position);
        holder.bind(item);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    /**
     * ViewHolder for checkout items
     */
    static class ViewHolder extends RecyclerView.ViewHolder {

        private final ImageView itemImage;
        private final TextView itemName;
        private final TextView itemPrice;
        private final TextView itemQuantity;
        private final TextView itemSubtotal;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            itemImage = itemView.findViewById(R.id.item_image);
            itemName = itemView.findViewById(R.id.item_name);
            itemPrice = itemView.findViewById(R.id.item_price);
            itemQuantity = itemView.findViewById(R.id.item_quantity);
            itemSubtotal = itemView.findViewById(R.id.item_subtotal);
        }

        /**
         * Bind item data to views
         *
         * @param item Cart item to display
         */
        public void bind(CartItem item) {
            itemName.setText(item.getName());
            itemPrice.setText("৳" + item.getPrice() + "/" + item.getUnit());
            itemQuantity.setText("Qty: " + item.getQuantity() + " " + item.getUnit());
            itemSubtotal.setText("৳" + item.getSubtotal());

            // Load item image
            if (item.getImageUrl() != null && !item.getImageUrl().isEmpty()) {
                String baseUrl = ApiClient.getBaseUrl();
                String imageUrl = baseUrl + "../" + item.getImageUrl();

                try {
                    Glide.with(itemView.getContext())
                            .load(imageUrl)
                            .placeholder(R.drawable.ic_laundry_placeholder)
                            .error(R.drawable.placeholder_image)
                            .into(itemImage);
                } catch (Exception e) {
                    // Fallback to placeholder_image if ic_laundry_placeholder is not found
                    itemImage.setImageResource(R.drawable.placeholder_image);
                }
            } else {
                try {
                    itemImage.setImageResource(R.drawable.ic_laundry_placeholder);
                } catch (Exception e) {
                    // Fallback to placeholder_image
                    itemImage.setImageResource(R.drawable.placeholder_image);
                }
            }
        }
    }
}
