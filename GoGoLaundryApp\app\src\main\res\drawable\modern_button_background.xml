<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_light_transparent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_light_transparent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
</selector>
