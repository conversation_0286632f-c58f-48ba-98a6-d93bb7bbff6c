package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;

import java.util.Locale;

/**
 * Language Manager for handling app language changes
 */
public class LanguageManager {
    private static final String TAG = "LanguageManager";
    private static final String PREF_NAME = "LanguagePref";
    private static final String KEY_LANGUAGE = "language";

    // Language codes
    public static final String LANGUAGE_ENGLISH = "en";
    public static final String LANGUAGE_BANGLA = "bn";
    public static final String LANGUAGE_ARABIC = "ar";

    private final SharedPreferences preferences;
    private final Context context;

    /**
     * Constructor
     *
     * @param context Application context
     */
    public LanguageManager(Context context) {
        this.context = context;
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Get current language
     *
     * @return Language code
     */
    public String getLanguage() {
        return preferences.getString(KEY_LANGUAGE, LANGUAGE_ENGLISH);
    }

    /**
     * Set language
     *
     * @param languageCode Language code
     */
    public void setLanguage(String languageCode) {
        preferences.edit().putString(KEY_LANGUAGE, languageCode).apply();
        Log.d(TAG, "Language set to: " + languageCode);
    }

    /**
     * Apply language to context
     *
     * @param context Context to apply language to
     * @return Context with updated configuration
     */
    public Context applyLanguage(Context context) {
        String languageCode = getLanguage();
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);

        Resources resources = context.getResources();
        Configuration config = new Configuration(resources.getConfiguration());

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            config.setLocale(locale);
            context = context.createConfigurationContext(config);
        } else {
            config.locale = locale;
            resources.updateConfiguration(config, resources.getDisplayMetrics());
        }

        Log.d(TAG, "Applied language: " + languageCode);
        return context;
    }

    /**
     * Get display name for a language code
     *
     * @param languageCode Language code
     * @return Display name
     */
    public String getLanguageDisplayName(String languageCode) {
        switch (languageCode) {
            case LANGUAGE_BANGLA:
                return "বাংলা";
            case LANGUAGE_ARABIC:
                return "العربية";
            case LANGUAGE_ENGLISH:
            default:
                return "English";
        }
    }
}
