<?php
/**
 * Admin Reset User Password
 *
 * This page allows administrators to reset user passwords
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/UserManager.php';
require_once '../includes/SettingsManager.php';

// Initialize managers
$userManager = new UserManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Get settings
$settings = $settingsManager->getAllSettings();
$otpEnabled = isset($settings['otp_enabled']) ? (bool)$settings['otp_enabled'] : true;

// Get user ID from query string if provided
$userId = isset($_GET['user_id']) && is_numeric($_GET['user_id']) ? (int)$_GET['user_id'] : null;
$user = null;

if ($userId) {
    $user = $userManager->getUserById($userId);
    if (!$user) {
        $_SESSION['error_message'] = 'User not found.';
        header('Location: users.php');
        exit;
    }
}

// Handle form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Check if we're searching for a user or resetting a password
        if (isset($_POST['search_user'])) {
            $searchTerm = trim($_POST['search_term']);
            
            if (empty($searchTerm)) {
                $error = 'Please enter a phone number or email to search.';
            } else {
                $user = $userManager->getUserByPhoneOrEmail($searchTerm);
                
                if (!$user) {
                    $error = 'No user found with the provided phone number or email.';
                } else {
                    $userId = $user['id'];
                }
            }
        } elseif (isset($_POST['reset_password']) && isset($_POST['user_id']) && is_numeric($_POST['user_id'])) {
            $userId = (int)$_POST['user_id'];
            $user = $userManager->getUserById($userId);
            
            if (!$user) {
                $error = 'User not found.';
            } else {
                $newPassword = isset($_POST['new_password']) ? trim($_POST['new_password']) : '';
                $confirmPassword = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
                
                if (empty($newPassword)) {
                    $error = 'Please enter a new password.';
                } elseif (strlen($newPassword) < 8) {
                    $error = 'Password must be at least 8 characters long.';
                } elseif ($newPassword !== $confirmPassword) {
                    $error = 'Passwords do not match.';
                } else {
                    // Reset the password
                    $result = $userManager->resetUserPassword($userId, $newPassword);
                    
                    if ($result) {
                        // Log action
                        $adminManager->logAdminAction(
                            $adminData['id'],
                            'password_reset',
                            'Reset password for user ID: ' . $userId . ' (' . $user['full_name'] . ')',
                            getClientIp()
                        );
                        
                        $success = 'Password has been reset successfully.';
                        
                        // Clear user data to show the search form again
                        $user = null;
                        $userId = null;
                    } else {
                        $error = 'Failed to reset password. Please try again.';
                    }
                }
            }
        }
    }
}

// Page title and breadcrumbs
$pageTitle = 'Reset User Password';
$breadcrumbs = [
    'Users' => 'users.php',
    'Reset Password' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Reset User Password</h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="users.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Users
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i> <?php echo $success; ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0"><?php echo $user ? 'Reset Password for ' . htmlspecialchars($user['full_name']) : 'Find User'; ?></h5>
    </div>
    <div class="card-body">
        <?php if (!$user): ?>
            <form action="reset_password.php" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                
                <div class="mb-3">
                    <label for="search_term" class="form-label">Phone Number or Email</label>
                    <input type="text" class="form-control" id="search_term" name="search_term" required>
                    <div class="form-text">Enter the user's phone number or email address to find their account.</div>
                </div>
                
                <div class="text-end">
                    <button type="submit" name="search_user" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i> Find User
                    </button>
                </div>
            </form>
        <?php else: ?>
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6>User Information</h6>
                    <table class="table table-sm">
                        <tr>
                            <th>Name:</th>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                        </tr>
                        <tr>
                            <th>Phone:</th>
                            <td><?php echo htmlspecialchars($user['phone']); ?></td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td><?php echo htmlspecialchars($user['email'] ?? 'N/A'); ?></td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <?php if ($user['is_verified']): ?>
                                    <span class="badge bg-success">Verified</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Unverified</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <form action="reset_password.php" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                
                <div class="mb-3">
                    <label for="new_password" class="form-label">New Password</label>
                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                    <div class="form-text">Password must be at least 8 characters long.</div>
                </div>
                
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">Confirm Password</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                </div>
                
                <div class="text-end">
                    <a href="reset_password.php" class="btn btn-secondary me-2">Cancel</a>
                    <button type="submit" name="reset_password" class="btn btn-primary">
                        <i class="fas fa-key me-1"></i> Reset Password
                    </button>
                </div>
            </form>
        <?php endif; ?>
    </div>
</div>

<?php if (!$otpEnabled): ?>
<div class="card mt-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">OTP Verification Status</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i> OTP verification is currently disabled in system settings. Users will not receive verification codes when resetting their passwords.
        </div>
        <p>When OTP verification is disabled, users who forget their passwords should contact an administrator for assistance.</p>
        <div class="text-end">
            <a href="settings.php" class="btn btn-primary">
                <i class="fas fa-cogs me-1"></i> Manage Settings
            </a>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password strength validation
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    
    if (newPasswordInput && confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (newPasswordInput.value !== confirmPasswordInput.value) {
                confirmPasswordInput.setCustomValidity('Passwords do not match');
            } else {
                confirmPasswordInput.setCustomValidity('');
            }
        });
        
        newPasswordInput.addEventListener('input', function() {
            if (newPasswordInput.value.length < 8) {
                newPasswordInput.setCustomValidity('Password must be at least 8 characters long');
            } else {
                newPasswordInput.setCustomValidity('');
                
                if (confirmPasswordInput.value) {
                    if (newPasswordInput.value !== confirmPasswordInput.value) {
                        confirmPasswordInput.setCustomValidity('Passwords do not match');
                    } else {
                        confirmPasswordInput.setCustomValidity('');
                    }
                }
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
