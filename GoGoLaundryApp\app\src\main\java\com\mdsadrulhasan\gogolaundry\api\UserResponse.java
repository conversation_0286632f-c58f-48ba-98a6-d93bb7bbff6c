package com.mdsadrulhasan.gogolaundry.api;

import com.google.gson.annotations.SerializedName;
import com.mdsadrulhasan.gogolaundry.model.User;

/**
 * User response model
 */
public class UserResponse {
    private User user;

    @SerializedName("otp_required")
    private boolean otpRequired;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public boolean isOtpRequired() {
        return otpRequired;
    }

    public void setOtpRequired(boolean otpRequired) {
        this.otpRequired = otpRequired;
    }
}
