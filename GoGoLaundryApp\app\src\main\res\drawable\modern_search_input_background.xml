<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/search_background_secondary" />
            <corners android:radius="20dp" />
            <stroke 
                android:width="1dp" 
                android:color="@color/search_accent_blue" />
        </shape>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
</selector>
