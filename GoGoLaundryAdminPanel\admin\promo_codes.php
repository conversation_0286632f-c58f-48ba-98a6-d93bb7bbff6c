<?php
/**
 * Promo Codes & Promotional Dialogs Management
 *
 * This page allows administrators to manage promo codes and promotional dialogs
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/PromoCodeManager.php';

// Initialize promo code manager
$promoCodeManager = new PromoCodeManager($pdo);

// Get current tab
$currentTab = isset($_GET['tab']) ? $_GET['tab'] : 'promo_codes';

// Process delete request
if (isset($_POST['delete_promo']) && isset($_POST['promo_id']) && isset($_POST['csrf_token'])) {
    // Verify CSRF token
    if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error_message'] = 'Invalid security token. Please try again.';
        header('Location: promo_codes.php');
        exit;
    }

    $promoId = (int)$_POST['promo_id'];

    // Check if promo code is in use
    if ($promoCodeManager->isPromoCodeInUse($promoId)) {
        $_SESSION['error_message'] = 'This promo code cannot be deleted because it is associated with existing orders.';
    } else {
        // Delete promo code
        if ($promoCodeManager->deletePromoCode($promoId)) {
            $_SESSION['success_message'] = 'Promo code deleted successfully.';
        } else {
            $_SESSION['error_message'] = 'Failed to delete promo code. Please try again.';
        }
    }

    header('Location: promo_codes.php');
    exit;
}

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : 'all';
$perPage = 10;

// Handle promotional dialog operations
if ($currentTab === 'promotional_dialogs') {
    // Handle promotional dialog form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create_dialog':
                    $result = createPromoDialog($_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = 'Promotional dialog created successfully!';
                    } else {
                        $_SESSION['error_message'] = $result['message'];
                    }
                    break;

                case 'update_dialog':
                    $result = updatePromoDialog($_POST);
                    if ($result['success']) {
                        $_SESSION['success_message'] = 'Promotional dialog updated successfully!';
                    } else {
                        $_SESSION['error_message'] = $result['message'];
                    }
                    break;

                case 'delete_dialog':
                    $result = deletePromoDialog($_POST['id']);
                    if ($result['success']) {
                        $_SESSION['success_message'] = 'Promotional dialog deleted successfully!';
                    } else {
                        $_SESSION['error_message'] = $result['message'];
                    }
                    break;

                case 'toggle_dialog_status':
                    $result = togglePromoDialogStatus($_POST['id']);
                    if ($result['success']) {
                        $_SESSION['success_message'] = 'Status updated successfully!';
                    } else {
                        $_SESSION['error_message'] = $result['message'];
                    }
                    break;
            }
            header('Location: promo_codes.php?tab=promotional_dialogs');
            exit;
        }
    }

    // Get promotional dialogs
    $promoDialogs = getAllPromoDialogs();
}

// Get promo codes with pagination and filtering (only if on promo codes tab)
if ($currentTab === 'promo_codes') {
    $result = $promoCodeManager->getAllPromoCodes($page, $perPage, $search, $status);
    $promoCodes = $result['promo_codes'];
    $pagination = $result['pagination'];
} else {
    $promoCodes = [];
    $pagination = [];
}

// Set page title
$pageTitle = $currentTab === 'promotional_dialogs' ? 'Manage Promotional Dialogs' : 'Manage Promo Codes';

// Promotional Dialog Functions
function createPromoDialog($data) {
    global $pdo;

    try {
        // Handle image upload
        $imagePath = '';
        if (isset($_FILES['promo_image']) && $_FILES['promo_image']['error'] == 0) {
            $uploadResult = uploadPromoImage($_FILES['promo_image']);
            if ($uploadResult['success']) {
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'message' => $uploadResult['message']];
            }
        }

        // Prepare background data based on type
        $backgroundType = $data['background_type'] ?? 'solid';
        $backgroundColor = null;
        $gradientColor1 = null;
        $gradientColor2 = null;
        $gradientDirection = null;

        if ($backgroundType === 'gradient') {
            $gradientColor1 = $data['gradient_color1'] ?? '#6c757d';
            $gradientColor2 = $data['gradient_color2'] ?? '#495057';
            $gradientDirection = $data['gradient_direction'] ?? '45deg';
        } else {
            $backgroundColor = $data['background_color'] ?? '#6c757d';
        }

        $stmt = $pdo->prepare("
            INSERT INTO promotional_dialogs
            (title, subtitle, description, discount_text, promo_code, button_text,
             image_path, image_url, background_color, background_type,
             gradient_color1, gradient_color2, gradient_direction,
             text_color, button_color, is_active, start_date, end_date, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $stmt->execute([
            $data['title'],
            $data['subtitle'],
            $data['description'],
            $data['discount_text'],
            $data['promo_code'],
            $data['button_text'],
            $imagePath,
            $data['image_url'],
            $backgroundColor,
            $backgroundType,
            $gradientColor1,
            $gradientColor2,
            $gradientDirection,
            $data['text_color'],
            $data['button_color'],
            isset($data['is_active']) ? 1 : 0,
            !empty($data['start_date']) ? $data['start_date'] : null,
            !empty($data['end_date']) ? $data['end_date'] : null
        ]);

        return ['success' => true];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function updatePromoDialog($data) {
    global $pdo;

    try {
        // Handle image upload if new image provided
        $imagePath = $data['existing_image_path'] ?? '';
        if (isset($_FILES['promo_image']) && $_FILES['promo_image']['error'] == 0) {
            $uploadResult = uploadPromoImage($_FILES['promo_image']);
            if ($uploadResult['success']) {
                // Delete old image if exists
                if (!empty($imagePath) && file_exists("../uploads/promo/" . basename($imagePath))) {
                    unlink("../uploads/promo/" . basename($imagePath));
                }
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'message' => $uploadResult['message']];
            }
        }

        // Prepare background data based on type
        $backgroundType = $data['background_type'] ?? 'solid';
        $backgroundColor = null;
        $gradientColor1 = null;
        $gradientColor2 = null;
        $gradientDirection = null;

        if ($backgroundType === 'gradient') {
            $gradientColor1 = $data['gradient_color1'] ?? '#6c757d';
            $gradientColor2 = $data['gradient_color2'] ?? '#495057';
            $gradientDirection = $data['gradient_direction'] ?? '45deg';
        } else {
            $backgroundColor = $data['background_color'] ?? '#6c757d';
        }

        $stmt = $pdo->prepare("
            UPDATE promotional_dialogs SET
            title = ?, subtitle = ?, description = ?, discount_text = ?,
            promo_code = ?, button_text = ?, image_path = ?, image_url = ?,
            background_color = ?, background_type = ?, gradient_color1 = ?,
            gradient_color2 = ?, gradient_direction = ?, text_color = ?, button_color = ?,
            is_active = ?, start_date = ?, end_date = ?, updated_at = NOW()
            WHERE id = ?
        ");

        $stmt->execute([
            $data['title'],
            $data['subtitle'],
            $data['description'],
            $data['discount_text'],
            $data['promo_code'],
            $data['button_text'],
            $imagePath,
            $data['image_url'],
            $backgroundColor,
            $backgroundType,
            $gradientColor1,
            $gradientColor2,
            $gradientDirection,
            $data['text_color'],
            $data['button_color'],
            isset($data['is_active']) ? 1 : 0,
            !empty($data['start_date']) ? $data['start_date'] : null,
            !empty($data['end_date']) ? $data['end_date'] : null,
            $data['id']
        ]);

        return ['success' => true];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function deletePromoDialog($id) {
    global $pdo;

    try {
        // Get image path before deleting
        $stmt = $pdo->prepare("SELECT image_path FROM promotional_dialogs WHERE id = ?");
        $stmt->execute([$id]);
        $dialog = $stmt->fetch(PDO::FETCH_ASSOC);

        // Delete the record
        $stmt = $pdo->prepare("DELETE FROM promotional_dialogs WHERE id = ?");
        $stmt->execute([$id]);

        // Delete image file if exists
        if (!empty($dialog['image_path']) && file_exists("../uploads/promo/" . basename($dialog['image_path']))) {
            unlink("../uploads/promo/" . basename($dialog['image_path']));
        }

        return ['success' => true];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function togglePromoDialogStatus($id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("UPDATE promotional_dialogs SET is_active = NOT is_active WHERE id = ?");
        $stmt->execute([$id]);

        return ['success' => true];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function getAllPromoDialogs() {
    global $pdo;

    $stmt = $pdo->prepare("
        SELECT * FROM promotional_dialogs
        ORDER BY created_at DESC
    ");
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function uploadPromoImage($file) {
    $uploadDir = '../uploads/promo/';

    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed.'];
    }

    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'File size too large. Maximum 5MB allowed.'];
    }

    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'promo_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filepath = $uploadDir . $filename;

    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'path' => 'uploads/promo/' . $filename];
    } else {
        return ['success' => false, 'message' => 'Failed to upload file.'];
    }
}
?>

<?php include 'includes/header.php'; ?>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <?php if ($currentTab === 'promo_codes'): ?>
                            <a href="add_promo.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Promo Code
                            </a>
                        <?php else: ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPromoModal">
                                <i class="fas fa-plus"></i> Create New Promo Dialog
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <?php include 'includes/alerts.php'; ?>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs mb-4" id="promoTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $currentTab === 'promo_codes' ? 'active' : ''; ?>"
                           href="promo_codes.php?tab=promo_codes">
                            <i class="fas fa-tags"></i> Promo Codes
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $currentTab === 'promotional_dialogs' ? 'active' : ''; ?>"
                           href="promo_codes.php?tab=promotional_dialogs">
                            <i class="fas fa-bullhorn"></i> Promotional Dialogs
                        </a>
                    </li>
                </ul>

                <?php if ($currentTab === 'promo_codes'): ?>
                <!-- Search Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Search Promo Codes</h6>
                    </div>
                    <div class="card-body">
                        <form method="get" action="promo_codes.php" class="row g-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="search" name="search"
                                       placeholder="Search by code"
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="expired" <?php echo $status === 'expired' ? 'selected' : ''; ?>>Expired</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <?php if (!empty($search) || $status !== 'all'): ?>
                                <a href="promo_codes.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Promo Codes Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Promo Codes List</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($promoCodes)): ?>
                            <div class="alert alert-info">
                                No promo codes found. <?php echo !empty($search) ? 'Try a different search term.' : ''; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="promoCodesTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Code</th>
                                            <th>Discount</th>
                                            <th>Min Order</th>
                                            <th>Valid Period</th>
                                            <th>Usage</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($promoCodes as $promo): ?>
                                            <?php
                                                $now = new DateTime();
                                                $startDate = new DateTime($promo['start_date']);
                                                $endDate = new DateTime($promo['end_date']);
                                                $isExpired = $endDate < $now;
                                                $isActive = $promo['is_active'] && !$isExpired && $startDate <= $now;
                                                $usageLimitReached = $promo['usage_limit'] !== null && $promo['usage_count'] >= $promo['usage_limit'];
                                            ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($promo['id']); ?></td>
                                                <td><?php echo htmlspecialchars($promo['code']); ?></td>
                                                <td>
                                                    <?php if ($promo['discount_type'] === 'percentage'): ?>
                                                        <?php echo htmlspecialchars($promo['discount_value']); ?>%
                                                        <?php if (!empty($promo['max_discount'])): ?>
                                                            <br><small>(Max: <?php echo htmlspecialchars($promo['max_discount']); ?>)</small>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <?php echo htmlspecialchars($promo['discount_value']); ?> (Fixed)
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($promo['min_order_value']); ?></td>
                                                <td>
                                                    <?php echo date('M d, Y', strtotime($promo['start_date'])); ?> -
                                                    <?php echo date('M d, Y', strtotime($promo['end_date'])); ?>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($promo['usage_count']); ?>
                                                    <?php if ($promo['usage_limit'] !== null): ?>
                                                        / <?php echo htmlspecialchars($promo['usage_limit']); ?>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($isActive && !$usageLimitReached): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php elseif (!$promo['is_active']): ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php elseif ($isExpired): ?>
                                                        <span class="badge bg-warning text-dark">Expired</span>
                                                    <?php elseif ($usageLimitReached): ?>
                                                        <span class="badge bg-info">Limit Reached</span>
                                                    <?php elseif ($startDate > $now): ?>
                                                        <span class="badge bg-primary">Upcoming</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="edit_promo.php?id=<?php echo $promo['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#deleteModal<?php echo $promo['id']; ?>">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </td>
                                            </tr>

                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $promo['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            Are you sure you want to delete the promo code: <strong><?php echo htmlspecialchars($promo['code']); ?></strong>?
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <form method="post" action="promo_codes.php">
                                                                <input type="hidden" name="promo_id" value="<?php echo $promo['id']; ?>">
                                                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                                <button type="submit" name="delete_promo" class="btn btn-danger">Delete</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php include 'includes/pagination.php'; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($currentTab === 'promotional_dialogs'): ?>
                <!-- Promotional Dialogs Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Promotional Dialogs List</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($promoDialogs)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No promotional dialogs found. Create your first promotional dialog to engage customers.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Discount</th>
                                            <th>Promo Code</th>
                                            <th>Status</th>
                                            <th>Valid Until</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($promoDialogs as $dialog): ?>
                                            <tr>
                                                <td><?php echo $dialog['id']; ?></td>
                                                <td><?php echo htmlspecialchars($dialog['title']); ?></td>
                                                <td><?php echo htmlspecialchars($dialog['discount_text']); ?></td>
                                                <td>
                                                    <?php if (!empty($dialog['promo_code'])): ?>
                                                        <code><?php echo htmlspecialchars($dialog['promo_code']); ?></code>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $dialog['is_active'] ? 'success' : 'secondary'; ?>">
                                                        <?php echo $dialog['is_active'] ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($dialog['end_date'])): ?>
                                                        <?php echo date('M d, Y', strtotime($dialog['end_date'])); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">No expiry</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="editPromoDialog(<?php echo htmlspecialchars(json_encode($dialog)); ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-<?php echo $dialog['is_active'] ? 'warning' : 'success'; ?>"
                                                                onclick="toggleStatus(<?php echo $dialog['id']; ?>)">
                                                            <i class="fas fa-<?php echo $dialog['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="deletePromoDialog(<?php echo $dialog['id']; ?>)">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

<!-- Create Promo Dialog Modal -->
<div class="modal fade" id="createPromoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Promotional Dialog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_dialog">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="title" name="title" required
                                       placeholder="e.g., HOT PICKS LOW PRICES">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control" id="subtitle" name="subtitle"
                                       placeholder="e.g., Best Deals on Best Prices">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Additional description or terms"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="discount_text" class="form-label">Discount Text *</label>
                                <input type="text" class="form-control" id="discount_text" name="discount_text" required
                                       placeholder="e.g., UP TO 60% OFF">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="promo_code" class="form-label">Promo Code</label>
                                <input type="text" class="form-control" id="promo_code" name="promo_code"
                                       placeholder="e.g., SAVE60">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="button_text" class="form-label">Button Text *</label>
                        <input type="text" class="form-control" id="button_text" name="button_text" required
                               value="Shop Now" placeholder="e.g., Shop Now">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="promo_image" class="form-label">Upload Image</label>
                                <input type="file" class="form-control" id="promo_image" name="promo_image"
                                       accept="image/*">
                                <div class="form-text">Max 5MB. JPG, PNG, GIF allowed.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="image_url" class="form-label">Or Image URL</label>
                                <input type="url" class="form-control" id="image_url" name="image_url"
                                       placeholder="https://example.com/image.jpg">
                            </div>
                        </div>
                    </div>

                    <!-- Background Color Section -->
                    <div class="mb-4">
                        <h6 class="mb-3">Background Style</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="background_type" class="form-label">Background Type</label>
                                    <select class="form-control" id="background_type" name="background_type" onchange="toggleBackgroundOptions()">
                                        <option value="solid">Solid Color</option>
                                        <option value="gradient">Gradient</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3" id="solid_color_section">
                                <div class="mb-3">
                                    <label for="background_color" class="form-label">Background Color</label>
                                    <input type="color" class="form-control form-control-color" id="background_color"
                                           name="background_color" value="#6c757d">
                                </div>
                            </div>
                            <div class="col-md-6" id="gradient_section" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="gradient_preset" class="form-label">Gradient Presets</label>
                                            <select class="form-control" id="gradient_preset" onchange="applyGradientPreset()">
                                                <option value="">Select Preset</option>
                                                <option value="sunset">Sunset (Orange to Pink)</option>
                                                <option value="ocean">Ocean (Blue to Teal)</option>
                                                <option value="forest">Forest (Green to Dark Green)</option>
                                                <option value="royal">Royal (Purple to Blue)</option>
                                                <option value="fire">Fire (Red to Orange)</option>
                                                <option value="mint">Mint (Light Green to Green)</option>
                                                <option value="gold">Gold (Yellow to Orange)</option>
                                                <option value="night">Night (Dark Blue to Black)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="gradient_direction" class="form-label">Direction</label>
                                            <select class="form-control" id="gradient_direction" name="gradient_direction">
                                                <option value="45deg">Diagonal (↗)</option>
                                                <option value="90deg">Vertical (↑)</option>
                                                <option value="0deg">Horizontal (→)</option>
                                                <option value="135deg">Diagonal (↖)</option>
                                                <option value="180deg">Vertical (↓)</option>
                                                <option value="270deg">Horizontal (←)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="gradient_color1" class="form-label">Start Color</label>
                                            <input type="color" class="form-control form-control-color" id="gradient_color1"
                                                   name="gradient_color1" value="#6c757d" onchange="updateGradientPreview()">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="gradient_color2" class="form-label">End Color</label>
                                            <input type="color" class="form-control form-control-color" id="gradient_color2"
                                                   name="gradient_color2" value="#495057" onchange="updateGradientPreview()">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Gradient Preview</label>
                                    <div id="gradient_preview" style="height: 50px; border: 1px solid #ddd; border-radius: 5px; background: linear-gradient(45deg, #6c757d, #495057);"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Text and Button Colors -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="text_color" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="text_color"
                                       name="text_color" value="#ffffff">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="button_color" class="form-label">Button Color</label>
                                <input type="color" class="form-control form-control-color" id="button_color"
                                       name="button_color" value="#ffd700">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" id="start_date" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="datetime-local" class="form-control" id="end_date" name="end_date">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                            <label class="form-check-label" for="is_active">
                                Active (Show to users)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Promo Dialog</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Promo Dialog Modal -->
<div class="modal fade" id="editPromoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Promotional Dialog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_dialog">
                    <input type="hidden" name="id" id="edit_id">
                    <input type="hidden" name="existing_image_path" id="edit_existing_image_path">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control" id="edit_subtitle" name="subtitle">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_discount_text" class="form-label">Discount Text *</label>
                                <input type="text" class="form-control" id="edit_discount_text" name="discount_text" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_promo_code" class="form-label">Promo Code</label>
                                <input type="text" class="form-control" id="edit_promo_code" name="promo_code">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_button_text" class="form-label">Button Text *</label>
                        <input type="text" class="form-control" id="edit_button_text" name="button_text" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_promo_image" class="form-label">Upload New Image</label>
                                <input type="file" class="form-control" id="edit_promo_image" name="promo_image" accept="image/*">
                                <div class="form-text">Leave empty to keep current image.</div>
                                <div id="current_image_preview"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_image_url" class="form-label">Or Image URL</label>
                                <input type="url" class="form-control" id="edit_image_url" name="image_url">
                            </div>
                        </div>
                    </div>

                    <!-- Background Color Section -->
                    <div class="mb-4">
                        <h6 class="mb-3">Background Style</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="edit_background_type" class="form-label">Background Type</label>
                                    <select class="form-control" id="edit_background_type" name="background_type" onchange="toggleEditBackgroundOptions()">
                                        <option value="solid">Solid Color</option>
                                        <option value="gradient">Gradient</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3" id="edit_solid_color_section">
                                <div class="mb-3">
                                    <label for="edit_background_color" class="form-label">Background Color</label>
                                    <input type="color" class="form-control form-control-color" id="edit_background_color"
                                           name="background_color">
                                </div>
                            </div>
                            <div class="col-md-6" id="edit_gradient_section" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_gradient_preset" class="form-label">Gradient Presets</label>
                                            <select class="form-control" id="edit_gradient_preset" onchange="applyEditGradientPreset()">
                                                <option value="">Select Preset</option>
                                                <option value="sunset">Sunset (Orange to Pink)</option>
                                                <option value="ocean">Ocean (Blue to Teal)</option>
                                                <option value="forest">Forest (Green to Dark Green)</option>
                                                <option value="royal">Royal (Purple to Blue)</option>
                                                <option value="fire">Fire (Red to Orange)</option>
                                                <option value="mint">Mint (Light Green to Green)</option>
                                                <option value="gold">Gold (Yellow to Orange)</option>
                                                <option value="night">Night (Dark Blue to Black)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_gradient_direction" class="form-label">Direction</label>
                                            <select class="form-control" id="edit_gradient_direction" name="gradient_direction">
                                                <option value="45deg">Diagonal (↗)</option>
                                                <option value="90deg">Vertical (↑)</option>
                                                <option value="0deg">Horizontal (→)</option>
                                                <option value="135deg">Diagonal (↖)</option>
                                                <option value="180deg">Vertical (↓)</option>
                                                <option value="270deg">Horizontal (←)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_gradient_color1" class="form-label">Start Color</label>
                                            <input type="color" class="form-control form-control-color" id="edit_gradient_color1"
                                                   name="gradient_color1" onchange="updateEditGradientPreview()">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="edit_gradient_color2" class="form-label">End Color</label>
                                            <input type="color" class="form-control form-control-color" id="edit_gradient_color2"
                                                   name="gradient_color2" onchange="updateEditGradientPreview()">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Gradient Preview</label>
                                    <div id="edit_gradient_preview" style="height: 50px; border: 1px solid #ddd; border-radius: 5px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Text and Button Colors -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_text_color" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="edit_text_color" name="text_color">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_button_color" class="form-label">Button Color</label>
                                <input type="color" class="form-control form-control-color" id="edit_button_color" name="button_color">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_start_date" class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" id="edit_start_date" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_end_date" class="form-label">End Date</label>
                                <input type="datetime-local" class="form-control" id="edit_end_date" name="end_date">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" value="1">
                            <label class="form-check-label" for="edit_is_active">
                                Active (Show to users)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Promo Dialog</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Gradient presets
const gradientPresets = {
    sunset: { color1: '#ff7e5f', color2: '#feb47b', direction: '45deg' },
    ocean: { color1: '#2196F3', color2: '#21CBF3', direction: '45deg' },
    forest: { color1: '#11998e', color2: '#38ef7d', direction: '45deg' },
    royal: { color1: '#667eea', color2: '#764ba2', direction: '45deg' },
    fire: { color1: '#f12711', color2: '#f5af19', direction: '45deg' },
    mint: { color1: '#00b09b', color2: '#96c93d', direction: '45deg' },
    gold: { color1: '#f7971e', color2: '#ffd200', direction: '45deg' },
    night: { color1: '#2c3e50', color2: '#000000', direction: '45deg' }
};

// Toggle background options for create form
function toggleBackgroundOptions() {
    const backgroundType = document.getElementById('background_type').value;
    const solidSection = document.getElementById('solid_color_section');
    const gradientSection = document.getElementById('gradient_section');

    if (backgroundType === 'gradient') {
        solidSection.style.display = 'none';
        gradientSection.style.display = 'block';
        updateGradientPreview();
    } else {
        solidSection.style.display = 'block';
        gradientSection.style.display = 'none';
    }
}

// Toggle background options for edit form
function toggleEditBackgroundOptions() {
    const backgroundType = document.getElementById('edit_background_type').value;
    const solidSection = document.getElementById('edit_solid_color_section');
    const gradientSection = document.getElementById('edit_gradient_section');

    if (backgroundType === 'gradient') {
        solidSection.style.display = 'none';
        gradientSection.style.display = 'block';
        updateEditGradientPreview();
    } else {
        solidSection.style.display = 'block';
        gradientSection.style.display = 'none';
    }
}

// Apply gradient preset for create form
function applyGradientPreset() {
    const preset = document.getElementById('gradient_preset').value;
    if (preset && gradientPresets[preset]) {
        const presetData = gradientPresets[preset];
        document.getElementById('gradient_color1').value = presetData.color1;
        document.getElementById('gradient_color2').value = presetData.color2;
        document.getElementById('gradient_direction').value = presetData.direction;
        updateGradientPreview();
    }
}

// Apply gradient preset for edit form
function applyEditGradientPreset() {
    const preset = document.getElementById('edit_gradient_preset').value;
    if (preset && gradientPresets[preset]) {
        const presetData = gradientPresets[preset];
        document.getElementById('edit_gradient_color1').value = presetData.color1;
        document.getElementById('edit_gradient_color2').value = presetData.color2;
        document.getElementById('edit_gradient_direction').value = presetData.direction;
        updateEditGradientPreview();
    }
}

// Update gradient preview for create form
function updateGradientPreview() {
    const color1 = document.getElementById('gradient_color1').value;
    const color2 = document.getElementById('gradient_color2').value;
    const direction = document.getElementById('gradient_direction').value;
    const preview = document.getElementById('gradient_preview');

    preview.style.background = `linear-gradient(${direction}, ${color1}, ${color2})`;
}

// Update gradient preview for edit form
function updateEditGradientPreview() {
    const color1 = document.getElementById('edit_gradient_color1').value;
    const color2 = document.getElementById('edit_gradient_color2').value;
    const direction = document.getElementById('edit_gradient_direction').value;
    const preview = document.getElementById('edit_gradient_preview');

    preview.style.background = `linear-gradient(${direction}, ${color1}, ${color2})`;
}

function editPromoDialog(dialog) {
    // Populate edit form
    document.getElementById('edit_id').value = dialog.id;
    document.getElementById('edit_title').value = dialog.title || '';
    document.getElementById('edit_subtitle').value = dialog.subtitle || '';
    document.getElementById('edit_description').value = dialog.description || '';
    document.getElementById('edit_discount_text').value = dialog.discount_text || '';
    document.getElementById('edit_promo_code').value = dialog.promo_code || '';
    document.getElementById('edit_button_text').value = dialog.button_text || '';
    document.getElementById('edit_image_url').value = dialog.image_url || '';
    document.getElementById('edit_text_color').value = dialog.text_color || '#ffffff';
    document.getElementById('edit_button_color').value = dialog.button_color || '#ffd700';
    document.getElementById('edit_existing_image_path').value = dialog.image_path || '';

    // Handle background type and colors
    const backgroundType = dialog.background_type || 'solid';
    document.getElementById('edit_background_type').value = backgroundType;

    if (backgroundType === 'gradient') {
        document.getElementById('edit_gradient_color1').value = dialog.gradient_color1 || '#6c757d';
        document.getElementById('edit_gradient_color2').value = dialog.gradient_color2 || '#495057';
        document.getElementById('edit_gradient_direction').value = dialog.gradient_direction || '45deg';
        toggleEditBackgroundOptions();
        updateEditGradientPreview();
    } else {
        document.getElementById('edit_background_color').value = dialog.background_color || '#6c757d';
        toggleEditBackgroundOptions();
    }

    // Format dates for datetime-local input
    if (dialog.start_date) {
        const startDate = new Date(dialog.start_date);
        document.getElementById('edit_start_date').value = startDate.toISOString().slice(0, 16);
    }
    if (dialog.end_date) {
        const endDate = new Date(dialog.end_date);
        document.getElementById('edit_end_date').value = endDate.toISOString().slice(0, 16);
    }

    document.getElementById('edit_is_active').checked = dialog.is_active == 1;

    // Show current image preview
    const imagePreview = document.getElementById('current_image_preview');
    if (dialog.image_path) {
        imagePreview.innerHTML = `
            <div class="mt-2">
                <small class="text-muted">Current image:</small><br>
                <img src="../${dialog.image_path}" alt="Current image" style="max-width: 100px; max-height: 100px;" class="img-thumbnail">
            </div>
        `;
    } else {
        imagePreview.innerHTML = '';
    }

    // Show modal
    new bootstrap.Modal(document.getElementById('editPromoModal')).show();
}

function toggleStatus(id) {
    if (confirm('Are you sure you want to toggle the status of this promotional dialog?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_dialog_status">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function deletePromoDialog(id) {
    if (confirm('Are you sure you want to delete this promotional dialog? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_dialog">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
