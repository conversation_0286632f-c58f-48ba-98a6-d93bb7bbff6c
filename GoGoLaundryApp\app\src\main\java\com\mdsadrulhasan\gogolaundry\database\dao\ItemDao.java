package com.mdsadrulhasan.gogolaundry.database.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity;

import java.util.List;

/**
 * Data Access Object for items table
 */
@Dao
public interface ItemDao {

    /**
     * Insert items
     *
     * @param items Items to insert
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<ItemEntity> items);

    /**
     * Get all items
     *
     * @return List of all items
     */
    @Query("SELECT * FROM items ORDER BY name ASC")
    List<ItemEntity> getAllItems();

    /**
     * Get all active items
     *
     * @return List of active items
     */
    @Query("SELECT * FROM items WHERE is_active = 1 ORDER BY name ASC")
    List<ItemEntity> getAllActiveItems();

    /**
     * Get items by service ID
     *
     * @param serviceId Service ID
     * @return List of items for the service
     */
    @Query("SELECT * FROM items WHERE service_id = :serviceId ORDER BY name ASC")
    List<ItemEntity> getItemsByServiceId(int serviceId);

    /**
     * Get active items by service ID
     *
     * @param serviceId Service ID
     * @return List of active items for the service
     */
    @Query("SELECT * FROM items WHERE service_id = :serviceId AND is_active = 1 ORDER BY name ASC")
    List<ItemEntity> getActiveItemsByServiceId(int serviceId);

    /**
     * Get item by ID
     *
     * @param itemId Item ID
     * @return Item with the specified ID
     */
    @Query("SELECT * FROM items WHERE id = :itemId")
    ItemEntity getItemById(int itemId);

    /**
     * Get limited number of active items
     *
     * @param limit Maximum number of items to return
     * @return List of active items, limited to the specified number
     */
    @Query("SELECT * FROM items WHERE is_active = 1 ORDER BY name ASC LIMIT :limit")
    List<ItemEntity> getLimitedActiveItems(int limit);

    /**
     * Delete all items
     */
    @Query("DELETE FROM items")
    void deleteAll();
}
