<?php
/**
 * Item Manager Class
 *
 * This class handles laundry item operations
 */

class ItemManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get all items with pagination and filtering
     *
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param string $search Search term
     * @param int|null $serviceId Filter by service ID
     * @return array Items and pagination data
     */
    public function getAllItems($page = 1, $perPage = 10, $search = '', $serviceId = null) {
        // Calculate offset
        $offset = ($page - 1) * $perPage;

        // Base query
        $query = "
            SELECT SQL_CALC_FOUND_ROWS i.*, s.name as service_name
            FROM items i
            JOIN services s ON i.service_id = s.id
            WHERE 1=1
        ";
        $params = [];

        // Add search condition if provided
        if (!empty($search)) {
            $query .= " AND (i.name LIKE ? OR i.description LIKE ? OR i.bn_name LIKE ? OR i.bn_description LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }

        // Add service filter if provided
        if ($serviceId !== null) {
            $query .= " AND i.service_id = ?";
            $params[] = $serviceId;
        }

        // Add order by and limit
        $query .= " ORDER BY s.name ASC, i.name ASC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $perPage;

        // Execute query
        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get total count
        $stmt = $this->pdo->query("SELECT FOUND_ROWS()");
        $totalCount = $stmt->fetchColumn();

        // Calculate pagination data
        $totalPages = ceil($totalCount / $perPage);

        return [
            'items' => $items,
            'pagination' => [
                'current' => $page,
                'total' => $totalPages,
                'count' => $totalCount,
                'perPage' => $perPage
            ]
        ];
    }

    /**
     * Get item by ID
     *
     * @param int $itemId Item ID
     * @return array|bool Item data or false if not found
     */
    public function getItemById($itemId) {
        $stmt = $this->pdo->prepare("
            SELECT i.*, s.name as service_name
            FROM items i
            JOIN services s ON i.service_id = s.id
            WHERE i.id = ?
        ");
        $stmt->execute([$itemId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Add a new item
     *
     * @param int $serviceId Service ID
     * @param string $name Item name
     * @param string $description Item description
     * @param float $price Item price
     * @param bool $isActive Item status
     * @param bool $inStock Stock status
     * @param string $bnName Bengali item name (optional)
     * @param string $bnDescription Bengali item description (optional)
     * @param string $imageUrl Image URL (optional)
     * @return int|bool New item ID or false on failure
     */
    public function addItem($serviceId, $name, $description, $price, $isActive = true, $inStock = true, $bnName = null, $bnDescription = null, $imageUrl = null) {
        $stmt = $this->pdo->prepare("
            INSERT INTO items (service_id, name, bn_name, description, bn_description, price, image_url, is_active, in_stock)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $serviceId,
            $name,
            $bnName,
            $description,
            $bnDescription,
            $price,
            $imageUrl,
            $isActive ? 1 : 0,
            $inStock ? 1 : 0
        ]);

        if ($result) {
            return $this->pdo->lastInsertId();
        }
        
        return false;
    }

    /**
     * Update an item
     *
     * @param int $itemId Item ID
     * @param int $serviceId Service ID
     * @param string $name Item name
     * @param string $description Item description
     * @param float $price Item price
     * @param bool $isActive Item status
     * @param bool $inStock Stock status
     * @param string $bnName Bengali item name (optional)
     * @param string $bnDescription Bengali item description (optional)
     * @param string $imageUrl Image URL (optional)
     * @return bool Success
     */
    public function updateItem($itemId, $serviceId, $name, $description, $price, $isActive, $inStock, $bnName = null, $bnDescription = null, $imageUrl = null) {
        // Get current item data for fields that are not being updated
        $currentItem = $this->getItemById($itemId);
        
        // Use current values if new ones are not provided
        if ($bnName === null) $bnName = $currentItem['bn_name'];
        if ($bnDescription === null) $bnDescription = $currentItem['bn_description'];
        if ($imageUrl === null) $imageUrl = $currentItem['image_url'];
        
        $stmt = $this->pdo->prepare("
            UPDATE items
            SET service_id = ?, name = ?, bn_name = ?, description = ?, bn_description = ?, 
                price = ?, image_url = ?, is_active = ?, in_stock = ?
            WHERE id = ?
        ");
        
        return $stmt->execute([
            $serviceId,
            $name,
            $bnName,
            $description,
            $bnDescription,
            $price,
            $imageUrl,
            $isActive ? 1 : 0,
            $inStock ? 1 : 0,
            $itemId
        ]);
    }

    /**
     * Delete an item
     *
     * @param int $itemId Item ID
     * @return bool Success
     */
    public function deleteItem($itemId) {
        // Check if item is in use
        if ($this->isItemInUse($itemId)) {
            return false;
        }
        
        $stmt = $this->pdo->prepare("
            DELETE FROM items
            WHERE id = ?
        ");
        
        return $stmt->execute([$itemId]);
    }

    /**
     * Check if an item is in use
     *
     * @param int $itemId Item ID
     * @return bool True if item is in use
     */
    public function isItemInUse($itemId) {
        // Check if item is associated with any orders
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) 
            FROM order_items 
            WHERE item_id = ?
        ");
        $stmt->execute([$itemId]);
        
        return $stmt->fetchColumn() > 0;
    }

    /**
     * Get items by service ID
     *
     * @param int $serviceId Service ID
     * @param bool $activeOnly Get only active items
     * @return array Items
     */
    public function getItemsByServiceId($serviceId, $activeOnly = false) {
        $query = "
            SELECT *
            FROM items
            WHERE service_id = ?
        ";
        
        if ($activeOnly) {
            $query .= " AND is_active = 1";
        }
        
        $query .= " ORDER BY name ASC";
        
        $stmt = $this->pdo->prepare($query);
        $stmt->execute([$serviceId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
