-- Shop Synchronization System Database Migration (PHP Compatible)
-- This migration adds tables for real-time synchronization between admin panel and mobile app

-- 1. Create shop_update_notifications table for tracking changes
CREATE TABLE IF NOT EXISTS `shop_update_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `update_type` enum('profile_update', 'service_update', 'item_update', 'pricing_update') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL,
  `is_processed` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `shop_id_idx` (`shop_id`),
  KEY `update_type_idx` (`update_type`),
  KEY `processed_idx` (`is_processed`, `created_at`),
  UNIQUE KEY `shop_update_unique` (`shop_id`, `update_type`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create API cache invalidation table
CREATE TABLE IF NOT EXISTS `api_cache_invalidation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cache_key` varchar(255) NOT NULL,
  `invalidated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `reason` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cache_key_idx` (`cache_key`),
  KEY `invalidated_at_idx` (`invalidated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Create shop location history table for tracking location changes
CREATE TABLE IF NOT EXISTS `shop_location_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `old_latitude` decimal(10,8) DEFAULT NULL,
  `old_longitude` decimal(11,8) DEFAULT NULL,
  `new_latitude` decimal(10,8) NOT NULL,
  `new_longitude` decimal(11,8) NOT NULL,
  `old_address` varchar(255) DEFAULT NULL,
  `new_address` varchar(255) NOT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `shop_id_idx` (`shop_id`),
  KEY `updated_at_idx` (`updated_at`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shops_location ON laundry_shops(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_shops_active_verified ON laundry_shops(is_active, is_verified);
CREATE INDEX IF NOT EXISTS idx_shop_services_available ON shop_services(shop_id, is_available);
CREATE INDEX IF NOT EXISTS idx_shop_items_available ON shop_items(shop_id, is_available);
