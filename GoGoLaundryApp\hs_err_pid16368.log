#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:112), pid=16368, tid=14864
#
# JRE version:  (21.0.2+13) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: 

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Thu May 22 05:31:27 2025 Bangladesh Standard Time elapsed time: 0.044613 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001935f15ba70):  JavaThread "main"             [_thread_in_vm, id=14864, stack(0x000000098ce00000,0x000000098cf00000) (1024K)]

Stack: [0x000000098ce00000,0x000000098cf00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0x8526ee]
V  [jvm.dll+0x66e705]
V  [jvm.dll+0x66e76a]
V  [jvm.dll+0x670f96]
V  [jvm.dll+0x670e62]
V  [jvm.dll+0x66f0de]
V  [jvm.dll+0x26c6d6]
V  [jvm.dll+0x218617]
V  [jvm.dll+0x20dd9e]
V  [jvm.dll+0x5ad3dc]
V  [jvm.dll+0x21f76a]
V  [jvm.dll+0x7cec21]
V  [jvm.dll+0x7cfc85]
V  [jvm.dll+0x7d022f]
V  [jvm.dll+0x7cfed8]
V  [jvm.dll+0x26ed7b]
V  [jvm.dll+0x3d3c0e]
C  0x0000019370d1a854

The last pc belongs to new (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.System.initPhase1()V+97 java.base
v  ~StubRoutines::call_stub 0x0000019370d0100d
new  187 new  [0x0000019370d1a6c0, 0x0000019370d1a8c8]  520 bytes
[MachCode]
  0x0000019370d1a6c0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x0000019370d1a6e0: 4424 0800 | 0000 00eb | 0150 410f | b755 010f | cac1 ea10 | 488b 4de8 | 488b 4908 | 488b 4908 
  0x0000019370d1a700: 488b 4108 | 807c 1004 | 070f 85d3 | 0000 0066 | 8b54 d148 | 488b 4928 | 488b 4cd1 | 0851 80b9 
  0x0000019370d1a720: 2001 0000 | 050f 85b6 | 0000 008b | 5108 f6c2 | 010f 85aa | 0000 0049 | 8b87 b801 | 0000 488d 
  0x0000019370d1a740: 1c10 493b | 9fc8 0100 | 000f 8792 | 0000 0049 | 899f b801 | 0000 4883 | ea10 0f84 | 0f00 0000 
  0x0000019370d1a760: 33c9 c1ea | 0348 894c | d008 48ff | ca75 f648 | c700 0100 | 0000 5933 | f689 700c | 49ba 0000 
  0x0000019370d1a780: 0000 0800 | 0000 492b | ca89 4808 | 49ba fa45 | b760 f87f | 0000 4180 | 3a00 0f84 | 3c00 0000 
  0x0000019370d1a7a0: 5048 8bc8 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 8009 6860 | f87f 0000 
  0x0000019370d1a7c0: ffd0 4883 | c408 e90c | 0000 0048 | b880 0968 | 60f8 7f00 | 00ff d048 | 83c4 2058 | e9cb 0000 
  0x0000019370d1a7e0: 0059 488b | 55e8 488b | 5208 488b | 5208 450f | b745 0141 | 0fc8 41c1 | e810 e805 | 0000 00e9 
  0x0000019370d1a800: a800 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af a803 | 0000 4989 | 8798 0300 
  0x0000019370d1a820: 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b8c0 3b32 | 60f8 7f00 | 00ff d048 
  0x0000019370d1a840: 83c4 08e9 | 0c00 0000 | 48b8 c03b | 3260 f87f | 0000 ffd0 | 4883 c420 | 49c7 8798 | 0300 0000 
  0x0000019370d1a860: 0000 0049 | c787 a803 | 0000 0000 | 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 
  0x0000019370d1a880: 000f 8405 | 0000 00e9 | 7466 feff | 498b 87f0 | 0300 0049 | c787 f003 | 0000 0000 | 0000 4c8b 
  0x0000019370d1a8a0: 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 410f b65d | 0349 83c5 | 0349 ba00 | 07ba 60f8 | 7f00 0041 
  0x0000019370d1a8c0: ff24 da0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001930227d140, length=1, elements={
0x000001935f15ba70
}

Java Threads: ( => current thread )
=>0x000001935f15ba70 JavaThread "main"                              [_thread_in_vm, id=14864, stack(0x000000098ce00000,0x000000098cf00000) (1024K)]
Total: 1

Other Threads:
  0x0000019302430180 VMThread "VM Thread"                           [id=8836, stack(0x000000098d500000,0x000000098d600000) (1024K)]
  0x00000193023a3d00 WatcherThread "VM Periodic Task Thread"        [id=11168, stack(0x000000098d400000,0x000000098d500000) (1024K)]
  0x00000193613d5c70 WorkerThread "GC Thread#0"                     [id=1636, stack(0x000000098cf00000,0x000000098d000000) (1024K)]
  0x00000193613e1d90 ConcurrentGCThread "G1 Main Marker"            [id=18868, stack(0x000000098d000000,0x000000098d100000) (1024K)]
  0x00000193613e4850 WorkerThread "G1 Conc#0"                       [id=17748, stack(0x000000098d100000,0x000000098d200000) (1024K)]
  0x0000019302274820 ConcurrentGCThread "G1 Refine#0"               [id=8104, stack(0x000000098d200000,0x000000098d300000) (1024K)]
  0x0000019302275390 ConcurrentGCThread "G1 Service"                [id=18908, stack(0x000000098d300000,0x000000098d400000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff860b8ace8] Metaspace_lock - owner thread: 0x000001935f15ba70

Heap address: 0x00000006a0c00000, size: 5620 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 5620M
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 360448K, used 0K [0x00000006a0c00000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 2421K, committed 2432K, reserved 1114112K
  class space    used 181K, committed 192K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x00000006a0c00000, 0x00000006a0c00000, 0x00000006a1000000|  0%| F|  |TAMS 0x00000006a0c00000| PB 0x00000006a0c00000| Untracked 
|   1|0x00000006a1000000, 0x00000006a1000000, 0x00000006a1400000|  0%| F|  |TAMS 0x00000006a1000000| PB 0x00000006a1000000| Untracked 
|   2|0x00000006a1400000, 0x00000006a1400000, 0x00000006a1800000|  0%| F|  |TAMS 0x00000006a1400000| PB 0x00000006a1400000| Untracked 
|   3|0x00000006a1800000, 0x00000006a1800000, 0x00000006a1c00000|  0%| F|  |TAMS 0x00000006a1800000| PB 0x00000006a1800000| Untracked 
|   4|0x00000006a1c00000, 0x00000006a1c00000, 0x00000006a2000000|  0%| F|  |TAMS 0x00000006a1c00000| PB 0x00000006a1c00000| Untracked 
|   5|0x00000006a2000000, 0x00000006a2000000, 0x00000006a2400000|  0%| F|  |TAMS 0x00000006a2000000| PB 0x00000006a2000000| Untracked 
|   6|0x00000006a2400000, 0x00000006a2400000, 0x00000006a2800000|  0%| F|  |TAMS 0x00000006a2400000| PB 0x00000006a2400000| Untracked 
|   7|0x00000006a2800000, 0x00000006a2800000, 0x00000006a2c00000|  0%| F|  |TAMS 0x00000006a2800000| PB 0x00000006a2800000| Untracked 
|   8|0x00000006a2c00000, 0x00000006a2c00000, 0x00000006a3000000|  0%| F|  |TAMS 0x00000006a2c00000| PB 0x00000006a2c00000| Untracked 
|   9|0x00000006a3000000, 0x00000006a3000000, 0x00000006a3400000|  0%| F|  |TAMS 0x00000006a3000000| PB 0x00000006a3000000| Untracked 
|  10|0x00000006a3400000, 0x00000006a3400000, 0x00000006a3800000|  0%| F|  |TAMS 0x00000006a3400000| PB 0x00000006a3400000| Untracked 
|  11|0x00000006a3800000, 0x00000006a3800000, 0x00000006a3c00000|  0%| F|  |TAMS 0x00000006a3800000| PB 0x00000006a3800000| Untracked 
|  12|0x00000006a3c00000, 0x00000006a3c00000, 0x00000006a4000000|  0%| F|  |TAMS 0x00000006a3c00000| PB 0x00000006a3c00000| Untracked 
|  13|0x00000006a4000000, 0x00000006a4000000, 0x00000006a4400000|  0%| F|  |TAMS 0x00000006a4000000| PB 0x00000006a4000000| Untracked 
|  14|0x00000006a4400000, 0x00000006a4400000, 0x00000006a4800000|  0%| F|  |TAMS 0x00000006a4400000| PB 0x00000006a4400000| Untracked 
|  15|0x00000006a4800000, 0x00000006a4800000, 0x00000006a4c00000|  0%| F|  |TAMS 0x00000006a4800000| PB 0x00000006a4800000| Untracked 
|  16|0x00000006a4c00000, 0x00000006a4c00000, 0x00000006a5000000|  0%| F|  |TAMS 0x00000006a4c00000| PB 0x00000006a4c00000| Untracked 
|  17|0x00000006a5000000, 0x00000006a5000000, 0x00000006a5400000|  0%| F|  |TAMS 0x00000006a5000000| PB 0x00000006a5000000| Untracked 
|  18|0x00000006a5400000, 0x00000006a5400000, 0x00000006a5800000|  0%| F|  |TAMS 0x00000006a5400000| PB 0x00000006a5400000| Untracked 
|  19|0x00000006a5800000, 0x00000006a5800000, 0x00000006a5c00000|  0%| F|  |TAMS 0x00000006a5800000| PB 0x00000006a5800000| Untracked 
|  20|0x00000006a5c00000, 0x00000006a5c00000, 0x00000006a6000000|  0%| F|  |TAMS 0x00000006a5c00000| PB 0x00000006a5c00000| Untracked 
|  21|0x00000006a6000000, 0x00000006a6000000, 0x00000006a6400000|  0%| F|  |TAMS 0x00000006a6000000| PB 0x00000006a6000000| Untracked 
|  22|0x00000006a6400000, 0x00000006a6400000, 0x00000006a6800000|  0%| F|  |TAMS 0x00000006a6400000| PB 0x00000006a6400000| Untracked 
|  23|0x00000006a6800000, 0x00000006a6800000, 0x00000006a6c00000|  0%| F|  |TAMS 0x00000006a6800000| PB 0x00000006a6800000| Untracked 
|  24|0x00000006a6c00000, 0x00000006a6c00000, 0x00000006a7000000|  0%| F|  |TAMS 0x00000006a6c00000| PB 0x00000006a6c00000| Untracked 
|  25|0x00000006a7000000, 0x00000006a7000000, 0x00000006a7400000|  0%| F|  |TAMS 0x00000006a7000000| PB 0x00000006a7000000| Untracked 
|  26|0x00000006a7400000, 0x00000006a7400000, 0x00000006a7800000|  0%| F|  |TAMS 0x00000006a7400000| PB 0x00000006a7400000| Untracked 
|  27|0x00000006a7800000, 0x00000006a7800000, 0x00000006a7c00000|  0%| F|  |TAMS 0x00000006a7800000| PB 0x00000006a7800000| Untracked 
|  28|0x00000006a7c00000, 0x00000006a7c00000, 0x00000006a8000000|  0%| F|  |TAMS 0x00000006a7c00000| PB 0x00000006a7c00000| Untracked 
|  29|0x00000006a8000000, 0x00000006a8000000, 0x00000006a8400000|  0%| F|  |TAMS 0x00000006a8000000| PB 0x00000006a8000000| Untracked 
|  30|0x00000006a8400000, 0x00000006a8400000, 0x00000006a8800000|  0%| F|  |TAMS 0x00000006a8400000| PB 0x00000006a8400000| Untracked 
|  31|0x00000006a8800000, 0x00000006a8800000, 0x00000006a8c00000|  0%| F|  |TAMS 0x00000006a8800000| PB 0x00000006a8800000| Untracked 
|  32|0x00000006a8c00000, 0x00000006a8c00000, 0x00000006a9000000|  0%| F|  |TAMS 0x00000006a8c00000| PB 0x00000006a8c00000| Untracked 
|  33|0x00000006a9000000, 0x00000006a9000000, 0x00000006a9400000|  0%| F|  |TAMS 0x00000006a9000000| PB 0x00000006a9000000| Untracked 
|  34|0x00000006a9400000, 0x00000006a9400000, 0x00000006a9800000|  0%| F|  |TAMS 0x00000006a9400000| PB 0x00000006a9400000| Untracked 
|  35|0x00000006a9800000, 0x00000006a9800000, 0x00000006a9c00000|  0%| F|  |TAMS 0x00000006a9800000| PB 0x00000006a9800000| Untracked 
|  36|0x00000006a9c00000, 0x00000006a9c00000, 0x00000006aa000000|  0%| F|  |TAMS 0x00000006a9c00000| PB 0x00000006a9c00000| Untracked 
|  37|0x00000006aa000000, 0x00000006aa000000, 0x00000006aa400000|  0%| F|  |TAMS 0x00000006aa000000| PB 0x00000006aa000000| Untracked 
|  38|0x00000006aa400000, 0x00000006aa400000, 0x00000006aa800000|  0%| F|  |TAMS 0x00000006aa400000| PB 0x00000006aa400000| Untracked 
|  39|0x00000006aa800000, 0x00000006aa800000, 0x00000006aac00000|  0%| F|  |TAMS 0x00000006aa800000| PB 0x00000006aa800000| Untracked 
|  40|0x00000006aac00000, 0x00000006aac00000, 0x00000006ab000000|  0%| F|  |TAMS 0x00000006aac00000| PB 0x00000006aac00000| Untracked 
|  41|0x00000006ab000000, 0x00000006ab000000, 0x00000006ab400000|  0%| F|  |TAMS 0x00000006ab000000| PB 0x00000006ab000000| Untracked 
|  42|0x00000006ab400000, 0x00000006ab400000, 0x00000006ab800000|  0%| F|  |TAMS 0x00000006ab400000| PB 0x00000006ab400000| Untracked 
|  43|0x00000006ab800000, 0x00000006ab800000, 0x00000006abc00000|  0%| F|  |TAMS 0x00000006ab800000| PB 0x00000006ab800000| Untracked 
|  44|0x00000006abc00000, 0x00000006abc00000, 0x00000006ac000000|  0%| F|  |TAMS 0x00000006abc00000| PB 0x00000006abc00000| Untracked 
|  45|0x00000006ac000000, 0x00000006ac000000, 0x00000006ac400000|  0%| F|  |TAMS 0x00000006ac000000| PB 0x00000006ac000000| Untracked 
|  46|0x00000006ac400000, 0x00000006ac400000, 0x00000006ac800000|  0%| F|  |TAMS 0x00000006ac400000| PB 0x00000006ac400000| Untracked 
|  47|0x00000006ac800000, 0x00000006ac800000, 0x00000006acc00000|  0%| F|  |TAMS 0x00000006ac800000| PB 0x00000006ac800000| Untracked 
|  48|0x00000006acc00000, 0x00000006acc00000, 0x00000006ad000000|  0%| F|  |TAMS 0x00000006acc00000| PB 0x00000006acc00000| Untracked 
|  49|0x00000006ad000000, 0x00000006ad000000, 0x00000006ad400000|  0%| F|  |TAMS 0x00000006ad000000| PB 0x00000006ad000000| Untracked 
|  50|0x00000006ad400000, 0x00000006ad400000, 0x00000006ad800000|  0%| F|  |TAMS 0x00000006ad400000| PB 0x00000006ad400000| Untracked 
|  51|0x00000006ad800000, 0x00000006ad800000, 0x00000006adc00000|  0%| F|  |TAMS 0x00000006ad800000| PB 0x00000006ad800000| Untracked 
|  52|0x00000006adc00000, 0x00000006adc00000, 0x00000006ae000000|  0%| F|  |TAMS 0x00000006adc00000| PB 0x00000006adc00000| Untracked 
|  53|0x00000006ae000000, 0x00000006ae000000, 0x00000006ae400000|  0%| F|  |TAMS 0x00000006ae000000| PB 0x00000006ae000000| Untracked 
|  54|0x00000006ae400000, 0x00000006ae400000, 0x00000006ae800000|  0%| F|  |TAMS 0x00000006ae400000| PB 0x00000006ae400000| Untracked 
|  55|0x00000006ae800000, 0x00000006ae800000, 0x00000006aec00000|  0%| F|  |TAMS 0x00000006ae800000| PB 0x00000006ae800000| Untracked 
|  56|0x00000006aec00000, 0x00000006aec00000, 0x00000006af000000|  0%| F|  |TAMS 0x00000006aec00000| PB 0x00000006aec00000| Untracked 
|  57|0x00000006af000000, 0x00000006af000000, 0x00000006af400000|  0%| F|  |TAMS 0x00000006af000000| PB 0x00000006af000000| Untracked 
|  58|0x00000006af400000, 0x00000006af400000, 0x00000006af800000|  0%| F|  |TAMS 0x00000006af400000| PB 0x00000006af400000| Untracked 
|  59|0x00000006af800000, 0x00000006af800000, 0x00000006afc00000|  0%| F|  |TAMS 0x00000006af800000| PB 0x00000006af800000| Untracked 
|  60|0x00000006afc00000, 0x00000006afc00000, 0x00000006b0000000|  0%| F|  |TAMS 0x00000006afc00000| PB 0x00000006afc00000| Untracked 
|  61|0x00000006b0000000, 0x00000006b0000000, 0x00000006b0400000|  0%| F|  |TAMS 0x00000006b0000000| PB 0x00000006b0000000| Untracked 
|  62|0x00000006b0400000, 0x00000006b0400000, 0x00000006b0800000|  0%| F|  |TAMS 0x00000006b0400000| PB 0x00000006b0400000| Untracked 
|  63|0x00000006b0800000, 0x00000006b0800000, 0x00000006b0c00000|  0%| F|  |TAMS 0x00000006b0800000| PB 0x00000006b0800000| Untracked 
|  64|0x00000006b0c00000, 0x00000006b0c00000, 0x00000006b1000000|  0%| F|  |TAMS 0x00000006b0c00000| PB 0x00000006b0c00000| Untracked 
|  65|0x00000006b1000000, 0x00000006b1000000, 0x00000006b1400000|  0%| F|  |TAMS 0x00000006b1000000| PB 0x00000006b1000000| Untracked 
|  66|0x00000006b1400000, 0x00000006b1400000, 0x00000006b1800000|  0%| F|  |TAMS 0x00000006b1400000| PB 0x00000006b1400000| Untracked 
|  67|0x00000006b1800000, 0x00000006b1800000, 0x00000006b1c00000|  0%| F|  |TAMS 0x00000006b1800000| PB 0x00000006b1800000| Untracked 
|  68|0x00000006b1c00000, 0x00000006b1c00000, 0x00000006b2000000|  0%| F|  |TAMS 0x00000006b1c00000| PB 0x00000006b1c00000| Untracked 
|  69|0x00000006b2000000, 0x00000006b2000000, 0x00000006b2400000|  0%| F|  |TAMS 0x00000006b2000000| PB 0x00000006b2000000| Untracked 
|  70|0x00000006b2400000, 0x00000006b2400000, 0x00000006b2800000|  0%| F|  |TAMS 0x00000006b2400000| PB 0x00000006b2400000| Untracked 
|  71|0x00000006b2800000, 0x00000006b2800000, 0x00000006b2c00000|  0%| F|  |TAMS 0x00000006b2800000| PB 0x00000006b2800000| Untracked 
|  72|0x00000006b2c00000, 0x00000006b2c00000, 0x00000006b3000000|  0%| F|  |TAMS 0x00000006b2c00000| PB 0x00000006b2c00000| Untracked 
|  73|0x00000006b3000000, 0x00000006b3000000, 0x00000006b3400000|  0%| F|  |TAMS 0x00000006b3000000| PB 0x00000006b3000000| Untracked 
|  74|0x00000006b3400000, 0x00000006b3400000, 0x00000006b3800000|  0%| F|  |TAMS 0x00000006b3400000| PB 0x00000006b3400000| Untracked 
|  75|0x00000006b3800000, 0x00000006b3800000, 0x00000006b3c00000|  0%| F|  |TAMS 0x00000006b3800000| PB 0x00000006b3800000| Untracked 
|  76|0x00000006b3c00000, 0x00000006b3c00000, 0x00000006b4000000|  0%| F|  |TAMS 0x00000006b3c00000| PB 0x00000006b3c00000| Untracked 
|  77|0x00000006b4000000, 0x00000006b4000000, 0x00000006b4400000|  0%| F|  |TAMS 0x00000006b4000000| PB 0x00000006b4000000| Untracked 
|  78|0x00000006b4400000, 0x00000006b4400000, 0x00000006b4800000|  0%| F|  |TAMS 0x00000006b4400000| PB 0x00000006b4400000| Untracked 
|  79|0x00000006b4800000, 0x00000006b4800000, 0x00000006b4c00000|  0%| F|  |TAMS 0x00000006b4800000| PB 0x00000006b4800000| Untracked 
|  80|0x00000006b4c00000, 0x00000006b4c00000, 0x00000006b5000000|  0%| F|  |TAMS 0x00000006b4c00000| PB 0x00000006b4c00000| Untracked 
|  81|0x00000006b5000000, 0x00000006b5000000, 0x00000006b5400000|  0%| F|  |TAMS 0x00000006b5000000| PB 0x00000006b5000000| Untracked 
|  82|0x00000006b5400000, 0x00000006b5400000, 0x00000006b5800000|  0%| F|  |TAMS 0x00000006b5400000| PB 0x00000006b5400000| Untracked 
|  83|0x00000006b5800000, 0x00000006b5800000, 0x00000006b5c00000|  0%| F|  |TAMS 0x00000006b5800000| PB 0x00000006b5800000| Untracked 
|  84|0x00000006b5c00000, 0x00000006b5c00000, 0x00000006b6000000|  0%| F|  |TAMS 0x00000006b5c00000| PB 0x00000006b5c00000| Untracked 
|  85|0x00000006b6000000, 0x00000006b6000000, 0x00000006b6400000|  0%| F|  |TAMS 0x00000006b6000000| PB 0x00000006b6000000| Untracked 
|  86|0x00000006b6400000, 0x00000006b6400000, 0x00000006b6800000|  0%| F|  |TAMS 0x00000006b6400000| PB 0x00000006b6400000| Untracked 
|  87|0x00000006b6800000, 0x00000006b68666d0, 0x00000006b6c00000| 10%| E|  |TAMS 0x00000006b6800000| PB 0x00000006b6800000| Complete 

Card table byte_map: [0x0000019379600000,0x000001937a100000] _byte_map_base: 0x00000193760fa000

Marking Bits: (CMBitMap*) 0x00000193613d6370
 Bits: [0x000001937a100000, 0x000001937f8d0000)

Polling page: 0x0000019360b30000

Metaspace:

Usage:
  Non-class:      2.19 MB used.
      Class:    181.35 KB used.
       Both:      2.36 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       2.19 MB (  3%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     192.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       2.38 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.75 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 2.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 38.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 3.
num_chunk_merges: 0.
num_chunk_splits: 2.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x00000193712a0000, 0x0000019371510000, 0x00000193787d0000]
CodeHeap 'profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x00000193697d0000, 0x0000019369a40000, 0x0000019370d00000]
CodeHeap 'non-nmethods': size=5760Kb used=415Kb max_used=415Kb free=5344Kb
 bounds [0x0000019370d00000, 0x0000019370f70000, 0x00000193712a0000]
 total_blobs=167 nmethods=0 adapters=137
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.005 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.041 Loading class java/lang/Class$1 done
Event: 0.041 Loading class java/lang/reflect/Modifier
Event: 0.042 Loading class java/lang/reflect/Modifier done
Event: 0.042 Loading class jdk/internal/reflect/MethodHandleAccessorFactory
Event: 0.042 Loading class jdk/internal/reflect/MethodHandleAccessorFactory done
Event: 0.042 Loading class jdk/internal/reflect/DirectConstructorHandleAccessor
Event: 0.042 Loading class jdk/internal/reflect/DirectConstructorHandleAccessor done
Event: 0.042 Loading class jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
Event: 0.042 Loading class jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor done
Event: 0.042 Loading class jdk/internal/util/StaticProperty
Event: 0.042 Loading class jdk/internal/util/StaticProperty done
Event: 0.042 Loading class java/io/FileInputStream
Event: 0.042 Loading class java/io/FileInputStream done
Event: 0.042 Loading class java/io/FileDescriptor
Event: 0.042 Loading class java/io/FileDescriptor done
Event: 0.042 Loading class java/io/FileDescriptor$1
Event: 0.042 Loading class jdk/internal/access/JavaIOFileDescriptorAccess
Event: 0.042 Loading class jdk/internal/access/JavaIOFileDescriptorAccess done
Event: 0.042 Loading class java/io/FileDescriptor$1 done
Event: 0.042 Loading class java/io/FileOutputStream

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Events (1 events):
Event: 0.010 Thread 0x000001935f15ba70 Thread added: 0x000001935f15ba70


Dynamic libraries:
0x00007ff654c80000 - 0x00007ff654c90000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ff8cebf0000 - 0x00007ff8cede8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8cd350000 - 0x00007ff8cd412000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8cc4c0000 - 0x00007ff8cc7b6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8cc850000 - 0x00007ff8cc950000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8a39d0000 - 0x00007ff8a39e9000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ff8a39f0000 - 0x00007ff8a3a0b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ff8cd6f0000 - 0x00007ff8cd7a1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8cce60000 - 0x00007ff8ccefe000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8ce9c0000 - 0x00007ff8cea5f000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8cd1e0000 - 0x00007ff8cd303000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8cc3e0000 - 0x00007ff8cc407000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8cdf20000 - 0x00007ff8ce0bd000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8ccbe0000 - 0x00007ff8ccc02000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8bcf80000 - 0x00007ff8bd21a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ff8cd320000 - 0x00007ff8cd34b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8ccac0000 - 0x00007ff8ccbd9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8cc950000 - 0x00007ff8cc9ed000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8c39b0000 - 0x00007ff8c39ba000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8cd6c0000 - 0x00007ff8cd6ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c2ba0000 - 0x00007ff8c2bac000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ff8a31a0000 - 0x00007ff8a322e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ff85ff50000 - 0x00007ff860c67000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ff8cd550000 - 0x00007ff8cd5bb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8cc0d0000 - 0x00007ff8cc11b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8c0fc0000 - 0x00007ff8c0fe7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8cc0b0000 - 0x00007ff8cc0c2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8caaf0000 - 0x00007ff8cab02000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8b5360000 - 0x00007ff8b536a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ff8ca880000 - 0x00007ff8caa81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8baf20000 - 0x00007ff8baf54000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8cc7c0000 - 0x00007ff8cc842000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8a3180000 - 0x00007ff8a319f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ff8cd7b0000 - 0x00007ff8cdf1e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ff8ca0d0000 - 0x00007ff8ca873000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ff8ce540000 - 0x00007ff8ce893000 	C:\WINDOWS\System32\combase.dll
0x00007ff8cbbe0000 - 0x00007ff8cbc0b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ff8cea80000 - 0x00007ff8ceb4d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ff8cd4a0000 - 0x00007ff8cd54d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ff8ceb50000 - 0x00007ff8cebab000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ff8cc1b0000 - 0x00007ff8cc1d5000 	C:\WINDOWS\SYSTEM32\profapi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server

VM Arguments:
java_command: <unknown>
java_class_path (initial): <not set>
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 5892997120                                {product} {ergonomic}
   size_t MaxNewSize                               = 3535798272                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 5892997120                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 9:02 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (5213M free)
TotalPageFile size 22476M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 20M, peak: 20M
current process commit charge ("private bytes"): 425M, peak: 425M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
