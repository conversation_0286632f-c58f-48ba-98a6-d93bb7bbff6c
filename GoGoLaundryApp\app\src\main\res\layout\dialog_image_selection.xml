<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Select Profile Picture"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
        android:layout_marginBottom="16dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_take_photo"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Take Photo"
        android:layout_marginBottom="8dp" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_choose_gallery"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Choose from Gallery" />

</LinearLayout>
