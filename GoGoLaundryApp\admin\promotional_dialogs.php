<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/auth.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit();
}

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create':
                $result = createPromoDialog($_POST);
                if ($result['success']) {
                    $message = 'Promotional dialog created successfully!';
                    $messageType = 'success';
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;

            case 'update':
                $result = updatePromoDialog($_POST);
                if ($result['success']) {
                    $message = 'Promotional dialog updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;

            case 'delete':
                $result = deletePromoDialog($_POST['id']);
                if ($result['success']) {
                    $message = 'Promotional dialog deleted successfully!';
                    $messageType = 'success';
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;

            case 'toggle_status':
                $result = togglePromoDialogStatus($_POST['id']);
                if ($result['success']) {
                    $message = 'Status updated successfully!';
                    $messageType = 'success';
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;
        }
    }
}

// Get all promotional dialogs
$promoDialogs = getAllPromoDialogs();

function createPromoDialog($data) {
    global $conn;

    try {
        // Handle image upload
        $imagePath = '';
        if (isset($_FILES['promo_image']) && $_FILES['promo_image']['error'] == 0) {
            $uploadResult = uploadPromoImage($_FILES['promo_image']);
            if ($uploadResult['success']) {
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'message' => $uploadResult['message']];
            }
        }

        $stmt = $conn->prepare("
            INSERT INTO promotional_dialogs
            (title, subtitle, description, discount_text, promo_code, button_text,
             image_path, image_url, background_color, text_color, button_color,
             is_active, start_date, end_date, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $stmt->bind_param("ssssssssssssss",
            $data['title'],
            $data['subtitle'],
            $data['description'],
            $data['discount_text'],
            $data['promo_code'],
            $data['button_text'],
            $imagePath,
            $data['image_url'],
            $data['background_color'],
            $data['text_color'],
            $data['button_color'],
            $data['is_active'],
            $data['start_date'],
            $data['end_date']
        );

        if ($stmt->execute()) {
            return ['success' => true];
        } else {
            return ['success' => false, 'message' => 'Database error: ' . $stmt->error];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function updatePromoDialog($data) {
    global $conn;

    try {
        // Handle image upload if new image provided
        $imagePath = $data['existing_image_path'];
        if (isset($_FILES['promo_image']) && $_FILES['promo_image']['error'] == 0) {
            $uploadResult = uploadPromoImage($_FILES['promo_image']);
            if ($uploadResult['success']) {
                // Delete old image if exists
                if (!empty($imagePath) && file_exists("../uploads/promo/" . basename($imagePath))) {
                    unlink("../uploads/promo/" . basename($imagePath));
                }
                $imagePath = $uploadResult['path'];
            } else {
                return ['success' => false, 'message' => $uploadResult['message']];
            }
        }

        $stmt = $conn->prepare("
            UPDATE promotional_dialogs SET
            title = ?, subtitle = ?, description = ?, discount_text = ?,
            promo_code = ?, button_text = ?, image_path = ?, image_url = ?,
            background_color = ?, text_color = ?, button_color = ?,
            is_active = ?, start_date = ?, end_date = ?, updated_at = NOW()
            WHERE id = ?
        ");

        $stmt->bind_param("ssssssssssssssi",
            $data['title'],
            $data['subtitle'],
            $data['description'],
            $data['discount_text'],
            $data['promo_code'],
            $data['button_text'],
            $imagePath,
            $data['image_url'],
            $data['background_color'],
            $data['text_color'],
            $data['button_color'],
            $data['is_active'],
            $data['start_date'],
            $data['end_date'],
            $data['id']
        );

        if ($stmt->execute()) {
            return ['success' => true];
        } else {
            return ['success' => false, 'message' => 'Database error: ' . $stmt->error];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function deletePromoDialog($id) {
    global $conn;

    try {
        // Get image path before deleting
        $stmt = $conn->prepare("SELECT image_path FROM promotional_dialogs WHERE id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $dialog = $result->fetch_assoc();

        // Delete the record
        $stmt = $conn->prepare("DELETE FROM promotional_dialogs WHERE id = ?");
        $stmt->bind_param("i", $id);

        if ($stmt->execute()) {
            // Delete image file if exists
            if (!empty($dialog['image_path']) && file_exists("../uploads/promo/" . basename($dialog['image_path']))) {
                unlink("../uploads/promo/" . basename($dialog['image_path']));
            }
            return ['success' => true];
        } else {
            return ['success' => false, 'message' => 'Database error: ' . $stmt->error];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function togglePromoDialogStatus($id) {
    global $conn;

    try {
        $stmt = $conn->prepare("UPDATE promotional_dialogs SET is_active = NOT is_active WHERE id = ?");
        $stmt->bind_param("i", $id);

        if ($stmt->execute()) {
            return ['success' => true];
        } else {
            return ['success' => false, 'message' => 'Database error: ' . $stmt->error];
        }

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
    }
}

function getAllPromoDialogs() {
    global $conn;

    $stmt = $conn->prepare("
        SELECT * FROM promotional_dialogs
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->fetch_all(MYSQLI_ASSOC);
}

function uploadPromoImage($file) {
    $uploadDir = '../uploads/promo/';

    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed.'];
    }

    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'File size too large. Maximum 5MB allowed.'];
    }

    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'promo_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filepath = $uploadDir . $filename;

    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'path' => 'uploads/promo/' . $filename];
    } else {
        return ['success' => false, 'message' => 'Failed to upload file.'];
    }
}

include 'includes/head.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include 'includes/sidebar.php'; ?>

        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Promotional Dialogs</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPromoModal">
                    <i class="fas fa-plus"></i> Create New Promo
                </button>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Promotional Dialogs List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Manage Promotional Dialogs</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($promoDialogs)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-bullhorn fa-3x text-muted mb-3"></i>
                            <h5>No promotional dialogs found</h5>
                            <p class="text-muted">Create your first promotional dialog to engage customers.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Discount</th>
                                        <th>Promo Code</th>
                                        <th>Status</th>
                                        <th>Valid Until</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($promoDialogs as $dialog): ?>
                                        <tr>
                                            <td><?php echo $dialog['id']; ?></td>
                                            <td><?php echo htmlspecialchars($dialog['title']); ?></td>
                                            <td><?php echo htmlspecialchars($dialog['discount_text']); ?></td>
                                            <td>
                                                <?php if (!empty($dialog['promo_code'])): ?>
                                                    <code><?php echo htmlspecialchars($dialog['promo_code']); ?></code>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $dialog['is_active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $dialog['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($dialog['end_date'])): ?>
                                                    <?php echo date('M d, Y', strtotime($dialog['end_date'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No expiry</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="editPromoDialog(<?php echo htmlspecialchars(json_encode($dialog)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-<?php echo $dialog['is_active'] ? 'warning' : 'success'; ?>"
                                                            onclick="toggleStatus(<?php echo $dialog['id']; ?>)">
                                                        <i class="fas fa-<?php echo $dialog['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deletePromoDialog(<?php echo $dialog['id']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Create Promo Modal -->
<div class="modal fade" id="createPromoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Promotional Dialog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="title" name="title" required
                                       placeholder="e.g., HOT PICKS LOW PRICES">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control" id="subtitle" name="subtitle"
                                       placeholder="e.g., Best Deals on Best Prices">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="Additional description or terms"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="discount_text" class="form-label">Discount Text *</label>
                                <input type="text" class="form-control" id="discount_text" name="discount_text" required
                                       placeholder="e.g., UP TO 60% OFF">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="promo_code" class="form-label">Promo Code</label>
                                <input type="text" class="form-control" id="promo_code" name="promo_code"
                                       placeholder="e.g., SAVE60">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="button_text" class="form-label">Button Text *</label>
                        <input type="text" class="form-control" id="button_text" name="button_text" required
                               value="Shop Now" placeholder="e.g., Shop Now">
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="promo_image" class="form-label">Upload Image</label>
                                <input type="file" class="form-control" id="promo_image" name="promo_image"
                                       accept="image/*">
                                <div class="form-text">Max 5MB. JPG, PNG, GIF allowed.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="image_url" class="form-label">Or Image URL</label>
                                <input type="url" class="form-control" id="image_url" name="image_url"
                                       placeholder="https://example.com/image.jpg">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="background_color" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="background_color"
                                       name="background_color" value="#6c757d">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="text_color" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="text_color"
                                       name="text_color" value="#ffffff">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="button_color" class="form-label">Button Color</label>
                                <input type="color" class="form-control form-control-color" id="button_color"
                                       name="button_color" value="#ffd700">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" id="start_date" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="datetime-local" class="form-control" id="end_date" name="end_date">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                            <label class="form-check-label" for="is_active">
                                Active (Show to users)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Promo Dialog</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Promo Modal -->
<div class="modal fade" id="editPromoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Promotional Dialog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="id" id="edit_id">
                    <input type="hidden" name="existing_image_path" id="edit_existing_image_path">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_title" class="form-label">Title *</label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_subtitle" class="form-label">Subtitle</label>
                                <input type="text" class="form-control" id="edit_subtitle" name="subtitle">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_discount_text" class="form-label">Discount Text *</label>
                                <input type="text" class="form-control" id="edit_discount_text" name="discount_text" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_promo_code" class="form-label">Promo Code</label>
                                <input type="text" class="form-control" id="edit_promo_code" name="promo_code">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_button_text" class="form-label">Button Text *</label>
                        <input type="text" class="form-control" id="edit_button_text" name="button_text" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_promo_image" class="form-label">Upload New Image</label>
                                <input type="file" class="form-control" id="edit_promo_image" name="promo_image" accept="image/*">
                                <div class="form-text">Leave empty to keep current image.</div>
                                <div id="current_image_preview"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_image_url" class="form-label">Or Image URL</label>
                                <input type="url" class="form-control" id="edit_image_url" name="image_url">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_background_color" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="edit_background_color" name="background_color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_text_color" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="edit_text_color" name="text_color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_button_color" class="form-label">Button Color</label>
                                <input type="color" class="form-control form-control-color" id="edit_button_color" name="button_color">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_start_date" class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" id="edit_start_date" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_end_date" class="form-label">End Date</label>
                                <input type="datetime-local" class="form-control" id="edit_end_date" name="end_date">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" value="1">
                            <label class="form-check-label" for="edit_is_active">
                                Active (Show to users)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Promo Dialog</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPromoDialog(dialog) {
    // Populate edit form
    document.getElementById('edit_id').value = dialog.id;
    document.getElementById('edit_title').value = dialog.title || '';
    document.getElementById('edit_subtitle').value = dialog.subtitle || '';
    document.getElementById('edit_description').value = dialog.description || '';
    document.getElementById('edit_discount_text').value = dialog.discount_text || '';
    document.getElementById('edit_promo_code').value = dialog.promo_code || '';
    document.getElementById('edit_button_text').value = dialog.button_text || '';
    document.getElementById('edit_image_url').value = dialog.image_url || '';
    document.getElementById('edit_background_color').value = dialog.background_color || '#6c757d';
    document.getElementById('edit_text_color').value = dialog.text_color || '#ffffff';
    document.getElementById('edit_button_color').value = dialog.button_color || '#ffd700';
    document.getElementById('edit_existing_image_path').value = dialog.image_path || '';

    // Format dates for datetime-local input
    if (dialog.start_date) {
        const startDate = new Date(dialog.start_date);
        document.getElementById('edit_start_date').value = startDate.toISOString().slice(0, 16);
    }
    if (dialog.end_date) {
        const endDate = new Date(dialog.end_date);
        document.getElementById('edit_end_date').value = endDate.toISOString().slice(0, 16);
    }

    document.getElementById('edit_is_active').checked = dialog.is_active == 1;

    // Show current image preview
    const imagePreview = document.getElementById('current_image_preview');
    if (dialog.image_path) {
        imagePreview.innerHTML = `
            <div class="mt-2">
                <small class="text-muted">Current image:</small><br>
                <img src="../${dialog.image_path}" alt="Current image" style="max-width: 100px; max-height: 100px;" class="img-thumbnail">
            </div>
        `;
    } else {
        imagePreview.innerHTML = '';
    }

    // Show modal
    new bootstrap.Modal(document.getElementById('editPromoModal')).show();
}

function toggleStatus(id) {
    if (confirm('Are you sure you want to toggle the status of this promotional dialog?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_status">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function deletePromoDialog(id) {
    if (confirm('Are you sure you want to delete this promotional dialog? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});
</script>

<?php include 'includes/footer.php'; ?>