<?php
/**
 * Direct FCM Test - Send notification to specific token
 * 
 * This script tests FCM notification sending directly to a specific token
 */

require_once 'config/db.php';
require_once 'includes/FCMService.php';

echo "<h2>Direct FCM Notification Test</h2>\n";

try {
    // Get the most recent FCM token from database
    $stmt = $pdo->prepare("
        SELECT ft.token, ft.device_type, ft.device_id, u.full_name, ft.created_at, ft.updated_at
        FROM fcm_tokens ft 
        JOIN users u ON ft.user_id = u.id 
        WHERE ft.is_active = 1 
        ORDER BY ft.updated_at DESC 
        LIMIT 1
    ");
    $stmt->execute();
    $tokenData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tokenData) {
        echo "<p style='color: red;'>❌ No active FCM tokens found in database</p>\n";
        exit();
    }
    
    echo "<h3>Token Information:</h3>\n";
    echo "<p><strong>User:</strong> " . htmlspecialchars($tokenData['full_name']) . "</p>\n";
    echo "<p><strong>Device Type:</strong> " . htmlspecialchars($tokenData['device_type']) . "</p>\n";
    echo "<p><strong>Device ID:</strong> " . htmlspecialchars($tokenData['device_id']) . "</p>\n";
    echo "<p><strong>Token:</strong> " . htmlspecialchars(substr($tokenData['token'], 0, 50)) . "...</p>\n";
    echo "<p><strong>Created:</strong> " . htmlspecialchars($tokenData['created_at']) . "</p>\n";
    echo "<p><strong>Updated:</strong> " . htmlspecialchars($tokenData['updated_at']) . "</p>\n";
    
    // Initialize FCM service
    $fcmService = new FCMService();
    echo "<p style='color: green;'>✅ FCM Service initialized</p>\n";
    
    // Test notification data
    $title = "Test Notification " . date('H:i:s');
    $message = "This is a test notification sent at " . date('Y-m-d H:i:s');
    $data = [
        'type' => 'custom',
        'test' => 'true',
        'timestamp' => time(),
        'source' => 'direct_test'
    ];
    
    echo "<h3>Sending Test Notification:</h3>\n";
    echo "<p><strong>Title:</strong> " . htmlspecialchars($title) . "</p>\n";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($message) . "</p>\n";
    echo "<p><strong>Data:</strong> " . htmlspecialchars(json_encode($data)) . "</p>\n";
    
    // Send notification
    $result = $fcmService->sendToToken(
        $tokenData['token'], 
        $title, 
        $message, 
        $data, 
        $tokenData['device_type']
    );
    
    echo "<h3>FCM Send Result:</h3>\n";
    if ($result['success']) {
        echo "<p style='color: green;'>✅ Notification sent successfully!</p>\n";
        echo "<p><strong>Response:</strong></p>\n";
        echo "<pre>" . htmlspecialchars(json_encode($result['response'], JSON_PRETTY_PRINT)) . "</pre>\n";
    } else {
        echo "<p style='color: red;'>❌ Failed to send notification</p>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($result['error']) . "</p>\n";
    }
    
    // Test with data-only payload (no notification section)
    echo "<h3>Testing Data-Only Notification:</h3>\n";
    
    // Create a custom payload with only data (no notification section)
    $dataOnlyPayload = [
        'message' => [
            'token' => $tokenData['token'],
            'data' => [
                'title' => $title . " (Data Only)",
                'message' => $message . " - Data only payload",
                'type' => 'custom',
                'test' => 'data_only',
                'timestamp' => (string)time()
            ]
        ]
    ];
    
    echo "<p><strong>Data-Only Payload:</strong></p>\n";
    echo "<pre>" . htmlspecialchars(json_encode($dataOnlyPayload, JSON_PRETTY_PRINT)) . "</pre>\n";
    
    // Send data-only notification using reflection to access private method
    $reflection = new ReflectionClass($fcmService);
    $sendWithCurlMethod = $reflection->getMethod('sendWithCurl');
    $sendWithCurlMethod->setAccessible(true);
    
    $dataOnlyResult = $sendWithCurlMethod->invoke($fcmService, $dataOnlyPayload);
    
    echo "<h3>Data-Only Send Result:</h3>\n";
    if ($dataOnlyResult['success']) {
        echo "<p style='color: green;'>✅ Data-only notification sent successfully!</p>\n";
        echo "<p><strong>Response:</strong></p>\n";
        echo "<pre>" . htmlspecialchars(json_encode($dataOnlyResult['response'], JSON_PRETTY_PRINT)) . "</pre>\n";
    } else {
        echo "<p style='color: red;'>❌ Failed to send data-only notification</p>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($dataOnlyResult['error']) . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p><strong>Stack trace:</strong></p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<h3>Instructions:</h3>\n";
echo "<ol>\n";
echo "<li>Check your Android device/emulator for notifications</li>\n";
echo "<li>Check Android Studio logcat for FCM logs with tag 'FCMService'</li>\n";
echo "<li>Look for logs starting with '=== FCM MESSAGE RECEIVED ==='</li>\n";
echo "<li>If no logs appear, the issue is with FCM delivery, not the Android app</li>\n";
echo "</ol>\n";
?>
