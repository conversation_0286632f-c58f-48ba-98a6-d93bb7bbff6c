<?php
/**
 * Services Admin API Endpoint
 *
 * This endpoint returns services with pagination support for admin panel
 *
 * Parameters:
 * - page: Page number (default: 1)
 * - per_page: Items per page (default: 5)
 * - active_only: Return only active services (1 or 0)
 * - search: Search term for filtering services
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ServiceManager.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Initialize service manager
$serviceManager = new ServiceManager($pdo);

// Get pagination parameters
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$perPage = isset($_GET['per_page']) ? max(1, min(50, (int)$_GET['per_page'])) : 5; // Default 5 per page, max 50
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === '1';

// Get services
if ($activeOnly) {
    // For active only, we still need pagination
    $services = $serviceManager->getActiveServices();

    // Manual pagination for active services
    $totalCount = count($services);
    $totalPages = ceil($totalCount / $perPage);
    $offset = ($page - 1) * $perPage;
    $paginatedServices = array_slice($services, $offset, $perPage);

    $result = [
        'services' => $paginatedServices,
        'pagination' => [
            'current' => $page,
            'total' => $totalPages,
            'count' => $totalCount,
            'perPage' => $perPage
        ]
    ];
} else {
    // Use the built-in pagination from ServiceManager
    $result = $serviceManager->getAllServices($page, $perPage, $search);
}

// Process services to include image URLs
foreach ($result['services'] as &$service) {
    // If image URL is relative, convert to absolute URL
    if (!empty($service['image_url']) && strpos($service['image_url'], 'http') !== 0) {
        // Create absolute URL using APP_URL
        $service['image_url'] = APP_URL . '/uploads/services/' . $service['image_url'];
    }
}

// Prepare response data
$responseData = [
    'services' => $result['services'],
    'pagination' => $result['pagination']
];

// Return success response
jsonResponse(true, 'Services retrieved successfully', $responseData);
