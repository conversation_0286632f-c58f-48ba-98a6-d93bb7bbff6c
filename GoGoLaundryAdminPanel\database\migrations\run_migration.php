<?php
/**
 * Database Migration Runner
 * Run this script to add image_url column to notifications table
 */

require_once __DIR__ . '/../../config/db.php';

try {
    echo "Starting migration: Add image_url to notifications table\n";

    // Check if column already exists
    $stmt = $pdo->prepare("
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'notifications'
        AND COLUMN_NAME = 'image_url'
    ");
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        echo "Column 'image_url' already exists in notifications table.\n";
    } else {
        // Add image_url column
        $pdo->exec("ALTER TABLE `notifications` ADD COLUMN `image_url` VARCHAR(500) NULL DEFAULT NULL AFTER `message`");
        echo "✓ Added image_url column to notifications table\n";

        // Add index for better performance
        $pdo->exec("ALTER TABLE `notifications` ADD INDEX `idx_image_url` (`image_url`)");
        echo "✓ Added index on image_url column\n";
    }

    // Verify the change
    $stmt = $pdo->prepare("
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'notifications'
        AND COLUMN_NAME = 'image_url'
    ");
    $stmt->execute();
    $result = $stmt->fetch();

    if ($result) {
        echo "\n✓ Migration completed successfully!\n";
        echo "Column details:\n";
        echo "- Name: " . $result['COLUMN_NAME'] . "\n";
        echo "- Type: " . $result['DATA_TYPE'] . "\n";
        echo "- Nullable: " . $result['IS_NULLABLE'] . "\n";
        echo "- Default: " . ($result['COLUMN_DEFAULT'] ?? 'NULL') . "\n";
    } else {
        echo "\n❌ Migration failed - column not found after creation\n";
    }

} catch (PDOException $e) {
    echo "\n❌ Migration failed: " . $e->getMessage() . "\n";
}
?>
