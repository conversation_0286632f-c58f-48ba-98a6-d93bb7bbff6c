package com.mdsadrulhasan.gogolaundry.repository;

import android.content.Context;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.GoGoLaundryApp;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.database.dao.OrderDao;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.model.OrderItem;
import com.mdsadrulhasan.gogolaundry.utils.AppExecutors;
import com.mdsadrulhasan.gogolaundry.utils.Resource;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Locale;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Repository for order-related operations
 */
public class OrderRepository {
    private static final String TAG = "OrderRepository";

    private static OrderRepository instance;

    private final OrderDao orderDao;
    private final ApiService apiService;
    private final AppExecutors executors;

    private OrderRepository() {
        orderDao = GoGoLaundryApp.getInstance().getDatabase().orderDao();
        apiService = ApiClient.getApiService(GoGoLaundryApp.getInstance());
        executors = AppExecutors.getInstance();
    }

    // Context for toast notifications
    private Context getContext() {
        return GoGoLaundryApp.getInstance().getApplicationContext();
    }

    /**
     * Get repository instance
     *
     * @return Repository instance
     */
    public static synchronized OrderRepository getInstance() {
        if (instance == null) {
            instance = new OrderRepository();
        }
        return instance;
    }

    /**
     * Get all orders for a user
     *
     * @param userId User ID
     * @param forceRefresh Whether to force refresh from network
     * @return LiveData of orders
     */
    public LiveData<Resource<List<OrderEntity>>> getOrdersByUserId(int userId, boolean forceRefresh) {
        MutableLiveData<Resource<List<OrderEntity>>> result = new MutableLiveData<>();

        // First, load from database
        executors.diskIO().execute(() -> {
            List<OrderEntity> orders = orderDao.getOrdersByUserId(userId);

            executors.mainThread().execute(() -> {
                if (orders.isEmpty() || forceRefresh) {
                    // If database is empty or force refresh, load from network
                    fetchOrdersFromNetwork(result, userId);
                } else {
                    // Otherwise, return data from database
                    result.setValue(Resource.success(orders));
                }
            });
        });

        return result;
    }

    /**
     * Get order by ID
     *
     * @param orderId Order ID
     * @param forceRefresh Whether to force refresh from network
     * @return LiveData of order
     */
    public LiveData<Resource<OrderEntity>> getOrderById(int orderId, boolean forceRefresh) {
        MutableLiveData<Resource<OrderEntity>> result = new MutableLiveData<>();

        // First, load from database
        executors.diskIO().execute(() -> {
            OrderEntity order = orderDao.getOrderById(orderId);

            executors.mainThread().execute(() -> {
                if (order == null || forceRefresh) {
                    // If not found in database or force refresh, load from network
                    fetchOrderFromNetwork(result, orderId);
                } else {
                    // Otherwise, return data from database
                    result.setValue(Resource.success(order));
                }
            });
        });

        return result;
    }

    /**
     * Get order items by order ID
     *
     * @param orderId Order ID
     * @return LiveData of order items
     */
    public LiveData<List<OrderItemEntity>> getOrderItemsByOrderId(int orderId) {
        return orderDao.getOrderItemsByOrderIdLive(orderId);
    }

    /**
     * Get order by tracking number
     *
     * @param trackingNumber Order tracking number
     * @return OrderEntity or null if not found
     */
    public OrderEntity getOrderByTrackingNumber(String trackingNumber) {
        return orderDao.getOrderByTrackingNumber(trackingNumber);
    }

    /**
     * Get order by order number
     *
     * @param orderNumber Order number
     * @return OrderEntity or null if not found
     */
    public OrderEntity getOrderByOrderNumber(String orderNumber) {
        return orderDao.getOrderByOrderNumber(orderNumber);
    }

    /**
     * Get all orders
     *
     * @return List of all orders
     */
    public List<OrderEntity> getAllOrders() {
        return orderDao.getAllOrders();
    }

    /**
     * Delete order by ID
     *
     * @param orderId Order ID
     */
    public void deleteOrder(int orderId) {
        orderDao.deleteOrder(orderId);
    }

    /**
     * Delete order items by order ID
     *
     * @param orderId Order ID
     */
    public void deleteOrderItemsByOrderId(int orderId) {
        orderDao.deleteOrderItemsByOrderId(orderId);
    }

    /**
     * Fetch orders from network
     *
     * @param result Result LiveData
     * @param userId User ID
     */
    private void fetchOrdersFromNetwork(MutableLiveData<Resource<List<OrderEntity>>> result, int userId) {
        result.setValue(Resource.loading(null));

        // Make API call to get user orders
        Call<ApiResponse<List<Order>>> call = apiService.getUserOrders(userId);
        call.enqueue(new Callback<ApiResponse<List<Order>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<Order>>> call, Response<ApiResponse<List<Order>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<Order> orders = response.body().getData();

                    // Log the response for debugging
                    Log.d("OrderRepository", "API Response: " + response.body().getMessage());
                    Log.d("OrderRepository", "Orders count: " + (orders != null ? orders.size() : 0));

                    if (orders != null) {
                        for (Order order : orders) {
                            Log.d("OrderRepository", "Order: " + order.getOrderNumber() + ", Status: " + order.getStatus());
                            if (order.getOrderItems() != null) {
                                Log.d("OrderRepository", "Order items: " + order.getOrderItems().size());
                            }
                        }
                    }

                    if (orders != null && !orders.isEmpty()) {
                        // Convert API models to database entities
                        List<OrderEntity> orderEntities = convertToOrderEntities(orders);

                        // Save to database
                        executors.diskIO().execute(() -> {
                            // First, delete ALL existing orders and order items for this user
                            List<OrderEntity> existingOrders = orderDao.getOrdersByUserId(userId);
                            for (OrderEntity order : existingOrders) {
                                // Delete all order items for this order
                                orderDao.deleteOrderItemsByOrderId(order.getId());
                                // Delete the order
                                orderDao.deleteOrder(order.getId());
                            }

                            // Then, insert new orders
                            for (OrderEntity order : orderEntities) {
                                long orderId = orderDao.insert(order);

                                // Insert order items if available
                                if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
                                    // Update order ID in case it changed during insertion
                                    for (OrderItemEntity item : order.getOrderItems()) {
                                        item.setOrderId((int)orderId);
                                    }
                                    orderDao.insertOrderItems(order.getOrderItems());
                                }
                            }

                            // Finally, get the updated orders from database
                            List<OrderEntity> updatedOrders = orderDao.getOrdersByUserId(userId);

                            executors.mainThread().execute(() -> {
                                result.setValue(Resource.success(updatedOrders));

                                // Show success toast
                                //ToastUtils.showSuccessToast(getContext(), "Orders loaded successfully");
                            });
                        });
                    } else {
                        // No orders found
                        executors.diskIO().execute(() -> {
                            // Clear existing orders for this user
                            List<OrderEntity> existingOrders = orderDao.getOrdersByUserId(userId);
                            for (OrderEntity order : existingOrders) {
                                List<OrderItemEntity> orderItems = orderDao.getOrderItemsByOrderId(order.getId());
                                for (OrderItemEntity orderItem : orderItems) {
                                    orderDao.updateOrderItem(orderItem);
                                }
                                orderDao.update(order);
                            }

                            executors.mainThread().execute(() -> {
                                result.setValue(Resource.success(new ArrayList<>()));

                                // Show info toast
                                ToastUtils.showInfoToast(getContext(), "No orders found");
                            });
                        });
                    }
                } else {
                    // API error
                    String errorMessage = "Failed to load orders";
                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    }
                    final String finalErrorMessage = errorMessage;

                    executors.mainThread().execute(() -> {
                        result.setValue(Resource.error(finalErrorMessage, null));

                        // Show error toast
                        ToastUtils.showErrorToast(getContext(), finalErrorMessage);
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<Order>>> call, Throwable t) {
                // Network error
                String errorMessage = "Network error: " + t.getMessage();

                // Simplify error message for common network issues
                if (t.getMessage() != null && t.getMessage().contains("Unable to resolve host")) {
                    errorMessage = "No internet connection. Please check your network settings.";
                }

                final String finalErrorMessage = errorMessage;

                executors.mainThread().execute(() -> {
                    result.setValue(Resource.error(finalErrorMessage, null));

                    // Show error toast
                    ToastUtils.showErrorToast(getContext(), finalErrorMessage);
                });

                // Fallback to local database if available
                executors.diskIO().execute(() -> {
                    // Get orders from local database
                    List<OrderEntity> localOrders = orderDao.getOrdersByUserId(userId);

                    if (!localOrders.isEmpty()) {
                        // Use local data if available
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.success(localOrders));

                            // Show info toast
                            ToastUtils.showInfoToast(getContext(), "Showing cached orders. Pull to refresh when online.");
                        });
                    } else {
                        // No local data available
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.error("No orders found. Please try again when online.", null));

                            // Show error toast
                            ToastUtils.showErrorToast(getContext(), "No orders found. Please try again when online.");
                        });
                    }
                });
            }
        });
    }

    /**
     * Convert API Order models to database OrderEntity objects
     *
     * @param orders List of API Order models
     * @return List of OrderEntity objects
     */
    private List<OrderEntity> convertToOrderEntities(List<Order> orders) {
        List<OrderEntity> orderEntities = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);

        for (Order order : orders) {
            OrderEntity entity = new OrderEntity();
            entity.setId(order.getId());
            entity.setOrderNumber(order.getOrderNumber());
            entity.setTrackingNumber(order.getTrackingNumber());
            entity.setUserId(order.getUserId());
            entity.setSubtotal(order.getSubtotal());
            entity.setDiscount(order.getDiscount());
            entity.setDeliveryFee(order.getDeliveryFee());
            entity.setTotal(order.getTotalAmount());
            entity.setStatus(order.getStatus());
            entity.setPaymentStatus(order.getPaymentStatus());
            entity.setPaymentMethod(order.getPaymentMethod());

            // Parse dates
            try {
                if (order.getCreatedAt() != null) {
                    entity.setCreatedAt(dateFormat.parse(order.getCreatedAt()));
                }
                if (order.getUpdatedAt() != null) {
                    entity.setUpdatedAt(dateFormat.parse(order.getUpdatedAt()));
                }
            } catch (ParseException e) {
                e.printStackTrace();
                // Use current date as fallback
                entity.setCreatedAt(new Date());
                entity.setUpdatedAt(new Date());
            }

            // Convert order items if available
            if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
                List<OrderItemEntity> orderItemEntities = convertToOrderItemEntities(order.getOrderItems(), order.getId());
                entity.setOrderItems(orderItemEntities);
            }

            orderEntities.add(entity);
        }

        return orderEntities;
    }

    /**
     * Convert API OrderItem models to database OrderItemEntity objects
     *
     * @param orderItems List of API OrderItem models
     * @param orderId Order ID
     * @return List of OrderItemEntity objects
     */
    private List<OrderItemEntity> convertToOrderItemEntities(List<OrderItem> orderItems, int orderId) {
        List<OrderItemEntity> orderItemEntities = new ArrayList<>();

        for (OrderItem item : orderItems) {
            OrderItemEntity entity = new OrderItemEntity();
            entity.setOrderId(orderId);

            // Log the item for debugging
            Log.d("OrderRepository", "Converting order item: " + item.getId() + ", Name: " + item.getServiceName());

            // Set fields based on API response
            entity.setId(item.getId());
            entity.setItemId(item.getServiceId());
            entity.setQuantity((int)item.getQuantity()); // Cast double to int
            entity.setPrice(item.getPrice());
            entity.setSubtotal(item.getSubtotal());

            // Set display fields
            entity.setItemName(item.getServiceName());
            if (item.getServiceName() != null) {
                entity.setServiceName(item.getServiceName());
            }

            // Set dates
            try {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
                if (item.getCreatedAt() != null) {
                    entity.setCreatedAt(dateFormat.parse(item.getCreatedAt()));
                } else {
                    entity.setCreatedAt(new Date());
                }
                if (item.getUpdatedAt() != null) {
                    entity.setUpdatedAt(dateFormat.parse(item.getUpdatedAt()));
                } else {
                    entity.setUpdatedAt(new Date());
                }
            } catch (ParseException e) {
                e.printStackTrace();
                entity.setCreatedAt(new Date());
                entity.setUpdatedAt(new Date());
            }

            orderItemEntities.add(entity);
        }

        return orderItemEntities;
    }

    /**
     * Fetch order from network
     *
     * @param result Result LiveData
     * @param orderId Order ID
     */
    private void fetchOrderFromNetwork(MutableLiveData<Resource<OrderEntity>> result, int orderId) {
        result.setValue(Resource.loading(null));

        // Make API call to get order details
        Call<ApiResponse<Order>> call = apiService.getOrderDetails(orderId);
        call.enqueue(new Callback<ApiResponse<Order>>() {
            @Override
            public void onResponse(Call<ApiResponse<Order>> call, Response<ApiResponse<Order>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    Order order = response.body().getData();

                    if (order != null) {
                        // Convert API model to database entity
                        List<Order> orders = new ArrayList<>();
                        orders.add(order);
                        List<OrderEntity> orderEntities = convertToOrderEntities(orders);

                        if (!orderEntities.isEmpty()) {
                            OrderEntity orderEntity = orderEntities.get(0);

                            // Save to database
                            executors.diskIO().execute(() -> {
                                // Insert or update order
                                long id = orderDao.insert(orderEntity);

                                // Insert order items if available
                                if (orderEntity.getOrderItems() != null && !orderEntity.getOrderItems().isEmpty()) {
                                    // Update order ID in case it changed during insertion
                                    for (OrderItemEntity item : orderEntity.getOrderItems()) {
                                        item.setOrderId((int)id);
                                    }
                                    orderDao.insertOrderItems(orderEntity.getOrderItems());
                                }

                                // Get the updated order from database
                                OrderEntity updatedOrder = orderDao.getOrderById(orderId);

                                executors.mainThread().execute(() -> {
                                    result.setValue(Resource.success(updatedOrder));

                                    // Show success toast
                                    ToastUtils.showSuccessToast(getContext(), "Order details loaded successfully");
                                });
                            });
                        } else {
                            executors.mainThread().execute(() -> {
                                result.setValue(Resource.error("Failed to convert order data", null));

                                // Show error toast
                                ToastUtils.showErrorToast(getContext(), "Failed to process order data");
                            });
                        }
                    } else {
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.error("Order not found", null));

                            // Show error toast
                            ToastUtils.showErrorToast(getContext(), "Order not found");
                        });
                    }
                } else {
                    // API error
                    String errorMessage = "Failed to load order";
                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    }
                    final String finalErrorMessage = errorMessage;

                    executors.mainThread().execute(() -> {
                        result.setValue(Resource.error(finalErrorMessage, null));

                        // Show error toast
                        ToastUtils.showErrorToast(getContext(), finalErrorMessage);
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Order>> call, Throwable t) {
                // Network error
                String errorMessage = "Network error: " + t.getMessage();

                // Simplify error message for common network issues
                if (t.getMessage() != null && t.getMessage().contains("Unable to resolve host")) {
                    errorMessage = "No internet connection. Please check your network settings.";
                }

                final String finalErrorMessage = errorMessage;

                executors.mainThread().execute(() -> {
                    result.setValue(Resource.error(finalErrorMessage, null));

                    // Show error toast
                    ToastUtils.showErrorToast(getContext(), finalErrorMessage);
                });

                // Fallback to database
                executors.diskIO().execute(() -> {
                    OrderEntity order = orderDao.getOrderById(orderId);

                    if (order != null) {
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.success(order));

                            // Show info toast about using cached data
                            ToastUtils.showInfoToast(getContext(), "Showing cached order data");
                        });
                    } else {
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.error("Order not found", null));

                            // Show error toast
                            ToastUtils.showErrorToast(getContext(), "Order not found in local database");
                        });
                    }
                });
            }
        });
    }

    // Removed dummy data methods - using real data only
}
