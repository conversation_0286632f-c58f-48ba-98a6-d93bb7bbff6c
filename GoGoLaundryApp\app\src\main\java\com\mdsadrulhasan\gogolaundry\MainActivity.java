package com.mdsadrulhasan.gogolaundry;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.print.PrintDocumentAdapter;
import android.print.PrintManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;

import com.bumptech.glide.Glide;
import com.google.android.material.bottomnavigation.BottomNavigationItemView;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.navigation.NavigationView;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.fragments.ProfileFragment;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.ui.dialog.PromoDialog;
import com.mdsadrulhasan.gogolaundry.api.PromoDialogApiService;
import com.mdsadrulhasan.gogolaundry.data.model.PromoDialogResponse;
import com.mdsadrulhasan.gogolaundry.ui.fragment.CartFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.HomeFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.InvoiceFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.ItemsFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.OrdersFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.ServicesFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.ShopMapFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.NotificationFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.SettingsFragment;
import com.mdsadrulhasan.gogolaundry.utils.LanguageManager;
import com.mdsadrulhasan.gogolaundry.utils.NotificationPermissionManager;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.CartViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.NotificationViewModel;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class MainActivity extends AppCompatActivity implements NavigationView.OnNavigationItemSelectedListener {

    private DrawerLayout drawerLayout;
    private NavigationView navigationView;
    private BottomNavigationView bottomNavigationView;
    private Toolbar toolbar;
    private SessionManager sessionManager;
    private LanguageManager languageManager;
    private NotificationPermissionManager notificationPermissionManager;
    private User currentUser;
    private CartViewModel cartViewModel;
    private NotificationViewModel notificationViewModel;
    private TextView cartBadgeTextView;
    private TextView bottomNavCartBadgeTextView;
    private TextView notificationBadgeTextView;
    private View notificationContainer;

    // Store original context for printing (before any wrapping)
    private Context originalActivityContext;

    // Navigation header views
    private TextView navHeaderName;
    private TextView navHeaderPhone;
    private TextView navHeaderEmail;
    private ShapeableImageView profileImage;
    private ImageView editProfileImage;

    // Toolbar views
    private ShapeableImageView toolbarProfileImage;
    private TextView toolbarTitle;

    // Animation duration for bottom navigation
    private static final int ANIMATION_DURATION = 300;

    // Static handler for promo dialog
    private static Handler promoHandler = new Handler(Looper.getMainLooper());
    private static boolean promoDialogScheduled = false;

    @Override
    protected void attachBaseContext(Context newBase) {
        // Store the original context before any wrapping for printing purposes
        originalActivityContext = newBase;
        Log.d("MainActivity", "attachBaseContext called with: " + newBase.getClass().getName());
        Log.d("MainActivity", "Stored original context for printing: " + originalActivityContext.toString());

        // Apply language settings using LanguageManager
        LanguageManager tempLanguageManager = new LanguageManager(newBase);
        Context languageContext = tempLanguageManager.applyLanguage(newBase);

        Log.d("MainActivity", "Language context created: " + languageContext.getClass().getName());

        // Call super with the language-wrapped context
        super.attachBaseContext(languageContext);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Initialize language manager (language context already applied in attachBaseContext)
        languageManager = new LanguageManager(this);

        super.onCreate(savedInstanceState);
        setContentView(R.layout.custom_navigation_drawer);

        // Initialize session manager
        sessionManager = new SessionManager(this);

        // Initialize notification permission manager
        notificationPermissionManager = new NotificationPermissionManager(this);

        // Initialize ViewModels
        cartViewModel = new ViewModelProvider(this).get(CartViewModel.class);
        notificationViewModel = new ViewModelProvider(this).get(NotificationViewModel.class);

        // Check if user is logged in
        if (!sessionManager.isLoggedIn()) {
            // Redirect to login activity
            startActivity(new Intent(this, LoginActivity.class));
            finish();
            return;
        }

        // Get current user
        currentUser = sessionManager.getUser();

        // Initialize views
        initViews();

        // Log the status of key views
        Log.d("MainActivity", "After initViews - toolbar: " + (toolbar != null));
        Log.d("MainActivity", "After initViews - toolbarProfileImage: " + (toolbarProfileImage != null));
        Log.d("MainActivity", "After initViews - toolbarTitle: " + (toolbarTitle != null));

        // Set up toolbar
        if (toolbar != null) {
            setSupportActionBar(toolbar);
            // Add null check before accessing ActionBar
            if (getSupportActionBar() != null) {
                getSupportActionBar().setDisplayShowTitleEnabled(false); // Hide default title
                Log.d("MainActivity", "ActionBar set up successfully");
            } else {
                Log.e("MainActivity", "getSupportActionBar() returned null");
            }
        } else {
            Log.e("MainActivity", "Toolbar is null, cannot set as support action bar");
        }

        // Set up navigation drawer
        setupNavigationDrawer();

        // Set up navigation header
        setupNavigationHeader();

        // Set up toolbar profile image
        setupToolbarProfileImage();

        // Set up notification icon
        setupNotificationIcon();

        // Set up bottom navigation
        setupBottomNavigation();

        // Observe cart item count
        cartViewModel.getItemCount().observe(this, count -> {
            // Update cart badge in toolbar
            invalidateOptionsMenu();

            // Update cart badge in bottom navigation
            updateBottomNavCartBadge(count);
        });

        // Observe notification count
        if (currentUser != null) {
            notificationViewModel.setUserId(currentUser.getId());
            notificationViewModel.getUnreadCount().observe(this, count -> {
                // Update notification badge
                updateNotificationBadge(count);
            });
        }

        // Set this activity as the current activity in the application
        GoGoLaundryApp.getInstance().setCurrentActivity(this);

        // Register FCM token for push notifications
        registerFCMToken();

        // Load default fragment or handle navigation intent
        if (savedInstanceState == null) {
            // Check if we have a navigation intent
            if (getIntent() != null && getIntent().hasExtra("navigate_to")) {
                String navigateTo = getIntent().getStringExtra("navigate_to");
                Log.d("MainActivity", "Navigation intent received: " + navigateTo);

                if ("orders".equals(navigateTo)) {
                    // Navigate to orders
                    navigateToOrders();
                    return;
                }
            }

            // Default navigation to home with app launch flag
            android.util.Log.d("MainActivity", "Creating HomeFragment with app launch flag");
            HomeFragment homeFragment = new HomeFragment();
            Bundle args = new Bundle();
            args.putBoolean("isAppLaunch", true);
            homeFragment.setArguments(args);
            android.util.Log.d("MainActivity", "Set arguments on HomeFragment: " + args);

            getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_container, homeFragment)
                    .commit();
            android.util.Log.d("MainActivity", "HomeFragment transaction committed");

            if (navigationView != null) {
                navigationView.setCheckedItem(R.id.nav_home);
            }

            if (bottomNavigationView != null) {
                bottomNavigationView.setSelectedItemId(R.id.nav_home);
            }

            if (toolbarTitle != null) {
                toolbarTitle.setText(getString(R.string.home));
            }

            // Schedule promo dialog to show after app launch
            schedulePromoDialog();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Set this activity as the current activity in the application
        GoGoLaundryApp.getInstance().setCurrentActivity(this);
    }

    @Override
    protected void onPause() {
        super.onPause();
        // Clear the current activity reference if it's this activity
        if (GoGoLaundryApp.getInstance().getCurrentActivity() == this) {
            GoGoLaundryApp.getInstance().setCurrentActivity(null);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // Handle notification permission result
        if (notificationPermissionManager != null) {
            notificationPermissionManager.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    private void initViews() {
        try {
            // Find toolbar views
            toolbar = findViewById(R.id.toolbar);

            // Log the toolbar status for debugging
            Log.d("MainActivity", "Toolbar found: " + (toolbar != null));

            // Find the included toolbar layout
            View customToolbarView = findViewById(R.id.custom_toolbar);

            // Find notification container and badge
            notificationContainer = findViewById(R.id.toolbar_notification_container);
            Log.d("MainActivity", "Notification container found: " + (notificationContainer != null));

            // If notification container is null, try to find it in the custom toolbar view
            if (notificationContainer == null && customToolbarView != null) {
                notificationContainer = customToolbarView.findViewById(R.id.toolbar_notification_container);
                Log.d("MainActivity", "Notification container found in custom toolbar: " + (notificationContainer != null));
            }

            if (toolbar != null) {
                // Try to find views directly in the toolbar
                toolbarProfileImage = toolbar.findViewById(R.id.toolbar_profile_image);
                toolbarTitle = toolbar.findViewById(R.id.toolbar_title);

                // Log the toolbar views status
                Log.d("MainActivity", "Toolbar profile image found directly in toolbar: " + (toolbarProfileImage != null));
                Log.d("MainActivity", "Toolbar title found directly in toolbar: " + (toolbarTitle != null));
            }

            // If toolbar views are still null, try to find them in the root view
            if (toolbarProfileImage == null) {
                toolbarProfileImage = findViewById(R.id.toolbar_profile_image);
                Log.d("MainActivity", "Toolbar profile image found in root view: " + (toolbarProfileImage != null));
            }

            if (toolbarTitle == null) {
                toolbarTitle = findViewById(R.id.toolbar_title);
                Log.d("MainActivity", "Toolbar title found in root view: " + (toolbarTitle != null));
            }

            // If still null and we have the custom toolbar view, try to find them there
            if (customToolbarView != null && (toolbarProfileImage == null || toolbarTitle == null)) {
                Log.d("MainActivity", "Custom toolbar view found, trying to find views there");

                if (toolbarProfileImage == null) {
                    toolbarProfileImage = customToolbarView.findViewById(R.id.toolbar_profile_image);
                    Log.d("MainActivity", "Toolbar profile image found in custom toolbar: " + (toolbarProfileImage != null));
                }

                if (toolbarTitle == null) {
                    toolbarTitle = customToolbarView.findViewById(R.id.toolbar_title);
                    Log.d("MainActivity", "Toolbar title found in custom toolbar: " + (toolbarTitle != null));
                }
            }

            // Find drawer layout and navigation view
            drawerLayout = findViewById(R.id.drawer_layout);
            navigationView = findViewById(R.id.nav_view);

            // Find bottom navigation view
            View bottomNavLayout = findViewById(R.id.bottom_navigation_layout);
            if (bottomNavLayout != null) {
                bottomNavigationView = bottomNavLayout.findViewById(R.id.bottom_navigation);
                Log.d("MainActivity", "Bottom navigation view found: " + (bottomNavigationView != null));
            } else {
                bottomNavigationView = findViewById(R.id.bottom_navigation);
                Log.d("MainActivity", "Bottom navigation view found in root: " + (bottomNavigationView != null));
            }

            // Get navigation header views if navigation view is available
            if (navigationView != null) {
                View headerView = navigationView.getHeaderView(0);
                if (headerView != null) {
                    navHeaderName = headerView.findViewById(R.id.nav_header_name);
                    navHeaderPhone = headerView.findViewById(R.id.nav_header_phone);
                    navHeaderEmail = headerView.findViewById(R.id.nav_header_email);
                    profileImage = headerView.findViewById(R.id.profile_image);
                    editProfileImage = headerView.findViewById(R.id.edit_profile_image);
                }
            }
        } catch (Exception e) {
            // Log any exceptions that occur during view initialization
            Log.e("MainActivity", "Error initializing views: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setupNavigationDrawer() {
        // Check if drawer layout and navigation view are available
        if (drawerLayout == null || navigationView == null) {
            return;
        }

        // We're not using the ActionBarDrawerToggle since we're using a custom profile image
        // Instead, we'll handle the drawer open/close manually

        // Set custom animations for the drawer
        drawerLayout.setDrawerShadow(R.drawable.drawer_shadow, GravityCompat.START);

        // Add drawer listener for animations
        drawerLayout.addDrawerListener(new DrawerLayout.DrawerListener() {
            @Override
            public void onDrawerSlide(@NonNull View drawerView, float slideOffset) {
                // Add subtle scale animation to main content
                View contentView = drawerLayout.getChildAt(0);
                float scaleFactor = 0.9f + (0.1f * (1 - slideOffset));
                contentView.setScaleX(scaleFactor);
                contentView.setScaleY(scaleFactor);
            }

            @Override
            public void onDrawerOpened(@NonNull View drawerView) {
                // Optional: Actions when drawer is opened
            }

            @Override
            public void onDrawerClosed(@NonNull View drawerView) {
                // Reset any animations or states
                View contentView = drawerLayout.getChildAt(0);
                contentView.setScaleX(1f);
                contentView.setScaleY(1f);
            }

            @Override
            public void onDrawerStateChanged(int newState) {
                // Optional: Handle state changes
            }
        });

        // Set up navigation item selection listener
        navigationView.setNavigationItemSelectedListener(this);
    }

    private void setupNavigationHeader() {
        // Check if navigation header views are available
        if (navHeaderName == null || navHeaderPhone == null || navHeaderEmail == null || profileImage == null) {
            return;
        }

        if (currentUser != null) {
            // Set user name and phone
            navHeaderName.setText(currentUser.getFullName());
            navHeaderPhone.setText(currentUser.getPhone());

            // Set email if available
            if (currentUser.getEmail() != null && !currentUser.getEmail().isEmpty()) {
                navHeaderEmail.setText(currentUser.getEmail());
            } else {
                navHeaderEmail.setVisibility(View.GONE);
            }

            // Set profile image if available
            if (currentUser.getProfilePictureUrl() != null && !currentUser.getProfilePictureUrl().isEmpty()) {
                String baseUrl = ApiClient.getBaseUrl();
                String imageUrl = baseUrl + "../" + currentUser.getProfilePictureUrl();

                // Load image into navigation header profile image
                Glide.with(this)
                        .load(imageUrl)
                        .placeholder(R.drawable.ic_person)
                        .error(R.drawable.ic_person)
                        .into(profileImage);
            }

            // Set up edit profile image click listener
            editProfileImage.setOnClickListener(v -> {
                // Navigate to profile fragment
                getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_container, new ProfileFragment())
                        .commit();
                navigationView.setCheckedItem(R.id.nav_profile);
                toolbarTitle.setText(getString(R.string.profile));
                drawerLayout.closeDrawer(GravityCompat.START);
            });
        }
    }

    /**
     * Set up the toolbar profile image with the user's profile picture
     * and configure it to open the navigation drawer when clicked
     */
    private void setupToolbarProfileImage() {
        // Check if toolbar profile image is available
        if (toolbarProfileImage == null) {
            Log.e("MainActivity", "Toolbar profile image is null, trying to find it again");

            // Try to find it again using different methods
            toolbarProfileImage = findViewById(R.id.toolbar_profile_image);

            if (toolbarProfileImage == null && toolbar != null) {
                toolbarProfileImage = toolbar.findViewById(R.id.toolbar_profile_image);
            }

            // If still null, try to find the custom toolbar and get the image from there
            if (toolbarProfileImage == null) {
                View customToolbarView = findViewById(R.id.custom_toolbar);
                if (customToolbarView != null) {
                    toolbarProfileImage = customToolbarView.findViewById(R.id.toolbar_profile_image);
                }
            }

            // If still null, we can't proceed
            if (toolbarProfileImage == null) {
                Log.e("MainActivity", "Could not find toolbar profile image, cannot set up profile image");
                return;
            } else {
                Log.d("MainActivity", "Successfully found toolbar profile image on retry");
            }
        }

        // Make sure the profile image is visible
        toolbarProfileImage.setVisibility(View.VISIBLE);

        // Set profile image if available
        if (currentUser != null && currentUser.getProfilePictureUrl() != null && !currentUser.getProfilePictureUrl().isEmpty()) {
            String baseUrl = ApiClient.getBaseUrl();
            String imageUrl = baseUrl + "../" + currentUser.getProfilePictureUrl();

            Log.d("MainActivity", "Loading profile image from URL: " + imageUrl);

            // Load image into toolbar profile image
            Glide.with(this)
                    .load(imageUrl)
                    .placeholder(R.drawable.ic_person)
                    .error(R.drawable.ic_person)
                    .into(toolbarProfileImage);
        } else {
            // Make sure we at least show the default icon
            toolbarProfileImage.setImageResource(R.drawable.ic_person);
            Log.d("MainActivity", "Using default profile image");
        }

        // Set click listener to open drawer
        toolbarProfileImage.setOnClickListener(v -> {
            Log.d("MainActivity", "Toolbar profile image clicked");
            if (drawerLayout != null) {
                if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
                    drawerLayout.closeDrawer(GravityCompat.START);
                } else {
                    drawerLayout.openDrawer(GravityCompat.START);
                }
            }
        });
    }

    /**
     * Set up the bottom navigation with animations and click listeners
     */
    private void setupBottomNavigation() {
        // Check if bottom navigation view is available
        if (bottomNavigationView == null) {
            Log.e("MainActivity", "Bottom navigation view is null, cannot set up bottom navigation");
            return;
        }

        // Apply custom animations and styling
        applyBottomNavAnimations();

        // Set up cart badge in bottom navigation
        setupBottomNavCartBadge();

        // Set up item selection listener
        bottomNavigationView.setOnNavigationItemSelectedListener(item -> {
            // Handle bottom navigation item selection
            Fragment selectedFragment = null;
            String title = "";

            int itemId = item.getItemId();
            if (itemId == R.id.nav_home) {
                selectedFragment = new HomeFragment();
                title = getString(R.string.home);
            } else if (itemId == R.id.nav_nearest_zone) {
                selectedFragment = new ShopMapFragment();
                title = getString(R.string.nearest_zone);
            } else if (itemId == R.id.nav_cart) {
                selectedFragment = new CartFragment();
                title = getString(R.string.cart);

            } else if (itemId == R.id.nav_profile) {
                selectedFragment = new ProfileFragment();
                title = getString(R.string.profile);
            }

            // Replace fragment
            if (selectedFragment != null) {
                // Clear the back stack when switching between main navigation items
                clearBackStack();

                getSupportFragmentManager().beginTransaction()
                        .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                        .replace(R.id.fragment_container, selectedFragment)
                        .commit();

                // Update toolbar title
                if (toolbarTitle != null) {
                    toolbarTitle.setText(title);
                }

                // Sync with navigation drawer
                if (navigationView != null) {
                    navigationView.setCheckedItem(itemId);
                }

                return true;
            }

            return false;
        });
    }

    /**
     * Apply custom animations to the bottom navigation
     */
    private void applyBottomNavAnimations() {
        // Add entrance animation for bottom navigation
        if (bottomNavigationView != null) {
            bottomNavigationView.setAlpha(0f);
            bottomNavigationView.setTranslationY(100f);

            bottomNavigationView.animate()
                    .alpha(1f)
                    .translationY(0f)
                    .setDuration(ANIMATION_DURATION)
                    .setInterpolator(new DecelerateInterpolator())
                    .start();
        }
    }

    /**
     * Set up cart badge in bottom navigation
     */
    private void setupBottomNavCartBadge() {
        if (bottomNavigationView == null) return;

        // Get the cart menu item view
        BottomNavigationItemView itemView = (BottomNavigationItemView) bottomNavigationView.findViewById(R.id.nav_cart);
        if (itemView == null) return;

        // Inflate the badge layout
        View badgeView = LayoutInflater.from(this).inflate(R.layout.bottom_nav_cart_item, itemView, true);

        // Get the badge text view
        bottomNavCartBadgeTextView = badgeView.findViewById(R.id.bottom_nav_cart_badge);

        // Update badge with current count
        Integer count = cartViewModel.getItemCount().getValue();
        updateBottomNavCartBadge(count != null ? count : 0);
    }

    /**
     * Update cart badge in bottom navigation
     *
     * @param count Number of items in cart
     */
    private void updateBottomNavCartBadge(int count) {
        if (bottomNavCartBadgeTextView != null) {
            if (count > 0) {
                bottomNavCartBadgeTextView.setVisibility(View.VISIBLE);
                bottomNavCartBadgeTextView.setText(String.valueOf(count));
            } else {
                bottomNavCartBadgeTextView.setVisibility(View.GONE);
            }
        }
    }

    /**
     * Update notification badge
     *
     * @param count Number of unread notifications
     */
    public void updateNotificationBadge(int count) {
        if (notificationBadgeTextView != null) {
            if (count > 0) {
                notificationBadgeTextView.setVisibility(View.VISIBLE);
                notificationBadgeTextView.setText(String.valueOf(count));
            } else {
                notificationBadgeTextView.setVisibility(View.GONE);
            }
        }
    }

    /**
     * Set up the notification icon with click listener and badge
     */
    private void setupNotificationIcon() {
        // Check if notification container is available
        if (notificationContainer == null) {
            Log.e("MainActivity", "Notification container is null, trying to find it again");

            // Try to find it again
            notificationContainer = findViewById(R.id.toolbar_notification_container);

            // Try to find it in the custom toolbar
            if (notificationContainer == null) {
                View customToolbarView = findViewById(R.id.custom_toolbar);
                if (customToolbarView != null) {
                    notificationContainer = customToolbarView.findViewById(R.id.toolbar_notification_container);
                }
            }

            // If still null, we can't proceed
            if (notificationContainer == null) {
                Log.e("MainActivity", "Could not find notification container, cannot set up notification icon");
                return;
            } else {
                Log.d("MainActivity", "Successfully found notification container on retry");
            }
        }

        // Set click listener for notification icon
        notificationContainer.setOnClickListener(v -> {
            Log.d("MainActivity", "Notification icon clicked");

            // Navigate to notification fragment
            getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_container, new NotificationFragment())
                    .addToBackStack(null)
                    .commit();

            // Update toolbar title
            if (toolbarTitle != null) {
                toolbarTitle.setText(getString(R.string.notifications));
            }
        });

        // Find notification badge
        View badgeLayout = notificationContainer.findViewById(R.id.toolbar_notification_badge);
        if (badgeLayout != null) {
            notificationBadgeTextView = badgeLayout.findViewById(R.id.notification_badge);

            // Update badge with current count
            if (currentUser != null) {
                Integer notificationCount = notificationViewModel.getUnreadCount().getValue();
                updateNotificationBadge(notificationCount != null ? notificationCount : 0);
            }
        }
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Handle navigation item selection
        Fragment selectedFragment = null;
        String title = "";

        int itemId = item.getItemId();
        if (itemId == R.id.nav_home) {
            selectedFragment = new HomeFragment();
            title = getString(R.string.home);
        } else if (itemId == R.id.nav_services) {
            selectedFragment = new ServicesFragment();
            title = getString(R.string.services);
        } else if (itemId == R.id.nav_nearest_zone) {
            selectedFragment = new ShopMapFragment();
            title = getString(R.string.nearest_zone);
        } else if (itemId == R.id.nav_orders) {
            // Navigate to orders using the existing method
            navigateToOrders();
            return true;
        } else if (itemId == R.id.nav_profile) {
            selectedFragment = new ProfileFragment();
            title = getString(R.string.profile);
        } else if (itemId == R.id.nav_cart) {
            selectedFragment = new CartFragment();
            title = getString(R.string.cart);
        } else if (itemId == R.id.nav_settings) {
            selectedFragment = new SettingsFragment();
            title = getString(R.string.settings);
        } else if (itemId == R.id.nav_logout) {
            // Logout user
            logout();
            return true;
        }
        // Replace fragment
        if (selectedFragment != null) {
            // Clear the back stack when switching between main navigation items
            clearBackStack();

            getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                    .replace(R.id.fragment_container, selectedFragment)
                    .commit();

            // Update toolbar title instead of activity title
            if (toolbarTitle != null) {
                toolbarTitle.setText(title);
            }

            // Sync with bottom navigation if it's one of the main navigation items
            if (bottomNavigationView != null &&
                (itemId == R.id.nav_home ||
                 itemId == R.id.nav_cart ||
                 itemId == R.id.nav_nearest_zone ||
                 itemId == R.id.nav_profile)) {
                bottomNavigationView.setSelectedItemId(itemId);
            }
        }

        // Close drawer with animation
        if (drawerLayout != null) {
            drawerLayout.closeDrawer(GravityCompat.START);
        }
        return true;
    }

    private void logout() {
        // Clear session
        sessionManager.logout();

        // Reset API client explicitly
        ApiClient.resetApiClient();

        // Redirect to login activity
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        // Check if drawer layout is available
        if (drawerLayout == null) {
            super.onBackPressed();
            return;
        }

        // Close drawer if open with animation
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            // Check if there are fragments in the back stack
            if (getSupportFragmentManager().getBackStackEntryCount() > 0) {
                // Pop the back stack to go back to the previous fragment
                getSupportFragmentManager().popBackStack();

                // Update UI based on the fragment that is now visible
                updateUIForCurrentFragment();
            } else {
                // If we're not on the home screen and there's nothing in the back stack, go to home
                Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);
                if (!(currentFragment instanceof HomeFragment)) {
                    // Navigate to home without app launch flag (this is internal navigation)
                    getSupportFragmentManager().beginTransaction()
                            .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                            .replace(R.id.fragment_container, new HomeFragment())
                            .commit();

                    if (navigationView != null) {
                        navigationView.setCheckedItem(R.id.nav_home);
                    }

                    // Update bottom navigation selection
                    if (bottomNavigationView != null) {
                        bottomNavigationView.setSelectedItemId(R.id.nav_home);
                    }

                    if (toolbarTitle != null) {
                        toolbarTitle.setText(getString(R.string.home));
                    }
                } else {
                    // If we're already on home screen, exit app
                    super.onBackPressed();
                }
            }
        }
    }

    /**
     * Update UI elements based on the current fragment
     */
    private void updateUIForCurrentFragment() {
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);
        if (currentFragment != null) {
            String title = "";
            int menuItemId = -1;

            if (currentFragment instanceof HomeFragment) {
                title = getString(R.string.home);
                menuItemId = R.id.nav_home;
            } else if (currentFragment instanceof ServicesFragment) {
                title = getString(R.string.services);
                menuItemId = R.id.nav_services;
            } else if (currentFragment instanceof ShopMapFragment) {
                title = getString(R.string.nearest_zone);
                menuItemId = R.id.nav_nearest_zone;
            } else if (currentFragment instanceof OrdersFragment) {
                title = getString(R.string.orders);
                menuItemId = R.id.nav_orders;
            } else if (currentFragment instanceof CartFragment) {
                title = getString(R.string.cart);
                menuItemId = R.id.nav_cart;
            } else if (currentFragment instanceof ProfileFragment) {
                title = getString(R.string.profile);
                menuItemId = R.id.nav_profile;
            } else if (currentFragment instanceof SettingsFragment) {
                title = getString(R.string.settings);
                menuItemId = R.id.nav_settings;
            } else if (currentFragment instanceof ItemsFragment) {
                // For ItemsFragment, we need to get the title from the fragment
                title = ((ItemsFragment) currentFragment).getServiceName();
                // Don't update bottom nav for ItemsFragment
                menuItemId = -1;
            }

            // Update toolbar title
            if (toolbarTitle != null && !title.isEmpty()) {
                toolbarTitle.setText(title);
            }

            // Update navigation view selection
            if (navigationView != null && menuItemId != -1) {
                navigationView.setCheckedItem(menuItemId);
            }

            // Update bottom navigation selection
            if (bottomNavigationView != null && menuItemId != -1 &&
                (menuItemId == R.id.nav_home ||
                 menuItemId == R.id.nav_cart ||
                 menuItemId == R.id.nav_nearest_zone ||
                 menuItemId == R.id.nav_profile)) {
                bottomNavigationView.setSelectedItemId(menuItemId);
            }
        }
    }

    /**
     * Clear the fragment back stack
     */
    private void clearBackStack() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        if (fragmentManager.getBackStackEntryCount() > 0) {
            FragmentManager.BackStackEntry first = fragmentManager.getBackStackEntryAt(0);
            fragmentManager.popBackStack(first.getId(), FragmentManager.POP_BACK_STACK_INCLUSIVE);
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // Notify current fragment about configuration change
        Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);
        if (currentFragment != null) {
            if (currentFragment instanceof OrdersFragment) {
                ((OrdersFragment) currentFragment).onConfigurationChanged(newConfig);
            }
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);

        // Get the cart menu item
        MenuItem cartItem = menu.findItem(R.id.action_cart);

        // Set up cart badge
        View actionView = cartItem.getActionView();
        cartBadgeTextView = actionView.findViewById(R.id.cart_badge);

        // Update badge with current cart count
        Integer count = cartViewModel.getItemCount().getValue();
        if (count != null && count > 0) {
            cartBadgeTextView.setVisibility(View.VISIBLE);
            cartBadgeTextView.setText(String.valueOf(count));
        } else {
            cartBadgeTextView.setVisibility(View.GONE);
        }

        // Set click listener for cart icon
        actionView.setOnClickListener(v -> {
            onOptionsItemSelected(cartItem);
        });

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int itemId = item.getItemId();

        if (itemId == R.id.action_cart) {
            // Navigate to cart fragment
            getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_container, new CartFragment())
                    .addToBackStack(null)
                    .commit();

            // Update title
            if (toolbarTitle != null) {
                toolbarTitle.setText(R.string.cart);
            }

            // Sync with bottom navigation
            if (bottomNavigationView != null) {
                bottomNavigationView.setSelectedItemId(R.id.nav_cart);
            }

            return true;
        }

        return super.onOptionsItemSelected(item);
    }



    /**
     * Navigate to the Orders fragment via drawer menu
     * This method is called from other fragments to navigate to the Orders screen
     */
    public void navigateToOrders() {
        try {
            // Create a new instance of OrdersFragment with highlight for newest order
            OrdersFragment ordersFragment = OrdersFragment.newInstanceWithHighlight();

            // Clear back stack to avoid navigation issues
            clearBackStack();

            // Replace current fragment with OrdersFragment
            getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                    .replace(R.id.fragment_container, ordersFragment)
                    .commit();

            // Update UI elements
            if (toolbarTitle != null) {
                toolbarTitle.setText(getString(R.string.orders));
            }

            // Update navigation drawer selection (Orders is still in drawer menu)
            if (navigationView != null) {
                navigationView.setCheckedItem(R.id.nav_orders);
            }

            // Log the navigation
            Log.d("MainActivity", "Successfully navigated to OrdersFragment");
        } catch (Exception e) {
            // Log any errors
            Log.e("MainActivity", "Error navigating to OrdersFragment: " + e.getMessage(), e);
            Toast.makeText(this, "Error navigating to Orders screen", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Update the bottom navigation to select a specific item
     * This method can be called from fragments to update the bottom navigation
     *
     * @param itemId The ID of the menu item to select
     */
    public void updateBottomNavigation(int itemId) {
        try {
            if (bottomNavigationView != null) {
                // Update bottom navigation selection
                bottomNavigationView.setSelectedItemId(itemId);
                Log.d("MainActivity", "Bottom navigation updated to item: " + itemId);
            } else {
                Log.w("MainActivity", "Bottom navigation view is null, cannot update selection");
            }

            // Also update toolbar title if needed
            if (toolbarTitle != null) {
                String title = "";

                if (itemId == R.id.nav_home) {
                    title = getString(R.string.home);
                } else if (itemId == R.id.nav_nearest_zone) {
                    title = getString(R.string.nearest_zone);
                } else if (itemId == R.id.nav_cart) {
                    title = getString(R.string.cart);
                } else if (itemId == R.id.nav_profile) {
                    title = getString(R.string.profile);
                }

                if (!title.isEmpty()) {
                    toolbarTitle.setText(title);
                }
            }

            // Update navigation drawer selection if applicable
            if (navigationView != null &&
                (itemId == R.id.nav_home ||
                 itemId == R.id.nav_services ||
                 itemId == R.id.nav_nearest_zone ||
                 itemId == R.id.nav_orders ||
                 itemId == R.id.nav_cart ||
                 itemId == R.id.nav_profile ||
                 itemId == R.id.nav_settings)) {
                navigationView.setCheckedItem(itemId);
            }
        } catch (Exception e) {
            Log.e("MainActivity", "Error updating bottom navigation: " + e.getMessage(), e);
        }
    }

    /**
     * Register FCM token for push notifications
     */
    private void registerFCMToken() {
        try {
            // First check and request notification permission
            notificationPermissionManager.checkAndRequestPermissionSilently(this,
                new NotificationPermissionManager.NotificationPermissionCallback() {
                    @Override
                    public void onPermissionGranted() {
                        Log.d("MainActivity", "Notification permission granted, registering FCM token");
                        // Use the GoGoLaundryApp instance to register FCM token
                        GoGoLaundryApp.getInstance().registerFCMToken();
                    }

                    @Override
                    public void onPermissionDenied() {
                        Log.d("MainActivity", "Notification permission denied, FCM token not registered");
                        // Still try to register FCM token for future use
                        GoGoLaundryApp.getInstance().registerFCMToken();
                    }

                    @Override
                    public void onPermissionPermanentlyDenied() {
                        Log.d("MainActivity", "Notification permission permanently denied");
                        // Still register FCM token but notifications won't work
                        GoGoLaundryApp.getInstance().registerFCMToken();
                    }
                });

            Log.d("MainActivity", "FCM token registration initiated");
        } catch (Exception e) {
            Log.e("MainActivity", "Error registering FCM token: " + e.getMessage(), e);
        }
    }

    /**
     * Show notification permission prompt (can be called from settings)
     */
    public void showNotificationPermissionPrompt() {
        if (notificationPermissionManager != null) {
            notificationPermissionManager.showSimplePermissionPrompt(this,
                new NotificationPermissionManager.NotificationPermissionCallback() {
                    @Override
                    public void onPermissionGranted() {
                        ToastUtils.showSuccess(MainActivity.this, "Notifications enabled! You'll now receive order updates.");
                        // Register FCM token now that permission is granted
                        GoGoLaundryApp.getInstance().registerFCMToken();
                    }

                    @Override
                    public void onPermissionDenied() {
                        ToastUtils.showInfo(MainActivity.this, "You can enable notifications later in Settings.");
                    }

                    @Override
                    public void onPermissionPermanentlyDenied() {
                        ToastUtils.showWarning(MainActivity.this, "Please enable notifications in app settings to receive order updates.");
                    }
                });
        }
    }

    /**
     * Check if notifications are enabled
     */
    public boolean areNotificationsEnabled() {
        return notificationPermissionManager != null && notificationPermissionManager.isNotificationPermissionGranted();
    }

    /**
     * Print document from Activity context
     * This method is called by fragments to print documents using the Activity context
     *
     * @param jobName Name of the print job
     * @param printAdapter PrintDocumentAdapter for the document to print
     */
    public void printDocument(String jobName, PrintDocumentAdapter printAdapter) {
        Log.d("MainActivity_Print", "=== PRINT METHOD CALLED ===");
        Log.d("MainActivity_Print", "printDocument() called with job: " + jobName);
        Log.d("MainActivity_Print", "PrintAdapter: " + (printAdapter != null ? printAdapter.getClass().getName() : "null"));
        Log.d("MainActivity_Print", "Current thread: " + Thread.currentThread().getName());
        Log.d("MainActivity_Print", "Is main thread: " + (Thread.currentThread() == getMainLooper().getThread()));

        // Try posting to the main thread with a delay to ensure Activity is fully ready
        Log.d("MainActivity_Print", "Posting print operation to main thread with delay");
        new android.os.Handler(getMainLooper()).post(() -> {
            Log.d("MainActivity_Print", "Executing print operation on main thread");
            executePrintOperationDirect(jobName, printAdapter);
        });
    }

    /**
     * Execute print operation directly from Activity without any context manipulation
     */
    private void executePrintOperationDirect(String jobName, PrintDocumentAdapter printAdapter) {
        Log.d("MainActivity_Print", "executePrintOperationDirect() called");
        Log.d("MainActivity_Print", "Thread: " + Thread.currentThread().getName());
        Log.d("MainActivity_Print", "Activity instance: " + this.toString());
        Log.d("MainActivity_Print", "Activity class: " + this.getClass().getName());

        try {
            // Use the original context stored in attachBaseContext (before any wrapping)
            Log.d("MainActivity_Print", "Using original context for printing");
            Log.d("MainActivity_Print", "Original context: " + (originalActivityContext != null ? originalActivityContext.getClass().getName() : "null"));
            Log.d("MainActivity_Print", "Original context instance: " + originalActivityContext);

            PrintManager printManager;
            if (originalActivityContext != null) {
                Log.d("MainActivity_Print", "Getting PrintManager from original context");
                printManager = (PrintManager) originalActivityContext.getSystemService(Context.PRINT_SERVICE);
            } else {
                Log.d("MainActivity_Print", "Original context is null, falling back to Activity");
                printManager = (PrintManager) getSystemService(PRINT_SERVICE);
            }

            Log.d("MainActivity_Print", "PrintManager obtained: " + (printManager != null));

            if (printManager == null) {
                Log.e("MainActivity_Print", "PrintManager is null");
                ToastUtils.showErrorToast(this, "Print service not available");
                return;
            }

            // Validate Activity state
            Log.d("MainActivity_Print", "Activity lifecycle state: " + getLifecycle().getCurrentState());
            Log.d("MainActivity_Print", "Activity isFinishing: " + isFinishing());
            Log.d("MainActivity_Print", "Activity isDestroyed: " + isDestroyed());

            if (isFinishing() || isDestroyed()) {
                Log.e("MainActivity_Print", "Activity is finishing or destroyed");
                ToastUtils.showErrorToast(this, "Cannot print: Activity is not active");
                return;
            }

            // Try the print operation directly
            Log.d("MainActivity_Print", "Calling printManager.print() directly from Activity");
            Log.d("MainActivity_Print", "PrintManager class: " + printManager.getClass().getName());
            Log.d("MainActivity_Print", "Job name: " + jobName);
            Log.d("MainActivity_Print", "Adapter class: " + printAdapter.getClass().getName());

            printManager.print(jobName, printAdapter, null);
            Log.d("MainActivity_Print", "Print operation completed successfully!");

            // Show success message
            ToastUtils.showInfoToast(this, "Preparing document for printing...");
            Log.d("MainActivity_Print", "=== PRINT METHOD COMPLETED ===");

        } catch (IllegalStateException e) {
            Log.e("MainActivity_Print", "IllegalStateException - trying alternative approach", e);

            // Try one more approach - check if this is related to the language context wrapping
            tryAlternativePrintApproach(jobName, printAdapter);

        } catch (Exception e) {
            Log.e("MainActivity_Print", "Exception in executePrintOperationDirect", e);
            Log.e("MainActivity_Print", "Exception class: " + e.getClass().getName());
            Log.e("MainActivity_Print", "Exception message: " + e.getMessage());
            ToastUtils.showErrorToast(this, "Error starting print job: " + e.getMessage());
        }
    }

    /**
     * Try alternative print approach by temporarily bypassing language context
     */
    private void tryAlternativePrintApproach(String jobName, PrintDocumentAdapter printAdapter) {
        Log.d("MainActivity_Print", "tryAlternativePrintApproach() called");

        try {
            // Get the application context and see if we can work around the issue
            Context appContext = getApplicationContext();
            Log.d("MainActivity_Print", "Application context: " + appContext.getClass().getName());

            // Try to get the base context directly
            Context baseContext = getBaseContext();
            Log.d("MainActivity_Print", "Base context: " + baseContext.getClass().getName());

            // Try creating a new Activity context without language wrapping
            Log.d("MainActivity_Print", "Attempting to create clean Activity context");

            // This is a last resort - try to use reflection to get the original Activity
            try {
                java.lang.reflect.Field mBaseField = android.content.ContextWrapper.class.getDeclaredField("mBase");
                mBaseField.setAccessible(true);
                Context originalBase = (Context) mBaseField.get(this);
                Log.d("MainActivity_Print", "Original base via reflection: " + originalBase.getClass().getName());

                if (originalBase instanceof android.app.Activity) {
                    Log.d("MainActivity_Print", "Found original Activity via reflection, trying print");
                    PrintManager reflectionPrintManager = (PrintManager) originalBase.getSystemService(Context.PRINT_SERVICE);
                    if (reflectionPrintManager != null) {
                        reflectionPrintManager.print(jobName, printAdapter, null);
                        Log.d("MainActivity_Print", "Reflection approach succeeded!");
                        ToastUtils.showInfoToast(this, "Preparing document for printing...");
                        return;
                    }
                }
            } catch (Exception reflectionException) {
                Log.e("MainActivity_Print", "Reflection approach failed", reflectionException);
            }

            // If all else fails, show error
            ToastUtils.showErrorToast(this, "Print functionality is not available on this device");

        } catch (Exception e) {
            Log.e("MainActivity_Print", "Alternative approach failed", e);
            ToastUtils.showErrorToast(this, "Print functionality is not available: " + e.getMessage());
        }
    }

    /**
     * Execute print operation directly without lambdas to avoid context capture issues
     */
    private void executePrintOperation(String jobName, PrintDocumentAdapter printAdapter) {
        Log.d("MainActivity_Print", "executePrintOperation() called");
        Log.d("MainActivity_Print", "Thread: " + Thread.currentThread().getName());

        try {
            // The issue is that LanguageManager.applyLanguage() creates a wrapped context
            // We need to get the original Activity context for printing
            Log.d("MainActivity_Print", "Getting original Activity context for printing");

            // Get the base context to bypass any language wrapping
            Context originalContext = this;
            while (originalContext instanceof android.content.ContextWrapper) {
                Context baseContext = ((android.content.ContextWrapper) originalContext).getBaseContext();
                if (baseContext instanceof android.app.Activity) {
                    originalContext = baseContext;
                    break;
                } else if (baseContext == originalContext) {
                    // Avoid infinite loop
                    break;
                } else {
                    originalContext = baseContext;
                }
            }

            Log.d("MainActivity_Print", "Original context class: " + originalContext.getClass().getName());
            Log.d("MainActivity_Print", "Original context: " + originalContext.toString());

            // Try using the original unwrapped context
            PrintManager printManager;
            if (originalContext instanceof android.app.Activity) {
                Log.d("MainActivity_Print", "Using unwrapped Activity context");
                printManager = (PrintManager) originalContext.getSystemService(Context.PRINT_SERVICE);
            } else {
                Log.d("MainActivity_Print", "Fallback to MainActivity.this context");
                // Fallback to using this Activity directly
                printManager = (PrintManager) MainActivity.this.getSystemService(Context.PRINT_SERVICE);
                originalContext = MainActivity.this;
            }

            Log.d("MainActivity_Print", "PrintManager obtained: " + (printManager != null));
            Log.d("MainActivity_Print", "Activity class: " + this.getClass().getName());
            Log.d("MainActivity_Print", "Using context: " + originalContext.toString());
            Log.d("MainActivity_Print", "Activity lifecycle state: " + getLifecycle().getCurrentState());

            if (printManager == null) {
                Log.e("MainActivity_Print", "PrintManager is null");
                ToastUtils.showErrorToast(this, "Print service not available");
                return;
            }

            // Additional validation
            Log.d("MainActivity_Print", "Activity isFinishing: " + isFinishing());
            Log.d("MainActivity_Print", "Activity isDestroyed: " + isDestroyed());

            if (isFinishing() || isDestroyed()) {
                Log.e("MainActivity_Print", "Activity is finishing or destroyed");
                ToastUtils.showErrorToast(this, "Cannot print: Activity is not active");
                return;
            }

            // Try direct print operation without any delays or handlers
            Log.d("MainActivity_Print", "About to call printManager.print() directly");
            Log.d("MainActivity_Print", "PrintManager class: " + printManager.getClass().getName());
            Log.d("MainActivity_Print", "Using 'this' reference: " + this);
            Log.d("MainActivity_Print", "MainActivity.this reference: " + MainActivity.this);

            // Try the print operation with the unwrapped context
            if (printManager != null) {
                Log.d("MainActivity_Print", "Testing with simple adapter using unwrapped context");
                try {
                    android.print.PrintDocumentAdapter testAdapter = new android.print.PrintDocumentAdapter() {
                        @Override
                        public void onLayout(android.print.PrintAttributes oldAttributes,
                                           android.print.PrintAttributes newAttributes,
                                           android.os.CancellationSignal cancellationSignal,
                                           android.print.PrintDocumentAdapter.LayoutResultCallback callback,
                                           android.os.Bundle extras) {
                            Log.d("MainActivity_Print", "Test adapter onLayout called");
                            callback.onLayoutFinished(new android.print.PrintDocumentInfo.Builder("test").build(), false);
                        }

                        @Override
                        public void onWrite(android.print.PageRange[] pages,
                                          android.os.ParcelFileDescriptor destination,
                                          android.os.CancellationSignal cancellationSignal,
                                          android.print.PrintDocumentAdapter.WriteResultCallback callback) {
                            Log.d("MainActivity_Print", "Test adapter onWrite called");
                            callback.onWriteFinished(new android.print.PageRange[]{android.print.PageRange.ALL_PAGES});
                        }
                    };

                    Log.d("MainActivity_Print", "Calling print with test adapter and unwrapped context");
                    printManager.print("Test Print", testAdapter, null);
                    Log.d("MainActivity_Print", "Test print operation completed successfully with unwrapped context!");

                    // If test adapter works, try with our custom adapter
                    Log.d("MainActivity_Print", "Test successful, now trying with custom adapter");

                    // Use the original adapter (it already has the correct context from InvoiceFragment)
                    // The key is using the unwrapped context for PrintManager, not changing the adapter
                    printManager.print(jobName, printAdapter, null);
                    Log.d("MainActivity_Print", "Custom adapter print operation completed successfully with unwrapped context!");

                } catch (Exception testException) {
                    Log.e("MainActivity_Print", "Test with unwrapped context failed", testException);

                    // Try with our original adapter anyway
                    Log.d("MainActivity_Print", "Trying with original adapter despite test failure");
                    printManager.print(jobName, printAdapter, null);
                    Log.d("MainActivity_Print", "Original adapter print operation completed");
                }

                // Show success message
                ToastUtils.showInfoToast(this, "Preparing document for printing...");
                Log.d("MainActivity_Print", "=== PRINT METHOD COMPLETED ===");
            } else {
                Log.e("MainActivity_Print", "PrintManager is null");
                ToastUtils.showErrorToast(this, "Print service not available");
            }

        } catch (Exception e) {
            Log.e("MainActivity_Print", "Exception in executePrintOperation", e);
            Log.e("MainActivity_Print", "Exception class: " + e.getClass().getName());
            Log.e("MainActivity_Print", "Exception message: " + e.getMessage());
            ToastUtils.showErrorToast(this, "Error starting print job: " + e.getMessage());
        }
    }

    /**
     * Schedule promotional dialog to show after app launch
     * First checks if there's an active promotional dialog from the API
     */
    private void schedulePromoDialog() {
        if (promoDialogScheduled) {
            Log.d("MainActivity", "Promo dialog already scheduled - skipping");
            return;
        }

        Log.d("MainActivity", "Scheduling promo dialog from MainActivity");
        promoDialogScheduled = true;

        // Schedule dialog to show after 3 seconds
        promoHandler.postDelayed(() -> {
            try {
                Log.d("MainActivity", "=== PROMO DIALOG TASK EXECUTING FROM MAINACTIVITY ===");

                // Find current fragment
                Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.fragment_container);
                Log.d("MainActivity", "Current fragment: " + (currentFragment != null ? currentFragment.getClass().getSimpleName() : "null"));

                if (currentFragment instanceof HomeFragment) {
                    Log.d("MainActivity", "Found HomeFragment, checking for active promo dialog");
                    checkAndShowPromoDialog();
                } else {
                    Log.w("MainActivity", "Current fragment is not HomeFragment: " +
                        (currentFragment != null ? currentFragment.getClass().getSimpleName() : "null"));
                    promoDialogScheduled = false;
                }
            } catch (Exception e) {
                Log.e("MainActivity", "Error checking promo dialog from MainActivity: " + e.getMessage(), e);
                e.printStackTrace();
                promoDialogScheduled = false;
            }
        }, 3000); // 3 seconds delay

        Log.d("MainActivity", "Promo dialog task posted to MainActivity handler");
    }

    /**
     * Check if there's an active promotional dialog and show it
     */
    private void checkAndShowPromoDialog() {
        Log.d("MainActivity", "Checking for active promotional dialog from API");

        try {
            PromoDialogApiService apiService = ApiClient.getRetrofitInstance().create(PromoDialogApiService.class);
            Call<PromoDialogResponse> call = apiService.getActivePromoDialog();

            call.enqueue(new Callback<PromoDialogResponse>() {
                @Override
                public void onResponse(Call<PromoDialogResponse> call, Response<PromoDialogResponse> response) {
                    try {
                        if (response.isSuccessful() && response.body() != null) {
                            PromoDialogResponse promoResponse = response.body();
                            Log.d("MainActivity", "API Response - Success: " + promoResponse.isSuccess() +
                                ", Has Dialog: " + promoResponse.isHasDialog());

                            if (promoResponse.isSuccess() && promoResponse.isHasDialog() && promoResponse.getDialog() != null) {
                                Log.d("MainActivity", "Active promotional dialog found, showing dialog");

                                // Create and show promo dialog with dynamic data
                                PromoDialog promoDialog = PromoDialog.newInstance(promoResponse.getDialog());
                                promoDialog.show(getSupportFragmentManager(), "PromoDialog");

                                // Log dialog view
                                logPromoDialogView(promoResponse.getDialog().getId());

                                Log.d("MainActivity", "PromoDialog.show() completed from MainActivity");
                            } else {
                                Log.d("MainActivity", "No active promotional dialog found - dialog will not be shown");
                            }
                        } else {
                            Log.w("MainActivity", "API response unsuccessful or empty: " + response.code());
                        }
                    } catch (Exception e) {
                        Log.e("MainActivity", "Error processing promo dialog response: " + e.getMessage(), e);
                    } finally {
                        promoDialogScheduled = false;
                        Log.d("MainActivity", "=== PROMO DIALOG TASK COMPLETED FROM MAINACTIVITY ===");
                    }
                }

                @Override
                public void onFailure(Call<PromoDialogResponse> call, Throwable t) {
                    Log.e("MainActivity", "Failed to fetch promotional dialog: " + t.getMessage(), t);
                    promoDialogScheduled = false;
                    Log.d("MainActivity", "=== PROMO DIALOG TASK COMPLETED (FAILED) FROM MAINACTIVITY ===");
                }
            });

        } catch (Exception e) {
            Log.e("MainActivity", "Error creating API call for promo dialog: " + e.getMessage(), e);
            promoDialogScheduled = false;
        }
    }

    /**
     * Log promotional dialog view for analytics
     */
    private void logPromoDialogView(int dialogId) {
        try {
            PromoDialogApiService apiService = ApiClient.getRetrofitInstance().create(PromoDialogApiService.class);

            Integer userId = null;
            if (currentUser != null) {
                userId = currentUser.getId();
            }

            PromoDialogApiService.PromoDialogInteractionRequest request =
                new PromoDialogApiService.PromoDialogInteractionRequest("view", dialogId, userId);

            Call<PromoDialogResponse> call = apiService.logPromoDialogInteraction(request);
            call.enqueue(new Callback<PromoDialogResponse>() {
                @Override
                public void onResponse(Call<PromoDialogResponse> call, Response<PromoDialogResponse> response) {
                    if (response.isSuccessful()) {
                        Log.d("MainActivity", "Promo dialog view logged successfully");
                    } else {
                        Log.w("MainActivity", "Failed to log promo dialog view: " + response.code());
                    }
                }

                @Override
                public void onFailure(Call<PromoDialogResponse> call, Throwable t) {
                    Log.w("MainActivity", "Failed to log promo dialog view: " + t.getMessage());
                }
            });

        } catch (Exception e) {
            Log.w("MainActivity", "Error logging promo dialog view: " + e.getMessage());
        }
    }
}