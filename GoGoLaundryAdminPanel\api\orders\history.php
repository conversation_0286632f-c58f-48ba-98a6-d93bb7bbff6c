<?php
/**
 * Order Status History API Endpoint
 *
 * This endpoint returns the status history for a specific order
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/OrderManager.php';

// Initialize managers
$orderManager = new OrderManager($pdo);

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get order ID from request
$orderId = isset($_GET['order_id']) ? (int)$_GET['order_id'] : null;

// Validate required parameters
if (!$orderId) {
    jsonResponse(false, 'Missing required parameter: order_id', [], 400);
}

// Get order status history
$statusHistory = $orderManager->getOrderStatusHistory($orderId);

// Check if order exists
if ($statusHistory === false) {
    jsonResponse(false, 'Order not found', [], 404);
}

// Return success response
jsonResponse(true, 'Order status history retrieved successfully', $statusHistory);
