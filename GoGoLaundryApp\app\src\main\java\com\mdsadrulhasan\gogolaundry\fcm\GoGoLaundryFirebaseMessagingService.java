package com.mdsadrulhasan.gogolaundry.fcm;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.os.AsyncTask;
import android.text.TextUtils;
import android.util.Log;

import androidx.core.app.NotificationCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.mdsadrulhasan.gogolaundry.MainActivity;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.fcm.FCMTokenRequest;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Firebase Cloud Messaging Service for GoGoLaundry
 * Handles FCM token registration and message reception
 */
public class GoGoLaundryFirebaseMessagingService extends FirebaseMessagingService {
    private static final String TAG = "FCMService";
    private static final String CHANNEL_ID = "gogolaundry_notifications";
    private static final String CHANNEL_NAME = "GoGoLaundry Notifications";
    private static final String CHANNEL_DESCRIPTION = "Notifications for order updates and promotions";

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
    }

    /**
     * Called when a new FCM token is generated
     */
    @Override
    public void onNewToken(String token) {
        Log.d(TAG, "Refreshed token: " + token);

        // Send token to server
        sendTokenToServer(token);

        // Store token locally
        SessionManager sessionManager = new SessionManager(this);
        sessionManager.saveFCMToken(token);
    }

    /**
     * Called when a message is received
     */
    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        Log.d(TAG, "=== FCM MESSAGE RECEIVED ===");
        Log.d(TAG, "From: " + remoteMessage.getFrom());
        Log.d(TAG, "Message ID: " + remoteMessage.getMessageId());
        Log.d(TAG, "Message Type: " + remoteMessage.getMessageType());
        Log.d(TAG, "Sent Time: " + remoteMessage.getSentTime());
        Log.d(TAG, "TTL: " + remoteMessage.getTtl());

        // Log all data
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());
            for (Map.Entry<String, String> entry : remoteMessage.getData().entrySet()) {
                Log.d(TAG, "Data key: " + entry.getKey() + ", value: " + entry.getValue());
            }
            handleDataMessage(remoteMessage.getData());
        } else {
            Log.d(TAG, "No data payload found");
        }

        // Log notification payload
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Notification Title: " + remoteMessage.getNotification().getTitle());
            Log.d(TAG, "Notification Body: " + remoteMessage.getNotification().getBody());
            Log.d(TAG, "Notification Icon: " + remoteMessage.getNotification().getIcon());
            Log.d(TAG, "Notification Sound: " + remoteMessage.getNotification().getSound());
            Log.d(TAG, "Notification Tag: " + remoteMessage.getNotification().getTag());
            Log.d(TAG, "Notification Color: " + remoteMessage.getNotification().getColor());
            Log.d(TAG, "Notification Click Action: " + remoteMessage.getNotification().getClickAction());

            showNotification(
                remoteMessage.getNotification().getTitle(),
                remoteMessage.getNotification().getBody(),
                remoteMessage.getData()
            );
        } else {
            Log.d(TAG, "No notification payload found");

            // If no notification payload but has data, create notification from data
            if (remoteMessage.getData().size() > 0) {
                String title = remoteMessage.getData().get("title");
                String message = remoteMessage.getData().get("message");
                if (title != null && message != null) {
                    Log.d(TAG, "Creating notification from data payload");
                    showNotification(title, message, remoteMessage.getData());
                }
            }
        }

        // Create notification in database for NotificationFragment
        createNotificationInDatabase(remoteMessage);

        // Broadcast notification received event
        Intent intent = new Intent("FCM_MESSAGE_RECEIVED");
        intent.putExtra("title", remoteMessage.getNotification() != null ?
            remoteMessage.getNotification().getTitle() :
            remoteMessage.getData().get("title"));
        intent.putExtra("message", remoteMessage.getNotification() != null ?
            remoteMessage.getNotification().getBody() :
            remoteMessage.getData().get("message"));
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);

        Log.d(TAG, "=== FCM MESSAGE PROCESSING COMPLETE ===");
    }

    /**
     * Handle data message
     */
    private void handleDataMessage(Map<String, String> data) {
        String title = data.get("title");
        String message = data.get("message");
        String type = data.get("type");
        String orderId = data.get("order_id");

        Log.d(TAG, "Handling data message - Type: " + type + ", Order ID: " + orderId);

        // NOTE: Do NOT show notification here - it will be shown in onMessageReceived
        // This method only handles specific notification type actions

        // Handle specific notification types
        switch (type != null ? type : "") {
            case "order_status":
                // Handle order status update
                handleOrderStatusUpdate(orderId);
                break;
            case "promo":
                // Handle promotion notification
                handlePromoNotification(data);
                break;
            case "system":
                // Handle system notification
                handleSystemNotification(data);
                break;
            default:
                // Handle custom notification
                handleCustomNotification(data);
                break;
        }
    }

    /**
     * Show notification in the notification tray
     */
    private void showNotification(String title, String message, Map<String, String> data) {
        Log.d(TAG, "=== SHOWING NOTIFICATION ===");
        Log.d(TAG, "Title: " + title);
        Log.d(TAG, "Message: " + message);
        Log.d(TAG, "Data: " + (data != null ? data.toString() : "null"));

        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        // Add notification data to intent
        if (data != null) {
            for (Map.Entry<String, String> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue());
            }
        }

        PendingIntent pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE
        );

        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

        // Handle image if provided
        String imageUrl = data != null ? data.get("image_url") : null;
        if (!TextUtils.isEmpty(imageUrl)) {
            // Convert relative URL to absolute URL if needed
            String fullImageUrl = convertToAbsoluteUrl(imageUrl);
            Log.d(TAG, "Loading image from URL: " + fullImageUrl);
            loadImageAndShowNotification(notificationBuilder, title, message, fullImageUrl, data);
            return;
        } else {
            // Use BigTextStyle for text-only notifications
            notificationBuilder.setStyle(new NotificationCompat.BigTextStyle().bigText(message));
        }

        // Set notification icon based on type
        String type = data != null ? data.get("type") : null;
        if (type != null) {
            switch (type) {
                case "order_status":
                    notificationBuilder.setSmallIcon(R.drawable.ic_receipt);
                    break;
                case "promo":
                    notificationBuilder.setSmallIcon(R.drawable.ic_add_shopping_cart);
                    break;
                case "system":
                    notificationBuilder.setSmallIcon(R.drawable.ic_info);
                    break;
                default:
                    notificationBuilder.setSmallIcon(R.drawable.ic_notification);
                    break;
            }
        }

        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Generate unique notification ID using hash of title + message + timestamp
        String uniqueString = title + message + System.currentTimeMillis();
        int notificationId = Math.abs(uniqueString.hashCode());
        notificationManager.notify(notificationId, notificationBuilder.build());

        Log.d(TAG, "Notification displayed with ID: " + notificationId);
        Log.d(TAG, "=== NOTIFICATION DISPLAY COMPLETE ===");
    }

    /**
     * Convert relative URL to absolute URL if needed
     */
    private String convertToAbsoluteUrl(String imageUrl) {
        if (TextUtils.isEmpty(imageUrl)) {
            return imageUrl;
        }

        // If already absolute URL, return as is
        if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
            return imageUrl;
        }

        // Get base URL from ApiClient and remove '/api/' suffix
        String apiBaseUrl = ApiClient.getBaseUrl();
        String baseUrl = apiBaseUrl.replace("/api/", "");

        Log.d(TAG, "Converting relative URL: " + imageUrl + " using base: " + baseUrl);

        // Remove leading slash if present to avoid double slashes
        if (imageUrl.startsWith("/")) {
            return baseUrl + imageUrl;
        } else {
            return baseUrl + "/" + imageUrl;
        }
    }

    /**
     * Load image from URL and show notification with image
     */
    private void loadImageAndShowNotification(NotificationCompat.Builder notificationBuilder,
                                            String title, String message, String imageUrl,
                                            Map<String, String> data) {
        new AsyncTask<String, Void, Bitmap>() {
            @Override
            protected Bitmap doInBackground(String... urls) {
                String imageUrl = urls[0];
                Log.d(TAG, "Attempting to load image from: " + imageUrl);

                try {
                    URL url = new URL(imageUrl);
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.setDoInput(true);
                    connection.setConnectTimeout(10000); // 10 seconds timeout
                    connection.setReadTimeout(10000); // 10 seconds timeout
                    connection.connect();

                    int responseCode = connection.getResponseCode();
                    Log.d(TAG, "HTTP response code: " + responseCode);

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        InputStream input = connection.getInputStream();
                        Bitmap bitmap = BitmapFactory.decodeStream(input);
                        input.close();
                        connection.disconnect();

                        if (bitmap != null) {
                            Log.d(TAG, "Image loaded successfully. Original size: " + bitmap.getWidth() + "x" + bitmap.getHeight());

                            // Resize bitmap if too large (max 1024x1024)
                            int maxSize = 1024;
                            int width = bitmap.getWidth();
                            int height = bitmap.getHeight();

                            if (width > maxSize || height > maxSize) {
                                float ratio = Math.min((float) maxSize / width, (float) maxSize / height);
                                int newWidth = Math.round(width * ratio);
                                int newHeight = Math.round(height * ratio);
                                bitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true);
                                Log.d(TAG, "Image resized to: " + newWidth + "x" + newHeight);
                            }
                        } else {
                            Log.e(TAG, "Failed to decode bitmap from stream");
                        }

                        return bitmap;
                    } else {
                        Log.e(TAG, "HTTP error response: " + responseCode + " for URL: " + imageUrl);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error loading image from URL: " + imageUrl + " - " + e.getMessage());
                    e.printStackTrace();
                }
                return null;
            }

            @Override
            protected void onPostExecute(Bitmap bitmap) {
                if (bitmap != null) {
                    Log.d(TAG, "Image loaded successfully, showing notification with image");

                    // Use BigPictureStyle for image notifications
                    NotificationCompat.BigPictureStyle bigPictureStyle = new NotificationCompat.BigPictureStyle()
                            .bigPicture(bitmap)
                            .setBigContentTitle(title)
                            .setSummaryText(message);

                    notificationBuilder.setStyle(bigPictureStyle);
                    notificationBuilder.setLargeIcon(bitmap);
                } else {
                    Log.w(TAG, "Failed to load image, falling back to text notification");
                    // Fallback to BigTextStyle if image loading fails
                    notificationBuilder.setStyle(new NotificationCompat.BigTextStyle().bigText(message));
                }

                // Set notification icon based on type
                String type = data != null ? data.get("type") : null;
                if (type != null) {
                    switch (type) {
                        case "order_status":
                            notificationBuilder.setSmallIcon(R.drawable.ic_receipt);
                            break;
                        case "promo":
                            notificationBuilder.setSmallIcon(R.drawable.ic_add_shopping_cart);
                            break;
                        case "system":
                            notificationBuilder.setSmallIcon(R.drawable.ic_info);
                            break;
                        default:
                            notificationBuilder.setSmallIcon(R.drawable.ic_notification);
                            break;
                    }
                }

                NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

                // Generate unique notification ID using hash of title + message + timestamp
                String uniqueString = title + message + System.currentTimeMillis();
                int notificationId = Math.abs(uniqueString.hashCode());
                notificationManager.notify(notificationId, notificationBuilder.build());

                Log.d(TAG, "Notification with image displayed with ID: " + notificationId);
            }
        }.execute(imageUrl);
    }

    /**
     * Create notification channel for Android O and above
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableLights(true);
            channel.enableVibration(true);
            channel.setLightColor(getResources().getColor(R.color.colorPrimary));

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * Send FCM token to server
     */
    private void sendTokenToServer(String token) {
        SessionManager sessionManager = new SessionManager(this);

        if (!sessionManager.isLoggedIn()) {
            Log.d(TAG, "User not logged in, skipping token registration");
            return;
        }

        int userId = sessionManager.getUserId();
        String deviceId = sessionManager.getDeviceId();

        ApiService apiService = ApiClient.getClient(this).create(ApiService.class);

        // Create FCM token registration request
        FCMTokenRequest request = new FCMTokenRequest(userId, token, deviceId, "android");

        Call<Object> call = apiService.registerFCMToken(request);
        call.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if (response.isSuccessful()) {
                    Log.d(TAG, "FCM token registered successfully");
                } else {
                    Log.e(TAG, "Failed to register FCM token: " + response.message());
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Log.e(TAG, "Error registering FCM token: " + t.getMessage());
            }
        });
    }

    /**
     * Handle order status update notification
     */
    private void handleOrderStatusUpdate(String orderId) {
        // Broadcast order status update
        Intent intent = new Intent("ORDER_STATUS_UPDATED");
        intent.putExtra("order_id", orderId);
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    /**
     * Handle promotion notification
     */
    private void handlePromoNotification(Map<String, String> data) {
        // Broadcast promotion notification
        Intent intent = new Intent("PROMO_NOTIFICATION_RECEIVED");
        for (Map.Entry<String, String> entry : data.entrySet()) {
            intent.putExtra(entry.getKey(), entry.getValue());
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    /**
     * Handle system notification
     */
    private void handleSystemNotification(Map<String, String> data) {
        // Broadcast system notification
        Intent intent = new Intent("SYSTEM_NOTIFICATION_RECEIVED");
        for (Map.Entry<String, String> entry : data.entrySet()) {
            intent.putExtra(entry.getKey(), entry.getValue());
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    /**
     * Handle custom notification
     */
    private void handleCustomNotification(Map<String, String> data) {
        // Broadcast custom notification
        Intent intent = new Intent("CUSTOM_NOTIFICATION_RECEIVED");
        for (Map.Entry<String, String> entry : data.entrySet()) {
            intent.putExtra(entry.getKey(), entry.getValue());
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    // Static set to track processed notifications and prevent duplicates
    private static final java.util.Set<String> processedNotifications = new java.util.HashSet<>();

    /**
     * Create notification in database so it appears in NotificationFragment
     */
    private void createNotificationInDatabase(RemoteMessage remoteMessage) {
        SessionManager sessionManager = new SessionManager(this);

        if (!sessionManager.isLoggedIn()) {
            Log.d(TAG, "User not logged in, skipping database notification creation");
            return;
        }

        int userId = sessionManager.getUserId();
        Map<String, String> data = remoteMessage.getData();

        // Extract notification details
        String title = remoteMessage.getNotification() != null ?
            remoteMessage.getNotification().getTitle() : data.get("title");
        String message = remoteMessage.getNotification() != null ?
            remoteMessage.getNotification().getBody() : data.get("message");
        String type = data.get("type");
        String orderIdStr = data.get("order_id");
        String imageUrl = data.get("image_url");

        // Create unique key to prevent duplicates
        String uniqueKey = userId + "_" + title + "_" + message + "_" + type;

        // Check if we've already processed this notification
        synchronized (processedNotifications) {
            if (processedNotifications.contains(uniqueKey)) {
                Log.d(TAG, "Notification already processed, skipping: " + uniqueKey);
                return;
            }
            // Add to processed set
            processedNotifications.add(uniqueKey);
        }

        // Parse order ID
        Integer orderId = null;
        if (orderIdStr != null && !orderIdStr.trim().isEmpty()) {
            try {
                orderId = Integer.parseInt(orderIdStr);
            } catch (NumberFormatException e) {
                Log.w(TAG, "Invalid order_id format: " + orderIdStr);
            }
        }

        // Convert relative image URL to absolute URL if needed
        String fullImageUrl = null;
        if (imageUrl != null && !imageUrl.trim().isEmpty()) {
            fullImageUrl = convertToAbsoluteUrl(imageUrl);
            Log.d(TAG, "Original image URL: " + imageUrl);
            Log.d(TAG, "Full image URL: " + fullImageUrl);
        }

        Log.d(TAG, "Creating notification in database - Title: " + title + ", Type: " + type + ", Image: " + fullImageUrl);

        // Create API service
        ApiService apiService = ApiClient.getClient(this).create(ApiService.class);

        // Generate a unique notification ID based on timestamp and content hash
        // Use a more stable ID to prevent duplicates
        String uniqueContent = title + message + type + (orderId != null ? orderId.toString() : "");
        int notificationId = Math.abs(uniqueContent.hashCode());

        Log.d(TAG, "Generated notification ID: " + notificationId);

        // Create notification in database
        Call<Object> call = apiService.createNotificationIfNotExists(
            notificationId,
            userId,
            title != null ? title : "Notification",
            message != null ? message : "",
            type != null ? type : "custom",
            0, // is_read = 0 (unread)
            orderId,
            fullImageUrl // Include full image URL
        );

        call.enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                if (response.isSuccessful()) {
                    Log.d(TAG, "Notification created in database successfully");
                } else {
                    Log.e(TAG, "Failed to create notification in database: " + response.message());
                    // Remove from processed set if failed to allow retry
                    synchronized (processedNotifications) {
                        processedNotifications.remove(uniqueKey);
                    }
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                Log.e(TAG, "Error creating notification in database: " + t.getMessage());
                // Remove from processed set if failed to allow retry
                synchronized (processedNotifications) {
                    processedNotifications.remove(uniqueKey);
                }
            }
        });

        // Clean up old entries to prevent memory leaks (keep only last 100)
        synchronized (processedNotifications) {
            if (processedNotifications.size() > 100) {
                processedNotifications.clear();
                Log.d(TAG, "Cleared processed notifications cache");
            }
        }
    }
}
