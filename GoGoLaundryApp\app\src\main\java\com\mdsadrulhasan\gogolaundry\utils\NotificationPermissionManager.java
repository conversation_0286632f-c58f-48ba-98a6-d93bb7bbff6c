package com.mdsadrulhasan.gogolaundry.utils;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.mdsadrulhasan.gogolaundry.R;

/**
 * Notification Permission Manager
 * 
 * Handles notification permission requests and management for Android 13+ (API 33+)
 * and provides fallback for older versions
 */
public class NotificationPermissionManager {
    private static final String TAG = "NotificationPermissionManager";
    private static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 1001;
    
    private Context context;
    private NotificationPermissionCallback callback;
    
    public interface NotificationPermissionCallback {
        void onPermissionGranted();
        void onPermissionDenied();
        void onPermissionPermanentlyDenied();
    }
    
    public NotificationPermissionManager(Context context) {
        this.context = context;
    }
    
    /**
     * Check if notification permission is granted
     */
    public boolean isNotificationPermissionGranted() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ requires POST_NOTIFICATIONS permission
            return ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) 
                   == PackageManager.PERMISSION_GRANTED;
        } else {
            // For older versions, check if notifications are enabled
            return areNotificationsEnabled();
        }
    }
    
    /**
     * Check if notifications are enabled (for older Android versions)
     */
    private boolean areNotificationsEnabled() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                android.app.NotificationManager notificationManager = 
                    (android.app.NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                return notificationManager.areNotificationsEnabled();
            }
            return true; // Assume enabled for very old versions
        } catch (Exception e) {
            Log.e(TAG, "Error checking notification status: " + e.getMessage());
            return true;
        }
    }
    
    /**
     * Request notification permission
     */
    public void requestNotificationPermission(Activity activity, NotificationPermissionCallback callback) {
        this.callback = callback;
        
        if (isNotificationPermissionGranted()) {
            Log.d(TAG, "Notification permission already granted");
            if (callback != null) {
                callback.onPermissionGranted();
            }
            return;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ - Request POST_NOTIFICATIONS permission
            requestPostNotificationPermission(activity);
        } else {
            // Older versions - Show dialog to enable notifications in settings
            showNotificationSettingsDialog(activity);
        }
    }
    
    /**
     * Request POST_NOTIFICATIONS permission for Android 13+
     */
    private void requestPostNotificationPermission(Activity activity) {
        if (ActivityCompat.shouldShowRequestPermissionRationale(activity, Manifest.permission.POST_NOTIFICATIONS)) {
            // Show rationale dialog
            showPermissionRationaleDialog(activity);
        } else {
            // Request permission directly
            ActivityCompat.requestPermissions(
                activity,
                new String[]{Manifest.permission.POST_NOTIFICATIONS},
                NOTIFICATION_PERMISSION_REQUEST_CODE
            );
        }
    }
    
    /**
     * Show permission rationale dialog
     */
    private void showPermissionRationaleDialog(Activity activity) {
        new AlertDialog.Builder(activity)
            .setTitle("Enable Notifications")
            .setMessage("GoGoLaundry needs notification permission to keep you updated about your orders, delivery status, and special offers.")
            .setIcon(R.drawable.ic_notification)
            .setPositiveButton("Allow", (dialog, which) -> {
                ActivityCompat.requestPermissions(
                    activity,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    NOTIFICATION_PERMISSION_REQUEST_CODE
                );
            })
            .setNegativeButton("Not Now", (dialog, which) -> {
                dialog.dismiss();
                if (callback != null) {
                    callback.onPermissionDenied();
                }
            })
            .setCancelable(false)
            .show();
    }
    
    /**
     * Show notification settings dialog for older Android versions
     */
    private void showNotificationSettingsDialog(Activity activity) {
        new AlertDialog.Builder(activity)
            .setTitle("Enable Notifications")
            .setMessage("To receive important updates about your orders and deliveries, please enable notifications for GoGoLaundry in your device settings.")
            .setIcon(R.drawable.ic_notification)
            .setPositiveButton("Open Settings", (dialog, which) -> {
                openNotificationSettings(activity);
            })
            .setNegativeButton("Skip", (dialog, which) -> {
                dialog.dismiss();
                if (callback != null) {
                    callback.onPermissionDenied();
                }
            })
            .setCancelable(false)
            .show();
    }
    
    /**
     * Open notification settings
     */
    private void openNotificationSettings(Activity activity) {
        try {
            Intent intent = new Intent();
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ - Open app notification settings
                intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.getPackageName());
            } else {
                // Older versions - Open app details settings
                intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
            }
            
            activity.startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening notification settings: " + e.getMessage());
            
            // Fallback - open general app settings
            try {
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                activity.startActivity(intent);
            } catch (Exception ex) {
                Log.e(TAG, "Error opening app settings: " + ex.getMessage());
                ToastUtils.showError(context, "Unable to open settings. Please enable notifications manually.");
            }
        }
    }
    
    /**
     * Handle permission request result
     */
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Notification permission granted");
                if (callback != null) {
                    callback.onPermissionGranted();
                }
            } else {
                Log.d(TAG, "Notification permission denied");
                
                // Check if permission was permanently denied
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU && 
                    context instanceof Activity) {
                    Activity activity = (Activity) context;
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(activity, Manifest.permission.POST_NOTIFICATIONS)) {
                        // Permission permanently denied
                        if (callback != null) {
                            callback.onPermissionPermanentlyDenied();
                        }
                        showPermanentlyDeniedDialog(activity);
                        return;
                    }
                }
                
                if (callback != null) {
                    callback.onPermissionDenied();
                }
            }
        }
    }
    
    /**
     * Show dialog when permission is permanently denied
     */
    private void showPermanentlyDeniedDialog(Activity activity) {
        new AlertDialog.Builder(activity)
            .setTitle("Notification Permission Required")
            .setMessage("Notifications have been permanently disabled. To receive order updates and important information, please enable notifications in app settings.")
            .setIcon(R.drawable.ic_notification)
            .setPositiveButton("Open Settings", (dialog, which) -> {
                openNotificationSettings(activity);
            })
            .setNegativeButton("Cancel", (dialog, which) -> {
                dialog.dismiss();
            })
            .setCancelable(false)
            .show();
    }
    
    /**
     * Show a simple notification permission prompt
     */
    public void showSimplePermissionPrompt(Activity activity, NotificationPermissionCallback callback) {
        this.callback = callback;
        
        if (isNotificationPermissionGranted()) {
            if (callback != null) {
                callback.onPermissionGranted();
            }
            return;
        }
        
        new AlertDialog.Builder(activity)
            .setTitle("Stay Updated!")
            .setMessage("Enable notifications to receive real-time updates about your laundry orders, delivery status, and exclusive offers.")
            .setIcon(R.drawable.ic_notification)
            .setPositiveButton("Enable", (dialog, which) -> {
                requestNotificationPermission(activity, callback);
            })
            .setNegativeButton("Maybe Later", (dialog, which) -> {
                dialog.dismiss();
                if (callback != null) {
                    callback.onPermissionDenied();
                }
            })
            .setCancelable(true)
            .show();
    }
    
    /**
     * Check and request permission if needed (silent check)
     */
    public void checkAndRequestPermissionSilently(Activity activity, NotificationPermissionCallback callback) {
        this.callback = callback;
        
        if (isNotificationPermissionGranted()) {
            if (callback != null) {
                callback.onPermissionGranted();
            }
        } else {
            // Only request if we haven't asked before or if it's not permanently denied
            SessionManager sessionManager = new SessionManager(context);
            boolean hasAskedBefore = sessionManager.hasAskedForNotificationPermission();
            
            if (!hasAskedBefore) {
                sessionManager.setHasAskedForNotificationPermission(true);
                requestNotificationPermission(activity, callback);
            } else {
                if (callback != null) {
                    callback.onPermissionDenied();
                }
            }
        }
    }
}
