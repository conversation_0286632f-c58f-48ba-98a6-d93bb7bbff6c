<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="176dp"
    android:background="?attr/colorPrimary"
    android:gravity="bottom"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.MaterialComponents.Dark">

    <FrameLayout
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="8dp">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/profile_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="2dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_person"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            app:strokeColor="@android:color/white"
            app:strokeWidth="2dp" />

        <ImageView
            android:id="@+id/edit_profile_image"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="bottom|end"
            android:background="@drawable/circle_background"
            android:padding="4dp"
            android:src="@drawable/ic_edit"
            app:tint="@android:color/white" />
    </FrameLayout>

    <TextView
        android:id="@+id/nav_header_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:text="User Name"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/nav_header_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="+8801XXXXXXXXX"
        android:textAppearance="@style/TextAppearance.MaterialComponents.Body2" />

</LinearLayout>
