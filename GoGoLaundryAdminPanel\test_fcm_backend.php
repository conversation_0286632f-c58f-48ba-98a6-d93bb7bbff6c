<?php
/**
 * FCM Backend Test Script
 * 
 * This script tests the FCM service functionality
 */

// Include required files
require_once 'config/db.php';
require_once 'includes/functions.php';
require_once 'includes/FCMService.php';

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<h1>FCM Service Test</h1>";
echo "<hr>";

// Test 1: Check if FCM dependencies are available
echo "<h2>Test 1: Dependency Check</h2>";
if (defined('FCM_DEPENDENCIES_AVAILABLE')) {
    if (FCM_DEPENDENCIES_AVAILABLE) {
        echo "<p style='color: green;'>✓ FCM dependencies are available (using Google Auth library)</p>";
    } else {
        echo "<p style='color: orange;'>⚠ FCM dependencies not available (using fallback implementation)</p>";
    }
} else {
    echo "<p style='color: red;'>✗ FCM_DEPENDENCIES_AVAILABLE constant not defined</p>";
}

// Test 2: Check service account file
echo "<h2>Test 2: Service Account File</h2>";
$serviceAccountPath = __DIR__ . '/service_account.json';
if (file_exists($serviceAccountPath)) {
    echo "<p style='color: green;'>✓ Service account file exists</p>";
    
    $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
    if ($serviceAccount && isset($serviceAccount['project_id'])) {
        echo "<p style='color: green;'>✓ Service account file is valid JSON</p>";
        echo "<p>Project ID: " . htmlspecialchars($serviceAccount['project_id']) . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Service account file is not valid JSON</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Service account file not found at: $serviceAccountPath</p>";
}

// Test 3: Initialize FCM Service
echo "<h2>Test 3: FCM Service Initialization</h2>";
try {
    $fcmService = new FCMService();
    echo "<p style='color: green;'>✓ FCM Service initialized successfully</p>";
    
    // Test access token generation
    $reflection = new ReflectionClass($fcmService);
    $method = $reflection->getMethod('getAccessToken');
    $method->setAccessible(true);
    
    echo "<p>Testing access token generation...</p>";
    $accessToken = $method->invoke($fcmService);
    
    if ($accessToken) {
        echo "<p style='color: green;'>✓ Access token generated successfully</p>";
        echo "<p>Token preview: " . htmlspecialchars(substr($accessToken, 0, 50)) . "...</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to generate access token</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Failed to initialize FCM Service: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: Database connection for FCM tokens
echo "<h2>Test 4: Database FCM Tokens</h2>";
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM fcm_tokens WHERE is_active = 1");
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    echo "<p>Active FCM tokens: " . $result['count'] . "</p>";
    
    // Show some sample tokens (without revealing full tokens)
    $stmt = $pdo->prepare("SELECT user_id, device_type, LEFT(token, 20) as token_preview, created_at FROM fcm_tokens WHERE is_active = 1 LIMIT 5");
    $stmt->execute();
    $tokens = $stmt->fetchAll();
    
    if (!empty($tokens)) {
        echo "<h3>Sample Active Tokens:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>User ID</th><th>Device Type</th><th>Token Preview</th><th>Created</th></tr>";
        foreach ($tokens as $token) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($token['user_id']) . "</td>";
            echo "<td>" . htmlspecialchars($token['device_type']) . "</td>";
            echo "<td>" . htmlspecialchars($token['token_preview']) . "...</td>";
            echo "<td>" . htmlspecialchars($token['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠ No active FCM tokens found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 5: Test notification creation (without sending)
echo "<h2>Test 5: Test Notification Structure</h2>";
try {
    if (isset($fcmService)) {
        $reflection = new ReflectionClass($fcmService);
        $method = $reflection->getMethod('buildPayload');
        $method->setAccessible(true);
        
        $testPayload = $method->invoke($fcmService, 'test_token_123', 'Test Title', 'Test Message', ['test' => true], 'android');
        
        echo "<p style='color: green;'>✓ Notification payload built successfully</p>";
        echo "<h3>Sample Payload:</h3>";
        echo "<pre>" . htmlspecialchars(json_encode($testPayload, JSON_PRETTY_PRINT)) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Failed to build notification payload: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='admin/notifications.php'>Go to Notifications Page</a> | <a href='test_fcm.php'>Go to FCM Frontend Test</a></p>";
?>
