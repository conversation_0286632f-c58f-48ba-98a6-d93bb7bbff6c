package com.mdsadrulhasan.gogolaundry.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.lifecycle.ViewModelProvider;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.slider.Slider;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;
import com.mdsadrulhasan.gogolaundry.model.District;
import com.mdsadrulhasan.gogolaundry.model.Division;
import com.mdsadrulhasan.gogolaundry.model.ShopFilter;
import com.mdsadrulhasan.gogolaundry.model.Upazilla;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.LocationViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.ServicesViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Dialog for filtering shops
 */
public class ShopFilterDialog extends DialogFragment {

    private static final String TAG = "ShopFilterDialog";
    private static final String ARG_CURRENT_FILTER = "current_filter";

    // Views
    private Slider distanceSlider;
    private TextView distanceText;
    private Slider ratingSlider;
    private TextView ratingText;
    private Spinner serviceSpinner;
    private Spinner divisionSpinner;
    private Spinner districtSpinner;
    private Spinner upazillaSpinner;
    private SwitchMaterial openNowSwitch;
    private SwitchMaterial verifiedOnlySwitch;

    // ViewModels
    private LocationViewModel locationViewModel;
    private ServicesViewModel servicesViewModel;

    // Data
    private ShopFilter currentFilter;
    private List<ServiceEntity> services = new ArrayList<>();
    private List<Division> divisions = new ArrayList<>();
    private List<District> districts = new ArrayList<>();
    private List<Upazilla> upazillas = new ArrayList<>();

    // Callback interface
    public interface OnFilterAppliedListener {
        void onFilterApplied(ShopFilter filter);
    }

    private OnFilterAppliedListener filterListener;

    public static ShopFilterDialog newInstance(ShopFilter currentFilter) {
        ShopFilterDialog dialog = new ShopFilterDialog();
        Bundle args = new Bundle();
        args.putSerializable(ARG_CURRENT_FILTER, currentFilter);
        dialog.setArguments(args);
        return dialog;
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        if (getParentFragment() instanceof OnFilterAppliedListener) {
            filterListener = (OnFilterAppliedListener) getParentFragment();
        } else if (context instanceof OnFilterAppliedListener) {
            filterListener = (OnFilterAppliedListener) context;
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.TransparentDialog);

        // Get current filter from arguments
        if (getArguments() != null) {
            currentFilter = (ShopFilter) getArguments().getSerializable(ARG_CURRENT_FILTER);
        }
        if (currentFilter == null) {
            currentFilter = new ShopFilter();
        }

        // Initialize ViewModels
        locationViewModel = new ViewModelProvider(this).get(LocationViewModel.class);
        servicesViewModel = new ViewModelProvider(this).get(ServicesViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.dialog_shop_filter, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initializeViews(view);
        setupSliders();
        setupSpinners();
        setupSwitches();
        setupButtons(view);
        loadData();
        applyCurrentFilter();
    }

    private void initializeViews(View view) {
        distanceSlider = view.findViewById(R.id.distanceSlider);
        distanceText = view.findViewById(R.id.distanceText);
        ratingSlider = view.findViewById(R.id.ratingSlider);
        ratingText = view.findViewById(R.id.ratingText);
        serviceSpinner = view.findViewById(R.id.serviceSpinner);
        divisionSpinner = view.findViewById(R.id.divisionSpinner);
        districtSpinner = view.findViewById(R.id.districtSpinner);
        upazillaSpinner = view.findViewById(R.id.upazillaSpinner);
        openNowSwitch = view.findViewById(R.id.openNowSwitch);
        verifiedOnlySwitch = view.findViewById(R.id.verifiedOnlySwitch);
    }

    private void setupSliders() {
        // Distance slider
        distanceSlider.addOnChangeListener((slider, value, fromUser) -> {
            if (fromUser) {
                distanceText.setText(getString(R.string.within_km, (int) value));
            }
        });

        // Rating slider
        ratingSlider.addOnChangeListener((slider, value, fromUser) -> {
            if (fromUser) {
                if (value == 0.0f) {
                    ratingText.setText(getString(R.string.any_rating));
                } else {
                    ratingText.setText(getString(R.string.rating_and_above, value));
                }
            }
        });
    }

    private void setupSpinners() {
        // Division spinner change listener
        divisionSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0) { // Skip "All Divisions" option
                    Division selectedDivision = divisions.get(position - 1);
                    locationViewModel.loadDistricts(selectedDivision.getId());
                } else {
                    districts.clear();
                    upazillas.clear();
                    updateDistrictSpinner();
                    updateUpazillaSpinner();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });

        // District spinner change listener
        districtSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position > 0 && !districts.isEmpty()) { // Skip "All Districts" option
                    District selectedDistrict = districts.get(position - 1);
                    locationViewModel.loadUpazillas(selectedDistrict.getId());
                } else {
                    upazillas.clear();
                    updateUpazillaSpinner();
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }

    private void setupSwitches() {
        // Switches are already initialized with their default values
    }

    private void setupButtons(View view) {
        ImageButton closeButton = view.findViewById(R.id.closeButton);
        MaterialButton clearFiltersButton = view.findViewById(R.id.clearFiltersButton);
        MaterialButton applyFiltersButton = view.findViewById(R.id.applyFiltersButton);

        closeButton.setOnClickListener(v -> dismiss());

        clearFiltersButton.setOnClickListener(v -> {
            clearAllFilters();
            ToastUtils.showInfo(requireContext(), "All filters cleared");
        });

        applyFiltersButton.setOnClickListener(v -> {
            applyFilters();
        });
    }

    private void loadData() {
        // Load divisions
        locationViewModel.getDivisions().observe(this, divisions -> {
            if (divisions != null) {
                this.divisions = divisions;
                updateDivisionSpinner();
            }
        });

        // Load districts
        locationViewModel.getDistricts().observe(this, districts -> {
            if (districts != null) {
                this.districts = districts;
                updateDistrictSpinner();
            }
        });

        // Load upazillas
        locationViewModel.getUpazillas().observe(this, upazillas -> {
            if (upazillas != null) {
                this.upazillas = upazillas;
                updateUpazillaSpinner();
            }
        });

        // Load services
        servicesViewModel.getServices().observe(this, resource -> {
            if (resource != null && resource.isSuccess() && resource.getData() != null) {
                this.services = resource.getData();
                updateServiceSpinner();
            }
        });

        // Load initial data
        locationViewModel.loadDivisions();
        servicesViewModel.loadServices();
    }

    private void updateServiceSpinner() {
        List<String> serviceNames = new ArrayList<>();
        serviceNames.add("All Services");
        for (ServiceEntity service : services) {
            serviceNames.add(service.getName());
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, serviceNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        serviceSpinner.setAdapter(adapter);
    }

    private void updateDivisionSpinner() {
        List<String> divisionNames = new ArrayList<>();
        divisionNames.add("All Divisions");
        for (Division division : divisions) {
            divisionNames.add(division.getName());
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, divisionNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        divisionSpinner.setAdapter(adapter);
    }

    private void updateDistrictSpinner() {
        List<String> districtNames = new ArrayList<>();
        districtNames.add("All Districts");
        for (District district : districts) {
            districtNames.add(district.getName());
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, districtNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        districtSpinner.setAdapter(adapter);
    }

    private void updateUpazillaSpinner() {
        List<String> upazillaNames = new ArrayList<>();
        upazillaNames.add("All Upazillas");
        for (Upazilla upazilla : upazillas) {
            upazillaNames.add(upazilla.getName());
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, upazillaNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        upazillaSpinner.setAdapter(adapter);
    }

    private void applyCurrentFilter() {
        // Apply current filter values to UI
        distanceSlider.setValue((float) currentFilter.getMaxDistance());
        distanceText.setText(getString(R.string.within_km, (int) currentFilter.getMaxDistance()));

        ratingSlider.setValue((float) currentFilter.getMinRating());
        if (currentFilter.getMinRating() == 0.0) {
            ratingText.setText(getString(R.string.any_rating));
        } else {
            ratingText.setText(getString(R.string.rating_and_above, currentFilter.getMinRating()));
        }

        openNowSwitch.setChecked(currentFilter.isOpenNowOnly());
        verifiedOnlySwitch.setChecked(currentFilter.isVerifiedOnly());

        // Set spinner selections will be handled after data is loaded
    }

    private void clearAllFilters() {
        distanceSlider.setValue(10.0f);
        distanceText.setText(getString(R.string.within_km, 10));

        ratingSlider.setValue(0.0f);
        ratingText.setText(getString(R.string.any_rating));

        serviceSpinner.setSelection(0);
        divisionSpinner.setSelection(0);
        districtSpinner.setSelection(0);
        upazillaSpinner.setSelection(0);

        openNowSwitch.setChecked(false);
        verifiedOnlySwitch.setChecked(true);
    }

    private void applyFilters() {
        Log.d(TAG, "applyFilters() called - Building filter from dialog inputs");
        ShopFilter filter = new ShopFilter();

        // Distance
        double distance = distanceSlider.getValue();
        filter.setMaxDistance(distance);
        Log.d(TAG, "Filter distance: " + distance + " km");

        // Rating
        double rating = ratingSlider.getValue();
        filter.setMinRating(rating);
        Log.d(TAG, "Filter min rating: " + rating);

        // Service
        int servicePosition = serviceSpinner.getSelectedItemPosition();
        if (servicePosition > 0 && !services.isEmpty()) {
            int serviceId = services.get(servicePosition - 1).getId();
            filter.setServiceId(serviceId);
            Log.d(TAG, "Filter service ID: " + serviceId + " (" + services.get(servicePosition - 1).getName() + ")");
        } else {
            Log.d(TAG, "No service filter selected");
        }

        // Location
        int divisionPosition = divisionSpinner.getSelectedItemPosition();
        if (divisionPosition > 0 && !divisions.isEmpty()) {
            int divisionId = divisions.get(divisionPosition - 1).getId();
            filter.setDivisionId(divisionId);
            Log.d(TAG, "Filter division ID: " + divisionId + " (" + divisions.get(divisionPosition - 1).getName() + ")");
        } else {
            Log.d(TAG, "No division filter selected");
        }

        int districtPosition = districtSpinner.getSelectedItemPosition();
        if (districtPosition > 0 && !districts.isEmpty()) {
            int districtId = districts.get(districtPosition - 1).getId();
            filter.setDistrictId(districtId);
            Log.d(TAG, "Filter district ID: " + districtId + " (" + districts.get(districtPosition - 1).getName() + ")");
        } else {
            Log.d(TAG, "No district filter selected");
        }

        int upazillaPosition = upazillaSpinner.getSelectedItemPosition();
        if (upazillaPosition > 0 && !upazillas.isEmpty()) {
            int upazillaId = upazillas.get(upazillaPosition - 1).getId();
            filter.setUpazillaId(upazillaId);
            Log.d(TAG, "Filter upazilla ID: " + upazillaId + " (" + upazillas.get(upazillaPosition - 1).getName() + ")");
        } else {
            Log.d(TAG, "No upazilla filter selected");
        }

        // Status
        boolean openNow = openNowSwitch.isChecked();
        boolean verifiedOnly = verifiedOnlySwitch.isChecked();
        filter.setOpenNowOnly(openNow);
        filter.setVerifiedOnly(verifiedOnly);
        Log.d(TAG, "Filter status - Open now: " + openNow + ", Verified only: " + verifiedOnly);

        Log.d(TAG, "Final filter: " + filter.toString());
        Log.d(TAG, "Filter has active filters: " + filter.hasActiveFilters());

        // Apply filter
        if (filterListener != null) {
            Log.d(TAG, "Calling filterListener.onFilterApplied()");
            filterListener.onFilterApplied(filter);
        } else {
            Log.w(TAG, "No filter listener set, cannot apply filter");
        }

        dismiss();
    }

}
