-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 24, 2025 at 08:16 AM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `gogolaundry`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `add_order_item` (IN `p_order_id` INT, IN `p_item_id` INT, IN `p_quantity` INT, IN `p_notes` TEXT)   BEGIN
    DECLARE v_price DECIMAL(10,2);
    DECLARE v_subtotal DECIMAL(10,2);
    DECLARE v_order_subtotal DECIMAL(10,2);
    DECLARE v_discount DECIMAL(10,2);
    DECLARE v_delivery_fee DECIMAL(10,2);
    DECLARE v_total DECIMAL(10,2);
    DECLARE v_promo_code_id INT;
    DECLARE v_discount_type ENUM('percentage','fixed');
    DECLARE v_discount_value DECIMAL(10,2);
    DECLARE v_min_order_value DECIMAL(10,2);
    DECLARE v_max_discount DECIMAL(10,2);
    
    -- Get the item price
    SELECT price INTO v_price FROM items WHERE id = p_item_id;
    
    -- Calculate subtotal for this item
    SET v_subtotal = v_price * p_quantity;
    
    -- Insert the order item
    INSERT INTO order_items (
        order_id,
        item_id,
        quantity,
        price,
        subtotal,
        notes
    ) VALUES (
        p_order_id,
        p_item_id,
        p_quantity,
        v_price,
        v_subtotal,
        p_notes
    );
    
    -- Get the order's promo code details
    SELECT promo_code_id, delivery_fee INTO v_promo_code_id, v_delivery_fee
    FROM orders WHERE id = p_order_id;
    
    IF v_promo_code_id IS NOT NULL THEN
        SELECT discount_type, discount_value, min_order_value, max_discount
        INTO v_discount_type, v_discount_value, v_min_order_value, v_max_discount
        FROM promo_codes WHERE id = v_promo_code_id;
    END IF;
    
    -- Calculate new order subtotal
    SELECT SUM(subtotal) INTO v_order_subtotal
    FROM order_items WHERE order_id = p_order_id;
    
    -- Calculate discount based on promo code
    IF v_promo_code_id IS NOT NULL AND v_order_subtotal >= v_min_order_value THEN
        IF v_discount_type = 'percentage' THEN
            SET v_discount = v_order_subtotal * (v_discount_value / 100);
            IF v_max_discount IS NOT NULL AND v_discount > v_max_discount THEN
                SET v_discount = v_max_discount;
            END IF;
        ELSE -- fixed discount
            SET v_discount = v_discount_value;
        END IF;
    ELSE
        SET v_discount = 0;
    END IF;
    
    -- Calculate total
    SET v_total = v_order_subtotal - v_discount + v_delivery_fee;
    
    -- Update the order with new totals
    UPDATE orders
    SET subtotal = v_order_subtotal,
        discount = v_discount,
        total = v_total
    WHERE id = p_order_id;
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `create_order` (IN `p_user_id` INT, IN `p_promo_code` VARCHAR(20), IN `p_payment_method` ENUM('cash','card','mobile_banking'), IN `p_pickup_address` VARCHAR(255), IN `p_pickup_division_id` INT, IN `p_pickup_district_id` INT, IN `p_pickup_upazilla_id` INT, IN `p_pickup_date` DATE, IN `p_pickup_time_slot` VARCHAR(50), IN `p_delivery_address` VARCHAR(255), IN `p_delivery_division_id` INT, IN `p_delivery_district_id` INT, IN `p_delivery_upazilla_id` INT, IN `p_notes` TEXT, OUT `p_order_id` INT, OUT `p_order_number` VARCHAR(20), OUT `p_tracking_number` VARCHAR(20))   BEGIN
    DECLARE v_promo_code_id INT DEFAULT NULL;
    DECLARE v_discount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_delivery_fee DECIMAL(10,2) DEFAULT 50.00; -- Default delivery fee
    DECLARE v_subtotal DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_total DECIMAL(10,2) DEFAULT 0.00;
    
    -- Generate unique order number and tracking number
    SET p_order_number = CONCAT('ORD', DATE_FORMAT(NOW(), '%y%m%d'), LPAD(FLOOR(RAND() * 10000), 4, '0'));
    SET p_tracking_number = CONCAT('TRK', DATE_FORMAT(NOW(), '%y%m%d'), LPAD(FLOOR(RAND() * 10000), 4, '0'));
    
    -- Check if promo code exists and is valid
    IF p_promo_code IS NOT NULL AND p_promo_code != '' THEN
        SELECT id, discount_type, discount_value, min_order_value, max_discount
        INTO v_promo_code_id, @discount_type, @discount_value, @min_order_value, @max_discount
        FROM promo_codes
        WHERE code = p_promo_code
        AND is_active = 1
        AND start_date <= NOW()
        AND end_date >= NOW()
        AND (usage_limit IS NULL OR usage_count < usage_limit);
    END IF;
    
    -- Insert the order
    INSERT INTO orders (
        order_number,
        tracking_number,
        user_id,
        promo_code_id,
        subtotal,
        discount,
        delivery_fee,
        total,
        payment_method,
        status,
        pickup_address,
        pickup_division_id,
        pickup_district_id,
        pickup_upazilla_id,
        pickup_date,
        pickup_time_slot,
        delivery_address,
        delivery_division_id,
        delivery_district_id,
        delivery_upazilla_id,
        notes
    ) VALUES (
        p_order_number,
        p_tracking_number,
        p_user_id,
        v_promo_code_id,
        v_subtotal,
        v_discount,
        v_delivery_fee,
        v_total,
        p_payment_method,
        'placed',
        p_pickup_address,
        p_pickup_division_id,
        p_pickup_district_id,
        p_pickup_upazilla_id,
        p_pickup_date,
        p_pickup_time_slot,
        p_delivery_address,
        p_delivery_division_id,
        p_delivery_district_id,
        p_delivery_upazilla_id,
        p_notes
    );
    
    -- Get the inserted order ID
    SET p_order_id = LAST_INSERT_ID();
    
    -- If promo code was used, increment its usage count
    IF v_promo_code_id IS NOT NULL THEN
        UPDATE promo_codes
        SET usage_count = usage_count + 1
        WHERE id = v_promo_code_id;
    END IF;
    
    -- Create notification for the user
    INSERT INTO notifications (
        user_id,
        order_id,
        title,
        message,
        type
    ) VALUES (
        p_user_id,
        p_order_id,
        'Order Placed',
        CONCAT('Your order #', p_order_number, ' has been placed successfully. Track your order with tracking number: ', p_tracking_number),
        'order_status'
    );
END$$

CREATE DEFINER=`root`@`localhost` PROCEDURE `update_order_status` (IN `p_order_id` INT, IN `p_status` ENUM('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled'), IN `p_notes` TEXT, IN `p_updated_by` INT, IN `p_updated_by_type` ENUM('admin','user','system','delivery_personnel'), IN `p_delivery_personnel_id` INT)   BEGIN
    DECLARE v_user_id INT;
    DECLARE v_order_number VARCHAR(20);
    DECLARE v_tracking_number VARCHAR(20);
    DECLARE v_old_status ENUM('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled');
    
    -- Get current order details
    SELECT user_id, order_number, tracking_number, status
    INTO v_user_id, v_order_number, v_tracking_number, v_old_status
    FROM orders WHERE id = p_order_id;
    
    -- Update order status
    UPDATE orders
    SET status = p_status,
        delivery_personnel_id = CASE 
            WHEN p_delivery_personnel_id IS NOT NULL THEN p_delivery_personnel_id
            ELSE delivery_personnel_id
        END,
        updated_at = NOW()
    WHERE id = p_order_id;
    
    -- Insert into status history
    INSERT INTO order_status_history (
        order_id,
        status,
        notes,
        updated_by,
        updated_by_type
    ) VALUES (
        p_order_id,
        p_status,
        p_notes,
        p_updated_by,
        p_updated_by_type
    );
    
    -- Create notification for the user
    INSERT INTO notifications (
        user_id,
        order_id,
        title,
        message,
        type
    ) VALUES (
        v_user_id,
        p_order_id,
        CONCAT('Order ', REPLACE(LOWER(p_status), '_', ' ')),
        CASE p_status
            WHEN 'confirmed' THEN CONCAT('Your order #', v_order_number, ' has been confirmed. We will pick up your items soon.')
            WHEN 'pickup_scheduled' THEN CONCAT('Pickup for your order #', v_order_number, ' has been scheduled. Our delivery person will arrive at the scheduled time.')
            WHEN 'picked_up' THEN CONCAT('Your items for order #', v_order_number, ' have been picked up and are on their way to our facility.')
            WHEN 'processing' THEN CONCAT('Your order #', v_order_number, ' is now being processed at our facility.')
            WHEN 'ready_for_delivery' THEN CONCAT('Your order #', v_order_number, ' is ready for delivery. We will deliver it soon.')
            WHEN 'out_for_delivery' THEN CONCAT('Your order #', v_order_number, ' is out for delivery and will arrive shortly.')
            WHEN 'delivered' THEN CONCAT('Your order #', v_order_number, ' has been delivered. Thank you for using our service!')
            WHEN 'cancelled' THEN CONCAT('Your order #', v_order_number, ' has been cancelled. Please contact support for more information.')
            ELSE CONCAT('Your order #', v_order_number, ' status has been updated to: ', REPLACE(LOWER(p_status), '_', ' '))
        END,
        'order_status'
    );
END$$

--
-- Functions
--
CREATE DEFINER=`root`@`localhost` FUNCTION `calculate_avg_delivery_time` (`p_start_date` DATE, `p_end_date` DATE) RETURNS DECIMAL(10,2) DETERMINISTIC BEGIN
    DECLARE v_avg_hours DECIMAL(10,2);
    
    SELECT AVG(TIMESTAMPDIFF(HOUR, o.created_at, h.created_at)) INTO v_avg_hours
    FROM orders o
    JOIN order_status_history h ON o.id = h.order_id
    WHERE h.status = 'delivered'
    AND DATE(o.created_at) BETWEEN p_start_date AND p_end_date;
    
    RETURN v_avg_hours;
END$$

CREATE DEFINER=`root`@`localhost` FUNCTION `get_order_status_count` (`p_status` ENUM('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled'), `p_start_date` DATE, `p_end_date` DATE) RETURNS INT(11) DETERMINISTIC BEGIN
    DECLARE v_count INT;
    
    SELECT COUNT(*) INTO v_count
    FROM orders
    WHERE status = p_status
    AND DATE(created_at) BETWEEN p_start_date AND p_end_date;
    
    RETURN v_count;
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `admin_logs`
--

CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_logs`
--

INSERT INTO `admin_logs` (`id`, `admin_id`, `action`, `details`, `ip_address`, `user_agent`, `created_at`) VALUES
(31, 1, 'login_attempt', 'Failed login attempt', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 05:55:29'),
(32, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 05:55:44'),
(33, 1, 'settings_update', 'Updated setting: otp_enabled from \"0\" to \"1\"', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 06:03:15'),
(34, 1, 'settings_update', 'Updated setting: otp_enabled from \"1\" to \"0\"', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 06:06:49'),
(35, 1, 'settings_update', 'Updated setting: otp_enabled from \"0\" to \"1\"', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 06:46:42'),
(36, 1, 'user_delete', 'Soft deleted user ID: 17 (Laundry)', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 07:11:47'),
(37, 1, 'logout', 'Admin logged out', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 17:13:04'),
(38, 1, 'login_attempt', 'Failed login attempt', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 17:13:08'),
(39, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 17:13:19'),
(40, 1, 'view_logs', 'Viewed admin activity logs', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 17:14:11'),
(41, 1, 'view_logs', 'Viewed admin activity logs', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 18:13:02'),
(42, 1, 'login', 'Successful login', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 18:42:51'),
(43, 1, 'view_logs', 'Viewed admin activity logs', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 19:00:14'),
(44, 1, 'view_logs', 'Viewed admin activity logs', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 19:02:55'),
(45, 1, 'view_user_details', 'Viewed details for user ID: 1 (Admin User)', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 19:12:12'),
(46, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:46:32'),
(47, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:46:52'),
(48, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:47:10'),
(49, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:47:46'),
(50, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:48:09'),
(51, 1, 'logout', 'Admin logged out', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 22:48:51'),
(52, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 22:49:02'),
(53, 1, 'logout', 'Admin logged out', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 22:49:27'),
(54, 1, 'login_attempt', 'Failed login attempt', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 22:49:30'),
(55, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-21 22:49:40'),
(56, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:50:36'),
(57, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:50:47'),
(58, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:51:13'),
(59, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36 Edg/136.0.0.0', '2025-05-21 22:52:11'),
(60, 1, 'login_attempt', 'Failed login attempt', '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36', '2025-05-22 11:02:02'),
(61, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36', '2025-05-22 11:02:45'),
(62, 1, 'login_attempt', 'Failed login attempt', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 12:05:00'),
(63, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 12:08:15'),
(64, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 12:08:31'),
(65, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 12:08:55'),
(66, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 12:09:39'),
(67, 1, 'settings_update', 'Updated setting: otp_enabled from \"1\" to \"0\"', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-22 16:43:16'),
(68, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 18:42:34'),
(69, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 18:44:35'),
(70, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 18:48:39'),
(71, 1, 'logout', 'Admin logged out', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 18:58:05'),
(72, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Mobile Safari/537.36', '2025-05-22 18:58:22'),
(73, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/27.0 Chrome/125.0.0.0 Mobile Safari/537.36', '2025-05-22 19:00:09'),
(74, 1, 'view_logs', 'Viewed admin activity logs', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-22 22:11:21'),
(75, 1, 'settings_update', 'Updated setting: delivery_fee from \"50.00\" to \"11\"', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-22 22:36:50'),
(76, 1, 'login_attempt', 'Failed login attempt', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-23 03:27:17'),
(77, 1, 'login', 'Successful login', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-23 03:27:34'),
(78, 1, 'view_logs', 'Viewed admin activity logs', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-24 02:31:34'),
(79, 1, 'view_user_details', 'Viewed details for user ID: 19 (test)', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-24 02:32:54'),
(80, 1, 'settings_update', 'Updated setting: otp_enabled from \"0\" to \"1\"', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-24 02:35:12'),
(81, 1, 'send_notification', 'Sent notification to all users: Testing notification', '*************', NULL, '2025-05-24 02:53:04'),
(82, 1, 'send_notification', 'Sent notification to all users: Testing notification', '*************', NULL, '2025-05-24 02:58:22'),
(83, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: dfsd', '*************', NULL, '2025-05-24 03:06:04'),
(84, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 03:17:20'),
(85, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 03:18:13'),
(86, 1, 'send_notification', 'Sent notification to all users: Testing notification', '*************', NULL, '2025-05-24 03:22:43'),
(87, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: test', '*************', NULL, '2025-05-24 03:22:57'),
(88, 1, 'send_notification', 'Sent notification to test: Testing notification', '*************', NULL, '2025-05-24 03:23:34'),
(89, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 03:27:50'),
(90, 1, 'order_update', 'Updated order #ORD2505248446 status to confirmed', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-24 03:37:46'),
(91, 1, 'order_update', 'Updated order #ORD2505248446 status to confirmed', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-24 03:37:56'),
(92, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 03:46:02'),
(93, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 03:49:25'),
(94, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 03:50:42'),
(95, 1, 'orders_export', 'Exported 27 orders to Excel', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', '2025-05-24 03:51:06'),
(96, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 03:59:14'),
(97, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 04:07:23'),
(98, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 04:17:56'),
(99, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 04:24:38'),
(100, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 04:27:06'),
(101, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: test', '*************', NULL, '2025-05-24 04:32:56'),
(102, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: test', '*************', NULL, '2025-05-24 04:36:23'),
(104, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 04:54:13'),
(105, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 04:54:27'),
(106, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 04:57:34'),
(107, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 04:58:17'),
(108, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:00:52'),
(109, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:04:07'),
(110, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:04:59'),
(111, 1, 'resend_notification', 'Resent notification \'Testing notification\' to all users via FCM', '*************', NULL, '2025-05-24 05:05:10'),
(112, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:05:24'),
(113, 1, 'resend_notification', 'Resent notification \'Testing notification\' to all users via FCM', '*************', NULL, '2025-05-24 05:05:37'),
(114, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:21:26'),
(115, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM, SMS', '*************', NULL, '2025-05-24 05:21:33'),
(116, 1, 'resend_notification', 'Resent notification \'test\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:27:07'),
(117, 1, 'resend_notification', 'Resent notification \'test\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:28:06'),
(118, 1, 'resend_notification', 'Resent notification \'test\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:28:34'),
(119, 1, 'resend_notification', 'Resent notification \'test\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:28:49'),
(120, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 05:29:18'),
(121, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 05:29:54'),
(122, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 05:37:13'),
(123, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 05:39:29'),
(124, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notificatioten', '*************', NULL, '2025-05-24 05:43:27'),
(125, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 05:44:22'),
(126, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:44:39'),
(127, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:44:48'),
(128, 1, 'resend_notification', 'Resent notification \'Test Image Notification\' to test via FCM', '*************', NULL, '2025-05-24 05:53:36'),
(129, 1, 'resend_notification', 'Resent notification \'Test Image Notification\' to test via FCM', '*************', NULL, '2025-05-24 05:53:42'),
(130, 1, 'resend_notification', 'Resent notification \'Test Image Notification\' to test via FCM', '*************', NULL, '2025-05-24 05:53:49'),
(131, 1, 'resend_notification', 'Resent notification \'Test Image Notification\' to test via FCM', '*************', NULL, '2025-05-24 05:53:54'),
(132, 1, 'send_notification', 'Sent notification to Sadrul hasan dider: Testing notification', '*************', NULL, '2025-05-24 05:54:55'),
(133, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:55:24'),
(134, 1, 'resend_notification', 'Resent notification \'Test Image Notification\' to test via FCM', '*************', NULL, '2025-05-24 05:56:11'),
(135, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:56:36'),
(136, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:57:11'),
(137, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:57:50'),
(138, 1, 'resend_notification', 'Resent notification \'Test Image Notification\' to test via FCM', '*************', NULL, '2025-05-24 05:58:00'),
(139, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:58:05'),
(140, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 05:58:20'),
(141, 1, 'resend_notification', 'Resent notification \'Testing notification\' to Sadrul hasan dider via FCM', '*************', NULL, '2025-05-24 06:12:55');

-- --------------------------------------------------------

--
-- Table structure for table `admin_notifications`
--

CREATE TABLE `admin_notifications` (
  `id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('admin','super_admin') NOT NULL DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `login_attempts` int(11) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `is_active`, `last_login`, `login_attempts`, `locked_until`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$aIhoduCLuqbjVE3QFniZleWPfbp.strw.0wMt1No4fsB0/a8h9RnK', 'System Administrator', 'super_admin', 1, '2025-05-23 03:27:34', 0, NULL, '2025-05-09 16:38:40', '2025-05-23 03:27:34');

-- --------------------------------------------------------

--
-- Table structure for table `deleted_users`
--

CREATE TABLE `deleted_users` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `division_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `upazilla_id` int(11) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `profile_picture_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `deleted_users`
--

INSERT INTO `deleted_users` (`id`, `user_id`, `full_name`, `phone`, `email`, `password`, `address`, `division_id`, `district_id`, `upazilla_id`, `is_verified`, `profile_picture_url`, `created_at`, `updated_at`, `deleted_at`) VALUES
(2, 17, 'Laundry', '8801778318921', '<EMAIL>', '$2y$10$/c/n7PlYDXi0ZYlMPQSsZuSoEwBOUU58ulkr/p6mSoVkwv/iw/dLq', 'test', 6, 41, 325, 1, NULL, '2025-05-21 06:03:52', '2025-05-21 06:03:52', '2025-05-21 07:11:47');

-- --------------------------------------------------------

--
-- Table structure for table `delivery_personnel`
--

CREATE TABLE `delivery_personnel` (
  `id` int(11) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `profile_picture_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_available` tinyint(1) NOT NULL DEFAULT 1,
  `current_location_lat` decimal(10,8) DEFAULT NULL,
  `current_location_lng` decimal(11,8) DEFAULT NULL,
  `last_location_update` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `districts`
--

CREATE TABLE `districts` (
  `id` int(11) NOT NULL,
  `division_id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `bn_name` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `districts`
--

INSERT INTO `districts` (`id`, `division_id`, `name`, `bn_name`, `created_at`) VALUES
(41, 6, 'গাজীপুর', NULL, '2025-05-21 07:10:01');

-- --------------------------------------------------------

--
-- Table structure for table `divisions`
--

CREATE TABLE `divisions` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `bn_name` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `divisions`
--

INSERT INTO `divisions` (`id`, `name`, `bn_name`, `created_at`) VALUES
(6, 'ঢাকা', NULL, '2025-05-21 07:10:01');

-- --------------------------------------------------------

--
-- Table structure for table `fcm_tokens`
--

CREATE TABLE `fcm_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `token` text NOT NULL,
  `device_type` enum('android','ios','web') NOT NULL DEFAULT 'android',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `fcm_tokens`
--

INSERT INTO `fcm_tokens` (`id`, `user_id`, `device_id`, `token`, `device_type`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 'test_device_1748053683631', 'test_token_1748053683631', 'web', 1, '2025-05-24 02:28:04', '2025-05-24 02:28:04'),
(2, 1, 'test_device_1748054592864', 'Rl7vsYU9CA7kUs6Y4L14u6EHBmCIE8y0f3VsdL73m_0G7SrS1orAwadbt7l9RmTsQs1MVv_FbBm4MDnHrhM4pFI5UegjS9U2eZmkMfKc_zrWs-ul5PpYzo5jUMuTLCZjjIkKtKKICAbWsYvhyjYiQdmG', 'web', 1, '2025-05-24 02:43:25', '2025-05-24 02:43:25'),
(3, 20, 'android_1748052993415_741616b100b4f521', 'cVD0mU8DRjCjQUHHQ5MrsR:APA91bEZOVJjiuuIz6XQgjxivhE5iVGrhCi9q0oY1YEnaW5szCqi4xvJvzAGKKDNzVUxx46A7ZCytXzsHwzUqjITvOfB5VPtScME5AuZV7bDeUOwKc8hR1k', 'android', 1, '2025-05-24 02:46:31', '2025-05-24 06:13:01'),
(4, 1, 'test_device_1748055776', 'test_token_1748055776', 'android', 1, '2025-05-24 03:02:57', '2025-05-24 03:02:57');

-- --------------------------------------------------------

--
-- Table structure for table `items`
--

CREATE TABLE `items` (
  `id` int(11) NOT NULL,
  `service_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `bn_name` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `bn_description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `in_stock` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `items`
--

INSERT INTO `items` (`id`, `service_id`, `name`, `bn_name`, `description`, `bn_description`, `price`, `image_url`, `is_active`, `in_stock`, `created_at`, `updated_at`) VALUES
(1, 2, 'test', 'test', 'test', 'test', '44.00', 'https://c8.alamy.com/comp/MTAT3N/iron-on-ironing-board-on-home-interior-background-MTAT3N.jpg', 1, 1, '2025-05-21 18:03:32', '2025-05-22 23:14:44'),
(2, 2, 'আইরন', 'আইরন', 'আইরন', 'আইরন', '22.00', '', 1, 1, '2025-05-22 10:43:57', '2025-05-22 10:43:57');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `type` enum('order_status','promo','system','custom') NOT NULL DEFAULT 'system',
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `fcm_sent` tinyint(1) NOT NULL DEFAULT 0,
  `sms_sent` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `order_number` varchar(20) NOT NULL,
  `tracking_number` varchar(20) NOT NULL,
  `user_id` int(11) NOT NULL,
  `delivery_personnel_id` int(11) DEFAULT NULL,
  `promo_code_id` int(11) DEFAULT NULL,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `delivery_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','card','bKash','Nagad','Rocket','mobile_banking') NOT NULL DEFAULT 'cash',
  `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
  `transaction_id` varchar(100) DEFAULT NULL,
  `payment_provider` varchar(50) DEFAULT NULL,
  `status` enum('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled') NOT NULL DEFAULT 'placed',
  `pickup_address` varchar(255) NOT NULL,
  `pickup_division_id` int(11) DEFAULT NULL,
  `pickup_district_id` int(11) DEFAULT NULL,
  `pickup_upazilla_id` int(11) DEFAULT NULL,
  `pickup_date` date NOT NULL,
  `pickup_time_slot` varchar(50) NOT NULL,
  `delivery_address` varchar(255) NOT NULL,
  `delivery_division_id` int(11) DEFAULT NULL,
  `delivery_district_id` int(11) DEFAULT NULL,
  `delivery_upazilla_id` int(11) DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `delivery_time_slot` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Triggers `orders`
--
DELIMITER $$
CREATE TRIGGER `order_status_history_insert` AFTER INSERT ON `orders` FOR EACH ROW BEGIN
  INSERT INTO `order_status_history` (`order_id`, `status`, `notes`, `updated_by_type`)
  VALUES (NEW.id, NEW.status, 'Order created', 'system');
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `order_status_history_update` AFTER UPDATE ON `orders` FOR EACH ROW BEGIN
  IF NEW.status != OLD.status THEN
    INSERT INTO `order_status_history` (`order_id`, `status`, `notes`, `updated_by_type`)
    VALUES (NEW.id, NEW.status, CONCAT('Status changed from ', OLD.status, ' to ', NEW.status), 'system');
  END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price` decimal(10,2) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `order_status_history`
--

CREATE TABLE `order_status_history` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `status` enum('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled') NOT NULL,
  `notes` text DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_by_type` enum('admin','user','system','delivery_personnel') NOT NULL DEFAULT 'system',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `otps`
--

CREATE TABLE `otps` (
  `id` int(11) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `otp` varchar(10) NOT NULL,
  `purpose` enum('registration','login','reset_password') NOT NULL,
  `is_used` tinyint(1) DEFAULT 0,
  `attempt_count` tinyint(4) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `otps`
--

INSERT INTO `otps` (`id`, `phone`, `email`, `otp`, `purpose`, `is_used`, `attempt_count`, `created_at`, `expires_at`) VALUES
(54, '8801778318921', '<EMAIL>', '503351', 'registration', 1, 1, '2025-05-21 07:12:00', '2025-05-21 07:22:00'),
(55, '8801303565074', '<EMAIL>', '379951', 'registration', 1, 1, '2025-05-24 02:36:01', '2025-05-24 02:46:01');

-- --------------------------------------------------------

--
-- Table structure for table `otp_logs`
--

CREATE TABLE `otp_logs` (
  `id` int(11) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('sent','failed','verified','expired') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `otp_logs`
--

INSERT INTO `otp_logs` (`id`, `phone`, `ip_address`, `created_at`, `status`) VALUES
(126, '8801778318921', '*************', '2025-05-21 06:03:17', 'sent'),
(127, '8801778318921', '*************', '2025-05-21 06:03:27', 'verified'),
(128, '8801778318921', '*************', '2025-05-21 06:03:52', 'expired'),
(129, '8801303565074', '*************', '2025-05-21 07:09:06', 'sent'),
(130, '8801303565074', '*************', '2025-05-21 07:09:42', 'verified'),
(131, '8801303565074', '*************', '2025-05-21 07:10:01', 'expired'),
(132, '8801778318921', '*************', '2025-05-21 07:12:00', 'sent'),
(133, '8801778318921', '*************', '2025-05-21 07:12:29', 'verified'),
(134, '8801778318921', '*************', '2025-05-21 07:12:48', 'expired'),
(135, '8801303565074', '*************', '2025-05-24 02:36:01', 'sent'),
(136, '8801303565074', '*************', '2025-05-24 02:36:21', 'verified'),
(137, '8801303565074', '*************', '2025-05-24 02:37:00', 'expired');

-- --------------------------------------------------------

--
-- Table structure for table `promo_codes`
--

CREATE TABLE `promo_codes` (
  `id` int(11) NOT NULL,
  `code` varchar(20) NOT NULL,
  `discount_type` enum('percentage','fixed') NOT NULL DEFAULT 'percentage',
  `discount_value` decimal(10,2) NOT NULL,
  `min_order_value` decimal(10,2) NOT NULL DEFAULT 0.00,
  `max_discount` decimal(10,2) DEFAULT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `usage_limit` int(11) DEFAULT NULL,
  `usage_count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `promo_codes`
--

INSERT INTO `promo_codes` (`id`, `code`, `discount_type`, `discount_value`, `min_order_value`, `max_discount`, `start_date`, `end_date`, `is_active`, `usage_limit`, `usage_count`, `created_at`, `updated_at`) VALUES
(1, 'SUMMER2025', 'fixed', '3.00', '3.00', NULL, '2025-05-21 19:33:00', '2025-06-20 19:33:00', 1, 3, 2, '2025-05-21 17:33:56', '2025-05-21 20:54:37');

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `bn_name` varchar(100) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `bn_description` text DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `name`, `bn_name`, `description`, `bn_description`, `image_url`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(2, 'Dry Cleaning', 'ড্রাই ক্লিনিং', 'Professional dry cleaning for delicate fabrics', NULL, NULL, 1, 2, '2025-05-21 17:03:36', '2025-05-21 17:03:36'),
(3, 'Ironing', 'ইস্ত্রি', 'Ironing service for all types of clothes', NULL, NULL, 1, 3, '2025-05-21 17:03:36', '2025-05-21 17:03:36'),
(5, 'Stain Removal', 'দাগ অপসারণ', 'Specialized stain removal service', NULL, NULL, 1, 5, '2025-05-21 17:03:36', '2025-05-21 17:03:36'),
(6, 'Wash & Fold', 'ধোয়া ও ভাঁজ করা', 'test', 'test', 'https://c8.alamy.com/comp/MTAT3N/iron-on-ironing-board-on-home-interior-background-MTAT3N.jpg', 1, 0, '2025-05-22 23:25:14', '2025-05-22 23:25:14');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'otp_enabled', '1', 'Enable or disable OTP verification (0 = disabled, 1 = enabled)', '2025-05-08 19:20:40', '2025-05-24 02:35:12'),
(2, 'app_name', 'OTP Management System', 'Application name', '2025-05-08 19:20:40', '2025-05-08 19:20:40'),
(3, 'app_version', '1.0.0', 'Application version', '2025-05-08 19:20:40', '2025-05-08 19:20:40'),
(4, 'otp_length', '6', 'Length of OTP', '2025-05-08 19:20:40', '2025-05-08 19:20:40'),
(5, 'otp_expiry', '600', 'OTP expiry time in seconds (10 minutes)', '2025-05-08 19:20:40', '2025-05-08 19:20:40'),
(6, 'otp_max_attempts', '3', 'Maximum verification attempts', '2025-05-08 19:20:40', '2025-05-08 19:20:40'),
(7, 'otp_daily_limit', '5', 'Maximum OTPs per day per user', '2025-05-08 19:20:40', '2025-05-08 19:20:40'),
(8, 'profile_picture_max_size', '2097152', 'Maximum profile picture size in bytes (2MB)', '2025-05-08 20:22:19', '2025-05-08 20:22:19'),
(9, 'profile_picture_allowed_types', 'jpg,jpeg,png', 'Allowed profile picture file types', '2025-05-08 20:22:19', '2025-05-08 20:22:19'),
(10, 'profile_picture_path', 'uploads/profile_pictures/', 'Path to store profile pictures', '2025-05-08 20:22:19', '2025-05-08 20:22:19'),
(1698, 'admin_whatsapp', '+8801303565075', 'WhatsApp number for admin contact when OTP is disabled', '2025-05-09 12:18:35', '2025-05-09 17:23:00'),
(1711, 'min_password_length', '6', NULL, '2025-05-09 17:19:35', '2025-05-09 17:26:19'),
(1720, 'delivery_fee', '11', 'Default delivery fee in BDT', '2025-05-22 22:17:26', '2025-05-22 22:36:50');

-- --------------------------------------------------------

--
-- Table structure for table `settings_history`
--

CREATE TABLE `settings_history` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `old_value` text DEFAULT NULL,
  `new_value` text DEFAULT NULL,
  `admin_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings_history`
--

INSERT INTO `settings_history` (`id`, `setting_key`, `old_value`, `new_value`, `admin_id`, `created_at`) VALUES
(1, 'otp_enabled', '1', '0', 1, '2025-05-09 17:19:35'),
(2, 'min_password_length', NULL, '8', 1, '2025-05-09 17:19:35'),
(3, 'min_password_length', '8', '6', 1, '2025-05-09 17:21:26'),
(4, 'admin_whatsapp', '+8801303565074', '+8801303565075', 1, '2025-05-09 17:23:00'),
(5, 'min_password_length', '6', '7', 1, '2025-05-09 17:26:15'),
(6, 'min_password_length', '7', '6', 1, '2025-05-09 17:26:19'),
(7, 'otp_enabled', '0', '1', 1, '2025-05-21 06:03:15'),
(8, 'otp_enabled', '1', '0', 1, '2025-05-21 06:06:49'),
(9, 'otp_enabled', '0', '1', 1, '2025-05-21 06:46:42'),
(10, 'otp_enabled', '1', '0', 1, '2025-05-22 16:43:16'),
(11, 'delivery_fee', '50.00', '11', 1, '2025-05-22 22:36:50'),
(12, 'otp_enabled', '0', '1', 1, '2025-05-24 02:35:12');

-- --------------------------------------------------------

--
-- Table structure for table `upazillas`
--

CREATE TABLE `upazillas` (
  `id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `bn_name` varchar(50) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `upazillas`
--

INSERT INTO `upazillas` (`id`, `district_id`, `name`, `bn_name`, `created_at`) VALUES
(325, 41, 'শ্রীপুর', NULL, '2025-05-21 07:10:01');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `phone` varchar(15) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `division_id` int(11) DEFAULT NULL,
  `district_id` int(11) DEFAULT NULL,
  `upazilla_id` int(11) DEFAULT NULL,
  `is_verified` tinyint(1) DEFAULT 0,
  `profile_picture_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `phone`, `email`, `password`, `address`, `division_id`, `district_id`, `upazilla_id`, `is_verified`, `profile_picture_url`, `created_at`, `updated_at`) VALUES
(1, 'Admin User', '+8801712345678', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'House 123, Road 456', 1, 1, 1, 1, NULL, '2025-05-08 19:20:40', '2025-05-08 19:20:40'),
(19, 'test', '8801778318921', '<EMAIL>', '$2y$10$4BzNUTtS.yuNdSvPEkfPAOhwjUgkHdTphC1LpxLphlKLkP54JB5ui', 'test', 6, 41, 325, 1, 'uploads/profile_pictures/profile_682f290333110.jpg', '2025-05-21 07:12:48', '2025-05-22 13:39:15'),
(20, 'Sadrul hasan dider', '8801303565074', '<EMAIL>', '$2y$10$8/txg4hr9xJOlle60SsYuO26gT.ssRPfzMXgjESe3RttB.JA6Z1ma', 'Beldia', 6, 41, 325, 1, NULL, '2025-05-24 02:37:00', '2025-05-24 02:37:00');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `action` (`action`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type` (`type`),
  ADD KEY `is_read` (`is_read`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `username_2` (`username`),
  ADD KEY `email_2` (`email`);

--
-- Indexes for table `deleted_users`
--
ALTER TABLE `deleted_users`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `phone` (`phone`),
  ADD KEY `email` (`email`),
  ADD KEY `deleted_at` (`deleted_at`);

--
-- Indexes for table `delivery_personnel`
--
ALTER TABLE `delivery_personnel`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `is_active` (`is_active`),
  ADD KEY `is_available` (`is_available`);

--
-- Indexes for table `districts`
--
ALTER TABLE `districts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `division_id` (`division_id`);

--
-- Indexes for table `divisions`
--
ALTER TABLE `divisions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `fcm_tokens`
--
ALTER TABLE `fcm_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_device` (`user_id`,`device_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `is_active` (`is_active`);

--
-- Indexes for table `items`
--
ALTER TABLE `items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `service_id` (`service_id`),
  ADD KEY `is_active` (`is_active`),
  ADD KEY `in_stock` (`in_stock`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `type` (`type`),
  ADD KEY `is_read` (`is_read`),
  ADD KEY `idx_image_url` (`image_url`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `order_number` (`order_number`),
  ADD UNIQUE KEY `tracking_number` (`tracking_number`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `delivery_personnel_id` (`delivery_personnel_id`),
  ADD KEY `promo_code_id` (`promo_code_id`),
  ADD KEY `status` (`status`),
  ADD KEY `payment_status` (`payment_status`),
  ADD KEY `pickup_date` (`pickup_date`),
  ADD KEY `delivery_date` (`delivery_date`),
  ADD KEY `idx_orders_transaction_id` (`transaction_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `item_id` (`item_id`);

--
-- Indexes for table `order_status_history`
--
ALTER TABLE `order_status_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `status` (`status`),
  ADD KEY `updated_by` (`updated_by`);

--
-- Indexes for table `otps`
--
ALTER TABLE `otps`
  ADD PRIMARY KEY (`id`),
  ADD KEY `phone` (`phone`),
  ADD KEY `email` (`email`),
  ADD KEY `expires_at` (`expires_at`);

--
-- Indexes for table `otp_logs`
--
ALTER TABLE `otp_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `phone` (`phone`),
  ADD KEY `ip_address` (`ip_address`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `promo_codes`
--
ALTER TABLE `promo_codes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`),
  ADD KEY `is_active` (`is_active`),
  ADD KEY `start_date` (`start_date`),
  ADD KEY `end_date` (`end_date`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `is_active` (`is_active`),
  ADD KEY `sort_order` (`sort_order`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `settings_history`
--
ALTER TABLE `settings_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `setting_key` (`setting_key`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `upazillas`
--
ALTER TABLE `upazillas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `district_id` (`district_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `phone` (`phone`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `phone_2` (`phone`),
  ADD KEY `email_2` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_logs`
--
ALTER TABLE `admin_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=142;

--
-- AUTO_INCREMENT for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `deleted_users`
--
ALTER TABLE `deleted_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `delivery_personnel`
--
ALTER TABLE `delivery_personnel`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `districts`
--
ALTER TABLE `districts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `divisions`
--
ALTER TABLE `divisions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `fcm_tokens`
--
ALTER TABLE `fcm_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `items`
--
ALTER TABLE `items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2046145992;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=84;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=106;

--
-- AUTO_INCREMENT for table `order_status_history`
--
ALTER TABLE `order_status_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=179;

--
-- AUTO_INCREMENT for table `otps`
--
ALTER TABLE `otps`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=56;

--
-- AUTO_INCREMENT for table `otp_logs`
--
ALTER TABLE `otp_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=138;

--
-- AUTO_INCREMENT for table `promo_codes`
--
ALTER TABLE `promo_codes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1723;

--
-- AUTO_INCREMENT for table `settings_history`
--
ALTER TABLE `settings_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `upazillas`
--
ALTER TABLE `upazillas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=326;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_logs`
--
ALTER TABLE `admin_logs`
  ADD CONSTRAINT `admin_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `districts`
--
ALTER TABLE `districts`
  ADD CONSTRAINT `districts_ibfk_1` FOREIGN KEY (`division_id`) REFERENCES `divisions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `fcm_tokens`
--
ALTER TABLE `fcm_tokens`
  ADD CONSTRAINT `fcm_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `items`
--
ALTER TABLE `items`
  ADD CONSTRAINT `items_ibfk_1` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`delivery_personnel_id`) REFERENCES `delivery_personnel` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`promo_code_id`) REFERENCES `promo_codes` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`);

--
-- Constraints for table `order_status_history`
--
ALTER TABLE `order_status_history`
  ADD CONSTRAINT `order_status_history_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `settings_history`
--
ALTER TABLE `settings_history`
  ADD CONSTRAINT `settings_history_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `upazillas`
--
ALTER TABLE `upazillas`
  ADD CONSTRAINT `upazillas_ibfk_1` FOREIGN KEY (`district_id`) REFERENCES `districts` (`id`) ON DELETE CASCADE;

DELIMITER $$
--
-- Events
--
CREATE DEFINER=`root`@`localhost` EVENT `purge_deleted_users` ON SCHEDULE EVERY 1 DAY STARTS '2025-05-09 22:06:12' ON COMPLETION NOT PRESERVE ENABLE DO BEGIN
    DELETE FROM deleted_users
    WHERE deleted_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
END$$

DELIMITER ;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
