package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;
import com.mdsadrulhasan.gogolaundry.model.Service;
import com.mdsadrulhasan.gogolaundry.repository.ServiceRepository;
import com.mdsadrulhasan.gogolaundry.utils.Resource;

import java.util.ArrayList;
import java.util.List;

/**
 * ViewModel for services screen
 */
public class ServicesViewModel extends AndroidViewModel {

    private static final String TAG = "ServicesViewModel";

    private final ServiceRepository serviceRepository;
    private final MutableLiveData<Boolean> forceRefresh = new MutableLiveData<>(false);
    private LiveData<Resource<List<ServiceEntity>>> servicesData;

    public ServicesViewModel(@NonNull Application application) {
        super(application);
        serviceRepository = ServiceRepository.getInstance();

        // Initialize services data
        servicesData = Transformations.switchMap(forceRefresh, refresh ->
            serviceRepository.getAllActiveServices(refresh));
    }

    /**
     * Get services LiveData
     *
     * @return LiveData of services resource
     */
    public LiveData<Resource<List<ServiceEntity>>> getServices() {
        return servicesData;
    }

    /**
     * Refresh services data
     */
    public void refreshServices() {
        forceRefresh.setValue(true);
    }

    /**
     * Load services data
     * Alias for refreshServices for better readability
     */
    public void loadServices() {
        refreshServices();
    }

    /**
     * Convert ServiceEntity to Service model (if needed for UI)
     *
     * @param serviceEntity ServiceEntity from database
     * @return Service model for UI
     */
    private Service convertToServiceModel(ServiceEntity serviceEntity) {
        // This is a placeholder. In a real app, you would map the entity to your model
        // For now, we'll return null since we're using the entity directly
        return null;
    }

    /**
     * Convert list of ServiceEntity to list of Service models (if needed for UI)
     *
     * @param serviceEntities List of ServiceEntity from database
     * @return List of Service models for UI
     */
    private List<Service> convertToServiceModels(List<ServiceEntity> serviceEntities) {
        // This is a placeholder. In a real app, you would map the entities to your models
        // For now, we'll return an empty list since we're using the entities directly
        return new ArrayList<>();
    }
}
