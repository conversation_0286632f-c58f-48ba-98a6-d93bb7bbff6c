package com.mdsadrulhasan.gogolaundry.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model class for order items
 */
public class OrderItem {

    @SerializedName("id")
    private int id;

    @SerializedName("order_id")
    private int orderId;

    @SerializedName("item_id")
    private int serviceId;

    @SerializedName("name")
    private String serviceName;

    @SerializedName("quantity")
    private double quantity;

    @SerializedName("unit")
    private String unit; // "kg" or "piece"

    @SerializedName("price")
    private double price;

    @SerializedName("subtotal")
    private double subtotal;

    @SerializedName("created_at")
    private String createdAt;

    @SerializedName("updated_at")
    private String updatedAt;

    @SerializedName("image_url")
    private String imageUrl;

    @SerializedName("service_name")
    private String serviceBnName;

    // Default constructor
    public OrderItem() {
    }

    // Constructor with all fields
    public OrderItem(int id, int orderId, int serviceId, String serviceName, double quantity,
                    String unit, double price, double subtotal, String createdAt, String updatedAt,
                    String imageUrl, String serviceBnName) {
        this.id = id;
        this.orderId = orderId;
        this.serviceId = serviceId;
        this.serviceName = serviceName;
        this.quantity = quantity;
        this.unit = unit;
        this.price = price;
        this.subtotal = subtotal;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.imageUrl = imageUrl;
        this.serviceBnName = serviceBnName;
    }

    // Getters and setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getServiceId() {
        return serviceId;
    }

    public void setServiceId(int serviceId) {
        this.serviceId = serviceId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(double subtotal) {
        this.subtotal = subtotal;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getServiceBnName() {
        return serviceBnName;
    }

    public void setServiceBnName(String serviceBnName) {
        this.serviceBnName = serviceBnName;
    }

    // Helper method to get formatted quantity with unit
    public String getFormattedQuantity() {
        return quantity + " " + unit;
    }

    // Helper method to get formatted price
    public String getFormattedPrice() {
        return "৳" + price + "/" + unit;
    }

    // Helper method to get formatted subtotal
    public String getFormattedSubtotal() {
        return "৳" + subtotal;
    }
}
