<?php
/**
 * Restore Deleted Account Page
 *
 * This page handles restoring a deleted user account
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/UserManager.php';

// Initialize user manager
$userManager = new UserManager($pdo);

// Verify CSRF token
if (!isset($_GET['csrf_token']) || !$adminManager->verifyCsrfToken($_GET['csrf_token'])) {
    $_SESSION['error_message'] = 'Invalid request. Please try again.';
    header('Location: deleted_accounts.php');
    exit;
}

// Get user ID
$userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($userId <= 0) {
    $_SESSION['error_message'] = 'Invalid user ID.';
    header('Location: deleted_accounts.php');
    exit;
}

// Get deleted user
$user = $userManager->getDeletedUserById($userId);

if (!$user) {
    $_SESSION['error_message'] = 'User not found.';
    header('Location: deleted_accounts.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !$adminManager->verifyCsrfToken($_POST['csrf_token'])) {
        $_SESSION['error_message'] = 'Invalid request. Please try again.';
        header('Location: deleted_accounts.php');
        exit;
    }
    
    // Restore user
    $result = $userManager->restoreDeletedUser($userId);
    
    if ($result) {
        // Log action
        $adminManager->logAdminAction(
            $adminData['id'],
            'account_restore',
            'Restored deleted account for user ID: ' . $userId . ' (' . $user['full_name'] . ')',
            getClientIp()
        );
        
        $_SESSION['success_message'] = 'Account restored successfully.';
        header('Location: deleted_accounts.php');
        exit;
    } else {
        $_SESSION['error_message'] = 'Failed to restore account. The user may already exist or there was a database error.';
    }
}

// Page title and breadcrumbs
$pageTitle = 'Restore Account';
$breadcrumbs = [
    'Deleted Accounts' => 'deleted_accounts.php',
    'Restore Account' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Restore Deleted Account</h1>
            <a href="deleted_accounts.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Deleted Accounts
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 col-xl-6">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0">Confirm Account Restoration</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> You are about to restore a deleted user account. This will move the user data back to the active users table.
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted mb-3">User Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Name:</strong> <?php echo htmlspecialchars($user['full_name']); ?></p>
                            <p><strong>Phone:</strong> <?php echo htmlspecialchars($user['phone']); ?></p>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($user['email'] ?: 'N/A'); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>User ID:</strong> <?php echo $user['user_id']; ?></p>
                            <p><strong>Deleted On:</strong> <?php echo (new DateTime($user['deleted_at']))->format('M d, Y H:i'); ?></p>
                            <?php
                                $deletedDate = new DateTime($user['deleted_at']);
                                $expiryDate = clone $deletedDate;
                                $expiryDate->modify('+30 days');
                                $now = new DateTime();
                                $daysLeft = $now->diff($expiryDate)->days;
                                $daysLeftClass = $daysLeft < 7 ? 'danger' : ($daysLeft < 15 ? 'warning' : 'info');
                            ?>
                            <p>
                                <strong>Days Until Permanent Deletion:</strong>
                                <span class="badge bg-<?php echo $daysLeftClass; ?>"><?php echo $daysLeft; ?> days</span>
                            </p>
                        </div>
                    </div>
                </div>
                
                <form method="post" action="restore_account.php?id=<?php echo $userId; ?>&csrf_token=<?php echo $_SESSION['csrf_token']; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <div class="d-flex justify-content-between">
                        <a href="deleted_accounts.php" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-trash-restore me-2"></i> Restore Account
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
