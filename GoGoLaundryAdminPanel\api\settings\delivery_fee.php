<?php
/**
 * Get Delivery Fee API Endpoint
 *
 * This endpoint returns the current delivery fee setting
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/SettingsManager.php';

// Initialize settings manager
$settingsManager = new SettingsManager($pdo);

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get delivery fee from settings
$deliveryFee = (float)$settingsManager->getSetting('delivery_fee', 50.00);

// Return success response
jsonResponse(true, 'Delivery fee retrieved successfully', [
    'delivery_fee' => $deliveryFee
]);
