<?php
/**
 * Login API Endpoint
 *
 * This endpoint handles user login with password
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/UserManager.php';
require_once '../includes/SettingsManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Log the input data for debugging
error_log('Login attempt with data: ' . json_encode($inputData));

// Validate input
if (empty($inputData['phone'])) {
    jsonResponse(false, 'Phone number is required', [], 400);
}

if (empty($inputData['password'])) {
    jsonResponse(false, 'Password is required', [], 400);
}

$phone = sanitize($inputData['phone']);
$password = $inputData['password']; // Don't sanitize password

// Log the formatted phone number
$formattedPhone = formatPhone($phone);
error_log('Formatted phone for login: ' . $formattedPhone);

// Validate phone number
if (!validatePhone($phone)) {
    jsonResponse(false, 'Invalid phone number format', [], 400);
}

// Initialize managers
$userManager = new UserManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Check if user exists first
$userExists = $userManager->userExistsByPhone($phone);
if (!$userExists) {
    error_log('Login failed: User with phone ' . $formattedPhone . ' does not exist');
    jsonResponse(false, 'Invalid phone number or password', [], 401);
}

// Authenticate user
$authenticated = $userManager->login($phone, $password);

if (!$authenticated) {
    error_log('Login failed: Invalid password for phone ' . $formattedPhone);
    jsonResponse(false, 'Invalid phone number or password', [], 401);
}

// Get user data with location names
$user = $userManager->getUserByPhoneWithLocationNames($phone);

// Check if user is verified
if (!$user['is_verified']) {
    error_log('Login failed: User with phone ' . $formattedPhone . ' is not verified');
    jsonResponse(false, 'Account not verified. Please verify your phone number first.', [], 403);
}

// Check if OTP verification is enabled for login from database
$otpEnabled = $settingsManager->isOtpEnabled();
error_log('OTP enabled status: ' . ($otpEnabled ? 'true' : 'false'));

if ($otpEnabled) {
    // Return success but indicate OTP is required
    // Convert numeric values to appropriate types for Android
    $user['is_verified'] = (bool)$user['is_verified'];
    if (isset($user['division_id'])) $user['division_id'] = (int)$user['division_id'];
    if (isset($user['district_id'])) $user['district_id'] = (int)$user['district_id'];
    if (isset($user['upazilla_id'])) $user['upazilla_id'] = (int)$user['upazilla_id'];

    jsonResponse(true, 'Password verified. OTP verification required.', [
        'user' => $user,
        'otp_required' => true,
        'session_id' => session_id()
    ]);
} else {
    // Set session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['phone'] = $user['phone'];
    $_SESSION['is_logged_in'] = true;

    // Log session information for debugging
    error_log('Login successful for user: ' . $user['id'] . ' with phone: ' . $user['phone']);
    error_log('Session ID after login: ' . session_id());
    error_log('Session data after login: ' . json_encode($_SESSION));
    error_log('Cookies after login: ' . json_encode($_COOKIE));

    // Return success response
    // Convert numeric values to appropriate types for Android
    $user['is_verified'] = (bool)$user['is_verified'];
    if (isset($user['division_id'])) $user['division_id'] = (int)$user['division_id'];
    if (isset($user['district_id'])) $user['district_id'] = (int)$user['district_id'];
    if (isset($user['upazilla_id'])) $user['upazilla_id'] = (int)$user['upazilla_id'];

    jsonResponse(true, 'Login successful', [
        'user' => $user,
        'otp_required' => false,
        'session_id' => session_id(),
        'is_logged_in' => true,
        'auth_token' => session_id(), // Provide an auth token the app can use
        'redirect_url' => 'main_activity', // Add a redirect URL that the app might be looking for
        'next_screen' => 'main', // Another possible field the app might be checking
        'login_status' => 'complete' // Yet another possible field
    ]);
}
