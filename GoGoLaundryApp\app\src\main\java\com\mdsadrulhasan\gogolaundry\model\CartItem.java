package com.mdsadrulhasan.gogolaundry.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model class for cart items
 */
public class CartItem {
    
    @SerializedName("item_id")
    private int itemId;
    
    @SerializedName("service_id")
    private int serviceId;
    
    @SerializedName("name")
    private String name;
    
    @SerializedName("image_url")
    private String imageUrl;
    
    @SerializedName("price")
    private double price;
    
    @SerializedName("quantity")
    private double quantity;
    
    @SerializedName("subtotal")
    private double subtotal;
    
    @SerializedName("service_name")
    private String serviceName;
    
    @SerializedName("unit")
    private String unit = "piece"; // Default unit
    
    /**
     * Default constructor
     */
    public CartItem() {
    }
    
    /**
     * Constructor with all fields
     * 
     * @param itemId Item ID
     * @param serviceId Service ID
     * @param name Item name
     * @param imageUrl Image URL
     * @param price Price
     * @param quantity Quantity
     * @param serviceName Service name
     */
    public CartItem(int itemId, int serviceId, String name, String imageUrl, 
                   double price, double quantity, String serviceName) {
        this.itemId = itemId;
        this.serviceId = serviceId;
        this.name = name;
        this.imageUrl = imageUrl;
        this.price = price;
        this.quantity = quantity;
        this.serviceName = serviceName;
        updateSubtotal();
    }
    
    /**
     * Update subtotal based on price and quantity
     */
    public void updateSubtotal() {
        this.subtotal = this.price * this.quantity;
    }
    
    // Getters and setters
    
    public int getItemId() {
        return itemId;
    }
    
    public void setItemId(int itemId) {
        this.itemId = itemId;
    }
    
    public int getServiceId() {
        return serviceId;
    }
    
    public void setServiceId(int serviceId) {
        this.serviceId = serviceId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public double getPrice() {
        return price;
    }
    
    public void setPrice(double price) {
        this.price = price;
        updateSubtotal();
    }
    
    public double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(double quantity) {
        this.quantity = quantity;
        updateSubtotal();
    }
    
    public double getSubtotal() {
        return subtotal;
    }
    
    public void setSubtotal(double subtotal) {
        this.subtotal = subtotal;
    }
    
    public String getServiceName() {
        return serviceName;
    }
    
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    /**
     * Get formatted price
     * 
     * @return Formatted price
     */
    public String getFormattedPrice() {
        return "৳" + price + "/" + unit;
    }
    
    /**
     * Get formatted subtotal
     * 
     * @return Formatted subtotal
     */
    public String getFormattedSubtotal() {
        return "৳" + subtotal;
    }
    
    /**
     * Get formatted quantity
     * 
     * @return Formatted quantity
     */
    public String getFormattedQuantity() {
        return quantity + " " + unit;
    }
}
