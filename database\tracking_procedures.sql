-- GoGoLaundry Tracking System Stored Procedures
-- This file contains stored procedures and functions for the order tracking system

DELIMITER $$

-- --------------------------------------------------------
--
-- Procedure to create a new order
--

CREATE PROCEDURE `create_order`(
    IN p_user_id INT,
    IN p_promo_code VARCHAR(20),
    IN p_payment_method ENUM('cash','card','mobile_banking'),
    IN p_pickup_address VARCHAR(255),
    IN p_pickup_division_id INT,
    IN p_pickup_district_id INT,
    IN p_pickup_upazilla_id INT,
    IN p_pickup_date DATE,
    IN p_pickup_time_slot VARCHAR(50),
    IN p_delivery_address VARCHAR(255),
    IN p_delivery_division_id INT,
    IN p_delivery_district_id INT,
    IN p_delivery_upazilla_id INT,
    IN p_notes TEXT,
    OUT p_order_id INT,
    OUT p_order_number VARCHAR(20),
    OUT p_tracking_number VARCHAR(20)
)
BEGIN
    DECLARE v_promo_code_id INT DEFAULT NULL;
    DECLARE v_discount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_delivery_fee DECIMAL(10,2) DEFAULT 50.00; -- Default delivery fee
    DECLARE v_subtotal DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_total DECIMAL(10,2) DEFAULT 0.00;

    -- Generate unique order number and tracking number
    SET p_order_number = CONCAT('ORD', DATE_FORMAT(NOW(), '%y%m%d'), LPAD(FLOOR(RAND() * 10000), 4, '0'));
    SET p_tracking_number = CONCAT('TRK', DATE_FORMAT(NOW(), '%y%m%d'), LPAD(FLOOR(RAND() * 10000), 4, '0'));

    -- Check if promo code exists and is valid
    IF p_promo_code IS NOT NULL AND p_promo_code != '' THEN
        SELECT id, discount_type, discount_value, min_order_value, max_discount
        INTO v_promo_code_id, @discount_type, @discount_value, @min_order_value, @max_discount
        FROM promo_codes
        WHERE code = p_promo_code
        AND is_active = 1
        AND start_date <= NOW()
        AND end_date >= NOW()
        AND (usage_limit IS NULL OR usage_count < usage_limit);
    END IF;

    -- Insert the order
    INSERT INTO orders (
        order_number,
        tracking_number,
        user_id,
        promo_code_id,
        subtotal,
        discount,
        delivery_fee,
        total,
        payment_method,
        status,
        pickup_address,
        pickup_division_id,
        pickup_district_id,
        pickup_upazilla_id,
        pickup_date,
        pickup_time_slot,
        delivery_address,
        delivery_division_id,
        delivery_district_id,
        delivery_upazilla_id,
        notes
    ) VALUES (
        p_order_number,
        p_tracking_number,
        p_user_id,
        v_promo_code_id,
        v_subtotal,
        v_discount,
        v_delivery_fee,
        v_total,
        p_payment_method,
        'placed',
        p_pickup_address,
        p_pickup_division_id,
        p_pickup_district_id,
        p_pickup_upazilla_id,
        p_pickup_date,
        p_pickup_time_slot,
        p_delivery_address,
        p_delivery_division_id,
        p_delivery_district_id,
        p_delivery_upazilla_id,
        p_notes
    );

    -- Get the inserted order ID
    SET p_order_id = LAST_INSERT_ID();

    -- If promo code was used, increment its usage count
    IF v_promo_code_id IS NOT NULL THEN
        UPDATE promo_codes
        SET usage_count = usage_count + 1
        WHERE id = v_promo_code_id;
    END IF;

    -- Create notification for the user
    INSERT INTO notifications (
        user_id,
        order_id,
        title,
        message,
        type
    ) VALUES (
        p_user_id,
        p_order_id,
        'Order Placed',
        CONCAT('Your order #', p_order_number, ' has been placed successfully. Track your order with tracking number: ', p_tracking_number),
        'order_status'
    );
END$$

-- --------------------------------------------------------
--
-- Procedure to add items to an order
--

CREATE PROCEDURE `add_order_item`(
    IN p_order_id INT,
    IN p_item_id INT,
    IN p_quantity INT,
    IN p_notes TEXT
)
BEGIN
    DECLARE v_price DECIMAL(10,2);
    DECLARE v_subtotal DECIMAL(10,2);
    DECLARE v_order_subtotal DECIMAL(10,2);
    DECLARE v_discount DECIMAL(10,2);
    DECLARE v_delivery_fee DECIMAL(10,2);
    DECLARE v_total DECIMAL(10,2);
    DECLARE v_promo_code_id INT;
    DECLARE v_discount_type ENUM('percentage','fixed');
    DECLARE v_discount_value DECIMAL(10,2);
    DECLARE v_min_order_value DECIMAL(10,2);
    DECLARE v_max_discount DECIMAL(10,2);

    -- Get the item price
    SELECT price INTO v_price FROM items WHERE id = p_item_id;

    -- Calculate subtotal for this item
    SET v_subtotal = v_price * p_quantity;

    -- Insert the order item
    INSERT INTO order_items (
        order_id,
        item_id,
        quantity,
        price,
        subtotal,
        notes
    ) VALUES (
        p_order_id,
        p_item_id,
        p_quantity,
        v_price,
        v_subtotal,
        p_notes
    );

    -- Get the order's promo code details
    SELECT promo_code_id, delivery_fee INTO v_promo_code_id, v_delivery_fee
    FROM orders WHERE id = p_order_id;

    IF v_promo_code_id IS NOT NULL THEN
        SELECT discount_type, discount_value, min_order_value, max_discount
        INTO v_discount_type, v_discount_value, v_min_order_value, v_max_discount
        FROM promo_codes WHERE id = v_promo_code_id;
    END IF;

    -- Calculate new order subtotal
    SELECT SUM(subtotal) INTO v_order_subtotal
    FROM order_items WHERE order_id = p_order_id;

    -- Calculate discount based on promo code
    IF v_promo_code_id IS NOT NULL AND v_order_subtotal >= v_min_order_value THEN
        IF v_discount_type = 'percentage' THEN
            SET v_discount = v_order_subtotal * (v_discount_value / 100);
            IF v_max_discount IS NOT NULL AND v_discount > v_max_discount THEN
                SET v_discount = v_max_discount;
            END IF;
        ELSE -- fixed discount
            SET v_discount = v_discount_value;
        END IF;
    ELSE
        SET v_discount = 0;
    END IF;

    -- Calculate total
    SET v_total = v_order_subtotal - v_discount + v_delivery_fee;

    -- Update the order with new totals
    UPDATE orders
    SET subtotal = v_order_subtotal,
        discount = v_discount,
        total = v_total
    WHERE id = p_order_id;
END$$

-- --------------------------------------------------------
--
-- Procedure to update order status
--

CREATE PROCEDURE `update_order_status`(
    IN p_order_id INT,
    IN p_status ENUM('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled'),
    IN p_notes TEXT,
    IN p_updated_by INT,
    IN p_updated_by_type ENUM('admin','user','system','delivery_personnel'),
    IN p_delivery_personnel_id INT
)
BEGIN
    DECLARE v_user_id INT;
    DECLARE v_order_number VARCHAR(20);
    DECLARE v_tracking_number VARCHAR(20);
    DECLARE v_old_status ENUM('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled');

    -- Get current order details
    SELECT user_id, order_number, tracking_number, status
    INTO v_user_id, v_order_number, v_tracking_number, v_old_status
    FROM orders WHERE id = p_order_id;

    -- Update order status
    UPDATE orders
    SET status = p_status,
        delivery_personnel_id = CASE
            WHEN p_delivery_personnel_id IS NOT NULL THEN p_delivery_personnel_id
            ELSE delivery_personnel_id
        END,
        updated_at = NOW()
    WHERE id = p_order_id;

    -- Insert into status history
    INSERT INTO order_status_history (
        order_id,
        status,
        notes,
        updated_by,
        updated_by_type
    ) VALUES (
        p_order_id,
        p_status,
        p_notes,
        p_updated_by,
        p_updated_by_type
    );

    -- Create notification for the user
    INSERT INTO notifications (
        user_id,
        order_id,
        title,
        message,
        type,
        fcm_sent,
        sms_sent
    ) VALUES (
        v_user_id,
        p_order_id,
        CONCAT('Order ', REPLACE(LOWER(p_status), '_', ' ')),
        CASE p_status
            WHEN 'confirmed' THEN CONCAT('Your order #', v_order_number, ' has been confirmed. We will pick up your items soon.')
            WHEN 'pickup_scheduled' THEN CONCAT('Pickup for your order #', v_order_number, ' has been scheduled. Our delivery person will arrive at the scheduled time.')
            WHEN 'picked_up' THEN CONCAT('Your items for order #', v_order_number, ' have been picked up and are on their way to our facility.')
            WHEN 'processing' THEN CONCAT('Your order #', v_order_number, ' is now being processed at our facility.')
            WHEN 'ready_for_delivery' THEN CONCAT('Your order #', v_order_number, ' is ready for delivery. We will deliver it soon.')
            WHEN 'out_for_delivery' THEN CONCAT('Your order #', v_order_number, ' is out for delivery and will arrive shortly.')
            WHEN 'delivered' THEN CONCAT('Your order #', v_order_number, ' has been delivered. Thank you for using our service!')
            WHEN 'cancelled' THEN CONCAT('Your order #', v_order_number, ' has been cancelled. Please contact support for more information.')
            ELSE CONCAT('Your order #', v_order_number, ' status has been updated to: ', REPLACE(LOWER(p_status), '_', ' '))
        END,
        'order_status',
        0, -- fcm_sent
        0  -- sms_sent
    );
END$$

-- --------------------------------------------------------
--
-- Function to get order status history
--

CREATE FUNCTION `get_order_status_count`(
    p_status ENUM('placed','confirmed','pickup_scheduled','picked_up','processing','ready_for_delivery','out_for_delivery','delivered','cancelled'),
    p_start_date DATE,
    p_end_date DATE
) RETURNS INT
DETERMINISTIC
BEGIN
    DECLARE v_count INT;

    SELECT COUNT(*) INTO v_count
    FROM orders
    WHERE status = p_status
    AND DATE(created_at) BETWEEN p_start_date AND p_end_date;

    RETURN v_count;
END$$

-- --------------------------------------------------------
--
-- Function to calculate average delivery time
--

CREATE FUNCTION `calculate_avg_delivery_time`(
    p_start_date DATE,
    p_end_date DATE
) RETURNS DECIMAL(10,2)
DETERMINISTIC
BEGIN
    DECLARE v_avg_hours DECIMAL(10,2);

    SELECT AVG(TIMESTAMPDIFF(HOUR, o.created_at, h.created_at)) INTO v_avg_hours
    FROM orders o
    JOIN order_status_history h ON o.id = h.order_id
    WHERE h.status = 'delivered'
    AND DATE(o.created_at) BETWEEN p_start_date AND p_end_date;

    RETURN v_avg_hours;
END$$

DELIMITER ;
