<?php
/**
 * Service Manager Class
 *
 * This class handles laundry service operations
 */

class ServiceManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get total number of services
     *
     * @return int Total number of services
     */
    public function getTotalServices() {
        $stmt = $this->pdo->query("SELECT COUNT(*) FROM services");
        return (int)$stmt->fetchColumn();
    }

    /**
     * Get total number of active services
     *
     * @return int Total number of active services
     */
    public function getTotalActiveServices() {
        $stmt = $this->pdo->query("SELECT COUNT(*) FROM services WHERE is_active = 1");
        return (int)$stmt->fetchColumn();
    }

    /**
     * Get all services with pagination and filtering
     *
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param string $search Search term
     * @return array Services and pagination data
     */
    public function getAllServices($page = 1, $perPage = 10, $search = '') {
        // Calculate offset
        $offset = ($page - 1) * $perPage;

        // Base query
        $query = "
            SELECT SQL_CALC_FOUND_ROWS *
            FROM services
            WHERE 1=1
        ";
        $params = [];

        // Add search condition if provided
        if (!empty($search)) {
            $query .= " AND (name LIKE ? OR description LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }

        // Add order by and limit
        $query .= " ORDER BY name ASC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $perPage;

        // Execute query
        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);
        $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Get total count
        $stmt = $this->pdo->query("SELECT FOUND_ROWS()");
        $totalCount = $stmt->fetchColumn();

        // Calculate pagination data
        $totalPages = ceil($totalCount / $perPage);

        return [
            'services' => $services,
            'pagination' => [
                'current' => $page,
                'total' => $totalPages,
                'count' => $totalCount,
                'perPage' => $perPage
            ]
        ];
    }

    /**
     * Get service by ID
     *
     * @param int $serviceId Service ID
     * @return array|bool Service data or false if not found
     */
    public function getServiceById($serviceId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM services
            WHERE id = ?
        ");
        $stmt->execute([$serviceId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Add a new service
     *
     * @param string $name Service name
     * @param string $bnName Bengali service name (optional)
     * @param string $description Service description
     * @param string $bnDescription Bengali service description (optional)
     * @param string $imageUrl Image URL (optional)
     * @param bool $isActive Service status
     * @param int $sortOrder Sort order (optional)
     * @return int|bool New service ID or false on failure
     */
    public function addService($name, $description, $isActive = true, $bnName = null, $bnDescription = null, $imageUrl = null, $sortOrder = 0) {
        $stmt = $this->pdo->prepare("
            INSERT INTO services (name, bn_name, description, bn_description, image_url, is_active, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $name,
            $bnName,
            $description,
            $bnDescription,
            $imageUrl,
            $isActive ? 1 : 0,
            $sortOrder
        ]);

        if ($result) {
            return $this->pdo->lastInsertId();
        }

        return false;
    }

    /**
     * Update a service
     *
     * @param int $serviceId Service ID
     * @param string $name Service name
     * @param string $description Service description
     * @param bool $isActive Service status
     * @param string $bnName Bengali service name (optional)
     * @param string $bnDescription Bengali service description (optional)
     * @param string $imageUrl Image URL (optional)
     * @param int $sortOrder Sort order (optional)
     * @return bool Success
     */
    public function updateService($serviceId, $name, $description, $isActive, $bnName = null, $bnDescription = null, $imageUrl = null, $sortOrder = null) {
        // Get current service data for fields that are not being updated
        $currentService = $this->getServiceById($serviceId);

        // Use current values if new ones are not provided
        if ($bnName === null) $bnName = $currentService['bn_name'];
        if ($bnDescription === null) $bnDescription = $currentService['bn_description'];
        if ($imageUrl === null) $imageUrl = $currentService['image_url'];
        if ($sortOrder === null) $sortOrder = $currentService['sort_order'];

        $stmt = $this->pdo->prepare("
            UPDATE services
            SET name = ?, bn_name = ?, description = ?, bn_description = ?,
                image_url = ?, is_active = ?, sort_order = ?
            WHERE id = ?
        ");

        return $stmt->execute([
            $name,
            $bnName,
            $description,
            $bnDescription,
            $imageUrl,
            $isActive ? 1 : 0,
            $sortOrder,
            $serviceId
        ]);
    }

    /**
     * Delete a service
     *
     * @param int $serviceId Service ID
     * @return bool Success
     */
    public function deleteService($serviceId) {
        // Check if service is in use
        if ($this->isServiceInUse($serviceId)) {
            return false;
        }

        $stmt = $this->pdo->prepare("
            DELETE FROM services
            WHERE id = ?
        ");

        return $stmt->execute([$serviceId]);
    }

    /**
     * Check if a service is in use
     *
     * @param int $serviceId Service ID
     * @return bool True if service is in use
     */
    public function isServiceInUse($serviceId) {
        // Check if service is associated with any orders through items
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*)
            FROM order_items oi
            JOIN items i ON oi.item_id = i.id
            WHERE i.service_id = ?
        ");
        $stmt->execute([$serviceId]);

        return $stmt->fetchColumn() > 0;
    }

    /**
     * Get active services
     *
     * @return array Active services
     */
    public function getActiveServices() {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM services
            WHERE is_active = 1
            ORDER BY name ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
