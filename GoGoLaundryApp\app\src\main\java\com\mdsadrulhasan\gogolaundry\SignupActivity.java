package com.mdsadrulhasan.gogolaundry;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.google.gson.Gson;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.AppConfig;
import com.mdsadrulhasan.gogolaundry.api.OtpResponse;
import com.mdsadrulhasan.gogolaundry.api.UserResponse;
import com.mdsadrulhasan.gogolaundry.database.BangladeshAdminDAO;
import com.mdsadrulhasan.gogolaundry.model.District;
import com.mdsadrulhasan.gogolaundry.model.Division;
import com.mdsadrulhasan.gogolaundry.model.Upazilla;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.utils.ConfigUpdateManager;
import com.mdsadrulhasan.gogolaundry.utils.DialogUtils;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ValidationUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import cn.pedant.SweetAlert.SweetAlertDialog;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SignupActivity extends AppCompatActivity implements ConfigUpdateManager.ConfigUpdateListener {

    private static final String TAG = "SignupActivity";

    // Step 1: Basic Information
    private TextInputLayout fullNameLayout, phoneLayout, emailLayout;
    private TextInputEditText fullNameEditText, phoneEditText, emailEditText;
    private Button step1NextButton;

    // Step 2: OTP Verification
    private TextInputLayout otpLayout;
    private TextInputEditText otpEditText;
    private Button sendOtpButton, verifyOtpButton;
    private TextView otpTimerText;

    // Step 3: Password and Location
    private TextInputLayout passwordLayout, confirmPasswordLayout, addressLayout;
    private TextInputEditText passwordEditText, confirmPasswordEditText, addressEditText;
    private TextInputLayout divisionLayout, districtLayout, upazillaLayout;
    private AutoCompleteTextView divisionDropdown, districtDropdown, upazillaDropdown;
    private Button signupButton;

    // Step views
    private View step1View, step2View, step3View;

    // Common
    private TextView loginText;

    private ApiService apiService;
    private SessionManager sessionManager;
    private ConfigUpdateManager configUpdateManager;
    private CountDownTimer otpTimer;
    private BangladeshAdminDAO adminDAO;

    // Flag to track if activity is still active
    private boolean isActivityActive = true;

    // Location data
    private List<Division> allDivisions;
    private List<District> allDistricts;
    private List<Upazilla> allUpazillas;
    private Division selectedDivision;
    private District selectedDistrict;
    private Upazilla selectedUpazilla;

    // User input data
    private String fullName, phone, email, otp, password, address;
    private Integer divisionId, districtId, upazillaId;
    private String divisionName, districtName, upazillaName;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_signup);

        // Initialize API service and session manager
        apiService = ApiClient.getApiService(this);
        sessionManager = new SessionManager(this);

        // Initialize config update manager
        configUpdateManager = ConfigUpdateManager.getInstance(this);

        // Only add the listener if it's not already registered
        if (!configUpdateManager.hasListener(this)) {
            configUpdateManager.addListener(this);
            Log.d(TAG, "Added config update listener");
        } else {
            Log.d(TAG, "Config update listener already registered");
        }

        // Set a medium update interval for the signup activity (45 seconds)
        configUpdateManager.setUpdateInterval(45);

        // Force refresh the API client
        ApiClient.forceRefreshApiClient();

        // Clear cached configuration and fetch from server
        sessionManager.clearConfig();

        // Show loading dialog
        SweetAlertDialog loadingDialog = DialogUtils.showLoadingDialog(this, "Loading...");

        // Fetch configuration from server
        sessionManager.fetchConfigFromServer(new SessionManager.Callback() {
            @Override
            public void onSuccess() {
                // Dismiss loading dialog
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }

                // Log OTP status for debugging
                Log.d(TAG, "OTP verification enabled: " + sessionManager.isOtpEnabled());

                // Continue with initialization
                continueInitialization();

                // Start the config update manager
                configUpdateManager.start();
            }

            @Override
            public void onError(String errorMessage) {
                // Dismiss loading dialog
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }

                // Log error
                Log.e(TAG, "Error fetching configuration: " + errorMessage);

                // Use default configuration
                Log.d(TAG, "Using default configuration");
                Log.d(TAG, "OTP verification enabled: " + sessionManager.isOtpEnabled());

                // Continue with initialization
                continueInitialization();

                // Start the config update manager even if there was an error
                configUpdateManager.start();
            }
        });

        // Initialize database
        adminDAO = new BangladeshAdminDAO(this);
        adminDAO.open();
    }

    private void initViews() {
        // Step views
        step1View = findViewById(R.id.step1View);
        step2View = findViewById(R.id.step2View);
        step3View = findViewById(R.id.step3View);

        // Step 1: Basic Information
        fullNameLayout = findViewById(R.id.fullNameLayout);
        fullNameEditText = findViewById(R.id.fullNameEditText);
        phoneLayout = findViewById(R.id.phoneLayout);
        phoneEditText = findViewById(R.id.phoneEditText);
        emailLayout = findViewById(R.id.emailLayout);
        emailEditText = findViewById(R.id.emailEditText);
        step1NextButton = findViewById(R.id.step1NextButton);

        // Step 2: OTP Verification
        otpLayout = findViewById(R.id.otpLayout);
        otpEditText = findViewById(R.id.otpEditText);
        sendOtpButton = findViewById(R.id.sendOtpButton);
        verifyOtpButton = findViewById(R.id.verifyOtpButton);
        otpTimerText = findViewById(R.id.otpTimerText);

        // Step 3: Password and Location
        passwordLayout = findViewById(R.id.passwordLayout);
        passwordEditText = findViewById(R.id.passwordEditText);
        confirmPasswordLayout = findViewById(R.id.confirmPasswordLayout);
        confirmPasswordEditText = findViewById(R.id.confirmPasswordEditText);
        addressLayout = findViewById(R.id.addressLayout);
        addressEditText = findViewById(R.id.addressEditText);
        divisionLayout = findViewById(R.id.divisionLayout);
        divisionDropdown = findViewById(R.id.divisionDropdown);
        districtLayout = findViewById(R.id.districtLayout);
        districtDropdown = findViewById(R.id.districtDropdown);
        upazillaLayout = findViewById(R.id.upazillaLayout);
        upazillaDropdown = findViewById(R.id.upazillaDropdown);
        signupButton = findViewById(R.id.signupButton);

        // Common
        loginText = findViewById(R.id.loginText);

        // Initially show step 1
        step1View.setVisibility(View.VISIBLE);
        step2View.setVisibility(View.GONE);
        step3View.setVisibility(View.GONE);
    }

    private void setupClickListeners() {
        // Step 1: Next button
        step1NextButton.setOnClickListener(v -> {
            if (validateStep1Input()) {
                // Save input data
                fullName = fullNameEditText.getText().toString().trim();
                phone = phoneEditText.getText().toString().trim();
                email = emailEditText.getText().toString().trim();

                // Format phone number
                phone = ValidationUtils.formatPhone(phone);

                // Move to step 2
                step1View.setVisibility(View.GONE);
                step2View.setVisibility(View.VISIBLE);

                // Check if OTP is enabled
                if (!sessionManager.isOtpEnabled()) {
                    // Skip OTP verification if disabled
                    Log.d("SignupActivity", "OTP verification is disabled, skipping OTP step");
                    step2View.setVisibility(View.GONE);
                    step3View.setVisibility(View.VISIBLE);

                    // Set OTP to empty string since it won't be verified
                    otp = "";

                    // Show message that OTP verification is skipped
                    Toast.makeText(SignupActivity.this,
                            "OTP verification is disabled. You can proceed with registration.",
                            Toast.LENGTH_SHORT).show();
                } else {
                    // Send OTP automatically
                    Log.d("SignupActivity", "OTP verification is enabled, proceeding to OTP step");
                    sendOtp();
                }
            }
        });

        // Step 2: Send OTP button
        sendOtpButton.setOnClickListener(v -> sendOtp());

        // Step 2: Verify OTP button
        verifyOtpButton.setOnClickListener(v -> {
            if (validateStep2Input()) {
                // Save input data
                otp = otpEditText.getText().toString().trim();

                // Verify OTP
                verifyOtp();
            }
        });

        // Step 3: Signup button
        signupButton.setOnClickListener(v -> {
            if (validateStep3Input()) {
                // Save input data
                password = passwordEditText.getText().toString().trim();
                address = addressEditText.getText().toString().trim();

                // Register user
                registerUser();
            }
        });

        // Login text
        loginText.setOnClickListener(v -> {
            // Stop the config update manager before navigating to LoginActivity
            if (configUpdateManager != null) {
                configUpdateManager.removeListener(this);
                configUpdateManager.stop();
            }

            Intent intent = new Intent(SignupActivity.this, LoginActivity.class);
            startActivity(intent);
            finish(); // It's okay to finish here since we're going back to LoginActivity
        });
    }

    private void loadLocationData() {
        // Load divisions
        allDivisions = adminDAO.getAllDivisions();
        allDistricts = adminDAO.getAllDistricts();
        allUpazillas = adminDAO.getAllUpazillas();

        // Setup division dropdown
        List<String> divisionNames = new ArrayList<>();
        for (Division division : allDivisions) {
            divisionNames.add(division.getName());
        }

        ArrayAdapter<String> divisionAdapter = new ArrayAdapter<>(
                this, android.R.layout.simple_dropdown_item_1line, divisionNames);
        divisionDropdown.setAdapter(divisionAdapter);

        // Division selection listener
        divisionDropdown.setOnItemClickListener((parent, view, position, id) -> {
            selectedDivision = allDivisions.get(position);
            divisionId = selectedDivision.getId();
            divisionName = selectedDivision.getName();

            // Log selection for debugging
            Log.d(TAG, "Selected division: ID=" + divisionId + ", Name=" + divisionName);

            // Update district dropdown
            updateDistrictDropdown(selectedDivision.getId());

            // Clear district and upazilla selection
            districtDropdown.setText("", false);
            upazillaDropdown.setText("", false);
            selectedDistrict = null;
            selectedUpazilla = null;
            districtId = null;
            upazillaId = null;
            districtName = null;
            upazillaName = null;
        });
    }

    private void updateDistrictDropdown(int divisionId) {
        // Filter districts by division ID
        List<District> filteredDistricts = new ArrayList<>();
        List<String> districtNames = new ArrayList<>();

        for (District district : allDistricts) {
            if (district.getDivisionId() == divisionId) {
                filteredDistricts.add(district);
                districtNames.add(district.getName());
            }
        }

        ArrayAdapter<String> districtAdapter = new ArrayAdapter<>(
                this, android.R.layout.simple_dropdown_item_1line, districtNames);
        districtDropdown.setAdapter(districtAdapter);

        // District selection listener
        districtDropdown.setOnItemClickListener((parent, view, position, id) -> {
            selectedDistrict = filteredDistricts.get(position);
            districtId = selectedDistrict.getId();
            districtName = selectedDistrict.getName();

            // Log selection for debugging
            Log.d(TAG, "Selected district: ID=" + districtId + ", Name=" + districtName);

            // Update upazilla dropdown
            updateUpazillaDropdown(selectedDistrict.getId());

            // Clear upazilla selection
            upazillaDropdown.setText("", false);
            selectedUpazilla = null;
            upazillaId = null;
            upazillaName = null;
        });
    }

    private void updateUpazillaDropdown(int districtId) {
        // Filter upazillas by district ID
        List<Upazilla> filteredUpazillas = new ArrayList<>();
        List<String> upazillaNames = new ArrayList<>();

        for (Upazilla upazilla : allUpazillas) {
            if (upazilla.getDistrictId() == districtId) {
                filteredUpazillas.add(upazilla);
                upazillaNames.add(upazilla.getName());
            }
        }

        ArrayAdapter<String> upazillaAdapter = new ArrayAdapter<>(
                this, android.R.layout.simple_dropdown_item_1line, upazillaNames);
        upazillaDropdown.setAdapter(upazillaAdapter);

        // Upazilla selection listener
        upazillaDropdown.setOnItemClickListener((parent, view, position, id) -> {
            selectedUpazilla = filteredUpazillas.get(position);
            upazillaId = selectedUpazilla.getId();
            upazillaName = selectedUpazilla.getName();

            // Log selection for debugging
            Log.d(TAG, "Selected upazilla: ID=" + upazillaId + ", Name=" + upazillaName);
        });
    }

    private boolean validateStep1Input() {
        boolean isValid = true;

        // Validate full name
        String fullName = fullNameEditText.getText().toString().trim();
        if (!ValidationUtils.isValidName(fullName)) {
            fullNameLayout.setError(getString(R.string.invalid_name));
            isValid = false;
        } else {
            fullNameLayout.setError(null);
        }

        // Validate phone
        String phone = phoneEditText.getText().toString().trim();
        if (!ValidationUtils.isValidPhone(phone)) {
            phoneLayout.setError(getString(R.string.invalid_phone));
            isValid = false;
        } else {
            phoneLayout.setError(null);
        }

        // Validate email (optional)
        String email = emailEditText.getText().toString().trim();
        if (!email.isEmpty() && !ValidationUtils.isValidEmail(email)) {
            emailLayout.setError(getString(R.string.invalid_email));
            isValid = false;
        } else {
            emailLayout.setError(null);
        }

        return isValid;
    }

    private boolean validateStep2Input() {
        boolean isValid = true;

        // Validate OTP
        String otp = otpEditText.getText().toString().trim();
        int otpLength = sessionManager.getConfig() != null ? sessionManager.getConfig().getOtpLength() : 6;
        if (!ValidationUtils.isValidOtp(otp, otpLength)) {
            otpLayout.setError(getString(R.string.invalid_otp));
            isValid = false;
        } else {
            otpLayout.setError(null);
        }

        return isValid;
    }

    private boolean validateStep3Input() {
        boolean isValid = true;

        // Get min password length from server config with default fallback
        int minPasswordLength = 6; // Default to 6 characters as configured in admin panel
        if (sessionManager.getConfig() != null) {
            minPasswordLength = sessionManager.getConfig().getMinPasswordLength();
            Log.d(TAG, "Using server config min password length: " + minPasswordLength);
        } else {
            Log.d(TAG, "Using default min password length: " + minPasswordLength);
        }

        // Validate password
        String password = passwordEditText.getText().toString().trim();
        if (!ValidationUtils.isValidPassword(password, minPasswordLength)) {
            passwordLayout.setError(getString(R.string.invalid_password, minPasswordLength));
            isValid = false;
        } else {
            passwordLayout.setError(null);
        }

        // Validate confirm password
        String confirmPassword = confirmPasswordEditText.getText().toString().trim();
        if (!confirmPassword.equals(password)) {
            confirmPasswordLayout.setError(getString(R.string.passwords_dont_match));
            isValid = false;
        } else {
            confirmPasswordLayout.setError(null);
        }

        return isValid;
    }

    private void sendOtp() {
        // Skip if OTP is disabled
        if (!sessionManager.isOtpEnabled()) {
            step2View.setVisibility(View.GONE);
            step3View.setVisibility(View.VISIBLE);
            otp = "";
            Toast.makeText(SignupActivity.this,
                    "OTP verification is disabled. You can proceed with registration.",
                    Toast.LENGTH_SHORT).show();
            return;
        }

        // Show loading
        sendOtpButton.setEnabled(false);
        sendOtpButton.setText(R.string.sending);

        String formattedPhone = ValidationUtils.formatPhone(phone);
        String trimmedEmail = email.trim();

        Log.d("SignupActivity", "Sending OTP to phone: " + formattedPhone + ", email: " + trimmedEmail);

        apiService.sendRegistrationOtp(formattedPhone, trimmedEmail, "registration")
                .enqueue(new Callback<Object>() {
                    @Override
                    public void onResponse(Call<Object> call, Response<Object> response) {
                        sendOtpButton.setEnabled(true);
                        sendOtpButton.setText(R.string.send_otp);

                        if (response.isSuccessful() && response.body() != null) {
                            try {
                                // Parse response manually
                                Gson gson = new Gson();
                                String jsonString = gson.toJson(response.body());
                                JSONObject jsonResponse = new JSONObject(jsonString);

                                boolean success = jsonResponse.optBoolean("success", false);
                                String message = jsonResponse.optString("message", "");

                                if (success) {
                                    // Move to step 2
                                    step1View.setVisibility(View.GONE);
                                    step2View.setVisibility(View.VISIBLE);

                                    // Get expires_in from data object
                                    JSONObject dataObject = jsonResponse.optJSONObject("data");
                                    int expiresIn = 600; // Default 10 minutes
                                    if (dataObject != null) {
                                        expiresIn = dataObject.optInt("expires_in", 600);
                                    }

                                    // Start OTP timer
                                    startOtpTimer(expiresIn);

                                    // Show success message
                                    Toast.makeText(SignupActivity.this, message, Toast.LENGTH_SHORT).show();
                                } else {
                                    // Show error message
                                    String errorMessage = message;
                                    if (errorMessage.contains("already registered")) {
                                        step2View.setVisibility(View.GONE);
                                        step1View.setVisibility(View.VISIBLE);
                                        errorMessage = "এই ফোন নম্বরটি ইতিমধ্যেই রেজিস্টার করা হয়েছে";
                                    }
                                    Toast.makeText(SignupActivity.this, errorMessage, Toast.LENGTH_LONG).show();
                                }
                            } catch (Exception e) {
                                Log.e("SignupActivity", "Error parsing OTP response", e);
                                Toast.makeText(SignupActivity.this, getString(R.string.otp_send_failed), Toast.LENGTH_LONG).show();
                            }
                        } else {
                            // Read error message from errorBody (for 400/500 responses)
                            String errorMsg = getString(R.string.otp_send_failed); // default

                            try {
                                if (response.errorBody() != null) {
                                    String errorJson = response.errorBody().string();
                                    JSONObject jsonObj = new JSONObject(errorJson);

                                    if (jsonObj.has("message")) {
                                        String serverMsg = jsonObj.getString("message");

                                        // Optional: Handle specific case
                                        if ("Phone number already registered".equalsIgnoreCase(serverMsg)) {
                                            step2View.setVisibility(View.GONE);
                                            step1View.setVisibility(View.VISIBLE);
                                            errorMsg = "এই ফোন নম্বরটি ইতিমধ্যেই রেজিস্টার করা হয়েছে";
                                        } else {
                                            errorMsg = serverMsg;
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                Log.e("SignupActivity", "Error parsing errorBody", e);
                            }

                            Log.d("SignupActivity", "OTP response failed or null body. Response code: " + response.code());
                            Toast.makeText(SignupActivity.this, errorMsg, Toast.LENGTH_LONG).show();
                        }

                    }

                    @Override
                    public void onFailure(Call<Object> call, Throwable t) {
                        sendOtpButton.setEnabled(true);
                        sendOtpButton.setText(R.string.send_otp);

                        Log.e("SignupActivity", "Network error: " + t.getMessage(), t);
                        Toast.makeText(SignupActivity.this,
                                "Network error: " + t.getMessage(),
                                Toast.LENGTH_LONG).show();
                    }
                });
    }



    private void verifyOtp() {
        // Show loading
        verifyOtpButton.setEnabled(false);
        verifyOtpButton.setText(R.string.verifying);

        // Make API call
        apiService.verifyOtp(phone, otp, "registration").enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                // Reset button
                verifyOtpButton.setEnabled(true);
                verifyOtpButton.setText(R.string.verify);

                if (response.isSuccessful() && response.body() != null) {
                    try {
                        // Parse response manually
                        Gson gson = new Gson();
                        String jsonString = gson.toJson(response.body());
                        JSONObject jsonResponse = new JSONObject(jsonString);

                        boolean success = jsonResponse.optBoolean("success", false);
                        String message = jsonResponse.optString("message", "");

                        if (success) {
                            // Show success message
                            Toast.makeText(SignupActivity.this, message, Toast.LENGTH_SHORT).show();

                            // Move to step 3
                            step2View.setVisibility(View.GONE);
                            step3View.setVisibility(View.VISIBLE);
                        } else {
                            // Show error message
                            Toast.makeText(SignupActivity.this, message, Toast.LENGTH_LONG).show();
                        }
                    } catch (Exception e) {
                        Log.e("SignupActivity", "Error parsing verify OTP response", e);
                        Toast.makeText(SignupActivity.this, R.string.otp_verification_failed, Toast.LENGTH_LONG).show();
                    }
                } else {
                    // Show error message
                    Toast.makeText(SignupActivity.this, R.string.otp_verification_failed, Toast.LENGTH_LONG).show();
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                // Reset button
                verifyOtpButton.setEnabled(true);
                verifyOtpButton.setText(R.string.verify);

                // Show error message
                Toast.makeText(SignupActivity.this, t.getMessage(), Toast.LENGTH_LONG).show();
            }
        });
    }

    private void registerUser() {
        // Show loading
        signupButton.setEnabled(false);
        signupButton.setText(R.string.signing_up);

        // Make API call
        // If OTP verification is disabled, we'll pass an empty OTP
        // The API should handle this case based on the server-side settings
        String otpToSend = sessionManager.isOtpEnabled() ? otp : "";

        // Log the registration attempt for debugging
        Log.d(TAG, "Registering user with OTP: " +
                (sessionManager.isOtpEnabled() ? "Required (sending: " + otpToSend + ")" : "Not required (OTP disabled)"));

        // Log location data being sent to server
        Log.d(TAG, "Sending location data to server:");
        Log.d(TAG, "Division ID: " + divisionId + ", Name: " + divisionName);
        Log.d(TAG, "District ID: " + districtId + ", Name: " + districtName);
        Log.d(TAG, "Upazilla ID: " + upazillaId + ", Name: " + upazillaName);

        apiService.register(
                fullName,
                phone,
                email,
                password,
                otpToSend,
                address,
                divisionId,
                districtId,
                upazillaId,
                divisionName,
                districtName,
                upazillaName
        ).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                // Reset button
                signupButton.setEnabled(true);
                signupButton.setText(R.string.sign_up);

                if (response.isSuccessful() && response.body() != null) {
                    try {
                        ApiResponse<UserResponse> apiResponse = response.body();

                        if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                            UserResponse userResponse = apiResponse.getData();

                            if (userResponse.getUser() != null) {
                                User user = userResponse.getUser();

                        // If location names are missing in the response, use the ones we have locally
                        if (user.getDivisionName() == null && divisionName != null) {
                            user.setDivisionName(divisionName);
                        }
                        if (user.getDistrictName() == null && districtName != null) {
                            user.setDistrictName(districtName);
                        }
                        if (user.getUpazillaName() == null && upazillaName != null) {
                            user.setUpazillaName(upazillaName);
                        }

                        // Log location names for verification
                        Log.d(TAG, "Registration successful with location data:");
                        Log.d(TAG, "Division ID: " + user.getDivisionId() +
                                ", Name: " + user.getDivisionName());
                        Log.d(TAG, "District ID: " + user.getDistrictId() +
                                ", Name: " + user.getDistrictName());
                        Log.d(TAG, "Upazilla ID: " + user.getUpazillaId() +
                                ", Name: " + user.getUpazillaName());

                        // Save user data and set logged in
                        sessionManager.saveUser(user);
                        sessionManager.setLoggedIn(true);

                                // Show success message
                                Toast.makeText(SignupActivity.this, apiResponse.getMessage(), Toast.LENGTH_SHORT).show();

                                // Navigate to main activity
                                Intent intent = new Intent(SignupActivity.this, MainActivity.class);
                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                startActivity(intent);
                                finish();
                            } else {
                                // Show error message - no user data
                                Toast.makeText(SignupActivity.this, "Registration failed: No user data received", Toast.LENGTH_LONG).show();
                            }
                        } else {
                            // Show error message
                            Toast.makeText(SignupActivity.this, apiResponse.getMessage(), Toast.LENGTH_LONG).show();
                        }
                    } catch (Exception e) {
                        Log.e("SignupActivity", "Error parsing registration response", e);
                        Toast.makeText(SignupActivity.this, R.string.registration_failed, Toast.LENGTH_LONG).show();
                    }
                } else {
                    // Show error message
                    Toast.makeText(SignupActivity.this, R.string.registration_failed, Toast.LENGTH_LONG).show();
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                // Reset button
                signupButton.setEnabled(true);
                signupButton.setText(R.string.sign_up);

                // Show error message
                Toast.makeText(SignupActivity.this, t.getMessage(), Toast.LENGTH_LONG).show();
            }
        });
    }

    private void startOtpTimer(int seconds) {
        // Cancel existing timer if any
        if (otpTimer != null) {
            otpTimer.cancel();
        }

        // Create new timer
        otpTimer = new CountDownTimer(seconds * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long minutes = millisUntilFinished / 60000;
                long seconds = (millisUntilFinished % 60000) / 1000;
                otpTimerText.setText(getString(R.string.otp_timer, minutes, seconds));
            }

            @Override
            public void onFinish() {
                otpTimerText.setText(R.string.otp_expired);
                sendOtpButton.setEnabled(true);
                sendOtpButton.setText(R.string.resend_otp);
            }
        }.start();
    }

    /**
     * Continue initialization after configuration is loaded
     */
    private void continueInitialization() {
        // Check if user is already logged in
        if (sessionManager.isLoggedIn()) {
            startActivity(new Intent(this, MainActivity.class));
            finish();
            return;
        }

        // Initialize views
        initViews();
        setupClickListeners();

        // Load location data
        loadLocationData();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Mark activity as inactive
        isActivityActive = false;

        // Close database connection
        if (adminDAO != null) {
            adminDAO.close();
        }

        // Cancel timer to prevent memory leaks
        if (otpTimer != null) {
            otpTimer.cancel();
        }

        // Stop the config update manager and remove this listener
        if (configUpdateManager != null) {
            configUpdateManager.removeListener(this);
            configUpdateManager.stop();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        isActivityActive = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        isActivityActive = false;
    }

    /**
     * Handle configuration updates
     * This method is called when the configuration is updated in the background
     *
     * @param oldConfig Previous configuration
     * @param newConfig New configuration
     */
    @Override
    public void onConfigUpdated(AppConfig oldConfig, AppConfig newConfig) {
        // Check if activity is still active
        if (!isActivityActive) {
            Log.w(TAG, "Ignoring config update callback - activity is no longer active");
            return;
        }

        // Check if OTP status has changed
        if (oldConfig.isOtpEnabled() != newConfig.isOtpEnabled()) {
            Log.d(TAG, "OTP status changed from " + oldConfig.isOtpEnabled() + " to " + newConfig.isOtpEnabled());

            try {
                // Show a notification to the user
                String message = "OTP verification is now " + (newConfig.isOtpEnabled() ? "enabled" : "disabled") +
                        ". The app will update automatically.";

                // Show a toast notification
                Toast.makeText(this, message, Toast.LENGTH_LONG).show();

                // Show a dialog with more information
                DialogUtils.showInfoDialog(
                        SignupActivity.this,
                        "Configuration Updated",
                        message);

                // Apply the new configuration by recreating the activity
                // This is done with a slight delay to allow the user to see the notification
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    // Check again if activity is still active before recreating
                    if (isActivityActive) {
                        Intent intent = getIntent();
                        finish();
                        startActivity(intent);
                    } else {
                        Log.w(TAG, "Not recreating activity - activity is no longer active");
                    }
                }, 3000); // 3 second delay
            } catch (Exception e) {
                Log.e(TAG, "Error handling config update: " + e.getMessage());
            }
        }
    }
}
