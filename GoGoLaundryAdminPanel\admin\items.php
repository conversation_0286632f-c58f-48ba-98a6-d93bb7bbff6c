<?php
/**
 * Items Management
 *
 * This page allows administrators to manage laundry items
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/ServiceManager.php';
require_once '../includes/ItemManager.php';

// Initialize managers
$serviceManager = new ServiceManager($pdo);
$itemManager = new ItemManager($pdo);

// Process delete request
if (isset($_POST['delete_item']) && isset($_POST['item_id']) && isset($_POST['csrf_token'])) {
    // Verify CSRF token
    if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error_message'] = 'Invalid security token. Please try again.';
        header('Location: items.php');
        exit;
    }

    $itemId = (int)$_POST['item_id'];
    
    // Check if item is in use
    if ($itemManager->isItemInUse($itemId)) {
        $_SESSION['error_message'] = 'This item cannot be deleted because it is associated with existing orders.';
    } else {
        // Delete item
        if ($itemManager->deleteItem($itemId)) {
            $_SESSION['success_message'] = 'Item deleted successfully.';
        } else {
            $_SESSION['error_message'] = 'Failed to delete item. Please try again.';
        }
    }
    
    header('Location: items.php');
    exit;
}

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$serviceId = isset($_GET['service_id']) && is_numeric($_GET['service_id']) ? (int)$_GET['service_id'] : null;
$perPage = 10;

// Get items with pagination and filtering
$result = $itemManager->getAllItems($page, $perPage, $search, $serviceId);
$items = $result['items'];
$pagination = $result['pagination'];

// Get all services for filter dropdown
$services = $serviceManager->getAllServices()['services'];

// Set page title
$pageTitle = 'Manage Items';
?>

<?php include 'includes/header.php'; ?>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="add_item.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Item
                        </a>
                    </div>
                </div>

                <?php include 'includes/alerts.php'; ?>

                <!-- Search Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Search Items</h6>
                    </div>
                    <div class="card-body">
                        <form method="get" action="items.php" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Search by name or description" 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" id="service_id" name="service_id">
                                    <option value="">All Services</option>
                                    <?php foreach ($services as $service): ?>
                                        <option value="<?php echo $service['id']; ?>" <?php echo $serviceId == $service['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($service['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <?php if (!empty($search) || $serviceId): ?>
                                <a href="items.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Items Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Items List</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($items)): ?>
                            <div class="alert alert-info">
                                No items found. <?php echo !empty($search) || $serviceId ? 'Try a different search term or service filter.' : ''; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="itemsTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Service</th>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Price</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($items as $item): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($item['id']); ?></td>
                                                <td><?php echo htmlspecialchars($item['service_name']); ?></td>
                                                <td><?php echo htmlspecialchars($item['name']); ?></td>
                                                <td><?php echo htmlspecialchars($item['description']); ?></td>
                                                <td><?php echo htmlspecialchars(number_format($item['price'], 2)); ?> Tk</td>
                                                <td>
                                                    <?php if ($item['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="edit_item.php?id=<?php echo $item['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#deleteModal<?php echo $item['id']; ?>">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </td>
                                            </tr>
                                            
                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $item['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            Are you sure you want to delete the item: <strong><?php echo htmlspecialchars($item['name']); ?></strong>?
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <form method="post" action="items.php">
                                                                <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                                <button type="submit" name="delete_item" class="btn btn-danger">Delete</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php include 'includes/pagination.php'; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>
