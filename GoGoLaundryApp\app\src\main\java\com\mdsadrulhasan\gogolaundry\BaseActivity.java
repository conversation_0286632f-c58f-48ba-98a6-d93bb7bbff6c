package com.mdsadrulhasan.gogolaundry;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.mdsadrulhasan.gogolaundry.utils.LanguageManager;

/**
 * Base activity class to handle language changes
 * All activities should extend this class to support language changes
 */
public abstract class BaseActivity extends AppCompatActivity {

    protected LanguageManager languageManager;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        // Initialize language manager before setting content view
        languageManager = new LanguageManager(this);
        
        // Apply language settings
        Context context = languageManager.applyLanguage(this);
        
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        // Apply language settings to base context
        LanguageManager languageManager = new LanguageManager(newBase);
        super.attachBaseContext(languageManager.applyLanguage(newBase));
    }

    @Override
    protected void onResume() {
        super.onResume();
        
        // Apply language settings on resume to handle language changes
        languageManager.applyLanguage(this);
    }
}
