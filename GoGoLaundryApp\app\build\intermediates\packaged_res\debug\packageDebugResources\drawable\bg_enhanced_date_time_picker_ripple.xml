<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/enhanced_picker_ripple_color">
    
    <!-- Background state -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Mask for ripple effect -->
    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
</ripple>
