<?php
/**
 * User Manager Class
 *
 * Handles user registration, login, and management
 */
class UserManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo Database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Check if a user exists by phone number
     *
     * @param string $phone Phone number
     * @return bool True if user exists, false otherwise
     */
    public function userExistsByPhone($phone) {
        // Format phone number
        $phone = formatPhone($phone);

        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM users
            WHERE phone = ?
        ");
        $stmt->execute([$phone]);

        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    /**
     * Check if a user exists by email
     *
     * @param string $email Email address
     * @return bool True if user exists, false otherwise
     */
    public function userExistsByEmail($email) {
        if (empty($email)) {
            return false;
        }

        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM users
            WHERE email = ?
        ");
        $stmt->execute([$email]);

        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    /**
     * Register a new user
     *
     * @param string $fullName Full name
     * @param string $phone Phone number
     * @param string $email Email address (optional)
     * @param string $password Password
     * @param string $address Address/Village (optional)
     * @param int $divisionId Division ID (optional)
     * @param int $districtId District ID (optional)
     * @param int $upazillaId Upazilla ID (optional)
     * @param bool $isVerified Whether the user is verified
     * @param string $divisionName Division name (optional)
     * @param string $districtName District name (optional)
     * @param string $upazillaName Upazilla name (optional)
     * @return int|bool User ID on success, false on failure
     */
    public function registerUser($fullName, $phone, $email, $password, $address = null, $divisionId = null, $districtId = null, $upazillaId = null, $isVerified = false, $divisionName = null, $districtName = null, $upazillaName = null) {
        // Format phone number
        $phone = formatPhone($phone);

        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => HASH_COST]);

        // Insert user
        $stmt = $this->pdo->prepare("
            INSERT INTO users (full_name, phone, email, password, address, division_id, district_id, upazilla_id, is_verified)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            $fullName,
            $phone,
            $email,
            $hashedPassword,
            $address,
            $divisionId,
            $districtId,
            $upazillaId,
            $isVerified ? 1 : 0
        ]);

        if (!$result) {
            return false;
        }

        $userId = $this->pdo->lastInsertId();

        // If location names are provided but not in the database, update the location tables
        if ($divisionId && $divisionName) {
            $this->updateDivisionName($divisionId, $divisionName);
        }

        if ($districtId && $districtName) {
            $this->updateDistrictName($districtId, $districtName, $divisionId);
        }

        if ($upazillaId && $upazillaName) {
            $this->updateUpazillaName($upazillaId, $upazillaName, $districtId);
        }

        return $userId;
    }

    /**
     * Update division name if it doesn't exist or is different
     *
     * @param int $divisionId Division ID
     * @param string $divisionName Division name
     * @return bool True on success, false on failure
     */
    private function updateDivisionName($divisionId, $divisionName) {
        // Log for debugging
        error_log("Updating division: ID=$divisionId, Name=$divisionName");

        try {
            // Check if division exists
            $stmt = $this->pdo->prepare("SELECT name FROM divisions WHERE id = ?");
            $stmt->execute([$divisionId]);
            $division = $stmt->fetch();

            if (!$division) {
                // Division doesn't exist, insert it
                error_log("Division $divisionId not found, inserting new record");
                $stmt = $this->pdo->prepare("INSERT INTO divisions (id, name) VALUES (?, ?)");
                $result = $stmt->execute([$divisionId, $divisionName]);
                error_log("Insert result: " . ($result ? "success" : "failure"));
                return $result;
            } else if ($division['name'] != $divisionName) {
                // Division exists but name is different, update it
                error_log("Division $divisionId exists but name is different, updating");
                $stmt = $this->pdo->prepare("UPDATE divisions SET name = ? WHERE id = ?");
                $result = $stmt->execute([$divisionName, $divisionId]);
                error_log("Update result: " . ($result ? "success" : "failure"));
                return $result;
            } else {
                error_log("Division $divisionId already exists with correct name");
            }

            return true;
        } catch (PDOException $e) {
            error_log("Error updating division: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update district name if it doesn't exist or is different
     *
     * @param int $districtId District ID
     * @param string $districtName District name
     * @param int $divisionId Division ID
     * @return bool True on success, false on failure
     */
    private function updateDistrictName($districtId, $districtName, $divisionId) {
        // Log for debugging
        error_log("Updating district: ID=$districtId, Name=$districtName, Division ID=$divisionId");

        try {
            // Check if district exists
            $stmt = $this->pdo->prepare("SELECT name, division_id FROM districts WHERE id = ?");
            $stmt->execute([$districtId]);
            $district = $stmt->fetch();

            if (!$district) {
                // District doesn't exist, insert it
                error_log("District $districtId not found, inserting new record");
                $stmt = $this->pdo->prepare("INSERT INTO districts (id, name, division_id) VALUES (?, ?, ?)");
                $result = $stmt->execute([$districtId, $districtName, $divisionId]);
                error_log("Insert result: " . ($result ? "success" : "failure"));
                return $result;
            } else if ($district['name'] != $districtName || $district['division_id'] != $divisionId) {
                // District exists but name or division_id is different, update it
                error_log("District $districtId exists but data is different, updating");
                $stmt = $this->pdo->prepare("UPDATE districts SET name = ?, division_id = ? WHERE id = ?");
                $result = $stmt->execute([$districtName, $divisionId, $districtId]);
                error_log("Update result: " . ($result ? "success" : "failure"));
                return $result;
            } else {
                error_log("District $districtId already exists with correct data");
            }

            return true;
        } catch (PDOException $e) {
            error_log("Error updating district: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update upazilla name if it doesn't exist or is different
     *
     * @param int $upazillaId Upazilla ID
     * @param string $upazillaName Upazilla name
     * @param int $districtId District ID
     * @return bool True on success, false on failure
     */
    private function updateUpazillaName($upazillaId, $upazillaName, $districtId) {
        // Log for debugging
        error_log("Updating upazilla: ID=$upazillaId, Name=$upazillaName, District ID=$districtId");

        try {
            // Check if upazilla exists
            $stmt = $this->pdo->prepare("SELECT name, district_id FROM upazillas WHERE id = ?");
            $stmt->execute([$upazillaId]);
            $upazilla = $stmt->fetch();

            if (!$upazilla) {
                // Upazilla doesn't exist, insert it
                error_log("Upazilla $upazillaId not found, inserting new record");
                $stmt = $this->pdo->prepare("INSERT INTO upazillas (id, name, district_id) VALUES (?, ?, ?)");
                $result = $stmt->execute([$upazillaId, $upazillaName, $districtId]);
                error_log("Insert result: " . ($result ? "success" : "failure"));
                return $result;
            } else if ($upazilla['name'] != $upazillaName || $upazilla['district_id'] != $districtId) {
                // Upazilla exists but name or district_id is different, update it
                error_log("Upazilla $upazillaId exists but data is different, updating");
                $stmt = $this->pdo->prepare("UPDATE upazillas SET name = ?, district_id = ? WHERE id = ?");
                $result = $stmt->execute([$upazillaName, $districtId, $upazillaId]);
                error_log("Update result: " . ($result ? "success" : "failure"));
                return $result;
            } else {
                error_log("Upazilla $upazillaId already exists with correct data");
            }

            return true;
        } catch (PDOException $e) {
            error_log("Error updating upazilla: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user by phone number
     *
     * @param string $phone Phone number
     * @return array|bool User data or false if not found
     */
    public function getUserByPhone($phone) {
        // Format phone number
        $phone = formatPhone($phone);

        $stmt = $this->pdo->prepare("
            SELECT *
            FROM users
            WHERE phone = ?
        ");
        $stmt->execute([$phone]);

        $user = $stmt->fetch();

        if ($user) {
            return $user;
        }

        return false;
    }

    /**
     * Get user by phone number with location names
     *
     * @param string $phone Phone number
     * @return array|bool User data with location names or false if not found
     */
    public function getUserByPhoneWithLocationNames($phone) {
        // Format phone number
        $phone = formatPhone($phone);

        $stmt = $this->pdo->prepare("
            SELECT
                u.*,
                d.name AS division_name,
                dist.name AS district_name,
                up.name AS upazilla_name
            FROM
                users u
                LEFT JOIN divisions d ON u.division_id = d.id
                LEFT JOIN districts dist ON u.district_id = dist.id
                LEFT JOIN upazillas up ON u.upazilla_id = up.id
            WHERE
                u.phone = ?
        ");
        $stmt->execute([$phone]);

        $user = $stmt->fetch();

        if ($user) {
            return $user;
        }

        return false;
    }

    /**
     * Get total number of customers
     *
     * @return int Total number of customers
     */
    public function getTotalCustomers() {
        $stmt = $this->pdo->query("SELECT COUNT(*) FROM users");
        return (int)$stmt->fetchColumn();
    }

    /**
     * Get user by email
     *
     * @param string $email Email address
     * @return array|bool User data or false if not found
     */
    public function getUserByEmail($email) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM users
            WHERE email = ?
        ");
        $stmt->execute([$email]);

        $user = $stmt->fetch();

        if ($user) {
            return $user;
        }

        return false;
    }

    /**
     * Verify user's phone number
     *
     * @param string $phone Phone number
     * @return bool True on success, false on failure
     */
    public function verifyUser($phone) {
        // Format phone number
        $phone = formatPhone($phone);

        $stmt = $this->pdo->prepare("
            UPDATE users
            SET is_verified = 1
            WHERE phone = ?
        ");

        return $stmt->execute([$phone]);
    }

    /**
     * Authenticate user
     *
     * @param string $phone Phone number
     * @param string $password Password
     * @return array|bool User data on success, false on failure
     */
    public function login($phone, $password) {
        // Format phone number
        $phone = formatPhone($phone);

        // Get user
        $user = $this->getUserByPhone($phone);

        if (!$user) {
            return false;
        }

        // Verify password
        if (password_verify($password, $user['password'])) {
            // Remove password from user data
            unset($user['password']);
            return $user;
        }

        return false;
    }

    /**
     * Update user password
     *
     * @param string $phone Phone number
     * @param string $newPassword New password
     * @return bool True on success, false on failure
     */
    public function updatePassword($phone, $newPassword) {
        // Format phone number
        $phone = formatPhone($phone);

        // Hash new password
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => HASH_COST]);

        $stmt = $this->pdo->prepare("
            UPDATE users
            SET password = ?
            WHERE phone = ?
        ");

        return $stmt->execute([$hashedPassword, $phone]);
    }

    /**
     * Get user by ID
     *
     * @param int $userId User ID
     * @return array|bool User data or false if not found
     */
    public function getUserById($userId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM users
            WHERE id = ?
        ");
        $stmt->execute([$userId]);

        $user = $stmt->fetch();

        if ($user) {
            return $user;
        }

        return false;
    }

    /**
     * Update user profile picture
     *
     * @param int $userId User ID
     * @param string $profilePictureUrl Profile picture URL/path
     * @return bool True on success, false on failure
     */
    public function updateProfilePicture($userId, $profilePictureUrl) {
        $stmt = $this->pdo->prepare("
            UPDATE users
            SET profile_picture_url = ?
            WHERE id = ?
        ");

        return $stmt->execute([$profilePictureUrl, $userId]);
    }

    /**
     * Update user password by ID
     *
     * @param int $userId User ID
     * @param string $newPassword New password
     * @return bool True on success, false on failure
     */
    public function updatePasswordById($userId, $newPassword) {
        // Hash new password
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => HASH_COST]);

        $stmt = $this->pdo->prepare("
            UPDATE users
            SET password = ?
            WHERE id = ?
        ");

        return $stmt->execute([$hashedPassword, $userId]);
    }

    /**
     * Soft delete a user account
     * Moves user data to deleted_users table and marks the account as deleted
     *
     * @param int $userId User ID
     * @return bool True on success, false on failure
     */
    public function softDeleteUser($userId) {
        try {
            // Start transaction
            $this->pdo->beginTransaction();

            // Get user data
            $user = $this->getUserById($userId);
            if (!$user) {
                $this->pdo->rollBack();
                return false;
            }

            // Insert into deleted_users table
            $stmt = $this->pdo->prepare("
                INSERT INTO deleted_users (
                    user_id, full_name, phone, email, password, address,
                    division_id, district_id, upazilla_id, is_verified,
                    profile_picture_url, created_at, updated_at, deleted_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )
            ");

            $result = $stmt->execute([
                $user['id'],
                $user['full_name'],
                $user['phone'],
                $user['email'],
                $user['password'],
                $user['address'],
                $user['division_id'],
                $user['district_id'],
                $user['upazilla_id'],
                $user['is_verified'],
                $user['profile_picture_url'],
                $user['created_at'],
                $user['updated_at']
            ]);

            if (!$result) {
                $this->pdo->rollBack();
                return false;
            }

            // Delete user from users table
            $stmt = $this->pdo->prepare("
                DELETE FROM users
                WHERE id = ?
            ");

            $result = $stmt->execute([$userId]);

            if (!$result) {
                $this->pdo->rollBack();
                return false;
            }

            // Commit transaction
            $this->pdo->commit();
            return true;
        } catch (PDOException $e) {
            // Rollback transaction on error
            $this->pdo->rollBack();
            error_log("Error soft deleting user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Restore a deleted user account
     *
     * @param int $userId Original user ID
     * @return bool True on success, false on failure
     */
    public function restoreDeletedUser($userId) {
        try {
            // Start transaction
            $this->pdo->beginTransaction();

            // Get deleted user data
            $stmt = $this->pdo->prepare("
                SELECT * FROM deleted_users
                WHERE user_id = ?
                ORDER BY deleted_at DESC
                LIMIT 1
            ");
            $stmt->execute([$userId]);
            $deletedUser = $stmt->fetch();

            if (!$deletedUser) {
                $this->pdo->rollBack();
                return false;
            }

            // Check if user already exists in users table
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count
                FROM users
                WHERE phone = ?
            ");
            $stmt->execute([$deletedUser['phone']]);
            $result = $stmt->fetch();

            if ($result['count'] > 0) {
                $this->pdo->rollBack();
                return false;
            }

            // Insert back into users table
            $stmt = $this->pdo->prepare("
                INSERT INTO users (
                    id, full_name, phone, email, password, address,
                    division_id, district_id, upazilla_id, is_verified,
                    profile_picture_url, created_at, updated_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
                )
            ");

            $result = $stmt->execute([
                $deletedUser['user_id'],
                $deletedUser['full_name'],
                $deletedUser['phone'],
                $deletedUser['email'],
                $deletedUser['password'],
                $deletedUser['address'],
                $deletedUser['division_id'],
                $deletedUser['district_id'],
                $deletedUser['upazilla_id'],
                $deletedUser['is_verified'],
                $deletedUser['profile_picture_url'],
                $deletedUser['created_at']
            ]);

            if (!$result) {
                $this->pdo->rollBack();
                return false;
            }

            // Delete from deleted_users table
            $stmt = $this->pdo->prepare("
                DELETE FROM deleted_users
                WHERE id = ?
            ");

            $result = $stmt->execute([$deletedUser['id']]);

            if (!$result) {
                $this->pdo->rollBack();
                return false;
            }

            // Commit transaction
            $this->pdo->commit();
            return true;
        } catch (PDOException $e) {
            // Rollback transaction on error
            $this->pdo->rollBack();
            error_log("Error restoring deleted user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get total number of users
     *
     * @return int Total number of users
     */
    public function getTotalUsers() {
        $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        return $result['count'];
    }

    /**
     * Get total number of verified users
     *
     * @return int Total number of verified users
     */
    public function getTotalVerifiedUsers() {
        $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM users WHERE is_verified = 1");
        $result = $stmt->fetch();
        return $result['count'];
    }

    /**
     * Get total number of deleted users
     *
     * @return int Total number of deleted users
     */
    public function getTotalDeletedUsers() {
        $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM deleted_users");
        $result = $stmt->fetch();
        return $result['count'];
    }

    /**
     * Get recently deleted users
     *
     * @param int $limit Number of users to return
     * @return array Recently deleted users
     */
    public function getRecentlyDeletedUsers($limit = 5) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM deleted_users
            ORDER BY deleted_at DESC
            LIMIT ?
        ");
        $stmt->bindValue(1, $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Get user with location names
     *
     * @param int $userId User ID
     * @return array|bool User data with location names or false if not found
     */
    public function getUserWithLocationNames($userId) {
        $stmt = $this->pdo->prepare("
            SELECT
                u.*,
                d.name AS division_name,
                dist.name AS district_name,
                up.name AS upazilla_name
            FROM
                users u
                LEFT JOIN divisions d ON u.division_id = d.id
                LEFT JOIN districts dist ON u.district_id = dist.id
                LEFT JOIN upazillas up ON u.upazilla_id = up.id
            WHERE
                u.id = ?
        ");
        $stmt->execute([$userId]);

        $user = $stmt->fetch();

        if ($user) {
            return $user;
        }

        return false;
    }

    /**
     * Get deleted users with pagination and filtering
     *
     * @param int $page Page number
     * @param int $perPage Items per page
     * @param string $search Search term
     * @param string $dateFrom Start date for filtering
     * @param string $dateTo End date for filtering
     * @return array Deleted users and pagination info
     */
    public function getDeletedUsers($page = 1, $perPage = 10, $search = '', $dateFrom = '', $dateTo = '') {
        $offset = ($page - 1) * $perPage;
        $params = [];
        $whereClause = '';

        // Build where clause
        if (!empty($search)) {
            $whereClause .= " WHERE (full_name LIKE ? OR phone LIKE ? OR email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        if (!empty($dateFrom) && !empty($dateTo)) {
            $whereClause .= empty($whereClause) ? " WHERE" : " AND";
            $whereClause .= " (deleted_at BETWEEN ? AND ?)";
            $params[] = $dateFrom . ' 00:00:00';
            $params[] = $dateTo . ' 23:59:59';
        } elseif (!empty($dateFrom)) {
            $whereClause .= empty($whereClause) ? " WHERE" : " AND";
            $whereClause .= " deleted_at >= ?";
            $params[] = $dateFrom . ' 00:00:00';
        } elseif (!empty($dateTo)) {
            $whereClause .= empty($whereClause) ? " WHERE" : " AND";
            $whereClause .= " deleted_at <= ?";
            $params[] = $dateTo . ' 23:59:59';
        }

        // Count total records
        $countSql = "SELECT COUNT(*) as count FROM deleted_users" . $whereClause;
        $countStmt = $this->pdo->prepare($countSql);
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch()['count'];

        // Get records with pagination
        $sql = "SELECT * FROM deleted_users" . $whereClause . " ORDER BY deleted_at DESC LIMIT ?, ?";
        $stmt = $this->pdo->prepare($sql);

        // Add pagination parameters
        $params[] = $offset;
        $params[] = $perPage;

        // Bind parameters with correct types
        for ($i = 0; $i < count($params); $i++) {
            if ($i >= count($params) - 2) {
                $stmt->bindValue($i + 1, $params[$i], PDO::PARAM_INT);
            } else {
                $stmt->bindValue($i + 1, $params[$i]);
            }
        }

        $stmt->execute();
        $users = $stmt->fetchAll();

        // Calculate pagination info
        $totalPages = ceil($totalCount / $perPage);

        return [
            'users' => $users,
            'pagination' => [
                'total' => $totalCount,
                'per_page' => $perPage,
                'current_page' => $page,
                'total_pages' => $totalPages
            ]
        ];
    }

    /**
     * Get deleted user by ID
     *
     * @param int $userId User ID
     * @return array|bool Deleted user data on success, false on failure
     */
    public function getDeletedUserById($userId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM deleted_users
            WHERE user_id = ?
            ORDER BY deleted_at DESC
            LIMIT 1
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch();
    }

    /**
     * Bulk restore deleted users
     *
     * @param array $userIds Array of user IDs to restore
     * @return array Result with success count and failed IDs
     */
    public function bulkRestoreDeletedUsers($userIds) {
        $successCount = 0;
        $failedIds = [];

        foreach ($userIds as $userId) {
            $result = $this->restoreDeletedUser($userId);
            if ($result) {
                $successCount++;
            } else {
                $failedIds[] = $userId;
            }
        }

        return [
            'success_count' => $successCount,
            'failed_ids' => $failedIds
        ];
    }

    /**
     * Admin reset user password
     *
     * @param int $userId User ID
     * @param string $newPassword New password
     * @return bool True on success, false on failure
     */
    public function resetUserPassword($userId, $newPassword) {
        // Hash password
        $hashedPassword = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => HASH_COST]);

        // Update password
        $stmt = $this->pdo->prepare("
            UPDATE users
            SET password = ?
            WHERE id = ?
        ");

        return $stmt->execute([$hashedPassword, $userId]);
    }

    /**
     * Get user by phone or email
     *
     * @param string $searchTerm Phone number or email
     * @return array|bool User data or false if not found
     */
    public function getUserByPhoneOrEmail($searchTerm) {
        // Try to find by phone first
        $formattedPhone = formatPhone($searchTerm);
        $user = $this->getUserByPhone($formattedPhone);

        if ($user) {
            return $user;
        }

        // If not found by phone, try email
        if (filter_var($searchTerm, FILTER_VALIDATE_EMAIL)) {
            return $this->getUserByEmail($searchTerm);
        }

        return false;
    }

    /**
     * Update user verification status
     *
     * @param int $userId User ID
     * @param bool $isVerified Whether the user should be verified
     * @return bool True on success, false on failure
     */
    public function updateUserVerificationStatus($userId, $isVerified) {
        $stmt = $this->pdo->prepare("
            UPDATE users
            SET is_verified = ?
            WHERE id = ?
        ");

        return $stmt->execute([$isVerified ? 1 : 0, $userId]);
    }

    /**
     * Get all users with pagination and filtering
     *
     * @param int $page Page number
     * @param int $perPage Items per page
     * @param string $search Search term
     * @param bool $verifiedOnly Only get verified users
     * @return array Users and pagination info
     */
    public function getAllUsers($page = 1, $perPage = 10, $search = '', $verifiedOnly = false) {
        $offset = ($page - 1) * $perPage;
        $params = [];
        $whereClause = '';

        // Build where clause
        if ($verifiedOnly) {
            $whereClause .= " WHERE is_verified = 1";
        }

        if (!empty($search)) {
            $whereClause .= empty($whereClause) ? " WHERE" : " AND";
            $whereClause .= " (full_name LIKE ? OR phone LIKE ? OR email LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }

        // Count total records
        $countSql = "SELECT COUNT(*) as count FROM users" . $whereClause;
        $countStmt = $this->pdo->prepare($countSql);
        $countStmt->execute($params);
        $totalCount = $countStmt->fetch()['count'];

        // Get records with pagination
        $sql = "SELECT * FROM users" . $whereClause . " ORDER BY id DESC LIMIT ?, ?";
        $stmt = $this->pdo->prepare($sql);

        // Add pagination parameters
        $params[] = $offset;
        $params[] = $perPage;

        // Bind parameters with correct types
        for ($i = 0; $i < count($params); $i++) {
            if ($i >= count($params) - 2) {
                $stmt->bindValue($i + 1, $params[$i], PDO::PARAM_INT);
            } else {
                $stmt->bindValue($i + 1, $params[$i]);
            }
        }

        $stmt->execute();
        $users = $stmt->fetchAll();

        // Calculate pagination info
        $totalPages = ceil($totalCount / $perPage);

        return [
            'users' => $users,
            'pagination' => [
                'total' => $totalCount,
                'per_page' => $perPage,
                'current_page' => $page,
                'total_pages' => $totalPages
            ]
        ];
    }
}
