<?php
/**
 * Check shops in database
 */

require_once 'config.php';

try {
    // Check total shops
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM laundry_shops");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total shops in database: " . $result['total'] . "\n";

    // Check active shops
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM laundry_shops WHERE is_active = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Active shops: " . $result['active'] . "\n";

    // Check verified shops
    $stmt = $pdo->query("SELECT COUNT(*) as verified FROM laundry_shops WHERE is_verified = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Verified shops: " . $result['verified'] . "\n";

    // Check active & verified shops
    $stmt = $pdo->query("SELECT COUNT(*) as both FROM laundry_shops WHERE is_active = 1 AND is_verified = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Active & Verified shops: " . $result['both'] . "\n";

    // Show all shops with their status
    $stmt = $pdo->query("SELECT id, name, is_active, is_verified, created_at FROM laundry_shops ORDER BY created_at DESC");
    $shops = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "\nAll shops:\n";
    echo "ID\tName\t\t\tActive\tVerified\tCreated\n";
    echo "-----------------------------------------------------------\n";
    foreach ($shops as $shop) {
        echo $shop['id'] . "\t" .
             substr($shop['name'], 0, 20) . "\t\t" .
             ($shop['is_active'] ? 'Yes' : 'No') . "\t" .
             ($shop['is_verified'] ? 'Yes' : 'No') . "\t\t" .
             $shop['created_at'] . "\n";
    }

    // If no active & verified shops, let's activate and verify the first shop
    if ($result['both'] == 0 && !empty($shops)) {
        echo "\nNo active & verified shops found. Activating and verifying the first shop...\n";
        $firstShop = $shops[0];
        $updateStmt = $pdo->prepare("UPDATE laundry_shops SET is_active = 1, is_verified = 1 WHERE id = ?");
        $updateStmt->execute([$firstShop['id']]);
        echo "Updated shop: " . $firstShop['name'] . " (ID: " . $firstShop['id'] . ")\n";
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
