<?php
/**
 * Shop Sync Status API
 * Returns information about shop updates and cache invalidation status
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include database connection
require_once '../includes/functions.php';

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        handleGetRequest();
    } elseif ($method === 'POST') {
        handlePostRequest();
    } else {
        throw new Exception('Method not allowed');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}

function handleGetRequest() {
    global $pdo;
    
    $action = $_GET['action'] ?? 'check_updates';
    
    switch ($action) {
        case 'check_updates':
            checkShopUpdates();
            break;
        case 'sync_status':
            getShopSyncStatus();
            break;
        case 'pending_notifications':
            getPendingNotifications();
            break;
        default:
            throw new Exception('Invalid action');
    }
}

function handlePostRequest() {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'mark_processed':
            markNotificationsProcessed($input);
            break;
        case 'invalidate_cache':
            invalidateCache($input);
            break;
        default:
            throw new Exception('Invalid action');
    }
}

function checkShopUpdates() {
    global $pdo;
    
    $lastCheck = $_GET['last_check'] ?? null;
    $shopIds = $_GET['shop_ids'] ?? null;
    
    $whereClause = "WHERE sun.is_processed = 0";
    $params = [];
    
    if ($lastCheck) {
        $whereClause .= " AND sun.created_at > ?";
        $params[] = $lastCheck;
    }
    
    if ($shopIds) {
        $shopIdsArray = explode(',', $shopIds);
        $placeholders = str_repeat('?,', count($shopIdsArray) - 1) . '?';
        $whereClause .= " AND sun.shop_id IN ($placeholders)";
        $params = array_merge($params, $shopIdsArray);
    }
    
    $sql = "
        SELECT 
            sun.id,
            sun.shop_id,
            sun.update_type,
            sun.created_at,
            ls.name as shop_name,
            ls.latitude,
            ls.longitude,
            ls.address,
            ls.is_active,
            ls.is_verified
        FROM shop_update_notifications sun
        JOIN laundry_shops ls ON sun.shop_id = ls.id
        $whereClause
        ORDER BY sun.created_at DESC
        LIMIT 100
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $updates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Group updates by shop and type
    $groupedUpdates = [];
    foreach ($updates as $update) {
        $shopId = $update['shop_id'];
        $updateType = $update['update_type'];
        
        if (!isset($groupedUpdates[$shopId])) {
            $groupedUpdates[$shopId] = [
                'shop_id' => $shopId,
                'shop_name' => $update['shop_name'],
                'latitude' => $update['latitude'],
                'longitude' => $update['longitude'],
                'address' => $update['address'],
                'is_active' => $update['is_active'],
                'is_verified' => $update['is_verified'],
                'updates' => []
            ];
        }
        
        $groupedUpdates[$shopId]['updates'][$updateType] = [
            'notification_id' => $update['id'],
            'created_at' => $update['created_at']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Shop updates retrieved successfully',
        'data' => [
            'updates' => array_values($groupedUpdates),
            'total_count' => count($updates),
            'check_time' => date('Y-m-d H:i:s')
        ]
    ]);
}

function getShopSyncStatus() {
    global $pdo;
    
    $shopId = $_GET['shop_id'] ?? null;
    
    if ($shopId) {
        // Get status for specific shop
        $sql = "SELECT * FROM shop_sync_status WHERE shop_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$shopId]);
        $status = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$status) {
            throw new Exception('Shop not found');
        }
        
        $data = $status;
    } else {
        // Get status for all shops
        $sql = "SELECT * FROM shop_sync_status ORDER BY last_update_notification DESC";
        $stmt = $pdo->query($sql);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Sync status retrieved successfully',
        'data' => $data
    ]);
}

function getPendingNotifications() {
    global $pdo;
    
    $limit = $_GET['limit'] ?? 50;
    $offset = $_GET['offset'] ?? 0;
    
    $sql = "
        SELECT 
            sun.*,
            ls.name as shop_name
        FROM shop_update_notifications sun
        JOIN laundry_shops ls ON sun.shop_id = ls.id
        WHERE sun.is_processed = 0
        ORDER BY sun.created_at DESC
        LIMIT ? OFFSET ?
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$limit, $offset]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM shop_update_notifications WHERE is_processed = 0";
    $countStmt = $pdo->query($countSql);
    $totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo json_encode([
        'success' => true,
        'message' => 'Pending notifications retrieved successfully',
        'data' => [
            'notifications' => $notifications,
            'total_count' => $totalCount,
            'limit' => $limit,
            'offset' => $offset
        ]
    ]);
}

function markNotificationsProcessed($input) {
    global $pdo;
    
    $notificationIds = $input['notification_ids'] ?? [];
    $shopId = $input['shop_id'] ?? null;
    $updateType = $input['update_type'] ?? null;
    
    if (empty($notificationIds) && !$shopId) {
        throw new Exception('Either notification_ids or shop_id must be provided');
    }
    
    if (!empty($notificationIds)) {
        // Mark specific notifications as processed
        $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
        $sql = "UPDATE shop_update_notifications SET is_processed = 1, processed_at = NOW() WHERE id IN ($placeholders)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($notificationIds);
        $affectedRows = $stmt->rowCount();
    } else {
        // Mark all notifications for a shop (and optionally specific type) as processed
        $sql = "UPDATE shop_update_notifications SET is_processed = 1, processed_at = NOW() WHERE shop_id = ?";
        $params = [$shopId];
        
        if ($updateType) {
            $sql .= " AND update_type = ?";
            $params[] = $updateType;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $affectedRows = $stmt->rowCount();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Notifications marked as processed successfully',
        'data' => [
            'affected_rows' => $affectedRows
        ]
    ]);
}

function invalidateCache($input) {
    global $pdo;
    
    $cacheKeys = $input['cache_keys'] ?? [];
    $reason = $input['reason'] ?? 'Manual invalidation';
    
    if (empty($cacheKeys)) {
        throw new Exception('Cache keys must be provided');
    }
    
    // Insert cache invalidation records
    $sql = "INSERT INTO api_cache_invalidation (cache_key, reason, invalidated_at) VALUES (?, ?, NOW())";
    $stmt = $pdo->prepare($sql);
    
    $insertedCount = 0;
    foreach ($cacheKeys as $cacheKey) {
        if ($stmt->execute([$cacheKey, $reason])) {
            $insertedCount++;
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Cache invalidation completed successfully',
        'data' => [
            'invalidated_count' => $insertedCount,
            'total_keys' => count($cacheKeys)
        ]
    ]);
}
?>
