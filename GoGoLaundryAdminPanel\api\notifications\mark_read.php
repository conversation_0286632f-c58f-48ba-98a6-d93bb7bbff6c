<?php
/**
 * API endpoint to mark a notification as read
 *
 * Required parameters:
 * - notification_id: ID of the notification to mark as read
 * - user_id: ID of the user for authentication
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 */

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/NotificationManager.php';
require_once '../../includes/UserManager.php';

// Initialize response
$response = [
    'success' => false,
    'message' => ''
];

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit();
}

// Check if required parameters are provided
if (!isset($_POST['notification_id']) || empty($_POST['notification_id']) ||
    !isset($_POST['user_id']) || empty($_POST['user_id'])) {
    $response['message'] = 'Notification ID and User ID are required';
    echo json_encode($response);
    exit();
}

// Get parameters
$notification_id = intval($_POST['notification_id']);
$user_id = intval($_POST['user_id']);

try {
    // Initialize managers
    $userManager = new UserManager($pdo);
    $notificationManager = new NotificationManager($pdo);

    // Validate user exists
    $user = $userManager->getUserById($user_id);
    if (!$user) {
        $response['message'] = 'User not found';
        echo json_encode($response);
        exit();
    }

    // Check if user is verified
    if (!$user['is_verified']) {
        $response['message'] = 'User account is not verified';
        echo json_encode($response);
        exit();
    }

    // Mark notification as read
    $result = $notificationManager->markAsRead($notification_id, $user_id);

    if ($result) {
        $response['success'] = true;
        $response['message'] = 'Notification marked as read successfully';
    } else {
        $response['message'] = 'Notification not found, already read, or does not belong to the user';
    }

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    // Log the error for server-side debugging
    error_log('Notification API Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
