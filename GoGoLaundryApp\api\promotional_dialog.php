<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../admin/includes/config.php';

/**
 * Get active promotional dialog
 * Returns the currently active promotional dialog that should be shown to users
 */
function getActivePromoDialog() {
    global $conn;
    
    try {
        // Get active promotional dialog that is within date range (if specified)
        $stmt = $conn->prepare("
            SELECT * FROM promotional_dialogs 
            WHERE is_active = 1 
            AND (start_date IS NULL OR start_date <= NOW()) 
            AND (end_date IS NULL OR end_date >= NOW())
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $dialog = $result->fetch_assoc();
            
            // Process image URL
            if (!empty($dialog['image_path'])) {
                $dialog['image_url'] = getBaseUrl() . $dialog['image_path'];
            } elseif (!empty($dialog['image_url'])) {
                // Use the provided image URL as is
                $dialog['image_url'] = $dialog['image_url'];
            } else {
                $dialog['image_url'] = null;
            }
            
            // Remove internal image_path from response
            unset($dialog['image_path']);
            
            // Convert boolean values
            $dialog['is_active'] = (bool)$dialog['is_active'];
            
            // Format dates
            if ($dialog['start_date']) {
                $dialog['start_date'] = date('Y-m-d H:i:s', strtotime($dialog['start_date']));
            }
            if ($dialog['end_date']) {
                $dialog['end_date'] = date('Y-m-d H:i:s', strtotime($dialog['end_date']));
            }
            
            return [
                'success' => true,
                'has_dialog' => true,
                'dialog' => $dialog,
                'message' => 'Active promotional dialog found'
            ];
        } else {
            return [
                'success' => true,
                'has_dialog' => false,
                'dialog' => null,
                'message' => 'No active promotional dialog found'
            ];
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'has_dialog' => false,
            'dialog' => null,
            'message' => 'Error fetching promotional dialog: ' . $e->getMessage()
        ];
    }
}

/**
 * Get base URL for constructing full image URLs
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = dirname(dirname($script)); // Go up two levels from /api/promotional_dialog.php
    
    return $protocol . '://' . $host . $path . '/';
}

/**
 * Log promotional dialog view (optional analytics)
 */
function logPromoDialogView($dialogId, $userId = null) {
    global $conn;
    
    try {
        $stmt = $conn->prepare("
            INSERT INTO promo_dialog_views (dialog_id, user_id, viewed_at, ip_address) 
            VALUES (?, ?, NOW(), ?)
        ");
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $stmt->bind_param("iis", $dialogId, $userId, $ipAddress);
        $stmt->execute();
        
        return true;
    } catch (Exception $e) {
        // Log error but don't fail the main request
        error_log("Failed to log promo dialog view: " . $e->getMessage());
        return false;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Get active promotional dialog
        $response = getActivePromoDialog();
        break;
        
    case 'POST':
        // Handle promotional dialog interaction (view, click, etc.)
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (isset($input['action'])) {
            switch ($input['action']) {
                case 'view':
                    // Log that user viewed the dialog
                    $dialogId = $input['dialog_id'] ?? null;
                    $userId = $input['user_id'] ?? null;
                    
                    if ($dialogId) {
                        logPromoDialogView($dialogId, $userId);
                        $response = [
                            'success' => true,
                            'message' => 'Dialog view logged'
                        ];
                    } else {
                        $response = [
                            'success' => false,
                            'message' => 'Dialog ID required'
                        ];
                    }
                    break;
                    
                case 'click':
                    // Log that user clicked the dialog button
                    $dialogId = $input['dialog_id'] ?? null;
                    $userId = $input['user_id'] ?? null;
                    
                    if ($dialogId) {
                        // You can add click tracking here if needed
                        $response = [
                            'success' => true,
                            'message' => 'Dialog click logged'
                        ];
                    } else {
                        $response = [
                            'success' => false,
                            'message' => 'Dialog ID required'
                        ];
                    }
                    break;
                    
                default:
                    $response = [
                        'success' => false,
                        'message' => 'Invalid action'
                    ];
                    break;
            }
        } else {
            $response = [
                'success' => false,
                'message' => 'Action required'
            ];
        }
        break;
        
    default:
        $response = [
            'success' => false,
            'message' => 'Method not allowed'
        ];
        break;
}

// Return JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>
