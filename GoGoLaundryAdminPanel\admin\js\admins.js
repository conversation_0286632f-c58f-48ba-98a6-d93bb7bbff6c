/**
 * Administrators Management JavaScript
 *
 * This file handles the administrators management page functionality with a custom dropdown implementation
 */

document.addEventListener('DOMContentLoaded', function() {
    // Custom dropdown implementation
    const actionButtons = document.querySelectorAll('.btn-group .dropdown-toggle');

    // Add click event listeners to all action buttons
    actionButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Get the ID of this button to find the corresponding menu
            const buttonId = this.id;
            const adminId = buttonId.replace('actionDropdown', '');
            const dropdownMenu = document.getElementById('actionMenu' + adminId);

            // If we can't find the menu by ID, fall back to next sibling
            const menu = dropdownMenu || this.nextElementSibling;

            // Close all other dropdowns first
            document.querySelectorAll('.dropdown-menu.show').forEach(function(openMenu) {
                if (openMenu !== menu) {
                    openMenu.classList.remove('show');
                }
            });

            // Toggle the current dropdown
            menu.classList.toggle('show');

            // Position the dropdown correctly
            positionDropdown(this, menu);
        });
    });

    // Function to position the dropdown correctly
    function positionDropdown(button, dropdown) {
        const buttonRect = button.getBoundingClientRect();
        const tableContainer = document.querySelector('.table-responsive');
        const tableRect = tableContainer ? tableContainer.getBoundingClientRect() : { top: 0, left: 0 };

        // Calculate available space
        const windowHeight = window.innerHeight;
        const spaceBelow = windowHeight - buttonRect.bottom;
        const spaceAbove = buttonRect.top;

        // Set dropdown position
        dropdown.style.position = 'fixed';
        dropdown.style.minWidth = '200px';
        dropdown.style.zIndex = '1050';

        // Position horizontally - align with button
        dropdown.style.left = buttonRect.left + 'px';

        // Position vertically - check if there's enough space below
        if (spaceBelow >= 200 || spaceBelow > spaceAbove) {
            // Position below the button
            dropdown.style.top = buttonRect.bottom + 'px';
            dropdown.style.maxHeight = (spaceBelow - 10) + 'px';
            dropdown.style.overflowY = 'auto';
        } else {
            // Position above the button
            dropdown.style.bottom = (windowHeight - buttonRect.top) + 'px';
            dropdown.style.top = 'auto';
            dropdown.style.maxHeight = (spaceAbove - 10) + 'px';
            dropdown.style.overflowY = 'auto';
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.matches('.dropdown-toggle') && !e.target.closest('.dropdown-menu')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(function(dropdown) {
                dropdown.classList.remove('show');
            });
        }
    });

    // Prevent dropdown menu items from closing the dropdown
    document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
        menu.addEventListener('click', function(e) {
            // Only prevent default for items that aren't form buttons
            if (!e.target.matches('button[type="submit"]')) {
                e.stopPropagation();
            }
        });
    });

    // Set up delete modal
    const deleteModal = document.getElementById('deleteModal');
    if (deleteModal) {
        deleteModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const adminId = button.getAttribute('data-admin-id');
            const adminName = button.getAttribute('data-admin-name');

            document.getElementById('deleteAdminId').value = adminId;
            document.getElementById('adminName').textContent = adminName;
        });
    }

    // Add custom CSS fix for dropdown menus
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .dropdown-menu {
            position: fixed !important;
            z-index: 9999 !important;
            display: none;
            background-color: #fff !important;
            border: 1px solid rgba(0,0,0,.15) !important;
            border-radius: 0.25rem !important;
            padding: 0.5rem 0 !important;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175) !important;
            min-width: 10rem !important;
            margin: 0 !important;
        }
        .dropdown-menu.show {
            display: block !important;
        }
        .dropdown-item {
            display: block !important;
            width: 100% !important;
            padding: 0.5rem 1.5rem !important;
            clear: both !important;
            font-weight: 400 !important;
            color: #212529 !important;
            text-align: inherit !important;
            white-space: nowrap !important;
            background-color: transparent !important;
            border: 0 !important;
            text-decoration: none !important;
            cursor: pointer !important;
        }
        .dropdown-item:hover, .dropdown-item:focus {
            color: #16181b !important;
            text-decoration: none !important;
            background-color: #f8f9fa !important;
        }
        .dropdown-divider {
            height: 0 !important;
            margin: 0.5rem 0 !important;
            overflow: hidden !important;
            border-top: 1px solid #e9ecef !important;
        }
        .btn-group {
            position: relative !important;
        }
        .custom-dropdown {
            overflow-y: auto !important;
            max-height: 300px !important;
        }
        .dropdown-item form {
            margin: 0 !important;
            padding: 0 !important;
        }
        .dropdown-item.text-danger {
            color: #dc3545 !important;
        }
        .dropdown-item.text-danger:hover {
            background-color: #f8d7da !important;
        }
    `;
    document.head.appendChild(styleElement);
});
