/**
 * Deleted Accounts JavaScript
 */

$(document).ready(function() {
    // <PERSON>le select all checkbox
    $('#selectAll').on('change', function() {
        $('.user-checkbox').prop('checked', $(this).prop('checked'));
        updateBulkRestoreButton();
    });
    
    // Handle individual checkboxes
    $('.user-checkbox').on('change', function() {
        updateBulkRestoreButton();
        
        // Update select all checkbox
        if ($('.user-checkbox:checked').length === $('.user-checkbox').length) {
            $('#selectAll').prop('checked', true);
        } else {
            $('#selectAll').prop('checked', false);
        }
    });
    
    // Update bulk restore button state
    function updateBulkRestoreButton() {
        if ($('.user-checkbox:checked').length > 0) {
            $('#bulkRestoreBtn').prop('disabled', false);
        } else {
            $('#bulkRestoreBtn').prop('disabled', true);
        }
    }
    
    // Handle reset filter button
    $('#resetFilterBtn').on('click', function() {
        $('#search').val('');
        $('#date_from').val('');
        $('#date_to').val('');
    });
    
    // Handle view details button
    $('.view-details').on('click', function() {
        const userId = $(this).data('user-id');
        
        // Show loader and hide content
        $('#userDetailsLoader').show();
        $('#userDetailsContent').hide();
        
        // Update restore button href
        $('#restoreUserBtn').attr('href', 'restore_account.php?id=' + userId + '&csrf_token=' + $('input[name="csrf_token"]').val());
        
        // Load user details via AJAX
        $.ajax({
            url: 'ajax/get_deleted_user.php',
            type: 'GET',
            data: {
                user_id: userId,
                csrf_token: $('input[name="csrf_token"]').val()
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Format user data
                    const user = response.data;
                    const deletedDate = new Date(user.deleted_at);
                    const expiryDate = new Date(deletedDate);
                    expiryDate.setDate(expiryDate.getDate() + 30);
                    const now = new Date();
                    const daysLeft = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
                    
                    // Create user details HTML
                    let html = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">Personal Information</h6>
                                <p><strong>Name:</strong> ${user.full_name}</p>
                                <p><strong>Phone:</strong> ${user.phone}</p>
                                <p><strong>Email:</strong> ${user.email || 'N/A'}</p>
                                <p><strong>Address:</strong> ${user.address || 'N/A'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">Account Information</h6>
                                <p><strong>User ID:</strong> ${user.user_id}</p>
                                <p><strong>Verified:</strong> ${user.is_verified == 1 ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-danger">No</span>'}</p>
                                <p><strong>Created:</strong> ${new Date(user.created_at).toLocaleString()}</p>
                                <p><strong>Deleted:</strong> ${deletedDate.toLocaleString()}</p>
                                <p><strong>Days Until Permanent Deletion:</strong> <span class="badge bg-${daysLeft < 7 ? 'danger' : (daysLeft < 15 ? 'warning' : 'info')}">${daysLeft} days</span></p>
                            </div>
                        </div>
                    `;
                    
                    // Update modal content
                    $('#userDetailsContent').html(html);
                    $('#userDetailsLoader').hide();
                    $('#userDetailsContent').show();
                } else {
                    // Show error message
                    $('#userDetailsContent').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> ${response.message}
                        </div>
                    `);
                    $('#userDetailsLoader').hide();
                    $('#userDetailsContent').show();
                }
            },
            error: function() {
                // Show error message
                $('#userDetailsContent').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i> Failed to load user details. Please try again.
                    </div>
                `);
                $('#userDetailsLoader').hide();
                $('#userDetailsContent').show();
            }
        });
    });
    
    // Confirm bulk restore
    $('#bulkActionForm').on('submit', function(e) {
        if (!confirm('Are you sure you want to restore the selected accounts?')) {
            e.preventDefault();
        }
    });
});
