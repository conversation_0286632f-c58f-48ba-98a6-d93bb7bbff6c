<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center">

    <ImageView
        android:id="@+id/bottom_nav_cart_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center"
        android:contentDescription="@string/cart"
        android:src="@drawable/ic_cart" />

    <TextView
        android:id="@+id/bottom_nav_cart_badge"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_gravity="top|end"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/badge_background"
        android:gravity="center"
        android:textColor="@android:color/white"
        android:textSize="10sp"
        android:visibility="gone" />

</FrameLayout>
