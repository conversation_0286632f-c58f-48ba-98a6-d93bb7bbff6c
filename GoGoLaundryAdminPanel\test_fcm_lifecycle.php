<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FCM Token Lifecycle Test - GoGoLaundry</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h3>FCM Token Lifecycle Management Test</h3>
                        <p class="mb-0">Test the complete FCM token lifecycle: Register → Update → Deactivate</p>
                    </div>
                    <div class="card-body">
                        <!-- User Selection -->
                        <div class="mb-3">
                            <label for="userId" class="form-label">Test User ID:</label>
                            <input type="number" id="userId" class="form-control" value="1" min="1">
                            <small class="text-muted">User ID to test with (must exist in database)</small>
                        </div>
                        
                        <!-- Device Info -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="deviceId" class="form-label">Device ID:</label>
                                <input type="text" id="deviceId" class="form-control" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="deviceType" class="form-label">Device Type:</label>
                                <select id="deviceType" class="form-control">
                                    <option value="web">Web</option>
                                    <option value="android">Android</option>
                                    <option value="ios">iOS</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Test Actions -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <button id="registerToken" class="btn btn-primary w-100">1. Register Token</button>
                            </div>
                            <div class="col-md-3">
                                <button id="updateToken" class="btn btn-warning w-100">2. Update Token</button>
                            </div>
                            <div class="col-md-3">
                                <button id="deactivateDevice" class="btn btn-danger w-100">3. Deactivate Device</button>
                            </div>
                            <div class="col-md-3">
                                <button id="deactivateAll" class="btn btn-dark w-100">4. Deactivate All</button>
                            </div>
                        </div>
                        
                        <!-- Current Tokens Display -->
                        <div class="mb-4">
                            <h5>Current Active Tokens:</h5>
                            <button id="refreshTokens" class="btn btn-info btn-sm mb-2">Refresh Token List</button>
                            <div id="tokensList" class="border p-3" style="min-height: 100px; background-color: #f8f9fa;">
                                <em>Click "Refresh Token List" to load current tokens</em>
                            </div>
                        </div>
                        
                        <!-- Test Results -->
                        <div id="results" class="mt-4">
                            <h5>Test Results:</h5>
                            <div id="resultContainer" class="border p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                                <!-- Results will appear here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const resultContainer = document.getElementById('resultContainer');
        let currentToken = null;
        
        // Generate unique device ID
        document.getElementById('deviceId').value = 'test_device_' + Date.now();
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `text-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'dark'}`;
            logEntry.innerHTML = `<small>[${timestamp}]</small> ${message}`;
            resultContainer.appendChild(logEntry);
            resultContainer.scrollTop = resultContainer.scrollHeight;
        }
        
        // Generate a fake FCM token for testing
        function generateFakeToken() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
            let token = '';
            for (let i = 0; i < 152; i++) {
                token += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return token;
        }
        
        // 1. Register Token
        async function registerToken() {
            try {
                const userId = document.getElementById('userId').value;
                const deviceId = document.getElementById('deviceId').value;
                const deviceType = document.getElementById('deviceType').value;
                
                currentToken = generateFakeToken();
                
                log('🔄 Registering FCM token...');
                
                const response = await fetch('api/fcm/register_token.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: parseInt(userId),
                        token: currentToken,
                        device_id: deviceId,
                        device_type: deviceType
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ Token registered successfully!', 'success');
                    log(`📱 Device: ${deviceId} (${deviceType})`);
                    log(`🔑 Token: ${currentToken.substring(0, 30)}...`);
                } else {
                    log('❌ Token registration failed: ' + result.message, 'error');
                }
                
                refreshTokens();
                
            } catch (error) {
                log('❌ Error registering token: ' + error.message, 'error');
            }
        }
        
        // 2. Update Token (simulate token refresh)
        async function updateToken() {
            try {
                if (!currentToken) {
                    log('⚠️ Please register a token first', 'error');
                    return;
                }
                
                const userId = document.getElementById('userId').value;
                const deviceId = document.getElementById('deviceId').value;
                const deviceType = document.getElementById('deviceType').value;
                
                // Generate new token (simulating Firebase token refresh)
                const newToken = generateFakeToken();
                
                log('🔄 Updating FCM token (simulating token refresh)...');
                
                const response = await fetch('api/fcm/register_token.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: parseInt(userId),
                        token: newToken,
                        device_id: deviceId,
                        device_type: deviceType
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ Token updated successfully!', 'success');
                    log(`🔄 Old token: ${currentToken.substring(0, 30)}...`);
                    log(`🆕 New token: ${newToken.substring(0, 30)}...`);
                    currentToken = newToken;
                } else {
                    log('❌ Token update failed: ' + result.message, 'error');
                }
                
                refreshTokens();
                
            } catch (error) {
                log('❌ Error updating token: ' + error.message, 'error');
            }
        }
        
        // 3. Deactivate Device Token
        async function deactivateDevice() {
            try {
                const userId = document.getElementById('userId').value;
                const deviceId = document.getElementById('deviceId').value;
                
                log('🔄 Deactivating device token...');
                
                const response = await fetch('api/fcm/deactivate_token.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: parseInt(userId),
                        device_id: deviceId
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ Device token deactivated successfully!', 'success');
                    log(`📱 Deactivated tokens: ${result.data.tokens_deactivated}`);
                } else {
                    log('❌ Token deactivation failed: ' + result.message, 'error');
                }
                
                refreshTokens();
                
            } catch (error) {
                log('❌ Error deactivating device token: ' + error.message, 'error');
            }
        }
        
        // 4. Deactivate All User Tokens
        async function deactivateAll() {
            try {
                const userId = document.getElementById('userId').value;
                
                log('🔄 Deactivating all user tokens...');
                
                const response = await fetch('api/fcm/deactivate_token.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: parseInt(userId)
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ All user tokens deactivated successfully!', 'success');
                    log(`🗑️ Deactivated tokens: ${result.data.tokens_deactivated}`);
                } else {
                    log('❌ Token deactivation failed: ' + result.message, 'error');
                }
                
                refreshTokens();
                
            } catch (error) {
                log('❌ Error deactivating all tokens: ' + error.message, 'error');
            }
        }
        
        // Refresh token list
        async function refreshTokens() {
            try {
                const userId = document.getElementById('userId').value;
                
                const response = await fetch(`test_fcm_backend.php?action=get_tokens&user_id=${userId}`);
                const html = await response.text();
                
                // Extract token information from the response
                // This is a simple implementation - in production you'd have a proper API
                document.getElementById('tokensList').innerHTML = '<em>Loading tokens...</em>';
                
                setTimeout(() => {
                    document.getElementById('tokensList').innerHTML = `
                        <small class="text-muted">
                            Tokens refreshed at ${new Date().toLocaleTimeString()}<br>
                            Check the database or backend test page for current token status.
                        </small>
                    `;
                }, 500);
                
            } catch (error) {
                document.getElementById('tokensList').innerHTML = '<em class="text-danger">Error loading tokens</em>';
            }
        }
        
        // Event listeners
        document.getElementById('registerToken').addEventListener('click', registerToken);
        document.getElementById('updateToken').addEventListener('click', updateToken);
        document.getElementById('deactivateDevice').addEventListener('click', deactivateDevice);
        document.getElementById('deactivateAll').addEventListener('click', deactivateAll);
        document.getElementById('refreshTokens').addEventListener('click', refreshTokens);
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 FCM Token Lifecycle Test initialized');
            log('📋 Test sequence: Register → Update → Deactivate Device → Deactivate All');
            refreshTokens();
        });
    </script>
</body>
</html>
