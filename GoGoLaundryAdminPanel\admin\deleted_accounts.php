<?php
/**
 * Deleted Accounts Management Page
 *
 * This page allows administrators to view and restore deleted user accounts
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/UserManager.php';

// Initialize user manager
$userManager = new UserManager($pdo);

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$dateFrom = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
$dateTo = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';
$perPage = 10;

// Get deleted users with pagination and filtering
$result = $userManager->getDeletedUsers($page, $perPage, $search, $dateFrom, $dateTo);
$deletedUsers = $result['users'];
$pagination = $result['pagination'];

// Handle bulk restore
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'bulk_restore') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !$adminManager->verifyCsrfToken($_POST['csrf_token'])) {
        $_SESSION['error_message'] = 'Invalid request. Please try again.';
        header('Location: deleted_accounts.php');
        exit;
    }
    
    // Get selected user IDs
    $selectedIds = isset($_POST['selected_users']) ? $_POST['selected_users'] : [];
    
    if (empty($selectedIds)) {
        $_SESSION['error_message'] = 'No accounts selected for restoration.';
        header('Location: deleted_accounts.php');
        exit;
    }
    
    // Perform bulk restore
    $result = $userManager->bulkRestoreDeletedUsers($selectedIds);
    
    // Log action
    $adminManager->logAdminAction(
        $adminData['id'],
        'bulk_account_restore',
        'Restored ' . $result['success_count'] . ' deleted accounts',
        getClientIp()
    );
    
    if ($result['success_count'] > 0) {
        $_SESSION['success_message'] = 'Successfully restored ' . $result['success_count'] . ' account(s).';
        
        if (!empty($result['failed_ids'])) {
            $_SESSION['error_message'] = 'Failed to restore ' . count($result['failed_ids']) . ' account(s).';
        }
    } else {
        $_SESSION['error_message'] = 'Failed to restore any accounts.';
    }
    
    header('Location: deleted_accounts.php');
    exit;
}

// Page title and breadcrumbs
$pageTitle = 'Deleted Accounts';
$breadcrumbs = [
    'Deleted Accounts' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Deleted Accounts</h1>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                    <i class="fas fa-filter me-2"></i> Filter
                </button>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Deleted User Accounts</h5>
        <div class="text-muted small">
            <i class="fas fa-info-circle me-1"></i> Accounts are permanently deleted after 30 days
        </div>
    </div>
    <div class="card-body">
        <form method="post" action="deleted_accounts.php" id="bulkActionForm">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            <input type="hidden" name="action" value="bulk_restore">
            
            <div class="mb-3">
                <button type="submit" class="btn btn-success btn-sm" id="bulkRestoreBtn" disabled>
                    <i class="fas fa-trash-restore me-1"></i> Restore Selected
                </button>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover table-striped">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Deleted On</th>
                            <th>Days Left</th>
                            <th width="100">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($deletedUsers) > 0): ?>
                            <?php foreach ($deletedUsers as $user): ?>
                                <?php
                                    $deletedDate = new DateTime($user['deleted_at']);
                                    $expiryDate = clone $deletedDate;
                                    $expiryDate->modify('+30 days');
                                    $now = new DateTime();
                                    $daysLeft = $now->diff($expiryDate)->days;
                                    $daysLeftClass = $daysLeft < 7 ? 'danger' : ($daysLeft < 15 ? 'warning' : 'info');
                                ?>
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input user-checkbox" type="checkbox" name="selected_users[]" value="<?php echo $user['user_id']; ?>">
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['phone']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email'] ?: 'N/A'); ?></td>
                                    <td><?php echo $deletedDate->format('M d, Y H:i'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $daysLeftClass; ?>">
                                            <?php echo $daysLeft; ?> days
                                        </span>
                                    </td>
                                    <td>
                                        <a href="restore_account.php?id=<?php echo $user['user_id']; ?>&csrf_token=<?php echo $_SESSION['csrf_token']; ?>" class="btn btn-sm btn-success btn-icon" data-bs-toggle="tooltip" title="Restore Account">
                                            <i class="fas fa-trash-restore"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-info btn-icon view-details" data-bs-toggle="modal" data-bs-target="#userDetailsModal" data-user-id="<?php echo $user['user_id']; ?>">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-info-circle me-2"></i> No deleted accounts found
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </form>
        
        <!-- Pagination -->
        <?php if ($pagination['total_pages'] > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($pagination['current_page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                        </li>
                    <?php endif; ?>
                    
                    <?php
                        $startPage = max(1, $pagination['current_page'] - 2);
                        $endPage = min($pagination['total_pages'], $pagination['current_page'] + 2);
                        
                        if ($startPage > 1) {
                            echo '<li class="page-item"><a class="page-link" href="?page=1&search=' . urlencode($search) . '&date_from=' . urlencode($dateFrom) . '&date_to=' . urlencode($dateTo) . '">1</a></li>';
                            if ($startPage > 2) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                        }
                        
                        for ($i = $startPage; $i <= $endPage; $i++) {
                            if ($i == $pagination['current_page']) {
                                echo '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
                            } else {
                                echo '<li class="page-item"><a class="page-link" href="?page=' . $i . '&search=' . urlencode($search) . '&date_from=' . urlencode($dateFrom) . '&date_to=' . urlencode($dateTo) . '">' . $i . '</a></li>';
                            }
                        }
                        
                        if ($endPage < $pagination['total_pages']) {
                            if ($endPage < $pagination['total_pages'] - 1) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                            echo '<li class="page-item"><a class="page-link" href="?page=' . $pagination['total_pages'] . '&search=' . urlencode($search) . '&date_from=' . urlencode($dateFrom) . '&date_to=' . urlencode($dateTo) . '">' . $pagination['total_pages'] . '</a></li>';
                        }
                    ?>
                    
                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&search=<?php echo urlencode($search); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</div>

<!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filterModalLabel">Filter Deleted Accounts</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="deleted_accounts.php" method="get" id="filterForm">
                    <div class="mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Name, phone, or email">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_from" class="form-label">Deleted From</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_to" class="form-label">Deleted To</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="resetFilterBtn">Reset</button>
                <button type="submit" form="filterForm" class="btn btn-primary">Apply Filter</button>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1" aria-labelledby="userDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userDetailsModalLabel">User Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center py-4" id="userDetailsLoader">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading user details...</p>
                </div>
                <div id="userDetailsContent" style="display: none;">
                    <!-- User details will be loaded here via AJAX -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="#" class="btn btn-success" id="restoreUserBtn">
                    <i class="fas fa-trash-restore me-1"></i> Restore Account
                </a>
            </div>
        </div>
    </div>
</div>

<?php
// Add page-specific scripts
$pageScripts = ['js/deleted_accounts.js'];
?>

<?php include 'includes/footer.php'; ?>
