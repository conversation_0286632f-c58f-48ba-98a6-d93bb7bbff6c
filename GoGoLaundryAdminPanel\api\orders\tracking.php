<?php
/**
 * Order Tracking API
 *
 * This file handles the API endpoints for order tracking
 */

// Include required files
require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../includes/functions.php';

// Set headers
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle different request methods
switch ($method) {
    case 'GET':
        // Check if tracking number is provided
        if (isset($_GET['tracking_number'])) {
            getOrderByTrackingNumber($_GET['tracking_number']);
        }
        // Check if order ID is provided
        elseif (isset($_GET['order_id'])) {
            // Check if history is requested
            if (isset($_GET['history']) && $_GET['history'] == 'true') {
                getOrderStatusHistory($_GET['order_id']);
            } else {
                getOrderStatus($_GET['order_id']);
            }
        }
        // If no parameters provided, return error
        else {
            jsonResponse(false, 'Missing required parameters', [], 400);
        }
        break;

    case 'POST':
        // Get input data
        $inputData = json_decode(file_get_contents("php://input"), true);

        // Check if required parameters are provided
        if (!isset($inputData['order_id']) || !isset($inputData['status'])) {
            jsonResponse(false, 'Missing required parameters', [], 400);
        }

        // Check if user is authenticated (admin or delivery personnel)
        // This should be implemented based on your authentication system
        if (!isAuthenticated()) {
            jsonResponse(false, 'Unauthorized', [], 401);
        }

        // Update order status
        updateOrderStatus(
            $inputData['order_id'],
            $inputData['status'],
            isset($inputData['notes']) ? $inputData['notes'] : null,
            isset($inputData['updated_by']) ? $inputData['updated_by'] : null,
            isset($inputData['updated_by_type']) ? $inputData['updated_by_type'] : 'admin',
            isset($inputData['delivery_personnel_id']) ? $inputData['delivery_personnel_id'] : null
        );
        break;

    default:
        // Method not allowed
        header("HTTP/1.1 405 Method Not Allowed");
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}

/**
 * Get order by tracking number
 *
 * @param string $trackingNumber Tracking number
 * @return void
 */
function getOrderByTrackingNumber($trackingNumber) {
    global $pdo;

    try {
        // Prepare query
        $stmt = $pdo->prepare("
            SELECT o.*,
                   u.full_name as customer_name,
                   u.phone as customer_phone,
                   dp.full_name as delivery_person_name,
                   dp.phone as delivery_person_phone
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            LEFT JOIN delivery_personnel dp ON o.delivery_personnel_id = dp.id
            WHERE o.tracking_number = ?
        ");

        // Execute query
        $stmt->execute([$trackingNumber]);

        // Get order
        $order = $stmt->fetch();

        // Check if order exists
        if (!$order) {
            jsonResponse(false, 'Order not found', [], 404);
        }

        // Get order items
        $stmt = $pdo->prepare("
            SELECT oi.*, i.name, i.bn_name, i.image_url, s.name as service_name, s.bn_name as service_bn_name
            FROM order_items oi
            JOIN items i ON oi.item_id = i.id
            JOIN services s ON i.service_id = s.id
            WHERE oi.order_id = ?
        ");

        // Execute query
        $stmt->execute([$order['id']]);

        // Get order items
        $orderItems = $stmt->fetchAll();

        // Get order status history
        $stmt = $pdo->prepare("
            SELECT *
            FROM order_status_history
            WHERE order_id = ?
            ORDER BY created_at ASC
        ");

        // Execute query
        $stmt->execute([$order['id']]);

        // Get order status history
        $statusHistory = $stmt->fetchAll();

        // Convert database payment method to user-friendly display name
        if (isset($order['payment_method'])) {
            $originalPaymentMethod = $order['payment_method'];
            $order['payment_method'] = convertPaymentMethodToDisplayName($originalPaymentMethod);
            error_log("Converted payment method from '$originalPaymentMethod' to '{$order['payment_method']}'");
        }

        // Prepare response data
        $responseData = [
            'order' => $order,
            'items' => $orderItems,
            'status_history' => $statusHistory
        ];

        // Return response
        jsonResponse(true, 'Order retrieved successfully', $responseData);
    } catch (PDOException $e) {
        // Log error
        error_log('Database Error: ' . $e->getMessage());

        // Return error response
        jsonResponse(false, 'An error occurred while retrieving the order', [], 500);
    }
}

/**
 * Get order status
 *
 * @param int $orderId Order ID
 * @return void
 */
function getOrderStatus($orderId) {
    global $pdo;

    try {
        // Prepare query
        $stmt = $pdo->prepare("
            SELECT id, order_number, tracking_number, status, updated_at
            FROM orders
            WHERE id = ?
        ");

        // Execute query
        $stmt->execute([$orderId]);

        // Get order
        $order = $stmt->fetch();

        // Check if order exists
        if (!$order) {
            jsonResponse(false, 'Order not found', [], 404);
        }

        // Return response
        jsonResponse(true, 'Order status retrieved successfully', $order);
    } catch (PDOException $e) {
        // Log error
        error_log('Database Error: ' . $e->getMessage());

        // Return error response
        jsonResponse(false, 'An error occurred while retrieving the order status', [], 500);
    }
}

/**
 * Get order status history
 *
 * @param int $orderId Order ID
 * @return void
 */
function getOrderStatusHistory($orderId) {
    global $pdo;

    try {
        // Prepare query
        $stmt = $pdo->prepare("
            SELECT osh.*,
                   CASE
                       WHEN osh.updated_by_type = 'admin' THEN au.full_name
                       WHEN osh.updated_by_type = 'user' THEN u.full_name
                       WHEN osh.updated_by_type = 'delivery_personnel' THEN dp.full_name
                       ELSE 'System'
                   END as updated_by_name
            FROM order_status_history osh
            LEFT JOIN admin_users au ON osh.updated_by = au.id AND osh.updated_by_type = 'admin'
            LEFT JOIN users u ON osh.updated_by = u.id AND osh.updated_by_type = 'user'
            LEFT JOIN delivery_personnel dp ON osh.updated_by = dp.id AND osh.updated_by_type = 'delivery_personnel'
            WHERE osh.order_id = ?
            ORDER BY osh.created_at ASC
        ");

        // Execute query
        $stmt->execute([$orderId]);

        // Get order status history
        $statusHistory = $stmt->fetchAll();

        // Check if order exists
        if (empty($statusHistory)) {
            // Check if order exists
            $stmt = $pdo->prepare("SELECT id FROM orders WHERE id = ?");
            $stmt->execute([$orderId]);

            if (!$stmt->fetch()) {
                jsonResponse(false, 'Order not found', [], 404);
            }
        }

        // Return response
        jsonResponse(true, 'Order status history retrieved successfully', $statusHistory);
    } catch (PDOException $e) {
        // Log error
        error_log('Database Error: ' . $e->getMessage());

        // Return error response
        jsonResponse(false, 'An error occurred while retrieving the order status history', [], 500);
    }
}

/**
 * Update order status
 *
 * @param int $orderId Order ID
 * @param string $status New status
 * @param string $notes Notes
 * @param int $updatedBy ID of the user who updated the status
 * @param string $updatedByType Type of user who updated the status
 * @param int $deliveryPersonnelId ID of the delivery personnel
 * @return void
 */
function updateOrderStatus($orderId, $status, $notes, $updatedBy, $updatedByType, $deliveryPersonnelId) {
    global $pdo;

    try {
        // Start transaction
        $pdo->beginTransaction();

        // Call stored procedure
        $stmt = $pdo->prepare("
            CALL update_order_status(?, ?, ?, ?, ?, ?)
        ");

        // Execute stored procedure
        $stmt->execute([
            $orderId,
            $status,
            $notes,
            $updatedBy,
            $updatedByType,
            $deliveryPersonnelId
        ]);

        // Commit transaction
        $pdo->commit();

        // Return success response
        jsonResponse(true, 'Order status updated successfully');
    } catch (PDOException $e) {
        // Rollback transaction
        $pdo->rollBack();

        // Log error
        error_log('Database Error: ' . $e->getMessage());

        // Return error response
        jsonResponse(false, 'An error occurred while updating the order status', [], 500);
    }
}

/**
 * Convert database payment method ENUM to user-friendly display name
 *
 * @param string $dbPaymentMethod Database ENUM value
 * @return string User-friendly display name
 */
function convertPaymentMethodToDisplayName($dbPaymentMethod) {
    if (empty($dbPaymentMethod)) {
        return 'Unknown';
    }

    switch (strtolower(trim($dbPaymentMethod))) {
        case 'cash':
            return 'Cash on Delivery';
        case 'card':
            return 'Credit/Debit Card';
        case 'bkash':
            return 'bKash';
        case 'nagad':
            return 'Nagad';
        case 'rocket':
            return 'Rocket';
        case 'mobile_banking':
            return 'Mobile Banking';
        default:
            return ucfirst($dbPaymentMethod);
    }
}

/**
 * Check if user is authenticated
 *
 * @return bool
 */
function isAuthenticated() {
    // This should be implemented based on your authentication system
    // For now, we'll just return true for testing purposes
    return true;
}

// Note: jsonResponse function is already defined in includes/functions.php
