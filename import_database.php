<?php
/**
 * Database Import Script
 * 
 * This script imports the SQL file into the database
 */

// Set time limit to 5 minutes
set_time_limit(300);

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gogolaundry';

// Path to SQL file
$sqlFile = 'gogolaundrynew.sql';

// Check if the SQL file exists
if (!file_exists($sqlFile)) {
    die("Error: SQL file not found at $sqlFile");
}

// Connect to MySQL server
$mysqli = new mysqli($host, $username, $password);

// Check connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

echo "<h2>Database Import Process</h2>";

// Check if database exists
$result = $mysqli->query("SHOW DATABASES LIKE '$database'");
if ($result->num_rows > 0) {
    echo "Database '$database' already exists. Dropping it...<br>";
    
    // Drop the database
    $mysqli->query("DROP DATABASE `$database`");
    
    if ($mysqli->error) {
        die("Error dropping database: " . $mysqli->error);
    }
    
    echo "Database dropped successfully.<br>";
}

// Create the database
echo "Creating database '$database'...<br>";
$mysqli->query("CREATE DATABASE `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

if ($mysqli->error) {
    die("Error creating database: " . $mysqli->error);
}

echo "Database created successfully.<br>";

// Select the database
$mysqli->select_db($database);

if ($mysqli->error) {
    die("Error selecting database: " . $mysqli->error);
}

// Read the SQL file
echo "Reading SQL file...<br>";
$sql = file_get_contents($sqlFile);

if (!$sql) {
    die("Error reading SQL file");
}

echo "SQL file read successfully. Size: " . strlen($sql) . " bytes<br>";

// Split SQL file into individual queries
echo "Executing SQL queries...<br>";
$queries = explode(';', $sql);
$queryCount = 0;
$successCount = 0;
$errorCount = 0;

foreach ($queries as $query) {
    $query = trim($query);
    
    if (empty($query)) {
        continue;
    }
    
    $queryCount++;
    
    // Execute the query
    $result = $mysqli->query($query);
    
    if ($result) {
        $successCount++;
    } else {
        $errorCount++;
        echo "Error executing query: " . $mysqli->error . "<br>";
        echo "Query: " . htmlspecialchars(substr($query, 0, 150)) . "...<br><br>";
    }
}

echo "Import completed.<br>";
echo "Total queries: $queryCount<br>";
echo "Successful queries: $successCount<br>";
echo "Failed queries: $errorCount<br>";

// Check if items table exists and has data
$result = $mysqli->query("SELECT * FROM `items` WHERE id IN (1, 2)");

if ($result) {
    echo "<h3>Items in Database:</h3>";
    echo "<ul>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: {$row['id']}, Name: {$row['name']}, Price: {$row['price']}</li>";
    }
    
    echo "</ul>";
} else {
    echo "Error checking items table: " . $mysqli->error . "<br>";
}

// Close the connection
$mysqli->close();

echo "<br><a href='GoGoLaundryAdminPanel/admin/index.php'>Go to Admin Panel</a>";
?>
