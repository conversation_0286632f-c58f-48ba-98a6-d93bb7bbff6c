<?php
/**
 * API endpoint to check notification read status
 *
 * Required parameters:
 * - notification_id: ID of the notification to check
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 * - data: Object with notification details including is_read status
 */

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Check if notification_id is provided
if (!isset($_GET['notification_id']) || empty($_GET['notification_id'])) {
    $response['message'] = 'Notification ID is required';
    echo json_encode($response);
    exit();
}

// Get parameters
$notification_id = intval($_GET['notification_id']);

try {
    // Get notification details - only return unread notifications (is_read=0)
    $stmt = $pdo->prepare("
        SELECT id, user_id, order_id, title, message, type, is_read, created_at
        FROM notifications
        WHERE id = ? AND is_read = 0
    ");
    $stmt->execute([$notification_id]);

    if ($stmt->rowCount() > 0) {
        $notification = $stmt->fetch(PDO::FETCH_ASSOC);
        $response['success'] = true;
        $response['message'] = 'Notification found';
        $response['data'] = $notification;
    } else {
        // Check if notification exists but is already read
        $stmt = $pdo->prepare("
            SELECT id, is_read
            FROM notifications
            WHERE id = ?
        ");
        $stmt->execute([$notification_id]);

        if ($stmt->rowCount() > 0) {
            $check = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($check['is_read'] == 1) {
                $response['message'] = 'Notification is already read';
            } else {
                $response['message'] = 'Notification not found';
            }
        } else {
            $response['message'] = 'Notification not found';
        }
    }
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    // Log the error for server-side debugging
    error_log('Notification API Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
