<?php
/**
 * Admin Activity Logs
 *
 * This page displays admin activity logs
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/AdminManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Initialize admin manager
$adminManager = new AdminManager($pdo);

// Get admin data
$adminData = $adminManager->getAdminById($_SESSION['admin_id']);

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$dateFrom = isset($_GET['date_from']) ? trim($_GET['date_from']) : '';
$dateTo = isset($_GET['date_to']) ? trim($_GET['date_to']) : '';
$adminFilter = isset($_GET['admin']) ? trim($_GET['admin']) : '';
$actionFilter = isset($_GET['action']) ? trim($_GET['action']) : '';
$perPage = 20;

// Build query conditions
$whereClause = '';
$params = [];

if (!empty($search)) {
    $whereClause .= " WHERE (al.details LIKE ? OR au.username LIKE ? OR al.ip_address LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($adminFilter)) {
    $whereClause .= empty($whereClause) ? " WHERE" : " AND";
    $whereClause .= " al.admin_id = ?";
    $params[] = $adminFilter;
}

if (!empty($actionFilter)) {
    $whereClause .= empty($whereClause) ? " WHERE" : " AND";
    $whereClause .= " al.action = ?";
    $params[] = $actionFilter;
}

if (!empty($dateFrom)) {
    $whereClause .= empty($whereClause) ? " WHERE" : " AND";
    $whereClause .= " al.created_at >= ?";
    $params[] = $dateFrom . ' 00:00:00';
}

if (!empty($dateTo)) {
    $whereClause .= empty($whereClause) ? " WHERE" : " AND";
    $whereClause .= " al.created_at <= ?";
    $params[] = $dateTo . ' 23:59:59';
}

// Count total records
$countSql = "
    SELECT COUNT(*) as count
    FROM admin_logs al
    JOIN admin_users au ON al.admin_id = au.id
    $whereClause
";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalCount = $countStmt->fetch()['count'];

// Calculate pagination
$offset = ($page - 1) * $perPage;
$totalPages = ceil($totalCount / $perPage);

// Get logs with pagination
$sql = "
    SELECT al.*, au.username
    FROM admin_logs al
    JOIN admin_users au ON al.admin_id = au.id
    $whereClause
    ORDER BY al.created_at DESC
    LIMIT ?, ?
";

// Add pagination parameters
$params[] = $offset;
$params[] = $perPage;

// Prepare and execute query
$stmt = $pdo->prepare($sql);

// Bind parameters with correct types
for ($i = 0; $i < count($params); $i++) {
    if ($i >= count($params) - 2) {
        $stmt->bindValue($i + 1, $params[$i], PDO::PARAM_INT);
    } else {
        $stmt->bindValue($i + 1, $params[$i]);
    }
}

$stmt->execute();
$logs = $stmt->fetchAll();

// Get all admins for filter dropdown
$adminsStmt = $pdo->query("SELECT id, username FROM admin_users ORDER BY username");
$admins = $adminsStmt->fetchAll();

// Get all action types for filter dropdown
$actionsStmt = $pdo->query("SELECT DISTINCT action FROM admin_logs ORDER BY action");
$actions = $actionsStmt->fetchAll();

// Log this view action
$adminManager->logAdminAction(
    $adminData['id'],
    'view_logs',
    'Viewed admin activity logs',
    getClientIp()
);

// Page title and breadcrumbs
$pageTitle = 'Activity Logs';
$breadcrumbs = [
    'Activity Logs' => false
];

// Include header
include 'includes/header.php';
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Activity Logs</h1>
    </div>
    <div class="col-md-6 text-end">
        <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
            <i class="fas fa-filter me-1"></i> Filter Logs
        </button>
    </div>
</div>

<div class="collapse mb-4" id="filterCollapse">
    <div class="card">
        <div class="card-body">
            <form action="activity_logs.php" method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="Search details, username, IP..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-4">
                    <label for="admin" class="form-label">Admin</label>
                    <select class="form-select" id="admin" name="admin">
                        <option value="">All Admins</option>
                        <?php foreach ($admins as $admin): ?>
                            <option value="<?php echo $admin['id']; ?>" <?php echo $adminFilter == $admin['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($admin['username']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="action" class="form-label">Action</label>
                    <select class="form-select" id="action" name="action">
                        <option value="">All Actions</option>
                        <?php foreach ($actions as $action): ?>
                            <option value="<?php echo $action['action']; ?>" <?php echo $actionFilter == $action['action'] ? 'selected' : ''; ?>>
                                <?php echo ucfirst(str_replace('_', ' ', $action['action'])); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($dateFrom); ?>">
                </div>
                <div class="col-md-4">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($dateTo); ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> Apply Filters
                    </button>
                    <a href="activity_logs.php" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> Clear Filters
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Admin Activity Logs</h5>
            </div>
            <div class="col text-end">
                <span class="badge bg-primary"><?php echo number_format($totalCount); ?> Total Records</span>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (count($logs) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Admin</th>
                            <th>Action</th>
                            <th>Details</th>
                            <th>IP Address</th>
                            <th>Date & Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($log['username']); ?></td>
                                <td>
                                    <?php
                                        $actionClass = '';
                                        switch ($log['action']) {
                                            case 'login':
                                                $actionClass = 'text-success';
                                                break;
                                            case 'login_attempt':
                                                $actionClass = 'text-warning';
                                                break;
                                            case 'logout':
                                                $actionClass = 'text-info';
                                                break;
                                            case 'settings_update':
                                                $actionClass = 'text-primary';
                                                break;
                                            case 'account_restore':
                                            case 'bulk_account_restore':
                                                $actionClass = 'text-warning';
                                                break;
                                            case 'password_reset':
                                                $actionClass = 'text-danger';
                                                break;
                                        }
                                    ?>
                                    <span class="<?php echo $actionClass; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $log['action'])); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($log['details']); ?></td>
                                <td><small><?php echo htmlspecialchars($log['ip_address']); ?></small></td>
                                <td><?php echo (new DateTime($log['created_at']))->format('M d, Y H:i:s'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&admin=<?php echo urlencode($adminFilter); ?>&action=<?php echo urlencode($actionFilter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>">
                                Previous
                            </a>
                        </li>

                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&admin=<?php echo urlencode($adminFilter); ?>&action=<?php echo urlencode($actionFilter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&admin=<?php echo urlencode($adminFilter); ?>&action=<?php echo urlencode($actionFilter); ?>&date_from=<?php echo urlencode($dateFrom); ?>&date_to=<?php echo urlencode($dateTo); ?>">
                                Next
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No activity logs found matching your criteria.
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
