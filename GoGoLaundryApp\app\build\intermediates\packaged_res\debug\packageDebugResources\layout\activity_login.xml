<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".LoginActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="@dimen/elevation_appbar"
        app:layout_constraintTop_toTopOf="parent"
        app:title="@string/login_title"
        android:fontFamily="@font/bnlatxf"
        app:titleTextColor="@color/white" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:tabIndicatorColor="@color/white"
        app:tabSelectedTextColor="@color/white"
        app:tabTextColor="@color/primary_light">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_with_password" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_with_otp" />

    </com.google.android.material.tabs.TabLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tabLayout">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/passwordLoginView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium">

                <!-- Background Image -->
                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:alpha="0.4"
                    android:scaleType="fitXY"
                    android:src="@drawable/approved"
                    tools:ignore="ContentDescription" />

                <!-- CardView Login Form (Centered) -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="20dp"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/draw"
                            android:layout_width="100dp"
                            android:layout_height="100dp"
                            android:layout_margin="5dp"
                            app:cardBackgroundColor="@color/white"
                            app:cardCornerRadius="25dp"
                            app:cardElevation="2dp"
                            app:cardMaxElevation="15dp">

                            <ImageView
                                android:id="@+id/splash_logo"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/profile"
                                tools:ignore="ContentDescription" />

                        </androidx.cardview.widget.CardView>

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/phoneLayout"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:hint="আপনার ফোন নম্বর দিন"
                            app:boxStrokeColor="#F44336"
                            app:hintTextColor="#2196F3"
                            app:startIconDrawable="@drawable/baseline_phone_24">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/phoneEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/bnlatxf"
                                android:inputType="phone" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="8dp" />

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/passwordLayout"
                            style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="10dp"
                            android:hint="আপনার পাসওয়ার্ড দিন"
                            app:boxStrokeColor="#2196F3"
                            app:endIconMode="password_toggle"
                            app:hintTextColor="#2196F3"
                            app:startIconDrawable="@drawable/baseline_password_24">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/passwordEditText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:fontFamily="@font/bnlatxf"
                                android:inputType="textPassword" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <TextView
                            android:id="@+id/forgotPasswordText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/bnlatxf"
                            android:gravity="end"
                            android:padding="4dp"
                            android:text="@string/forgot_password"
                            android:textColor="#2196F3"
                            android:textSize="14sp" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/loginButton"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/button_height"
                            android:layout_marginTop="20dp"
                            android:layout_marginBottom="20dp"
                            android:text="@string/login"
                            android:backgroundTint="#F44336"
                            android:fontFamily="@font/bnlatxf"
                            app:cornerRadius="@dimen/button_corner_radius" />

                        <TextView
                            android:id="@+id/signupText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/bnlatxf"
                            android:gravity="center"
                            android:padding="16dp"
                            android:text="@string/dont_have_account"
                            android:textColor="#2196F3"
                            android:textSize="14sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>




            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- OTP Login View -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/otpLoginView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/margin_medium"
                android:visibility="gone">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/otpPhoneLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_large"
                    android:hint="@string/phone_number"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/otpPhoneEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/sendOtpButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:text="@string/send_otp"
                    app:cornerRadius="@dimen/button_corner_radius"
                    app:layout_constraintTop_toBottomOf="@id/otpPhoneLayout" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/otpLayout"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:hint="@string/enter_otp"
                    app:layout_constraintTop_toBottomOf="@id/sendOtpButton">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/otpEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="6" />

                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                    android:id="@+id/otpTimerText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/margin_small"
                    android:fontFamily="@font/bnlatxf"
                    android:textColor="@color/text_secondary"
                    android:textSize="@dimen/text_size_small"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/otpLayout"
                    tools:text="Time remaining: 10:00" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/verifyOtpButton"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/button_height"
                    android:layout_marginTop="@dimen/margin_medium"
                    android:text="@string/verify_and_login"
                    app:cornerRadius="@dimen/button_corner_radius"
                    app:layout_constraintTop_toBottomOf="@id/otpTimerText" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>