<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow -->
    <item android:top="2dp" android:left="2dp">
        <shape android:shape="oval">
            <solid android:color="#20000000" />
        </shape>
    </item>
    
    <!-- Main background with gradient -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="oval">
            <gradient
                android:startColor="#80FFFFFF"
                android:endColor="#60FFFFFF"
                android:angle="45"
                android:type="linear" />
        </shape>
    </item>
    
    <!-- Border -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="oval">
            <stroke 
                android:width="1dp" 
                android:color="#A0FFFFFF" />
        </shape>
    </item>
    
    <!-- Inner glow -->
    <item android:top="4dp" android:left="4dp" android:right="6dp" android:bottom="6dp">
        <shape android:shape="oval">
            <stroke 
                android:width="1dp" 
                android:color="#C0FFFFFF" />
        </shape>
    </item>
    
</layer-list>
