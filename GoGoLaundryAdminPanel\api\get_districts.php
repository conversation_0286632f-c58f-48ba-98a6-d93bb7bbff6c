<?php
/**
 * Get Districts API Endpoint
 *
 * This endpoint returns districts for a given division
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get division ID from query parameters
$divisionId = isset($_GET['division_id']) ? (int)$_GET['division_id'] : 0;

// Validate division ID
if ($divisionId <= 0) {
    jsonResponse(false, 'Invalid division ID', [], 400);
}

// Get districts for the given division
$stmt = $pdo->prepare("SELECT * FROM districts WHERE division_id = ? ORDER BY name");
$stmt->execute([$divisionId]);
$districts = $stmt->fetchAll();

// Return success response
jsonResponse(true, 'Districts retrieved successfully', $districts);
