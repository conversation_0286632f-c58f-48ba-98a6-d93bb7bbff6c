-- Create promotional_dialogs table
CREATE TABLE IF NOT EXISTS `promotional_dialogs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `subtitle` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `discount_text` varchar(100) NOT NULL,
  `promo_code` varchar(50) DEFAULT NULL,
  `button_text` varchar(50) NOT NULL DEFAULT 'Shop Now',
  `image_path` varchar(500) DEFAULT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `background_color` varchar(7) DEFAULT '#6c757d',
  `text_color` varchar(7) DEFAULT '#ffffff',
  `button_color` varchar(7) DEFAULT '#ffd700',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_dates` (`start_date`, `end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample promotional dialog
INSERT INTO `promotional_dialogs` (
  `title`, 
  `subtitle`, 
  `description`, 
  `discount_text`, 
  `promo_code`, 
  `button_text`, 
  `background_color`, 
  `text_color`, 
  `button_color`, 
  `is_active`, 
  `start_date`, 
  `end_date`
) VALUES (
  'HOT PICKS\nLOW PRICES',
  'Best Deals on Best Prices',
  'Limited time offer on all laundry services. Get the best quality service at unbeatable prices!',
  'UP TO\n60%\nOFF',
  'SAVE60',
  'Shop Now',
  '#6c757d',
  '#ffffff',
  '#ffd700',
  1,
  NOW(),
  DATE_ADD(NOW(), INTERVAL 30 DAY)
);
