<?php
/**
 * Promo Code Manager Class
 *
 * This class handles promo code operations
 */

class PromoCodeManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get all promo codes with pagination and filtering
     *
     * @param int $page Current page number
     * @param int $perPage Items per page
     * @param string $search Search term
     * @param string $status Filter by status (active, inactive, expired, all)
     * @return array Promo codes and pagination data
     */
    public function getAllPromoCodes($page = 1, $perPage = 10, $search = '', $status = 'all') {
        // Calculate offset
        $offset = ($page - 1) * $perPage;

        // Base query
        $query = "
            SELECT SQL_CALC_FOUND_ROWS *
            FROM promo_codes
            WHERE 1=1
        ";
        $params = [];

        // Add search condition if provided
        if (!empty($search)) {
            $query .= " AND (code LIKE ? OR discount_type LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }

        // Add status filter
        if ($status === 'active') {
            $query .= " AND is_active = 1 AND start_date <= NOW() AND end_date >= NOW()";
        } elseif ($status === 'inactive') {
            $query .= " AND is_active = 0";
        } elseif ($status === 'expired') {
            $query .= " AND (end_date < NOW() OR (usage_limit IS NOT NULL AND usage_count >= usage_limit))";
        }

        // Add order and limit
        $query .= " ORDER BY created_at DESC LIMIT ?, ?";
        $params[] = $offset;
        $params[] = $perPage;

        // Execute query
        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);
        $promoCodes = $stmt->fetchAll();

        // Get total count
        $stmt = $this->pdo->query("SELECT FOUND_ROWS() as total");
        $total = $stmt->fetch()['total'];

        // Calculate pagination data
        $totalPages = ceil($total / $perPage);

        return [
            'promo_codes' => $promoCodes,
            'pagination' => [
                'count' => $total,
                'perPage' => $perPage,
                'current' => $page,
                'total' => $totalPages,
                'has_more' => $page < $totalPages
            ]
        ];
    }

    /**
     * Get promo code by ID
     *
     * @param int $id Promo code ID
     * @return array|bool Promo code data or false if not found
     */
    public function getPromoCodeById($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM promo_codes WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    /**
     * Get promo code by code
     *
     * @param string $code Promo code
     * @return array|bool Promo code data or false if not found
     */
    public function getPromoCodeByCode($code) {
        $stmt = $this->pdo->prepare("SELECT * FROM promo_codes WHERE code = ?");
        $stmt->execute([$code]);
        return $stmt->fetch();
    }

    /**
     * Add a new promo code
     *
     * @param string $code Promo code
     * @param string $discountType Discount type (percentage or fixed)
     * @param float $discountValue Discount value
     * @param float $minOrderValue Minimum order value
     * @param float|null $maxDiscount Maximum discount amount (for percentage discounts)
     * @param string $startDate Start date (Y-m-d H:i:s)
     * @param string $endDate End date (Y-m-d H:i:s)
     * @param bool $isActive Whether the promo code is active
     * @param int|null $usageLimit Maximum number of times the code can be used
     * @return int|bool New promo code ID or false on failure
     */
    public function addPromoCode($code, $discountType, $discountValue, $minOrderValue, $maxDiscount, $startDate, $endDate, $isActive = true, $usageLimit = null) {
        $stmt = $this->pdo->prepare("
            INSERT INTO promo_codes (
                code, discount_type, discount_value, min_order_value, max_discount,
                start_date, end_date, is_active, usage_limit
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            strtoupper($code),
            $discountType,
            $discountValue,
            $minOrderValue,
            $maxDiscount,
            $startDate,
            $endDate,
            $isActive ? 1 : 0,
            $usageLimit
        ]);

        if ($result) {
            return $this->pdo->lastInsertId();
        }

        return false;
    }

    /**
     * Update a promo code
     *
     * @param int $id Promo code ID
     * @param string $code Promo code
     * @param string $discountType Discount type (percentage or fixed)
     * @param float $discountValue Discount value
     * @param float $minOrderValue Minimum order value
     * @param float|null $maxDiscount Maximum discount amount (for percentage discounts)
     * @param string $startDate Start date (Y-m-d H:i:s)
     * @param string $endDate End date (Y-m-d H:i:s)
     * @param bool $isActive Whether the promo code is active
     * @param int|null $usageLimit Maximum number of times the code can be used
     * @return bool Success
     */
    public function updatePromoCode($id, $code, $discountType, $discountValue, $minOrderValue, $maxDiscount, $startDate, $endDate, $isActive, $usageLimit) {
        $stmt = $this->pdo->prepare("
            UPDATE promo_codes
            SET code = ?, discount_type = ?, discount_value = ?, min_order_value = ?,
                max_discount = ?, start_date = ?, end_date = ?, is_active = ?, usage_limit = ?
            WHERE id = ?
        ");

        return $stmt->execute([
            strtoupper($code),
            $discountType,
            $discountValue,
            $minOrderValue,
            $maxDiscount,
            $startDate,
            $endDate,
            $isActive ? 1 : 0,
            $usageLimit,
            $id
        ]);
    }

    /**
     * Delete a promo code
     *
     * @param int $id Promo code ID
     * @return bool Success
     */
    public function deletePromoCode($id) {
        // Check if promo code is in use
        if ($this->isPromoCodeInUse($id)) {
            return false;
        }

        $stmt = $this->pdo->prepare("DELETE FROM promo_codes WHERE id = ?");
        return $stmt->execute([$id]);
    }

    /**
     * Check if a promo code is in use
     *
     * @param int $id Promo code ID
     * @return bool True if in use, false otherwise
     */
    public function isPromoCodeInUse($id) {
        $stmt = $this->pdo->prepare("
            SELECT COUNT(*) as count
            FROM orders
            WHERE promo_code_id = ?
        ");
        $stmt->execute([$id]);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    /**
     * Check if a promo code already exists
     *
     * @param string $code Promo code
     * @param int|null $excludeId Exclude this ID when checking (for updates)
     * @return bool True if exists, false otherwise
     */
    public function codeExists($code, $excludeId = null) {
        $query = "SELECT COUNT(*) as count FROM promo_codes WHERE code = ?";
        $params = [strtoupper($code)];

        if ($excludeId !== null) {
            $query .= " AND id != ?";
            $params[] = $excludeId;
        }

        $stmt = $this->pdo->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    /**
     * Get all active promo codes
     *
     * @return array Active promo codes
     */
    public function getActivePromoCodes() {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM promo_codes
            WHERE is_active = 1
            AND start_date <= NOW()
            AND end_date >= NOW()
            AND (usage_limit IS NULL OR usage_count < usage_limit)
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }
}
