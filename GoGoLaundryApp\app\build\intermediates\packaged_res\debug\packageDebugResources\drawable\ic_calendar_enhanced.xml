<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">

    <!-- Calendar background with gradient effect -->
    <path
        android:fillColor="@color/enhanced_picker_icon_tint"
        android:pathData="M19,4h-1V2h-2v2H8V2H6v2H5C3.89,4 3.01,4.9 3.01,6L3,20c0,1.1 0.89,2 2,2h14c1.1,0 2,-0.9 2,-2V6C21,4.9 20.1,4 19,4zM19,20H5V9h14V20z" />

    <!-- Calendar header -->
    <path
        android:fillColor="@color/enhanced_picker_icon_tint"
        android:pathData="M19,7H5V6h14V7z" />

    <!-- Calendar grid with enhanced spacing -->
    <path
        android:fillColor="@color/white"
        android:pathData="M7,11h1.5v1.5H7V11zM10.25,11h1.5v1.5h-1.5V11zM13.5,11h1.5v1.5h-1.5V11zM16.75,11h1.5v1.5h-1.5V11z" />
    
    <path
        android:fillColor="@color/white"
        android:pathData="M7,14h1.5v1.5H7V14zM10.25,14h1.5v1.5h-1.5V14zM13.5,14h1.5v1.5h-1.5V14zM16.75,14h1.5v1.5h-1.5V14z" />
    
    <path
        android:fillColor="@color/white"
        android:pathData="M7,17h1.5v1.5H7V17zM10.25,17h1.5v1.5h-1.5V17zM13.5,17h1.5v1.5h-1.5V17z" />

    <!-- Highlight current date -->
    <path
        android:fillColor="@color/enhanced_picker_gradient_start"
        android:pathData="M10.25,14h1.5v1.5h-1.5V14z" />

</vector>
