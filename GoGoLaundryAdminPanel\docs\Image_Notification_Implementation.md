# Image Notification Implementation Guide

## Overview
This document outlines the implementation of image support in the GoGoLaundry notification system, allowing administrators to send notifications with images that are displayed in the Android app.

## Features Implemented

### 1. Database Changes
- **Added `image_url` column** to the `notifications` table
- **Column specifications**: `VARCHAR(500) NULL DEFAULT NULL`
- **Added index** on `image_url` for better performance
- **Migration script** created for easy deployment

### 2. Admin Panel Enhancements
- **Image upload form** with two options:
  - Direct image URL input
  - File upload (max 5MB, supports JPEG, PNG, GIF, WebP)
- **File validation** for type and size
- **Automatic file naming** with timestamp and unique ID
- **Upload directory** creation (`/uploads/notifications/`)
- **Form enctype** updated to support file uploads

### 3. Android App Enhancements
- **Enhanced FirebaseMessagingService** to handle images
- **BigPictureStyle** notifications for images
- **BigTextStyle** notifications for text-only messages
- **Asynchronous image loading** with proper error handling
- **Image resizing** to prevent memory issues (max 1024x1024)
- **Fallback mechanism** if image loading fails
- **Updated Notification model** to include imageUrl field

## File Changes

### Database
- `GoGoLaundryAdminPanel/database/migrations/add_image_url_to_notifications.sql`
- `GoGoLaundryAdminPanel/database/migrations/run_migration.php`

### Admin Panel
- `GoGoLaundryAdminPanel/admin/notifications.php` - Enhanced form and processing
- `GoGoLaundryAdminPanel/uploads/notifications/` - New upload directory

### Android App
- `GoGoLaundryApp/app/src/main/java/com/mdsadrulhasan/gogolaundry/model/Notification.java`
- `GoGoLaundryApp/app/src/main/java/com/mdsadrulhasan/gogolaundry/fcm/GoGoLaundryFirebaseMessagingService.java`

## Usage Instructions

### For Administrators
1. **Access notifications page**: Navigate to Admin Panel → Notifications
2. **Click "Send Notification"** button
3. **Fill in notification details**:
   - Title (required)
   - Message (required)
   - Type (custom, promo, system)
4. **Add image (optional)**:
   - **Option 1**: Enter direct image URL
   - **Option 2**: Upload image file (max 5MB)
5. **Select recipients**: Choose specific user or send to all
6. **Send notification**: Click "Send Notification"

### Image Requirements
- **File formats**: JPEG, PNG, GIF, WebP
- **Maximum size**: 5MB
- **Recommended dimensions**: 1024x1024 or smaller
- **URL format**: Must be a valid HTTP/HTTPS URL

## Technical Details

### Image Processing Flow
1. **Admin uploads image** or provides URL
2. **Server validates** file type and size
3. **File is saved** with unique filename
4. **Image URL is stored** in database
5. **FCM notification sent** with image_url in data payload
6. **Android app receives** notification
7. **App downloads image** asynchronously
8. **Notification displayed** with BigPictureStyle

### Error Handling
- **Invalid file types** → Error message displayed
- **File too large** → Error message displayed
- **Upload failure** → Error message displayed
- **Image load failure** → Fallback to text notification
- **Network timeout** → 10-second timeout with fallback

### Performance Optimizations
- **Image resizing** on Android to prevent memory issues
- **Asynchronous loading** to prevent UI blocking
- **Database indexing** on image_url column
- **File validation** before upload

## Testing

### Test Cases
1. **Text-only notification** → Should display with BigTextStyle
2. **Notification with image URL** → Should display with BigPictureStyle
3. **Notification with uploaded image** → Should display with BigPictureStyle
4. **Invalid image URL** → Should fallback to text notification
5. **Large image** → Should be resized and displayed
6. **Network failure** → Should fallback to text notification

### Test URLs for Image Notifications
- `https://picsum.photos/800/600` - Random image
- `https://via.placeholder.com/500x300.png` - Placeholder image

## Security Considerations
- **File type validation** prevents malicious uploads
- **File size limits** prevent storage abuse
- **Unique filenames** prevent conflicts
- **Upload directory** outside web root (recommended)
- **URL validation** for external image links

## Future Enhancements
- **Image compression** before storage
- **CDN integration** for better performance
- **Image preview** in admin panel
- **Bulk image upload** support
- **Image gallery** for reusable images
- **Automatic image optimization**
