<?php
/**
 * Create Order Page
 *
 * This page allows administrators to create new orders manually
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OrderManager.php';
require_once '../includes/UserManager.php';
require_once '../includes/ServiceManager.php';
require_once '../includes/ItemManager.php';
require_once '../includes/PromoCodeManager.php';
require_once '../includes/LocationManager.php';
require_once '../includes/SettingsManager.php';

// Initialize managers
$orderManager = new OrderManager($pdo);
$userManager = new UserManager($pdo);
$serviceManager = new ServiceManager($pdo);
$itemManager = new ItemManager($pdo);
$promoCodeManager = new PromoCodeManager($pdo);
$locationManager = new LocationManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Get all users for dropdown
$result = $userManager->getAllUsers(1, 1000); // Get up to 1000 users without pagination
$users = $result['users'];

// Get all services and items
$servicesResult = $serviceManager->getAllServices(1, 1000); // Get up to 1000 services without pagination
$services = $servicesResult['services'];

$itemsResult = $itemManager->getAllItems(1, 1000); // Get up to 1000 items without pagination
$items = $itemsResult['items'];

// Debug services and items
error_log("Services: " . print_r($services, true));
error_log("Items: " . print_r($items, true));

// Get all active promo codes
$promoCodes = $promoCodeManager->getActivePromoCodes();

// Get all divisions
$divisions = $locationManager->getAllDivisions();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_order'])) {
    // Validate form data
    $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : null;
    $promoCode = isset($_POST['promo_code']) ? trim($_POST['promo_code']) : null;
    $paymentMethod = isset($_POST['payment_method']) ? trim($_POST['payment_method']) : null;
    $pickupAddress = isset($_POST['pickup_address']) ? trim($_POST['pickup_address']) : null;
    $pickupDivisionId = isset($_POST['pickup_division_id']) ? (int)$_POST['pickup_division_id'] : null;
    $pickupDistrictId = isset($_POST['pickup_district_id']) ? (int)$_POST['pickup_district_id'] : null;
    $pickupUpazillaId = isset($_POST['pickup_upazilla_id']) ? (int)$_POST['pickup_upazilla_id'] : null;
    $pickupDate = isset($_POST['pickup_date']) ? trim($_POST['pickup_date']) : null;
    $pickupTimeSlot = isset($_POST['pickup_time_slot']) ? trim($_POST['pickup_time_slot']) : null;
    $deliveryAddress = isset($_POST['delivery_address']) ? trim($_POST['delivery_address']) : null;
    $deliveryDivisionId = isset($_POST['delivery_division_id']) ? (int)$_POST['delivery_division_id'] : null;
    $deliveryDistrictId = isset($_POST['delivery_district_id']) ? (int)$_POST['delivery_district_id'] : null;
    $deliveryUpazillaId = isset($_POST['delivery_upazilla_id']) ? (int)$_POST['delivery_upazilla_id'] : null;
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : null;
    $orderItems = isset($_POST['order_items']) ? json_decode($_POST['order_items'], true) : [];

    // Validate required fields
    $errors = [];

    if (empty($userId)) {
        $errors[] = 'Customer is required';
    }

    if (empty($paymentMethod)) {
        $errors[] = 'Payment method is required';
    }

    if (empty($pickupAddress)) {
        $errors[] = 'Pickup address is required';
    }

    if (empty($pickupDivisionId)) {
        $errors[] = 'Pickup division is required';
    }

    if (empty($pickupDistrictId)) {
        $errors[] = 'Pickup district is required';
    }

    if (empty($pickupUpazillaId)) {
        $errors[] = 'Pickup upazilla is required';
    }

    if (empty($pickupDate)) {
        $errors[] = 'Pickup date is required';
    }

    if (empty($pickupTimeSlot)) {
        $errors[] = 'Pickup time slot is required';
    }

    if (empty($deliveryAddress)) {
        $errors[] = 'Delivery address is required';
    }

    if (empty($deliveryDivisionId)) {
        $errors[] = 'Delivery division is required';
    }

    if (empty($deliveryDistrictId)) {
        $errors[] = 'Delivery district is required';
    }

    if (empty($deliveryUpazillaId)) {
        $errors[] = 'Delivery upazilla is required';
    }

    if (empty($orderItems)) {
        $errors[] = 'At least one order item is required';
    }

    // If no errors, create order
    if (empty($errors)) {
        try {
            // Start transaction
            $pdo->beginTransaction();

            // Calculate order totals
            $subtotal = 0;
            foreach ($orderItems as $item) {
                $subtotal += $item['total'];
            }

            // Apply promo code discount if applicable
            $discount = 0;
            $promoCodeId = null;

            if (!empty($promoCode)) {
                $promoCodeData = $promoCodeManager->getPromoCodeByCode($promoCode);

                if ($promoCodeData && $promoCodeData['is_active']) {
                    $promoCodeId = $promoCodeData['id'];

                    // Calculate discount
                    if ($promoCodeData['discount_type'] === 'percentage') {
                        $discount = ($subtotal * $promoCodeData['discount_value']) / 100;

                        // Apply max discount if set
                        if ($promoCodeData['max_discount'] > 0 && $discount > $promoCodeData['max_discount']) {
                            $discount = $promoCodeData['max_discount'];
                        }
                    } else {
                        $discount = $promoCodeData['discount_value'];
                    }
                }
            }

            // Get delivery fee from settings
            $deliveryFee = (float)$settingsManager->getSetting('delivery_fee', 50.00);

            // Calculate total
            $total = $subtotal - $discount + $deliveryFee;

            // Generate unique order number and tracking number
            $orderNumber = 'ORD' . date('ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $trackingNumber = 'TRK' . date('ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

            // Insert order
            $stmt = $pdo->prepare("
                INSERT INTO orders (
                    order_number, tracking_number, user_id, promo_code_id,
                    subtotal, discount, delivery_fee, total, payment_method,
                    status, pickup_address, pickup_division_id, pickup_district_id,
                    pickup_upazilla_id, pickup_date, pickup_time_slot,
                    delivery_address, delivery_division_id, delivery_district_id,
                    delivery_upazilla_id, notes
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )
            ");

            $stmt->execute([
                $orderNumber,
                $trackingNumber,
                $userId,
                $promoCodeId,
                $subtotal,
                $discount,
                $deliveryFee,
                $total,
                $paymentMethod,
                'placed', // Initial status
                $pickupAddress,
                $pickupDivisionId,
                $pickupDistrictId,
                $pickupUpazillaId,
                $pickupDate,
                $pickupTimeSlot,
                $deliveryAddress,
                $deliveryDivisionId,
                $deliveryDistrictId,
                $deliveryUpazillaId,
                $notes
            ]);

            // Get order ID
            $orderId = $pdo->lastInsertId();

            // Insert order items
            $stmt = $pdo->prepare("
                INSERT INTO order_items (
                    order_id, item_id, quantity, price, subtotal
                ) VALUES (?, ?, ?, ?, ?)
            ");

            foreach ($orderItems as $item) {
                $stmt->execute([
                    $orderId,
                    $item['item_id'],
                    $item['quantity'],
                    $item['price'],
                    $item['total']
                ]);
            }

            // Insert order status history
            $stmt = $pdo->prepare("
                INSERT INTO order_status_history (
                    order_id, status, notes, updated_by, updated_by_type
                ) VALUES (?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $orderId,
                'placed',
                'Order created by admin',
                $adminData['id'],
                'admin'
            ]);

            // Update promo code usage count if used
            if ($promoCodeId) {
                $stmt = $pdo->prepare("
                    UPDATE promo_codes
                    SET usage_count = usage_count + 1
                    WHERE id = ?
                ");
                $stmt->execute([$promoCodeId]);
            }

            // Commit transaction
            $pdo->commit();

            // Set success message
            $_SESSION['success_message'] = "Order #$orderNumber created successfully. Tracking number: $trackingNumber";

            // Redirect to order details page
            header("Location: order_details.php?id=$orderId");
            exit;
        } catch (PDOException $e) {
            // Rollback transaction
            $pdo->rollBack();

            // Set error message
            $_SESSION['error_message'] = 'Failed to create order: ' . $e->getMessage();
        }
    } else {
        // Set error message
        $_SESSION['error_message'] = 'Please fix the following errors: ' . implode(', ', $errors);
    }
}

// Page title and breadcrumbs
$pageTitle = 'Create Order';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Orders', 'link' => 'orders.php'],
    ['text' => 'Create Order', 'link' => '']
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">Create Order</h1>
        <a href="orders.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Orders
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="create_order.php">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="user_id" class="form-label">Customer <span class="text-danger">*</span></label>
                        <select class="form-control" id="user_id" name="user_id" required>
                            <option value="">Select Customer</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?= $user['id'] ?>">
                                    <?= isset($user['full_name']) ? htmlspecialchars($user['full_name']) : 'Unknown' ?>
                                    (<?= isset($user['phone']) ? htmlspecialchars($user['phone']) : 'No phone' ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="promo_code" class="form-label">Promo Code</label>
                        <select class="form-control" id="promo_code" name="promo_code">
                            <option value="">Select Promo Code (Optional)</option>
                            <?php foreach ($promoCodes as $code): ?>
                                <?php if(isset($code['code']) && isset($code['discount_type']) && isset($code['discount_value'])): ?>
                                    <option value="<?= htmlspecialchars($code['code']) ?>">
                                        <?= htmlspecialchars($code['code']) ?> -
                                        <?= $code['discount_type'] === 'percentage' ? $code['discount_value'] . '%' : 'BDT ' . $code['discount_value'] ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                        <select class="form-control" id="payment_method" name="payment_method" required>
                            <option value="">Select Payment Method</option>
                            <option value="cash">Cash</option>
                            <option value="card">Card</option>
                            <option value="mobile_banking">Mobile Banking</option>
                        </select>
                    </div>
                </div>

                <h5 class="mt-4 mb-3">Order Items</h5>
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="service_id" class="form-label">Service</label>
                                <select class="form-control" id="service_id">
                                    <option value="">Select Service</option>
                                    <?php foreach ($services as $service): ?>
                                        <?php if(isset($service['id']) && isset($service['name'])): ?>
                                            <option value="<?= $service['id'] ?>"><?= htmlspecialchars($service['name']) ?></option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="item_id" class="form-label">Item</label>
                                <select class="form-control" id="item_id">
                                    <option value="">Select Item</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" min="1" value="1">
                            </div>
                            <div class="col-md-2">
                                <label class="d-block">&nbsp;</label>
                                <button type="button" id="add_item" class="btn btn-primary">Add Item</button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered" id="order_items_table" style="display: none;">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Item</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Order items will be added here dynamically -->
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="4" class="text-end">Subtotal:</th>
                                        <th id="subtotal">0.00</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div id="no_items_message" class="alert alert-warning">
                            No items added to the order yet. Please add at least one item.
                        </div>

                        <!-- Hidden input to store order items as JSON -->
                        <input type="hidden" name="order_items" id="order_items" value="[]">
                    </div>
                </div>

                <h5 class="mt-4 mb-3">Pickup Information</h5>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="pickup_address" class="form-label">Pickup Address <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="pickup_address" name="pickup_address" rows="3" required></textarea>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="pickup_division_id" class="form-label">Division <span class="text-danger">*</span></label>
                        <select class="form-control" id="pickup_division_id" name="pickup_division_id" required>
                            <option value="">Select Division</option>
                            <?php foreach ($divisions as $division): ?>
                                <?php if(isset($division['id']) && isset($division['name'])): ?>
                                    <option value="<?= $division['id'] ?>"><?= htmlspecialchars($division['name']) ?></option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="pickup_district_id" class="form-label">District <span class="text-danger">*</span></label>
                        <select class="form-control" id="pickup_district_id" name="pickup_district_id" required>
                            <option value="">Select District</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="pickup_upazilla_id" class="form-label">Upazilla <span class="text-danger">*</span></label>
                        <select class="form-control" id="pickup_upazilla_id" name="pickup_upazilla_id" required>
                            <option value="">Select Upazilla</option>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="pickup_date" class="form-label">Pickup Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="pickup_date" name="pickup_date" required min="<?= date('Y-m-d') ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="pickup_time_slot" class="form-label">Pickup Time Slot <span class="text-danger">*</span></label>
                        <select class="form-control" id="pickup_time_slot" name="pickup_time_slot" required>
                            <option value="">Select Time Slot</option>
                            <option value="9:00 AM - 12:00 PM">9:00 AM - 12:00 PM</option>
                            <option value="12:00 PM - 3:00 PM">12:00 PM - 3:00 PM</option>
                            <option value="3:00 PM - 6:00 PM">3:00 PM - 6:00 PM</option>
                            <option value="6:00 PM - 9:00 PM">6:00 PM - 9:00 PM</option>
                        </select>
                    </div>
                </div>

                <h5 class="mt-4 mb-3">Delivery Information</h5>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="delivery_address" class="form-label">Delivery Address <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="delivery_address" name="delivery_address" rows="3" required></textarea>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="delivery_division_id" class="form-label">Division <span class="text-danger">*</span></label>
                        <select class="form-control" id="delivery_division_id" name="delivery_division_id" required>
                            <option value="">Select Division</option>
                            <?php foreach ($divisions as $division): ?>
                                <?php if(isset($division['id']) && isset($division['name'])): ?>
                                    <option value="<?= $division['id'] ?>"><?= htmlspecialchars($division['name']) ?></option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="delivery_district_id" class="form-label">District <span class="text-danger">*</span></label>
                        <select class="form-control" id="delivery_district_id" name="delivery_district_id" required>
                            <option value="">Select District</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="delivery_upazilla_id" class="form-label">Upazilla <span class="text-danger">*</span></label>
                        <select class="form-control" id="delivery_upazilla_id" name="delivery_upazilla_id" required>
                            <option value="">Select Upazilla</option>
                        </select>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" name="create_order" class="btn btn-primary">Create Order</button>
                    <a href="orders.php" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
    $(document).ready(function() {
        // Initialize variables
        var orderItems = [];
        var itemsData = <?php echo json_encode($items); ?> || [];
        var servicesData = <?php echo json_encode($services); ?> || [];

        console.log("Page loaded. Services data:", servicesData);
        console.log("Page loaded. Items data:", itemsData);

        // Check if services and items are available
        if (!servicesData || servicesData.length === 0) {
            console.error("No services available. Please add services to the database.");
            alert("No services available. Please add services to the database.");
        }

        if (!itemsData || itemsData.length === 0) {
            console.error("No items available. Please add items to the database.");
            alert("No items available. Please add items to the database.");
        }

        // Make sure the order items table is hidden and the message is shown
        $('#order_items_table').hide();
        $('#no_items_message').show();

        // Set up the hidden input
        $('#order_items').val(JSON.stringify(orderItems));

        // Load districts when division is selected
        $('#pickup_division_id').change(function() {
            var divisionId = $(this).val();
            if (divisionId) {
                $.ajax({
                    url: '../api/location/districts.php',
                    type: 'GET',
                    data: { division_id: divisionId },
                    success: function(data) {
                        $('#pickup_district_id').html('<option value="">Select District</option>');
                        $('#pickup_upazilla_id').html('<option value="">Select Upazilla</option>');
                        $.each(data, function(key, value) {
                            $('#pickup_district_id').append('<option value="' + value.id + '">' + value.name + '</option>');
                        });
                    }
                });
            } else {
                $('#pickup_district_id').html('<option value="">Select District</option>');
                $('#pickup_upazilla_id').html('<option value="">Select Upazilla</option>');
            }
        });

        // Load upazillas when district is selected
        $('#pickup_district_id').change(function() {
            var districtId = $(this).val();
            if (districtId) {
                $.ajax({
                    url: '../api/location/upazillas.php',
                    type: 'GET',
                    data: { district_id: districtId },
                    success: function(data) {
                        $('#pickup_upazilla_id').html('<option value="">Select Upazilla</option>');
                        $.each(data, function(key, value) {
                            $('#pickup_upazilla_id').append('<option value="' + value.id + '">' + value.name + '</option>');
                        });
                    }
                });
            } else {
                $('#pickup_upazilla_id').html('<option value="">Select Upazilla</option>');
            }
        });

        // Load districts when delivery division is selected
        $('#delivery_division_id').change(function() {
            var divisionId = $(this).val();
            if (divisionId) {
                $.ajax({
                    url: '../api/location/districts.php',
                    type: 'GET',
                    data: { division_id: divisionId },
                    success: function(data) {
                        $('#delivery_district_id').html('<option value="">Select District</option>');
                        $('#delivery_upazilla_id').html('<option value="">Select Upazilla</option>');
                        $.each(data, function(key, value) {
                            $('#delivery_district_id').append('<option value="' + value.id + '">' + value.name + '</option>');
                        });
                    }
                });
            } else {
                $('#delivery_district_id').html('<option value="">Select District</option>');
                $('#delivery_upazilla_id').html('<option value="">Select Upazilla</option>');
            }
        });

        // Load upazillas when delivery district is selected
        $('#delivery_district_id').change(function() {
            var districtId = $(this).val();
            if (districtId) {
                $.ajax({
                    url: '../api/location/upazillas.php',
                    type: 'GET',
                    data: { district_id: districtId },
                    success: function(data) {
                        $('#delivery_upazilla_id').html('<option value="">Select Upazilla</option>');
                        $.each(data, function(key, value) {
                            $('#delivery_upazilla_id').append('<option value="' + value.id + '">' + value.name + '</option>');
                        });
                    }
                });
            } else {
                $('#delivery_upazilla_id').html('<option value="">Select Upazilla</option>');
            }
        });

        // Load items when service is selected
        $('#service_id').change(function() {
            var serviceId = $(this).val();
            console.log("Service selected:", serviceId);

            if (serviceId) {
                // Filter items by service ID
                var filteredItems = itemsData.filter(function(item) {
                    return item && item.service_id == serviceId;
                });

                console.log("Filtered items:", filteredItems);

                // Update items dropdown
                $('#item_id').html('<option value="">Select Item</option>');

                if (filteredItems && filteredItems.length > 0) {
                    $.each(filteredItems, function(key, item) {
                        if (item && item.id && item.name && item.price) {
                            $('#item_id').append('<option value="' + item.id + '" data-price="' + item.price + '">' + item.name + ' (BDT ' + item.price + ')</option>');
                        }
                    });
                } else {
                    console.log("No items found for service ID:", serviceId);
                    $('#item_id').append('<option value="" disabled>No items available for this service</option>');
                }
            } else {
                $('#item_id').html('<option value="">Select Item</option>');
            }
        });

        // Add item to order
        $('#add_item').click(function() {
            console.log("Add item button clicked");
            var serviceId = $('#service_id').val();
            var itemId = $('#item_id').val();
            var quantity = parseInt($('#quantity').val());

            console.log("Selected values:", { serviceId, itemId, quantity });

            // Validate inputs
            if (!serviceId) {
                alert('Please select a service');
                return;
            }

            if (!itemId) {
                alert('Please select an item');
                return;
            }

            if (isNaN(quantity) || quantity < 1) {
                alert('Please enter a valid quantity');
                return;
            }

            // Get service and item details
            var services = <?php echo json_encode($services); ?> || [];
            console.log("Available services:", services);

            var service = services.find(function(s) {
                return s && s.id == serviceId;
            });

            console.log("Selected service:", service);

            if (!service) {
                alert('Service not found. Please try again.');
                return;
            }

            console.log("Available items:", itemsData);
            var item = itemsData.find(function(i) {
                return i && i.id == itemId;
            });

            console.log("Selected item:", item);

            if (!item) {
                alert('Item not found. Please try again.');
                return;
            }

            // Check if item already exists in order
            var existingItemIndex = orderItems.findIndex(function(orderItem) {
                return orderItem.item_id == itemId;
            });

            console.log("Existing item index:", existingItemIndex);

            if (existingItemIndex !== -1) {
                // Update quantity if item already exists
                orderItems[existingItemIndex].quantity += quantity;
                orderItems[existingItemIndex].total = orderItems[existingItemIndex].quantity * orderItems[existingItemIndex].price;
                console.log("Updated existing item:", orderItems[existingItemIndex]);
            } else {
                // Add new item to order
                var newItem = {
                    service_id: serviceId,
                    service_name: service.name,
                    item_id: itemId,
                    item_name: item.name,
                    price: parseFloat(item.price),
                    quantity: quantity,
                    total: parseFloat(item.price) * quantity
                };

                orderItems.push(newItem);
                console.log("Added new item:", newItem);
            }

            console.log("Current order items:", orderItems);

            // Update order items table
            updateOrderItemsTable();

            // Reset form
            $('#service_id').val('');
            $('#item_id').html('<option value="">Select Item</option>');
            $('#quantity').val(1);
        });

        // Remove item from order
        $(document).on('click', '.remove-item', function() {
            var index = $(this).data('index');
            orderItems.splice(index, 1);
            updateOrderItemsTable();
        });

        // Update order items table
        function updateOrderItemsTable() {
            console.log("Updating order items table. Items:", orderItems);
            var tbody = $('#order_items_table tbody');
            var subtotal = 0;

            // Clear table
            tbody.empty();

            // Add items to table
            if (orderItems && orderItems.length > 0) {
                // Show table and hide message
                $('#order_items_table').show();
                $('#no_items_message').hide();

                $.each(orderItems, function(index, item) {
                    tbody.append(
                        '<tr>' +
                        '<td>' + (item.service_name || 'Unknown') + '</td>' +
                        '<td>' + (item.item_name || 'Unknown') + '</td>' +
                        '<td>BDT ' + (item.price ? item.price.toFixed(2) : '0.00') + '</td>' +
                        '<td>' + (item.quantity || 0) + '</td>' +
                        '<td>BDT ' + (item.total ? item.total.toFixed(2) : '0.00') + '</td>' +
                        '<td><button type="button" class="btn btn-sm btn-danger remove-item" data-index="' + index + '"><i class="fas fa-trash"></i></button></td>' +
                        '</tr>'
                    );

                    if (item.total) {
                        subtotal += item.total;
                    }
                });
            } else {
                // Hide table and show message
                $('#order_items_table').hide();
                $('#no_items_message').show();
            }

            // Update subtotal
            $('#subtotal').text('BDT ' + subtotal.toFixed(2));

            // Update hidden input
            $('#order_items').val(JSON.stringify(orderItems || []));

            console.log("Order items updated. Hidden input value:", $('#order_items').val());
        }

        // Form submission validation
        $('form').submit(function(e) {
            console.log("Form submitted. Order items:", orderItems);

            if (!orderItems || orderItems.length === 0) {
                e.preventDefault();
                alert('Please add at least one item to the order');
                console.log("Form submission prevented - no items");
                return false;
            }

            // Ensure the order_items field is properly populated
            $('#order_items').val(JSON.stringify(orderItems));
            console.log("Form submission allowed. order_items value:", $('#order_items').val());
            return true;
        });
    });
</script>
