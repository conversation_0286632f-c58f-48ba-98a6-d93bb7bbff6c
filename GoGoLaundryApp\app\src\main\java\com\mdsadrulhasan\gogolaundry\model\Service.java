package com.mdsadrulhasan.gogolaundry.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import com.google.gson.annotations.SerializedName;

/**
 * Model class for laundry services
 */
@Entity(tableName = "services")
public class Service {

    @PrimaryKey
    @SerializedName("id")
    private int id;

    @SerializedName("name")
    private String name;

    @SerializedName("description")
    private String description;

    @SerializedName("price")
    private double price;

    @SerializedName("price_unit")
    private String priceUnit; // "kg" or "piece"

    @SerializedName(value = "icon", alternate = {"image_url", "icon_url", "image"})
    private String iconUrl;

    // We use Integer for is_active to handle both boolean and numeric (1/0) values from API
    @SerializedName("is_active")
    private Integer isActiveRaw;

    // This field is not directly mapped from JSON but set programmatically
    private boolean isActive;

    @SerializedName("created_at")
    private String createdAt;

    @SerializedName("updated_at")
    private String updatedAt;

    // Default constructor required by Room
    public Service() {
    }

    // Constructor with all fields
    public Service(int id, String name, String description, double price, String priceUnit,
                  String iconUrl, boolean isActive, String createdAt, String updatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.price = price;
        this.priceUnit = priceUnit;
        this.iconUrl = iconUrl;
        this.isActive = isActive;
        this.isActiveRaw = isActive ? 1 : 0; // Convert boolean to integer
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // Getters and setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getPriceUnit() {
        return priceUnit;
    }

    public void setPriceUnit(String priceUnit) {
        this.priceUnit = priceUnit;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public boolean isActive() {
        // If isActiveRaw is available, use it to determine active status
        if (isActiveRaw != null) {
            return isActiveRaw == 1;
        }
        return isActive;
    }

    public void setActive(boolean active) {
        this.isActive = active;
        this.isActiveRaw = active ? 1 : 0;
    }

    public Integer getIsActiveRaw() {
        return isActiveRaw;
    }

    public void setIsActiveRaw(Integer isActiveRaw) {
        this.isActiveRaw = isActiveRaw;
        // Also update the boolean field for consistency
        if (isActiveRaw != null) {
            this.isActive = (isActiveRaw == 1);
        }
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper method to format price with unit
    public String getFormattedPrice() {
        if ("kg".equalsIgnoreCase(priceUnit)) {
            return "৳" + price + "/kg";
        } else if ("piece".equalsIgnoreCase(priceUnit)) {
            return "৳" + price + "/piece";
        } else {
            return "৳" + price;
        }
    }
}
