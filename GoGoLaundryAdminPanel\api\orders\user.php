<?php
/**
 * User Orders API
 *
 * This file handles the API endpoints for retrieving user orders
 */

// Include required files
require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../includes/functions.php';

// Set headers
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle different request methods
if ($method !== 'GET') {
    // Method not allowed
    header("HTTP/1.1 405 Method Not Allowed");
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get user ID from URL or query parameter
if (isset($_GET['user_id'])) {
    // Get from query parameter (for .htaccess rewrite)
    $userId = $_GET['user_id'];
    getUserOrders($userId);
} else {
    // Try to extract from URL path
    $requestUri = $_SERVER['REQUEST_URI'];
    $pattern = '/\/user\/(\d+)/';
    if (preg_match($pattern, $requestUri, $matches)) {
        $userId = $matches[1];
        getUserOrders($userId);
    } else {
        // Missing user ID
        jsonResponse(false, 'Missing user ID', [], 400);
    }
}

/**
 * Get orders for a user
 *
 * @param int $userId User ID
 * @return void
 */
function getUserOrders($userId) {
    global $pdo;

    try {
        // Prepare query
        $stmt = $pdo->prepare("
            SELECT o.*,
                   u.full_name as customer_name,
                   u.phone as customer_phone,
                   dp.full_name as delivery_person_name,
                   dp.phone as delivery_person_phone
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            LEFT JOIN delivery_personnel dp ON o.delivery_personnel_id = dp.id
            WHERE o.user_id = ?
            ORDER BY o.created_at DESC
        ");

        // Execute query
        $stmt->execute([$userId]);

        // Get orders
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Check if user has orders
        if (empty($orders)) {
            // Check if user exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
            $stmt->execute([$userId]);

            if (!$stmt->fetch()) {
                jsonResponse(false, 'User not found', [], 404);
            } else {
                jsonResponse(true, 'No orders found for this user', []);
            }
            return;
        }

        // Process orders
        $processedOrders = [];
        foreach ($orders as $order) {
            // Get order items
            $stmt = $pdo->prepare("
                SELECT oi.*, i.name, i.bn_name, i.image_url, s.name as service_name, s.bn_name as service_bn_name
                FROM order_items oi
                JOIN items i ON oi.item_id = i.id
                JOIN services s ON i.service_id = s.id
                WHERE oi.order_id = ?
            ");

            // Execute query
            $stmt->execute([$order['id']]);

            // Get order items
            $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Format order data
            $processedOrder = formatOrderData($order, $orderItems);
            $processedOrders[] = $processedOrder;
        }

        // Return response
        jsonResponse(true, 'Orders retrieved successfully', $processedOrders);
    } catch (PDOException $e) {
        // Log error
        error_log('Database Error: ' . $e->getMessage());

        // Return error response
        jsonResponse(false, 'An error occurred while retrieving the orders', [], 500);
    }
}

/**
 * Format order data for response
 *
 * @param array $order Order data from database
 * @param array $orderItems Order items from database
 * @return array Formatted order data
 */
function formatOrderData($order, $orderItems) {
    // Format order items
    $formattedItems = [];
    foreach ($orderItems as $item) {
        $formattedItems[] = [
            'id' => (int)$item['id'],
            'order_id' => (int)$item['order_id'],
            'item_id' => (int)$item['item_id'],
            'quantity' => (int)$item['quantity'],
            'price' => (float)$item['price'],
            'subtotal' => (float)$item['subtotal'],
            'notes' => $item['notes'],
            'created_at' => $item['created_at'],
            'updated_at' => $item['updated_at'] ?? $item['created_at'],
            'name' => $item['name'],
            'bn_name' => $item['bn_name'],
            'image_url' => $item['image_url'],
            'service_name' => $item['service_name'],
            'service_bn_name' => $item['service_bn_name']
        ];
    }

    // Format order data
    return [
        'id' => (int)$order['id'],
        'order_number' => $order['order_number'],
        'tracking_number' => $order['tracking_number'],
        'user_id' => (int)$order['user_id'],
        'delivery_personnel_id' => $order['delivery_personnel_id'] ? (int)$order['delivery_personnel_id'] : null,
        'promo_code_id' => $order['promo_code_id'] ? (int)$order['promo_code_id'] : null,
        'subtotal' => (float)$order['subtotal'],
        'discount' => (float)$order['discount'],
        'delivery_fee' => (float)$order['delivery_fee'],
        'total' => (float)$order['total'],
        'payment_method' => $order['payment_method'],
        'payment_status' => $order['payment_status'],
        'status' => $order['status'],
        'pickup_address' => $order['pickup_address'],
        'pickup_division_id' => $order['pickup_division_id'] ? (int)$order['pickup_division_id'] : null,
        'pickup_district_id' => $order['pickup_district_id'] ? (int)$order['pickup_district_id'] : null,
        'pickup_upazilla_id' => $order['pickup_upazilla_id'] ? (int)$order['pickup_upazilla_id'] : null,
        'pickup_date' => $order['pickup_date'],
        'pickup_time_slot' => $order['pickup_time_slot'],
        'delivery_address' => $order['delivery_address'],
        'delivery_division_id' => $order['delivery_division_id'] ? (int)$order['delivery_division_id'] : null,
        'delivery_district_id' => $order['delivery_district_id'] ? (int)$order['delivery_district_id'] : null,
        'delivery_upazilla_id' => $order['delivery_upazilla_id'] ? (int)$order['delivery_upazilla_id'] : null,
        'delivery_date' => $order['delivery_date'],
        'delivery_time_slot' => $order['delivery_time_slot'],
        'notes' => $order['notes'],
        'created_at' => $order['created_at'],
        'updated_at' => $order['updated_at'],
        'customer_name' => $order['customer_name'],
        'customer_phone' => $order['customer_phone'],
        'delivery_person_name' => $order['delivery_person_name'],
        'delivery_person_phone' => $order['delivery_person_phone'],
        'order_items' => $formattedItems
    ];
}
