package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import com.mdsadrulhasan.gogolaundry.ui.adapter.ShopItemAdapter;
import com.mdsadrulhasan.gogolaundry.viewmodel.ShopDetailsViewModel;

import java.util.ArrayList;

/**
 * Fragment for displaying shop items
 */
public class ShopItemsFragment extends Fragment implements ShopItemAdapter.OnItemClickListener {

    private static final String TAG = "ShopItemsFragment";
    private static final String ARG_SHOP_ID = "shop_id";

    private RecyclerView itemsRecyclerView;
    private TextView emptyTextView;
    private ShopDetailsViewModel viewModel;
    private ShopItemAdapter adapter;
    private int shopId;

    public static ShopItemsFragment newInstance(int shopId) {
        ShopItemsFragment fragment = new ShopItemsFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_SHOP_ID, shopId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            shopId = getArguments().getInt(ARG_SHOP_ID);
        }
        Log.d(TAG, "ShopItemsFragment onCreate() - shopId: " + shopId);

        // Get shared ViewModel from parent fragment
        viewModel = new ViewModelProvider(requireParentFragment()).get(ShopDetailsViewModel.class);
        Log.d(TAG, "ShopItemsFragment - ViewModel obtained: " + (viewModel != null));
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_shop_items, container, false);

        initializeViews(view);
        setupRecyclerView();
        observeViewModel();

        return view;
    }

    private void initializeViews(View view) {
        itemsRecyclerView = view.findViewById(R.id.itemsRecyclerView);
        emptyTextView = view.findViewById(R.id.emptyTextView);
    }

    private void setupRecyclerView() {
        itemsRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        adapter = new ShopItemAdapter(new ArrayList<>(), this);
        itemsRecyclerView.setAdapter(adapter);
    }

    private void observeViewModel() {
        // Observe filtered items data
        viewModel.getFilteredItems().observe(getViewLifecycleOwner(), items -> {
            if (items != null && !items.isEmpty()) {
                itemsRecyclerView.setVisibility(View.VISIBLE);
                emptyTextView.setVisibility(View.GONE);
                adapter.updateItems(items);
            } else {
                itemsRecyclerView.setVisibility(View.GONE);
                emptyTextView.setVisibility(View.VISIBLE);
                emptyTextView.setText(R.string.no_items_found_shop);
            }
        });

        // Observe loading state
        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            // TODO: Show/hide loading indicator
        });
    }

    @Override
    public void onItemClick(ShopItemEntity item) {
        // Show item details
        Toast.makeText(getContext(), "Item clicked: " + item.getItemName(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onAddToCartClick(ShopItemEntity item) {
        // Add item to cart
        Toast.makeText(getContext(), "Added to cart: " + item.getItemName(), Toast.LENGTH_SHORT).show();
        // TODO: Implement add to cart functionality
    }
}
