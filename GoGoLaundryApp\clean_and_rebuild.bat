@echo off
echo Cleaning and rebuilding GoGoLaundry Android app...
echo.

echo Step 1: Cleaning project...
call gradlew clean
if %errorlevel% neq 0 (
    echo Error during clean. Exiting.
    pause
    exit /b 1
)

echo.
echo Step 2: Building project...
call gradlew build
if %errorlevel% neq 0 (
    echo Error during build. Exiting.
    pause
    exit /b 1
)

echo.
echo Clean and rebuild completed successfully!
echo The database schema has been regenerated.
echo You can now run the app.
pause
