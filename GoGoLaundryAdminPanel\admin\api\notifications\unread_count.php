<?php
/**
 * Get Unread Notifications Count API
 * 
 * This endpoint returns the count of unread notifications
 * 
 * Returns:
 * - success: true/false
 * - data: Number of unread notifications
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Include required files
require_once '../../../config/db.php';
require_once '../../../includes/functions.php';

// Start session
session_start();

// Initialize response
$response = [
    'success' => false,
    'message' => 'Unknown error occurred',
    'data' => 0
];

try {
    // Check if admin is logged in
    if (!isset($_SESSION['admin_id']) || empty($_SESSION['admin_id'])) {
        $response['message'] = 'Admin not logged in';
        echo json_encode($response);
        exit();
    }
    
    // Get unread notifications count
    // For admin panel, we'll count all unread notifications that are relevant to admins
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as unread_count
        FROM notifications n
        WHERE n.is_read = 0
        AND n.type IN ('new_order', 'order_status', 'user_registration', 'system')
        AND n.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $unreadCount = $result ? intval($result['unread_count']) : 0;
    
    $response['success'] = true;
    $response['message'] = 'Unread count retrieved successfully';
    $response['data'] = $unreadCount;
    
} catch (Exception $e) {
    $response['message'] = 'Server error: ' . $e->getMessage();
    error_log('Get Unread Count Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
?>
