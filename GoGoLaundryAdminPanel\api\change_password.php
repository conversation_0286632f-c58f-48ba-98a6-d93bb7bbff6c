<?php
/**
 * Change Password API Endpoint
 *
 * This endpoint handles changing a user's password
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/UserManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Validate input
if (empty($inputData['current_password'])) {
    jsonResponse(false, 'Current password is required', [], 400);
}

if (empty($inputData['new_password'])) {
    jsonResponse(false, 'New password is required', [], 400);
}

// Get input values
$currentPassword = sanitize($inputData['current_password']);
$newPassword = sanitize($inputData['new_password']);

// Get minimum password length from settings
$minPasswordLength = 6; // Default to 6 characters
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'min_password_length'");
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
if ($result && !empty($result['setting_value'])) {
    $minPasswordLength = (int)$result['setting_value'];
}

// Validate password strength
if (strlen($newPassword) < $minPasswordLength) {
    jsonResponse(false, "New password must be at least {$minPasswordLength} characters long", [], 400);
}

// Check for user ID in input data or session
$userId = null;

// First try to get user ID from input data
if (!empty($inputData['user_id'])) {
    $userId = (int)$inputData['user_id'];
    error_log('Using user_id from input data: ' . $userId);
}
// If not found in input data, try to get from session
else if (isset($_SESSION['user_id']) && isset($_SESSION['is_logged_in']) && $_SESSION['is_logged_in']) {
    $userId = $_SESSION['user_id'];
    error_log('Using user_id from session: ' . $userId);
}
// If still not found, return error
else {
    error_log('User not authenticated - no user_id in input data or session');
    jsonResponse(false, 'User not authenticated. Please provide user_id or login again.', [], 401);
}

// Initialize user manager
$userManager = new UserManager($pdo);

// Get user data
$user = $userManager->getUserById($userId);
if (!$user) {
    jsonResponse(false, 'User not found', [], 404);
}

// Verify current password
if (!password_verify($currentPassword, $user['password'])) {
    jsonResponse(false, 'Current password is incorrect', [], 401);
}

// Check if new password is the same as current password
if ($currentPassword === $newPassword) {
    jsonResponse(false, 'New password must be different from current password', [], 400);
}

// Update password
$result = $userManager->updatePasswordById($userId, $newPassword);

if (!$result) {
    jsonResponse(false, 'Failed to update password. Please try again later.', [], 500);
}

// Log success
error_log('Password changed successfully for user ID: ' . $userId);

// Return success response
jsonResponse(true, 'Password changed successfully');
