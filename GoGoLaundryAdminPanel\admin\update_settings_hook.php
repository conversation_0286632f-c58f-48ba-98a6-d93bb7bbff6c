<?php
// This script is a hook that updates the config.php file whenever settings are changed

// Function to update the config.php file
function updateConfigFile($otpEnabled) {
    // Path to the config.php file
    $configFile = __DIR__ . '/../api/config.php';

    // Get settings from database
    global $pdo;
    $adminWhatsapp = '+1234567890'; // Default value
    $minPasswordLength = 8; // Default value
    $otpLength = 6; // Default value
    $otpExpiry = 600; // Default value
    $otpMaxAttempts = 3; // Default value

    try {
        // Get admin WhatsApp number
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'admin_whatsapp'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && !empty($result['setting_value'])) {
            $adminWhatsapp = $result['setting_value'];
        }

        // Get minimum password length
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'min_password_length'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && !empty($result['setting_value'])) {
            $minPasswordLength = (int)$result['setting_value'];
        }

        // Get OTP length
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'otp_length'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && !empty($result['setting_value'])) {
            $otpLength = (int)$result['setting_value'];
        }

        // Get OTP expiry
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'otp_expiry'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && !empty($result['setting_value'])) {
            $otpExpiry = (int)$result['setting_value'];
        }

        // Get OTP max attempts
        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'otp_max_attempts'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && !empty($result['setting_value'])) {
            $otpMaxAttempts = (int)$result['setting_value'];
        }
    } catch (PDOException $e) {
        // If there's an error, use the default values
        error_log("Error fetching settings: " . $e->getMessage());
    }

    // Create the fixed content
    $fixedContent = '<?php
// API configuration endpoint

// Include database connection
require_once __DIR__ . "/../config/db.php";

// Set headers
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Get app version from request
$app_version = isset($_GET["app_version"]) ? $_GET["app_version"] : "1.0.0";

// Get settings from database
$settings = array();
$stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $settings[$row[\'setting_key\']] = $row[\'setting_value\'];
}

// Create response data
$data = array(
    "app_name" => "OTP Management System",
    "app_version" => "1.0.0",
    "otp_enabled" => ' . $otpEnabled . ', // Set dynamically from database
    "otp_length" => ' . $otpLength . ',
    "otp_expiry" => ' . $otpExpiry . ',
    "otp_max_attempts" => ' . $otpMaxAttempts . ',
    "min_password_length" => ' . $minPasswordLength . ',
    "admin_whatsapp" => "' . $adminWhatsapp . '",
    "server_time" => date("Y-m-d H:i:s")
);

// Create response
$response = array(
    "success" => true,
    "message" => "Configuration retrieved successfully",
    "data" => $data
);

// Return response
echo json_encode($response);
';

    // Write the fixed content to the file
    if (file_put_contents($configFile, $fixedContent) !== false) {
        return true;
    } else {
        return false;
    }
}

// This function should be called after updating the OTP setting in the database
// Example usage:
// updateConfigFile(0); // Disable OTP
// updateConfigFile(1); // Enable OTP
?>
