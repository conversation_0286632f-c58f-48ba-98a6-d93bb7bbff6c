<?php
/**
 * Run shop system migration to add shop_id to orders table
 */

require_once 'config/db.php';

try {
    echo "Starting shop system migration...\n";
    
    // Check if shop_id column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM orders LIKE 'shop_id'");
    $result = $stmt->fetch();
    
    if ($result) {
        echo "shop_id column already exists in orders table.\n";
    } else {
        echo "Adding shop_id column to orders table...\n";
        
        // Add shop_id column
        $pdo->exec("ALTER TABLE orders ADD COLUMN shop_id int(11) DEFAULT NULL AFTER user_id");
        echo "Added shop_id column.\n";
        
        // Add foreign key constraint
        try {
            $pdo->exec("ALTER TABLE orders ADD CONSTRAINT orders_shop_fk FOREIGN KEY (shop_id) REFERENCES laundry_shops(id)");
            echo "Added foreign key constraint.\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "Foreign key constraint already exists.\n";
            } else {
                echo "Warning: Could not add foreign key constraint: " . $e->getMessage() . "\n";
            }
        }
        
        // Add index for better performance
        try {
            $pdo->exec("CREATE INDEX idx_orders_shop ON orders(shop_id)");
            echo "Added index on shop_id.\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "Index already exists.\n";
            } else {
                echo "Warning: Could not add index: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Check if laundry_shops table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'laundry_shops'");
    $result = $stmt->fetch();
    
    if (!$result) {
        echo "Creating laundry_shops table...\n";
        
        $sql = "
        CREATE TABLE IF NOT EXISTS `laundry_shops` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL,
          `bn_name` varchar(100) DEFAULT NULL,
          `description` text DEFAULT NULL,
          `bn_description` text DEFAULT NULL,
          `owner_name` varchar(100) NOT NULL,
          `phone` varchar(15) NOT NULL,
          `email` varchar(100) DEFAULT NULL,
          `address` varchar(255) NOT NULL,
          `division_id` int(11) DEFAULT NULL,
          `district_id` int(11) DEFAULT NULL,
          `upazilla_id` int(11) DEFAULT NULL,
          `latitude` decimal(10,8) NOT NULL,
          `longitude` decimal(11,8) NOT NULL,
          `operating_hours` json DEFAULT NULL,
          `rating` decimal(3,2) DEFAULT 0.00,
          `total_reviews` int(11) DEFAULT 0,
          `commission_percentage` decimal(5,2) DEFAULT 10.00,
          `is_active` tinyint(1) DEFAULT 1,
          `is_verified` tinyint(1) DEFAULT 1,
          `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `phone` (`phone`),
          UNIQUE KEY `email` (`email`),
          KEY `location_idx` (`latitude`,`longitude`),
          KEY `active_verified_idx` (`is_active`,`is_verified`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        echo "Created laundry_shops table.\n";
        
        // Insert sample shop data
        $insertSql = "
        INSERT IGNORE INTO `laundry_shops` (`id`, `name`, `bn_name`, `description`, `owner_name`, `phone`, `email`, `address`, `division_id`, `district_id`, `upazilla_id`, `latitude`, `longitude`, `operating_hours`, `rating`, `total_reviews`, `commission_percentage`, `is_active`, `is_verified`) VALUES
        (1, 'Clean & Fresh Laundry', 'ক্লিন এন্ড ফ্রেশ লন্ড্রি', 'Professional laundry service with modern equipment and eco-friendly cleaning solutions.', 'Mohammad Rahman', '01712345678', '<EMAIL>', 'House 123, Road 15, Dhanmondi, Dhaka', 1, 1, 1, 23.7461, 90.3742, '{\"monday\":{\"is_open\":true,\"open_time\":\"08:00\",\"close_time\":\"22:00\"},\"tuesday\":{\"is_open\":true,\"open_time\":\"08:00\",\"close_time\":\"22:00\"},\"wednesday\":{\"is_open\":true,\"open_time\":\"08:00\",\"close_time\":\"22:00\"},\"thursday\":{\"is_open\":true,\"open_time\":\"08:00\",\"close_time\":\"22:00\"},\"friday\":{\"is_open\":true,\"open_time\":\"08:00\",\"close_time\":\"22:00\"},\"saturday\":{\"is_open\":true,\"open_time\":\"08:00\",\"close_time\":\"22:00\"},\"sunday\":{\"is_open\":true,\"open_time\":\"10:00\",\"close_time\":\"20:00\"}}', 4.5, 123, 12.00, 1, 1),
        (2, 'Express Wash Center', 'এক্সপ্রেস ওয়াশ সেন্টার', 'Fast and reliable laundry service with same-day delivery options.', 'Fatima Khatun', '01823456789', '<EMAIL>', 'Plot 45, Gulshan Avenue, Gulshan, Dhaka', 1, 1, 2, 23.7925, 90.4078, '{\"monday\":{\"is_open\":true,\"open_time\":\"07:00\",\"close_time\":\"23:00\"},\"tuesday\":{\"is_open\":true,\"open_time\":\"07:00\",\"close_time\":\"23:00\"},\"wednesday\":{\"is_open\":true,\"open_time\":\"07:00\",\"close_time\":\"23:00\"},\"thursday\":{\"is_open\":true,\"open_time\":\"07:00\",\"close_time\":\"23:00\"},\"friday\":{\"is_open\":true,\"open_time\":\"07:00\",\"close_time\":\"23:00\"},\"saturday\":{\"is_open\":true,\"open_time\":\"07:00\",\"close_time\":\"23:00\"},\"sunday\":{\"is_open\":true,\"open_time\":\"09:00\",\"close_time\":\"21:00\"}}', 4.2, 87, 10.00, 1, 1),
        (3, 'Premium Dry Cleaners', 'প্রিমিয়াম ড্রাই ক্লিনার্স', 'Specialized in dry cleaning and premium garment care services.', 'Ahmed Hassan', '01934567890', '<EMAIL>', 'Building 78, Banani Commercial Area, Dhaka', 1, 1, 3, 23.7937, 90.4066, '{\"monday\":{\"is_open\":true,\"open_time\":\"09:00\",\"close_time\":\"21:00\"},\"tuesday\":{\"is_open\":true,\"open_time\":\"09:00\",\"close_time\":\"21:00\"},\"wednesday\":{\"is_open\":true,\"open_time\":\"09:00\",\"close_time\":\"21:00\"},\"thursday\":{\"is_open\":true,\"open_time\":\"09:00\",\"close_time\":\"21:00\"},\"friday\":{\"is_open\":true,\"open_time\":\"09:00\",\"close_time\":\"21:00\"},\"saturday\":{\"is_open\":true,\"open_time\":\"09:00\",\"close_time\":\"21:00\"},\"sunday\":{\"is_open\":false,\"open_time\":\"\",\"close_time\":\"\"}}', 4.8, 156, 15.00, 1, 1);
        ";
        
        $pdo->exec($insertSql);
        echo "Inserted sample shop data.\n";
    } else {
        echo "laundry_shops table already exists.\n";
    }
    
    echo "Migration completed successfully!\n";
    
} catch (PDOException $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
