package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.Manifest;
import android.content.pm.PackageManager;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import android.view.MotionEvent;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;

import org.osmdroid.api.IMapController;
import org.osmdroid.config.Configuration;
import org.osmdroid.tileprovider.tilesource.TileSourceFactory;
import org.osmdroid.util.GeoPoint;
import org.osmdroid.views.MapView;
import org.osmdroid.views.overlay.Marker;
import org.osmdroid.views.overlay.mylocation.GpsMyLocationProvider;
import org.osmdroid.views.overlay.mylocation.MyLocationNewOverlay;

import java.io.IOException;
import java.util.List;
import java.util.Locale;

/**
 * Fragment for selecting address using an interactive map
 */
public class AddressPickerFragment extends Fragment {

    private static final String TAG = "AddressPickerFragment";
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;
    
    // Default location (Dhaka, Bangladesh)
    private static final double DEFAULT_LAT = 23.8103;
    private static final double DEFAULT_LNG = 90.4125;
    private static final int DEFAULT_ZOOM = 15;

    // Views
    private MapView mapView;
    private EditText searchEditText;
    private TextView selectedAddressTextView;
    private Button confirmButton;
    private FloatingActionButton currentLocationFab;

    // Map components
    private IMapController mapController;
    private Marker selectedLocationMarker;
    private MyLocationNewOverlay myLocationOverlay;
    private FusedLocationProviderClient fusedLocationClient;
    private Geocoder geocoder;

    // Selected location data
    private GeoPoint selectedLocation;
    private String selectedAddress;

    // Callback interface
    public interface OnAddressSelectedListener {
        void onAddressSelected(double latitude, double longitude, String address);
    }

    private OnAddressSelectedListener addressSelectedListener;

    public static AddressPickerFragment newInstance() {
        return new AddressPickerFragment();
    }

    public void setOnAddressSelectedListener(OnAddressSelectedListener listener) {
        this.addressSelectedListener = listener;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Initialize OSMDroid configuration
        Configuration.getInstance().setUserAgentValue(requireContext().getPackageName());
        
        // Initialize location services
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(requireActivity());
        geocoder = new Geocoder(requireContext(), Locale.getDefault());
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_address_picker, container, false);
        
        initializeViews(view);
        setupMap();
        setupClickListeners();
        
        return view;
    }

    private void initializeViews(View view) {
        mapView = view.findViewById(R.id.mapView);
        searchEditText = view.findViewById(R.id.searchEditText);
        selectedAddressTextView = view.findViewById(R.id.selectedAddressTextView);
        confirmButton = view.findViewById(R.id.confirmButton);
        currentLocationFab = view.findViewById(R.id.currentLocationFab);
    }

    private void setupMap() {
        // Set tile source
        mapView.setTileSource(TileSourceFactory.MAPNIK);
        mapView.setMultiTouchControls(true);

        // Get map controller
        mapController = mapView.getController();
        mapController.setZoom(DEFAULT_ZOOM);

        // Set default location
        GeoPoint defaultPoint = new GeoPoint(DEFAULT_LAT, DEFAULT_LNG);
        mapController.setCenter(defaultPoint);

        // Setup my location overlay
        setupMyLocationOverlay();

        // Setup map click listener
        mapView.getOverlays().add(new org.osmdroid.views.overlay.Overlay() {
            @Override
            // CHANGE THIS LINE: Remove the "org.osmdroid.views." prefix
            public boolean onSingleTapConfirmed(MotionEvent e, MapView mapView) {
                GeoPoint geoPoint = (GeoPoint) mapView.getProjection().fromPixels((int) e.getX(), (int) e.getY());
                selectLocation(geoPoint);
                return true;
            }
            // You might need to override other event methods here
            // if you want to handle them, but for now just the tap one is sufficient.
        });

        // Request location permission
        requestLocationPermission();
    }

    private void setupMyLocationOverlay() {
        myLocationOverlay = new MyLocationNewOverlay(new GpsMyLocationProvider(requireContext()), mapView);
        myLocationOverlay.enableMyLocation();
        mapView.getOverlays().add(myLocationOverlay);
    }

    private void setupClickListeners() {
        currentLocationFab.setOnClickListener(v -> getCurrentLocation());
        
        confirmButton.setOnClickListener(v -> {
            if (selectedLocation != null && selectedAddress != null) {
                if (addressSelectedListener != null) {
                    addressSelectedListener.onAddressSelected(
                        selectedLocation.getLatitude(),
                        selectedLocation.getLongitude(),
                        selectedAddress
                    );
                }
                requireActivity().getSupportFragmentManager().popBackStack();
            } else {
                ToastUtils.showWarning(requireContext(), "Please select a location on the map");
            }
        });
    }

    private void selectLocation(GeoPoint geoPoint) {
        selectedLocation = geoPoint;
        
        // Remove existing marker
        if (selectedLocationMarker != null) {
            mapView.getOverlays().remove(selectedLocationMarker);
        }
        
        // Add new marker
        selectedLocationMarker = new Marker(mapView);
        selectedLocationMarker.setPosition(geoPoint);
        selectedLocationMarker.setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM);
        selectedLocationMarker.setTitle("Selected Location");
        mapView.getOverlays().add(selectedLocationMarker);
        
        // Refresh map
        mapView.invalidate();
        
        // Get address for the selected location
        getAddressFromLocation(geoPoint.getLatitude(), geoPoint.getLongitude());
    }

    private void getAddressFromLocation(double latitude, double longitude) {
        new Thread(() -> {
            try {
                List<Address> addresses = geocoder.getFromLocation(latitude, longitude, 1);
                if (addresses != null && !addresses.isEmpty()) {
                    Address address = addresses.get(0);
                    StringBuilder addressBuilder = new StringBuilder();
                    
                    for (int i = 0; i <= address.getMaxAddressLineIndex(); i++) {
                        if (i > 0) addressBuilder.append(", ");
                        addressBuilder.append(address.getAddressLine(i));
                    }
                    
                    selectedAddress = addressBuilder.toString();
                    
                    requireActivity().runOnUiThread(() -> {
                        selectedAddressTextView.setText(selectedAddress);
                        selectedAddressTextView.setVisibility(View.VISIBLE);
                        confirmButton.setEnabled(true);
                    });
                } else {
                    selectedAddress = "Location: " + String.format(Locale.getDefault(), "%.6f, %.6f", latitude, longitude);
                    requireActivity().runOnUiThread(() -> {
                        selectedAddressTextView.setText(selectedAddress);
                        selectedAddressTextView.setVisibility(View.VISIBLE);
                        confirmButton.setEnabled(true);
                    });
                }
            } catch (IOException e) {
                Log.e(TAG, "Error getting address from location", e);
                selectedAddress = "Location: " + String.format(Locale.getDefault(), "%.6f, %.6f", latitude, longitude);
                requireActivity().runOnUiThread(() -> {
                    selectedAddressTextView.setText(selectedAddress);
                    selectedAddressTextView.setVisibility(View.VISIBLE);
                    confirmButton.setEnabled(true);
                });
            }
        }).start();
    }

    private void getCurrentLocation() {
        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION) 
                != PackageManager.PERMISSION_GRANTED) {
            requestLocationPermission();
            return;
        }

        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        GeoPoint userLocation = new GeoPoint(location.getLatitude(), location.getLongitude());
                        mapController.animateTo(userLocation);
                        mapController.setZoom(16);
                        selectLocation(userLocation);
                    } else {
                        ToastUtils.showWarning(requireContext(), "Unable to get current location");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Error getting current location", e);
                    ToastUtils.showError(requireContext(), "Failed to get current location");
                });
    }

    private void requestLocationPermission() {
        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION) 
                != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(
                new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                LOCATION_PERMISSION_REQUEST_CODE
            );
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                setupMyLocationOverlay();
                getCurrentLocation();
            } else {
                ToastUtils.showWarning(requireContext(), "Location permission denied");
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mapView != null) {
            mapView.onResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mapView != null) {
            mapView.onPause();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mapView != null) {
            mapView.onDetach();
        }
    }
}
