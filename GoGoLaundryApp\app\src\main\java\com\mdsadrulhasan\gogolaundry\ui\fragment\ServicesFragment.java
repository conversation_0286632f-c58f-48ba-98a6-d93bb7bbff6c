package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.app.AlertDialog;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.ServiceGridCardAdapter;
import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.model.Service;
import com.mdsadrulhasan.gogolaundry.ui.fragment.CartFragment;
import com.mdsadrulhasan.gogolaundry.ui.fragment.ItemsFragment;
import com.mdsadrulhasan.gogolaundry.util.GridSpacingItemDecoration;
import com.mdsadrulhasan.gogolaundry.viewmodel.CartViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.ServicesViewModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Fragment for displaying laundry services in a grid layout
 */
public class ServicesFragment extends Fragment implements ServiceGridCardAdapter.OnServiceClickListener {

    private ServicesViewModel viewModel;
    private CartViewModel cartViewModel;
    private RecyclerView recyclerView;
    private ServiceGridCardAdapter adapter;
    private ProgressBar progressBar;
    private TextView emptyView;
    private com.google.android.material.card.MaterialCardView progressCard;
    private com.google.android.material.card.MaterialCardView emptyCard;
    private SwipeRefreshLayout swipeRefreshLayout;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(this).get(ServicesViewModel.class);
        cartViewModel = new ViewModelProvider(requireActivity()).get(CartViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_services, container, false);

        // Initialize views
        recyclerView = view.findViewById(R.id.services_recycler_view);
        progressBar = view.findViewById(R.id.progress_bar);
        progressCard = view.findViewById(R.id.progress_card);
        emptyView = view.findViewById(R.id.empty_view);
        emptyCard = view.findViewById(R.id.empty_card);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);

        // Set up RecyclerView with optimized GridLayoutManager for scrolling
        setupRecyclerView();

        // Set up adapter
        adapter = new ServiceGridCardAdapter(new ArrayList<>(), this);
        recyclerView.setAdapter(adapter);

        // Set up SwipeRefreshLayout
        swipeRefreshLayout.setOnRefreshListener(() -> {
            viewModel.refreshServices();
        });

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Observe services data
        viewModel.getServices().observe(getViewLifecycleOwner(), resource -> {
            swipeRefreshLayout.setRefreshing(false);

            if (resource == null) {
                showError("No data received");
                return;
            }

            if (resource.isLoading()) {
                showLoading();
            } else if (resource.isSuccess()) {
                List<ServiceEntity> serviceEntities = resource.getData();
                if (serviceEntities != null && !serviceEntities.isEmpty()) {
                    // Convert entities to UI models if needed
                    List<Service> services = convertToServiceModels(serviceEntities);
                    showServices(services);
                } else {
                    showEmpty();
                }
            } else if (resource.isError()) {
                String errorMessage = resource.getMessage();
                showError(errorMessage != null ? errorMessage : "Unknown error occurred");
            }
        });

        // Force refresh to load data from API
        viewModel.refreshServices();
    }

    /**
     * Set up RecyclerView with optimized settings for smooth scrolling
     */
    private void setupRecyclerView() {
        if (getContext() == null) return;

        // Calculate optimal span count based on screen size
        int spanCount = calculateSpanCount();

        // Create GridLayoutManager with optimized settings
        GridLayoutManager layoutManager = new GridLayoutManager(getContext(), spanCount);
        layoutManager.setRecycleChildrenOnDetach(true);

        // Set the layout manager
        recyclerView.setLayoutManager(layoutManager);

        // Enable performance optimizations for smooth scrolling
        recyclerView.setHasFixedSize(true);
        recyclerView.setItemViewCacheSize(20);
        recyclerView.setDrawingCacheEnabled(true);
        recyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        // Add spacing between grid items
        int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.grid_spacing);
        recyclerView.addItemDecoration(new GridSpacingItemDecoration(spanCount, spacingInPixels, true));

        // Add scroll listener for performance optimization
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                // Optimize performance during scrolling
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    // Re-enable animations when scrolling stops
                    recyclerView.setLayoutAnimation(null);
                }
            }
        });
    }

    /**
     * Convert ServiceEntity list to Service model list for UI
     *
     * @param serviceEntities List of service entities
     * @return List of service models
     */
    private List<Service> convertToServiceModels(List<ServiceEntity> serviceEntities) {
        List<Service> services = new ArrayList<>();

        for (ServiceEntity entity : serviceEntities) {
            Service service = new Service();
            service.setId(entity.getId());
            service.setName(entity.getName());
            service.setDescription(entity.getDescription());
            service.setIconUrl(entity.getImageUrl());

            // We don't need to set price for services as we're not displaying them
            // Prices should only be shown at the item level

            // Handle active status - convert boolean to appropriate format
            boolean isActive = entity.isActive();
            service.setActive(isActive);
            service.setIsActiveRaw(isActive ? 1 : 0);

            // Set dates if available
            if (entity.getCreatedAt() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
                service.setCreatedAt(dateFormat.format(entity.getCreatedAt()));
            }

            if (entity.getUpdatedAt() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
                service.setUpdatedAt(dateFormat.format(entity.getUpdatedAt()));
            }

            services.add(service);
        }

        return services;
    }

    /**
     * Show loading state
     */
    private void showLoading() {
        progressCard.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.GONE);
    }

    /**
     * Show services data
     *
     * @param services List of services to display
     */
    private void showServices(List<Service> services) {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        emptyCard.setVisibility(View.GONE);
        adapter.updateServices(services);
    }

    /**
     * Show empty state
     */
    private void showEmpty() {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.VISIBLE);
        emptyView.setText(getString(R.string.no_services_available));
    }

    /**
     * Show error state
     *
     * @param message Error message
     */
    private void showError(String message) {
        progressCard.setVisibility(View.GONE);
        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();

        // If adapter is empty, show empty view with error message
        if (adapter.getItemCount() == 0) {
            recyclerView.setVisibility(View.GONE);
            emptyCard.setVisibility(View.VISIBLE);
            emptyView.setText(getString(R.string.error) + ": " + message);
        }
    }

    /**
     * Handle service item click
     *
     * @param service Clicked service
     */
    @Override
    public void onServiceClick(Service service) {
        // Navigate to items screen for this service
        if (getActivity() != null) {
            // Create and show ItemsFragment
            ItemsFragment itemsFragment = ItemsFragment.newInstance(service.getId(), service.getName());
            getActivity().getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.fragment_container, itemsFragment)
                    .addToBackStack(null)
                    .commit();
        }
    }

    /**
     * Handle add to cart button click
     *
     * @param service Service to add to cart
     */
    @Override
    public void onAddToCartClick(Service service) {
        // Direct users to select items instead of adding services to cart
        AlertDialog.Builder builder = new AlertDialog.Builder(requireContext(), R.style.AlertDialogTheme);

        // Create a custom view for the dialog
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_select_items, null);
        TextView titleView = dialogView.findViewById(R.id.dialog_title);
        TextView messageView = dialogView.findViewById(R.id.dialog_message);

        // Set dialog content
        titleView.setText("Select Items");
        messageView.setText("Please select specific items from this service to add them to your cart.");

        builder.setView(dialogView);

        // Set buttons
        builder.setPositiveButton("View Items", (dialog, which) -> {
            // Navigate to items screen for this service
            if (getActivity() != null) {
                // Create and show ItemsFragment
                ItemsFragment itemsFragment = ItemsFragment.newInstance(service.getId(), service.getName());
                getActivity().getSupportFragmentManager()
                        .beginTransaction()
                        .replace(R.id.fragment_container, itemsFragment)
                        .addToBackStack(null)
                        .commit();
            }
        });

        builder.setNegativeButton("Cancel", null);

        // Show the dialog
        AlertDialog dialog = builder.create();
        dialog.show();
    }

    /**
     * Calculate the optimal number of columns for the grid based on screen size
     * Provides responsive design that adapts to different screen sizes
     *
     * @return Number of columns for the grid
     */
    private int calculateSpanCount() {
        if (getContext() == null) {
            return 2; // Default fallback
        }

        DisplayMetrics displayMetrics = getContext().getResources().getDisplayMetrics();
        float screenWidthDp = displayMetrics.widthPixels / displayMetrics.density;
        int orientation = getResources().getConfiguration().orientation;

        // Calculate span count based on screen width and orientation
        int spanCount;

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // Landscape mode - more columns
            if (screenWidthDp >= 900) {
                spanCount = 4; // Large tablets in landscape
            } else if (screenWidthDp >= 600) {
                spanCount = 3; // Medium tablets in landscape
            } else {
                spanCount = 3; // Phones in landscape
            }
        } else {
            // Portrait mode
            if (screenWidthDp >= 900) {
                spanCount = 3; // Large tablets in portrait
            } else if (screenWidthDp >= 600) {
                spanCount = 3; // Medium tablets in portrait
            } else if (screenWidthDp >= 480) {
                spanCount = 2; // Large phones
            } else {
                spanCount = 2; // Regular phones
            }
        }

        return spanCount;
    }
}
