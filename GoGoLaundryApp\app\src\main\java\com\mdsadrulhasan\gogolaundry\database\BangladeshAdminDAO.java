package com.mdsadrulhasan.gogolaundry.database;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;


import com.mdsadrulhasan.gogolaundry.model.District;
import com.mdsadrulhasan.gogolaundry.model.Division;
import com.mdsadrulhasan.gogolaundry.model.Upazilla;

import java.util.ArrayList;
import java.util.List;

public class BangladeshAdminDAO {
    private SQLiteDatabase database;
    private BangladeshAdminDBHelper dbHelper;

    public BangladeshAdminDAO(Context context) {
        dbHelper = new BangladeshAdminDBHelper(context);
    }

    public void open() {
        database = dbHelper.getWritableDatabase();
    }

    public void close() {
        dbHelper.close();
    }

    // Get all divisions
    public List<Division> getAllDivisions() {
        List<Division> divisions = new ArrayList<>();
        
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_DIVISIONS
                + " ORDER BY " + BangladeshAdminDBHelper.KEY_NAME;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        if (cursor.moveToFirst()) {
            do {
                Division division = new Division();
                division.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
                division.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
                
                divisions.add(division);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        return divisions;
    }

    // Get all districts
    public List<District> getAllDistricts() {
        List<District> districts = new ArrayList<>();
        
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_DISTRICTS
                + " ORDER BY " + BangladeshAdminDBHelper.KEY_NAME;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        if (cursor.moveToFirst()) {
            do {
                District district = new District();
                district.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
                district.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
                district.setDivisionId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_DIVISION_ID)));
                
                districts.add(district);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        return districts;
    }

    // Get districts by division ID
    public List<District> getDistrictsByDivision(int divisionId) {
        List<District> districts = new ArrayList<>();
        
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_DISTRICTS
                + " WHERE " + BangladeshAdminDBHelper.KEY_DIVISION_ID + " = " + divisionId
                + " ORDER BY " + BangladeshAdminDBHelper.KEY_NAME;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        if (cursor.moveToFirst()) {
            do {
                District district = new District();
                district.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
                district.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
                district.setDivisionId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_DIVISION_ID)));
                
                districts.add(district);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        return districts;
    }

    // Get all upazillas
    public List<Upazilla> getAllUpazillas() {
        List<Upazilla> upazillas = new ArrayList<>();
        
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_UPAZILLAS
                + " ORDER BY " + BangladeshAdminDBHelper.KEY_NAME;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        if (cursor.moveToFirst()) {
            do {
                Upazilla upazilla = new Upazilla();
                upazilla.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
                upazilla.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
                upazilla.setDistrictId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_DISTRICT_ID)));
                
                upazillas.add(upazilla);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        return upazillas;
    }

    // Get upazillas by district ID
    public List<Upazilla> getUpazillasByDistrict(int districtId) {
        List<Upazilla> upazillas = new ArrayList<>();
        
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_UPAZILLAS
                + " WHERE " + BangladeshAdminDBHelper.KEY_DISTRICT_ID + " = " + districtId
                + " ORDER BY " + BangladeshAdminDBHelper.KEY_NAME;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        if (cursor.moveToFirst()) {
            do {
                Upazilla upazilla = new Upazilla();
                upazilla.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
                upazilla.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
                upazilla.setDistrictId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_DISTRICT_ID)));
                
                upazillas.add(upazilla);
            } while (cursor.moveToNext());
        }
        
        cursor.close();
        return upazillas;
    }

    // Get a division by ID
    public Division getDivisionById(int id) {
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_DIVISIONS
                + " WHERE " + BangladeshAdminDBHelper.KEY_ID + " = " + id;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        Division division = null;
        if (cursor.moveToFirst()) {
            division = new Division();
            division.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
            division.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
        }
        
        cursor.close();
        return division;
    }

    // Get a district by ID
    public District getDistrictById(int id) {
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_DISTRICTS
                + " WHERE " + BangladeshAdminDBHelper.KEY_ID + " = " + id;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        District district = null;
        if (cursor.moveToFirst()) {
            district = new District();
            district.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
            district.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
            district.setDivisionId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_DIVISION_ID)));
        }
        
        cursor.close();
        return district;
    }

    // Get an upazilla by ID
    public Upazilla getUpazillaById(int id) {
        String selectQuery = "SELECT * FROM " + BangladeshAdminDBHelper.TABLE_UPAZILLAS
                + " WHERE " + BangladeshAdminDBHelper.KEY_ID + " = " + id;
        
        Cursor cursor = database.rawQuery(selectQuery, null);
        
        Upazilla upazilla = null;
        if (cursor.moveToFirst()) {
            upazilla = new Upazilla();
            upazilla.setId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_ID)));
            upazilla.setName(cursor.getString(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_NAME)));
            upazilla.setDistrictId(cursor.getInt(cursor.getColumnIndexOrThrow(BangladeshAdminDBHelper.KEY_DISTRICT_ID)));
        }
        
        cursor.close();
        return upazilla;
    }
}
