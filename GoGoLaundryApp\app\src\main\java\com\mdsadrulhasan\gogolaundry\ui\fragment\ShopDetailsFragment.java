package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import java.util.List;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.imageview.ShapeableImageView;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.model.CartItem;
import com.mdsadrulhasan.gogolaundry.utils.CartManager;
import com.mdsadrulhasan.gogolaundry.ui.adapter.ShopDetailsAdapter;
import com.mdsadrulhasan.gogolaundry.ui.fragment.CheckoutFragment;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.ShopDetailsViewModel;

/**
 * Fragment for displaying detailed shop information with enhanced RecyclerView implementation
 */
public class ShopDetailsFragment extends Fragment implements ShopDetailsAdapter.OnItemClickListener {

    private static final String TAG = "ShopDetailsFragment";
    private static final String ARG_SHOP_ID = "shop_id";

    // Views
    private CollapsingToolbarLayout collapsingToolbar;
    private ImageView shopCoverImageView;
    private ImageView notificationIcon;
    private ImageView shareIcon;
    private ImageView favoriteIcon;
    private TextView shopNameTextView;
    private TextView liveStatusBadge;
    private TextView distanceBadge;
    private TextView shopAddressTextView;
    private TextView deliveryInfoTextView;
    private LinearLayout ratingStarsLayout;
    private TextView ratingTextView;
    private TextView reviewsCountTextView;
    private MaterialButton viewReviewsButton;
    private TextView currentStatusIndicator;
    private LinearLayout operatingHoursContainer;
    private TextView serviceCountBadge;
    private MaterialButton viewAllServicesButton;
    private MaterialButton callButton;
    private MaterialButton directionsButton;
    private RecyclerView detailsRecyclerView;
    private MaterialButton orderFab;

    // Quick action views
    private LinearLayout expressServiceAction;
    private LinearLayout schedulePickupAction;
    private LinearLayout trackOrderAction;

    // ViewModel
    private ShopDetailsViewModel viewModel;

    // Enhanced Adapter (replaces ViewPager2 as requested)
    private ShopDetailsAdapter detailsAdapter;

    // Shop data
    private int shopId;

    public static ShopDetailsFragment newInstance(int shopId) {
        ShopDetailsFragment fragment = new ShopDetailsFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_SHOP_ID, shopId);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            shopId = getArguments().getInt(ARG_SHOP_ID);
        }

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(ShopDetailsViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_shop_details, container, false);

        initializeViews(view);
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        observeViewModel();

        // Load shop data
        Log.d(TAG, "Loading shop details for shopId: " + shopId);
        Log.d(TAG, "ViewModel instance: " + (viewModel != null ? "initialized" : "null"));
        Log.d(TAG, "About to call viewModel.setShopId(" + shopId + ")");
        viewModel.setShopId(shopId);
        Log.d(TAG, "Called viewModel.setShopId(" + shopId + ") successfully");

        return view;
    }

    private void initializeViews(View view) {
        // Initialize main views
        collapsingToolbar = view.findViewById(R.id.collapsingToolbar);
        shopCoverImageView = view.findViewById(R.id.shopCoverImageView);

        // Initialize action icons
        notificationIcon = view.findViewById(R.id.notificationIcon);
        shareIcon = view.findViewById(R.id.shareIcon);
        favoriteIcon = view.findViewById(R.id.favoriteIcon);

        // Initialize shop info views
        shopNameTextView = view.findViewById(R.id.shopNameTextView);
        liveStatusBadge = view.findViewById(R.id.liveStatusBadge);
        distanceBadge = view.findViewById(R.id.distanceBadge);
        shopAddressTextView = view.findViewById(R.id.shopAddressTextView);
        deliveryInfoTextView = view.findViewById(R.id.deliveryInfoTextView);

        // Initialize rating views
        ratingStarsLayout = view.findViewById(R.id.ratingStarsLayout);
        ratingTextView = view.findViewById(R.id.ratingTextView);
        reviewsCountTextView = view.findViewById(R.id.reviewsCountTextView);
        viewReviewsButton = view.findViewById(R.id.viewReviewsButton);

        // Initialize operating hours views
        currentStatusIndicator = view.findViewById(R.id.currentStatusIndicator);
        operatingHoursContainer = view.findViewById(R.id.operatingHoursContainer);

        // Initialize services views
        serviceCountBadge = view.findViewById(R.id.serviceCountBadge);
        viewAllServicesButton = view.findViewById(R.id.viewAllServicesButton);
        detailsRecyclerView = view.findViewById(R.id.detailsRecyclerView);

        // Initialize action buttons
        callButton = view.findViewById(R.id.callButton);
        directionsButton = view.findViewById(R.id.directionsButton);
        orderFab = view.findViewById(R.id.orderFab);

        // Initialize quick action views
        expressServiceAction = view.findViewById(R.id.expressServiceAction);
        schedulePickupAction = view.findViewById(R.id.schedulePickupAction);
        trackOrderAction = view.findViewById(R.id.trackOrderAction);
    }

    private void setupToolbar() {
        // Remove toolbar setup since MainActivity already has a toolbar
        // Just set up the collapsing toolbar behavior
        if (collapsingToolbar != null) {
            collapsingToolbar.setTitle(""); // Will be set when shop data loads
        }
    }

    private void setupRecyclerView() {
        Log.d(TAG, "Setting up RecyclerView for shop details");

        // Initialize enhanced adapter
        detailsAdapter = new ShopDetailsAdapter(requireContext(), this);

        // Setup RecyclerView with LinearLayoutManager for better performance
        LinearLayoutManager layoutManager = new LinearLayoutManager(requireContext());
        detailsRecyclerView.setLayoutManager(layoutManager);
        detailsRecyclerView.setAdapter(detailsAdapter);

        // Optimize RecyclerView performance
        detailsRecyclerView.setHasFixedSize(false);
        detailsRecyclerView.setItemViewCacheSize(20);
        detailsRecyclerView.setDrawingCacheEnabled(true);
        detailsRecyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        Log.d(TAG, "RecyclerView setup completed successfully");
    }

    private void setupClickListeners() {
        // Action icons
        if (notificationIcon != null) {
            notificationIcon.setOnClickListener(v -> {
                ToastUtils.showInfo(requireContext(), "Notifications");
            });
        }

        if (shareIcon != null) {
            shareIcon.setOnClickListener(v -> {
                shareShop();
            });
        }

        if (favoriteIcon != null) {
            favoriteIcon.setOnClickListener(v -> {
                toggleFavorite();
            });
        }

        // Review button
        if (viewReviewsButton != null) {
            viewReviewsButton.setOnClickListener(v -> {
                viewAllReviews();
            });
        }

        // Services button
        if (viewAllServicesButton != null) {
            viewAllServicesButton.setOnClickListener(v -> {
                viewAllServices();
            });
        }

        // Action buttons
        if (callButton != null) {
            callButton.setOnClickListener(v -> {
                LaundryShopEntity shop = viewModel.getShop().getValue();
                if (shop != null && shop.getPhone() != null) {
                    makePhoneCall(shop.getPhone());
                } else {
                    ToastUtils.showWarning(requireContext(), "Phone number not available");
                }
            });
        }

        if (directionsButton != null) {
            directionsButton.setOnClickListener(v -> {
                LaundryShopEntity shop = viewModel.getShop().getValue();
                if (shop != null) {
                    openDirections(shop.getLatitude(), shop.getLongitude());
                } else {
                    ToastUtils.showWarning(requireContext(), "Location not available");
                }
            });
        }

        if (orderFab != null) {
            orderFab.setOnClickListener(v -> {
                openOrderPlacement();
            });
        }

        // Quick action buttons
        if (expressServiceAction != null) {
            expressServiceAction.setOnClickListener(v -> {
                handleExpressService();
            });
        }

        if (schedulePickupAction != null) {
            schedulePickupAction.setOnClickListener(v -> {
                handleSchedulePickup();
            });
        }

        if (trackOrderAction != null) {
            trackOrderAction.setOnClickListener(v -> {
                handleTrackOrder();
            });
        }
    }

    private void observeViewModel() {
        // Observe shop data
        viewModel.getShop().observe(getViewLifecycleOwner(), shop -> {
            Log.d(TAG, "observeViewModel: Shop data received - " + (shop != null ? shop.getName() : "null"));
            if (shop != null) {
                Log.d(TAG, "Updating shop UI for: " + shop.getName());
                updateShopUI(shop);
            } else {
                Log.w(TAG, "Shop data is null, not updating UI");
            }
        });

        // Observe services and items data for RecyclerView
        viewModel.getServices().observe(getViewLifecycleOwner(), services -> {
            Log.d(TAG, "Services data received - count: " + (services != null ? services.size() : "null"));
            updateRecyclerViewData();
        });

        viewModel.getItems().observe(getViewLifecycleOwner(), items -> {
            Log.d(TAG, "Items data received - count: " + (items != null ? items.size() : "null"));
            updateRecyclerViewData();
        });

        // Observe loading state
        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            // Show/hide loading indicator if needed
        });

        // Observe error messages
        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                ToastUtils.showError(requireContext(), errorMessage);
                viewModel.clearErrorMessage();
            }
        });
    }

    private void updateRecyclerViewData() {
        if (detailsAdapter != null) {
            List<ShopServiceEntity> services = viewModel.getServices().getValue();
            List<ShopItemEntity> items = viewModel.getItems().getValue();
            detailsAdapter.updateData(services, items);
            Log.d(TAG, "RecyclerView data updated with " +
                  (services != null ? services.size() : 0) + " services and " +
                  (items != null ? items.size() : 0) + " items");
        }
    }

    private void updateShopUI(LaundryShopEntity shop) {
        Log.d(TAG, "updateShopUI() called for shop: " + shop.getName());

        try {
            // Set shop name in collapsing toolbar
            if (collapsingToolbar != null) {
                collapsingToolbar.setTitle(shop.getName());
                Log.d(TAG, "Set collapsing toolbar title: " + shop.getName());
            }

            // Load cover image
            if (shop.getCoverImageUrl() != null && !shop.getCoverImageUrl().isEmpty()) {
                Log.d(TAG, "Loading cover image: " + shop.getCoverImageUrl());
                Glide.with(this)
                        .load(shop.getCoverImageUrl())
                        .placeholder(R.drawable.shop_cover_placeholder)
                        .error(R.drawable.shop_cover_placeholder)
                        .into(shopCoverImageView);
            } else {
                Log.d(TAG, "No cover image URL, using placeholder");
                if (shopCoverImageView != null) {
                    shopCoverImageView.setImageResource(R.drawable.shop_cover_placeholder);
                }
            }

            // Update shop name
            if (shopNameTextView != null) {
                shopNameTextView.setText(shop.getName());
                shopNameTextView.setVisibility(View.VISIBLE);
                Log.d(TAG, "Set shop name: " + shop.getName());
            }

            // Update status badges
            updateStatusBadges(shop);

            // Update address and delivery info
            updateAddressInfo(shop);

            // Update rating and reviews
            updateRatingInfo(shop);

            // Update operating hours
            updateOperatingHours(shop);

            // Update service count
            updateServiceCount();

            Log.d(TAG, "updateShopUI() completed successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error updating shop UI", e);
        }
    }

    private void updateStatusBadges(LaundryShopEntity shop) {
        // Update live status badge
        if (liveStatusBadge != null) {
            if (viewModel.isShopOpen()) {
                liveStatusBadge.setText("Open Now");
                liveStatusBadge.setBackgroundResource(R.drawable.live_status_badge);
            } else {
                liveStatusBadge.setText("Closed");
                liveStatusBadge.setBackgroundResource(R.drawable.status_indicator_background);
            }
        }

        // Update distance badge
        if (distanceBadge != null && shop.getDistance() > 0) {
            distanceBadge.setText(String.format("%.1f km away", shop.getDistance()));
            distanceBadge.setVisibility(View.VISIBLE);
        }

        // Update current status indicator
        if (currentStatusIndicator != null) {
            if (viewModel.isShopOpen()) {
                currentStatusIndicator.setText("Open Now");
                currentStatusIndicator.setSelected(true);
            } else {
                currentStatusIndicator.setText("Closed");
                currentStatusIndicator.setSelected(false);
            }
        }
    }

    private void updateAddressInfo(LaundryShopEntity shop) {
        if (shopAddressTextView != null) {
            shopAddressTextView.setText(shop.getAddress());
            shopAddressTextView.setVisibility(View.VISIBLE);
            Log.d(TAG, "Set shop address: " + shop.getAddress());
        }

        if (deliveryInfoTextView != null) {
            String deliveryInfo = "Free delivery";
            if (shop.getDistance() > 0) {
                deliveryInfo += " • " + String.format("%.1f km away", shop.getDistance());
            }
            deliveryInfoTextView.setText(deliveryInfo);
        }
    }

    private void updateRatingInfo(LaundryShopEntity shop) {
        // Update rating stars
        updateRatingStars(shop.getRating());

        // Update rating text
        if (ratingTextView != null) {
            ratingTextView.setText(String.format("%.1f", shop.getRating()));
        }

        // Update reviews count
        if (reviewsCountTextView != null) {
            String reviewText = "Based on " + shop.getTotalReviews() + " reviews";
            reviewsCountTextView.setText(reviewText);
        }
    }

    private void updateOperatingHours(LaundryShopEntity shop) {
        // For now, we'll use static hours. In a real app, this would come from the API
        // The operating hours container already has default hours in the layout
        Log.d(TAG, "Operating hours updated (using default layout hours)");
    }

    private void updateServiceCount() {
        if (serviceCountBadge != null) {
            List<ShopServiceEntity> services = viewModel.getServices().getValue();
            int count = services != null ? services.size() : 0;
            serviceCountBadge.setText(String.valueOf(count));
            serviceCountBadge.setVisibility(count > 0 ? View.VISIBLE : View.GONE);
        }
    }

    private void updateRatingStars(double rating) {
        ratingStarsLayout.removeAllViews();

        int fullStars = (int) rating;
        boolean hasHalfStar = (rating - fullStars) >= 0.5;

        // Add full stars
        for (int i = 0; i < fullStars; i++) {
            ImageView star = createStarImageView(R.drawable.ic_star_filled);
            ratingStarsLayout.addView(star);
        }

        // Add half star if needed
        if (hasHalfStar) {
            ImageView star = createStarImageView(R.drawable.ic_star_half);
            ratingStarsLayout.addView(star);
        }

        // Add empty stars to make total of 5
        int totalStars = fullStars + (hasHalfStar ? 1 : 0);
        for (int i = totalStars; i < 5; i++) {
            ImageView star = createStarImageView(R.drawable.ic_star_empty);
            ratingStarsLayout.addView(star);
        }
    }

    private ImageView createStarImageView(int drawableRes) {
        ImageView star = new ImageView(requireContext());
        star.setImageResource(drawableRes);
        star.setColorFilter(getResources().getColor(R.color.rating_star, null));

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                getResources().getDimensionPixelSize(R.dimen.star_size),
                getResources().getDimensionPixelSize(R.dimen.star_size)
        );
        params.setMarginEnd(getResources().getDimensionPixelSize(R.dimen.star_margin));
        star.setLayoutParams(params);

        return star;
    }



    private void makePhoneCall(String phoneNumber) {
        Intent intent = new Intent(Intent.ACTION_DIAL);
        intent.setData(Uri.parse("tel:" + phoneNumber));
        startActivity(intent);
    }

    private void openDirections(double latitude, double longitude) {
        String uri = String.format("geo:%f,%f?q=%f,%f", latitude, longitude, latitude, longitude);
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
        intent.setPackage("com.google.android.apps.maps");

        if (intent.resolveActivity(requireActivity().getPackageManager()) != null) {
            startActivity(intent);
        } else {
            // Fallback to browser
            String url = String.format("https://www.google.com/maps?q=%f,%f", latitude, longitude);
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(browserIntent);
        }
    }

    private void openOrderPlacement() {
        LaundryShopEntity shop = viewModel.getShop().getValue();
        if (shop == null) {
            ToastUtils.showError(requireContext(), "Shop information not available");
            return;
        }

        // Validate cart has items before proceeding
        CartManager cartManager = CartManager.getInstance(requireContext());
        List<CartItem> cartItems = cartManager.getCartItems();

        if (cartItems == null || cartItems.isEmpty()) {
            ToastUtils.showWarning(requireContext(), "Your cart is empty. Please add items to cart first.");
            return;
        }

        // Validate that cart items belong to this shop
        boolean hasShopItems = validateCartItemsForShop(cartItems, shop.getId());
        if (!hasShopItems) {
            ToastUtils.showWarning(requireContext(),
                "Your cart contains items from other shops. Please clear cart and add items from " + shop.getName());
            return;
        }

        Log.d(TAG, "Cart validation passed. Proceeding to checkout with " + cartItems.size() + " items");

        // Navigate to checkout with shop context
        navigateToCheckoutWithShop(shop);
    }

    private void navigateToCheckoutWithShop(LaundryShopEntity shop) {
        try {
            // Create checkout fragment with shop context
            CheckoutFragment checkoutFragment = new CheckoutFragment();

            // Pass shop information to checkout
            Bundle args = new Bundle();
            args.putInt("shop_id", shop.getId());
            args.putString("shop_name", shop.getName());
            args.putString("shop_address", shop.getAddress());
            checkoutFragment.setArguments(args);

            // Navigate to checkout
            requireActivity().getSupportFragmentManager()
                    .beginTransaction()
                    .setCustomAnimations(R.anim.slide_in_right, R.anim.slide_out_left,
                                       R.anim.slide_in_left, R.anim.slide_out_right)
                    .replace(R.id.fragment_container, checkoutFragment)
                    .addToBackStack(null)
                    .commit();

            Log.d(TAG, "Navigated to checkout for shop: " + shop.getName());
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to checkout", e);
            ToastUtils.showError(requireContext(), "Unable to proceed to checkout");
        }
    }

    // ShopDetailsAdapter.OnItemClickListener implementation
    @Override
    public void onServiceClick(ShopServiceEntity service) {
        Log.d(TAG, "Service clicked: " + service.getServiceName());
        ToastUtils.showInfo(requireContext(), "Service: " + service.getServiceName());
        // TODO: Navigate to service details or filter items by service
    }

    @Override
    public void onItemClick(ShopItemEntity item) {
        Log.d(TAG, "Item clicked: " + item.getItemName());
        ToastUtils.showInfo(requireContext(), "Item: " + item.getItemName());
        // TODO: Show item details dialog
    }

    @Override
    public void onAddToCartClick(ShopItemEntity item) {
        Log.d(TAG, "Add to cart clicked for: " + item.getItemName());

        // Convert ShopItemEntity to Item model for cart
        Item cartItem = convertShopItemToItem(item);

        // Add to cart using CartManager
        CartManager cartManager = CartManager.getInstance(requireContext());
        boolean success = cartManager.addToCart(cartItem, 1.0); // Default quantity of 1

        if (success) {
            ToastUtils.showSuccess(requireContext(), "Added " + item.getItemName() + " to cart");
            Log.d(TAG, "Successfully added " + item.getItemName() + " to cart");
        } else {
            ToastUtils.showError(requireContext(), "Failed to add item to cart");
            Log.e(TAG, "Failed to add " + item.getItemName() + " to cart");
        }
    }

    /**
     * Convert ShopItemEntity to Item model for cart operations
     */
    private Item convertShopItemToItem(ShopItemEntity shopItem) {
        Item item = new Item();
        item.setId(shopItem.getItemId());
        item.setName(shopItem.getItemName());
        item.setBnName(shopItem.getItemBnName());
        item.setImageUrl(shopItem.getItemImageUrl());
        item.setPrice(shopItem.getEffectivePrice()); // Use effective price (custom or default)
        item.setServiceName(shopItem.getServiceName());
        item.setDescription(shopItem.getDescription());
        item.setBnDescription(shopItem.getBnDescription());
        item.setActive(shopItem.isAvailable());
        item.setInStock(shopItem.isAvailable()); // Assuming available means in stock

        // Try to find service ID from the current shop's services
        int serviceId = findServiceIdForItem(shopItem);
        item.setServiceId(serviceId);

        return item;
    }

    /**
     * Find service ID for a shop item by matching service name
     */
    private int findServiceIdForItem(ShopItemEntity shopItem) {
        List<ShopServiceEntity> services = viewModel.getServices().getValue();
        if (services != null && shopItem.getServiceName() != null) {
            for (ShopServiceEntity service : services) {
                if (shopItem.getServiceName().equals(service.getServiceName())) {
                    return service.getServiceId();
                }
            }
        }
        // Return a default service ID if not found (you might want to handle this differently)
        return 1; // Default service ID
    }

    /**
     * Validate that cart items belong to the specified shop
     * For now, we'll allow mixed cart items but log a warning
     * In future, you might want to enforce shop-specific carts
     */
    private boolean validateCartItemsForShop(List<CartItem> cartItems, int shopId) {
        if (cartItems == null || cartItems.isEmpty()) {
            return false;
        }

        // For now, we'll be permissive and allow any cart items
        // In a more strict implementation, you would check if items belong to the shop
        // This would require adding shop_id to CartItem model or checking against shop items

        Log.d(TAG, "Validating " + cartItems.size() + " cart items for shop ID: " + shopId);

        // TODO: Implement strict shop validation if needed
        // For now, return true to allow checkout with any items
        return true;
    }

    // New methods for enhanced UI functionality
    private void shareShop() {
        LaundryShopEntity shop = viewModel.getShop().getValue();
        if (shop != null) {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, "Check out " + shop.getName());
            shareIntent.putExtra(Intent.EXTRA_TEXT,
                "I found this great laundry service: " + shop.getName() +
                "\nAddress: " + shop.getAddress() +
                "\nRating: " + shop.getRating() + " stars");
            startActivity(Intent.createChooser(shareIntent, "Share Shop"));
        }
    }

    private void toggleFavorite() {
        // TODO: Implement favorite functionality
        ToastUtils.showInfo(requireContext(), "Added to favorites");
        // Update favorite icon state
        if (favoriteIcon != null) {
            favoriteIcon.setImageResource(R.drawable.ic_favorite_filled);
        }
    }

    private void viewAllReviews() {
        // TODO: Navigate to reviews fragment
        ToastUtils.showInfo(requireContext(), "View all reviews");
    }

    private void viewAllServices() {
        // TODO: Navigate to services fragment
        ToastUtils.showInfo(requireContext(), "View all services");
    }

    private void handleExpressService() {
        ToastUtils.showInfo(requireContext(), "Express service selected");
        // TODO: Navigate to express service booking
    }

    private void handleSchedulePickup() {
        ToastUtils.showInfo(requireContext(), "Schedule pickup");
        // TODO: Navigate to pickup scheduling
    }

    private void handleTrackOrder() {
        ToastUtils.showInfo(requireContext(), "Track order");
        // TODO: Navigate to order tracking
    }
}