package com.mdsadrulhasan.gogolaundry.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity;

import java.util.List;

/**
 * Adapter for invoice items
 */
public class InvoiceItemAdapter extends RecyclerView.Adapter<InvoiceItemAdapter.ViewHolder> {

    private List<OrderItemEntity> items;

    /**
     * Constructor
     *
     * @param items List of order items
     */
    public InvoiceItemAdapter(List<OrderItemEntity> items) {
        this.items = items;
    }

    /**
     * Update items and refresh the adapter
     *
     * @param items New list of order items
     */
    public void updateItems(List<OrderItemEntity> items) {
        this.items = items;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_invoice_row, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        OrderItemEntity item = items.get(position);

        // Set item name with service
        String itemName = item.getItemName();
        if (item.getServiceName() != null && !item.getServiceName().isEmpty()) {
            itemName += " (" + item.getServiceName() + ")";
        }
        holder.itemName.setText(itemName);

        // Set quantity
        holder.itemQuantity.setText(String.valueOf(item.getQuantity()));

        // Set price
        holder.itemPrice.setText(String.format("৳ %.2f", item.getPrice()));

        // Set total
        holder.itemTotal.setText(String.format("৳ %.2f", item.getSubtotal()));
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    /**
     * Get the current list of items
     *
     * @return List of order items
     */
    public List<OrderItemEntity> getItems() {
        return items;
    }

    /**
     * ViewHolder for invoice items
     */
    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView itemName;
        TextView itemQuantity;
        TextView itemPrice;
        TextView itemTotal;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            itemName = itemView.findViewById(R.id.item_name);
            itemQuantity = itemView.findViewById(R.id.item_quantity);
            itemPrice = itemView.findViewById(R.id.item_price);
            itemTotal = itemView.findViewById(R.id.item_total);
        }
    }
}
