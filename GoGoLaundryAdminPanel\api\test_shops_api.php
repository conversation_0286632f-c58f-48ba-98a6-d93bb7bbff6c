<?php
/**
 * Test script for shop APIs
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>GoGoLaundry Shop API Test</h1>";

$baseUrl = "http://*************/GoGoLaundry/GoGoLaundryAdminPanel/api/shops/";

// Test 1: List all shops
echo "<h2>Test 1: List All Shops</h2>";
$listUrl = $baseUrl . "list.php?active_only=1&verified_only=1&limit=10";
echo "<p><strong>URL:</strong> <a href='$listUrl' target='_blank'>$listUrl</a></p>";

$response = @file_get_contents($listUrl);
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p><strong>✅ Success:</strong> Found " . count($data['data']) . " shops</p>";
        if (!empty($data['data'])) {
            echo "<p><strong>First shop:</strong> " . htmlspecialchars($data['data'][0]['name']) . "</p>";
        }
    } else {
        echo "<p><strong>❌ Error:</strong> " . ($data['message'] ?? 'Unknown error') . "</p>";
    }
} else {
    echo "<p><strong>❌ Failed to connect to API</strong></p>";
}

// Test 2: Search shops
echo "<h2>Test 2: Search Shops</h2>";
$searchUrl = $baseUrl . "search.php?query=laundry&active_only=1&verified_only=1&limit=5";
echo "<p><strong>URL:</strong> <a href='$searchUrl' target='_blank'>$searchUrl</a></p>";

$response = @file_get_contents($searchUrl);
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p><strong>✅ Success:</strong> Found " . count($data['data']) . " shops matching 'laundry'</p>";
        if (!empty($data['data'])) {
            echo "<p><strong>First result:</strong> " . htmlspecialchars($data['data'][0]['name']) . "</p>";
        }
    } else {
        echo "<p><strong>❌ Error:</strong> " . ($data['message'] ?? 'Unknown error') . "</p>";
    }
} else {
    echo "<p><strong>❌ Failed to connect to API</strong></p>";
}

// Test 3: Nearby shops
echo "<h2>Test 3: Nearby Shops</h2>";
$nearbyUrl = $baseUrl . "nearby.php?latitude=23.8103&longitude=90.4125&radius=10&limit=5";
echo "<p><strong>URL:</strong> <a href='$nearbyUrl' target='_blank'>$nearbyUrl</a></p>";

$response = @file_get_contents($nearbyUrl);
if ($response) {
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "<p><strong>✅ Success:</strong> Found " . count($data['data']) . " shops near Dhaka</p>";
        if (!empty($data['data'])) {
            echo "<p><strong>Nearest shop:</strong> " . htmlspecialchars($data['data'][0]['name']) . " (" . $data['data'][0]['distance'] . " km away)</p>";
        }
    } else {
        echo "<p><strong>❌ Error:</strong> " . ($data['message'] ?? 'Unknown error') . "</p>";
    }
} else {
    echo "<p><strong>❌ Failed to connect to API</strong></p>";
}

// Test 4: Shop details
echo "<h2>Test 4: Shop Details</h2>";
// First get a shop ID from the list
$listResponse = @file_get_contents($baseUrl . "list.php?limit=1");
$shopId = null;
if ($listResponse) {
    $listData = json_decode($listResponse, true);
    if ($listData && $listData['success'] && !empty($listData['data'])) {
        $shopId = $listData['data'][0]['id'];
    }
}

if ($shopId) {
    $detailsUrl = $baseUrl . "details.php?shop_id=$shopId&include_services=true&include_items=true";
    echo "<p><strong>URL:</strong> <a href='$detailsUrl' target='_blank'>$detailsUrl</a></p>";
    
    $response = @file_get_contents($detailsUrl);
    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "<p><strong>✅ Success:</strong> Got details for shop: " . htmlspecialchars($data['data']['name']) . "</p>";
            echo "<p><strong>Services:</strong> " . count($data['data']['services'] ?? []) . "</p>";
            echo "<p><strong>Items:</strong> " . count($data['data']['items'] ?? []) . "</p>";
        } else {
            echo "<p><strong>❌ Error:</strong> " . ($data['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p><strong>❌ Failed to connect to API</strong></p>";
    }
} else {
    echo "<p><strong>❌ No shops found to test details</strong></p>";
}

// Database check
echo "<h2>Database Check</h2>";
try {
    require_once 'includes/config.php';
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM laundry_shops");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p><strong>Total shops in database:</strong> " . $result['total'] . "</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM laundry_shops WHERE is_active = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p><strong>Active shops:</strong> " . $result['active'] . "</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as verified FROM laundry_shops WHERE is_verified = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p><strong>Verified shops:</strong> " . $result['verified'] . "</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as both FROM laundry_shops WHERE is_active = 1 AND is_verified = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p><strong>Active & Verified shops:</strong> " . $result['both'] . "</p>";
    
    // Show recent shops
    $stmt = $pdo->query("SELECT name, created_at FROM laundry_shops ORDER BY created_at DESC LIMIT 3");
    $recentShops = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p><strong>Recent shops:</strong></p>";
    echo "<ul>";
    foreach ($recentShops as $shop) {
        echo "<li>" . htmlspecialchars($shop['name']) . " (added: " . $shop['created_at'] . ")</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p><strong>❌ Database Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>Note:</strong> If any tests fail, check:</p>";
echo "<ul>";
echo "<li>Database connection in includes/config.php</li>";
echo "<li>Shop data exists in laundry_shops table</li>";
echo "<li>Shops are marked as active and verified</li>";
echo "<li>API files have correct permissions</li>";
echo "</ul>";
?>
