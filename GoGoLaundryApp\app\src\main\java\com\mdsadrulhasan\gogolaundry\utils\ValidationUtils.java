package com.mdsadrulhasan.gogolaundry.utils;

import android.text.TextUtils;
import android.util.Patterns;

/**
 * Utility class for input validation
 */
public class ValidationUtils {

    /**
     * Validate phone number (Bangladesh format)
     *
     * @param phone Phone number to validate
     * @return True if valid, false otherwise
     */
    public static boolean isValidPhone(String phone) {
        if (TextUtils.isEmpty(phone)) {
            return false;
        }

        // Remove any non-digit characters
        String digitsOnly = phone.replaceAll("\\D", "");

        // Check if it's a valid Bangladesh phone number
        // Format: +8801XXXXXXXXX or 01XXXXXXXXX or 8801XXXXXXXXX (X = digit)
        // Allow both 11-digit (01XXXXXXXXX) and 13-digit (8801XXXXXXXXX) formats
        return digitsOnly.matches("(?:(?:\\+|00)880|0|880)1[3-9]\\d{8}");
    }

    /**
     * Format phone number to standard format
     *
     * @param phone Phone number to format
     * @return Formatted phone number
     */
    public static String formatPhone(String phone) {
        if (TextUtils.isEmpty(phone)) {
            return phone;
        }

        // Remove any non-digit characters
        String digitsOnly = phone.replaceAll("\\D", "");

        // If the number starts with 0, replace with +880
        if (digitsOnly.startsWith("0")) {
            return "+880" + digitsOnly.substring(1);
        }

        // If the number starts with 880 (without +), add +
        if (digitsOnly.startsWith("880")) {
            return "+" + digitsOnly;
        }

        // If the number is just the 10-digit part (1XXXXXXXXX), add +880
        if (digitsOnly.length() == 10 && digitsOnly.startsWith("1")) {
            return "+880" + digitsOnly;
        }

        // If the number doesn't have country code, add it
        if (!digitsOnly.startsWith("+880") && !digitsOnly.startsWith("880")) {
            return "+880" + digitsOnly;
        }

        return phone;
    }

    /**
     * Validate email address
     *
     * @param email Email to validate
     * @return True if valid, false otherwise
     */
    public static boolean isValidEmail(String email) {
        return !TextUtils.isEmpty(email) && Patterns.EMAIL_ADDRESS.matcher(email).matches();
    }

    /**
     * Validate password strength
     *
     * @param password Password to validate
     * @param minLength Minimum password length
     * @return True if valid, false otherwise
     */
    public static boolean isValidPassword(String password, int minLength) {
        return !TextUtils.isEmpty(password) && password.length() >= minLength;
    }

    /**
     * Validate OTP
     *
     * @param otp OTP to validate
     * @param length Expected OTP length
     * @return True if valid, false otherwise
     */
    public static boolean isValidOtp(String otp, int length) {
        return !TextUtils.isEmpty(otp) && otp.length() == length && otp.matches("\\d+");
    }

    /**
     * Validate name
     *
     * @param name Name to validate
     * @return True if valid, false otherwise
     */
    public static boolean isValidName(String name) {
        return !TextUtils.isEmpty(name) && name.length() >= 2;
    }
}
