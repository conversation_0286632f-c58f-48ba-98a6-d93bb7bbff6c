package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.DrawableRes;
import androidx.annotation.StringRes;
import androidx.core.content.ContextCompat;

import com.mdsadrulhasan.gogolaundry.R;

/**
 * Utility class for displaying toast notifications
 */
public class ToastUtils {
    private static final String TAG = "ToastUtils";

    // Toast duration constants
    public static final int LENGTH_SHORT = Toast.LENGTH_SHORT;
    public static final int LENGTH_LONG = Toast.LENGTH_LONG;

    // Toast types
    public static final int TYPE_INFO = 0;
    public static final int TYPE_SUCCESS = 1;
    public static final int TYPE_WARNING = 2;
    public static final int TYPE_ERROR = 3;

    /**
     * Show a custom toast with an icon
     *
     * @param context Context
     * @param message Message to display
     * @param type Toast type (TYPE_INFO, TYPE_SUCCESS, TYPE_WARNING, TYPE_ERROR)
     * @param duration Toast duration (LENGTH_SHORT, LENGTH_LONG)
     */
    public static void showToast(Context context, String message, int type, int duration) {
        if (context == null) return;

        // Create handler for main thread
        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                // Inflate custom toast layout
                LayoutInflater inflater = LayoutInflater.from(context);
                View layout = inflater.inflate(R.layout.custom_toast, null);

                // Get views
                TextView text = layout.findViewById(R.id.toast_text);
                ImageView icon = layout.findViewById(R.id.toast_icon);
                View background = layout.findViewById(R.id.toast_background);

                // Set message
                text.setText(message);

                // Set icon and background color based on type
                switch (type) {
                    case TYPE_SUCCESS:
                        icon.setImageResource(R.drawable.ic_success);
                        background.setBackgroundResource(R.drawable.bg_toast_success);
                        break;
                    case TYPE_WARNING:
                        icon.setImageResource(R.drawable.ic_warning);
                        background.setBackgroundResource(R.drawable.bg_toast_warning);
                        break;
                    case TYPE_ERROR:
                        icon.setImageResource(R.drawable.ic_error);
                        background.setBackgroundResource(R.drawable.bg_toast_error);
                        break;
                    case TYPE_INFO:
                    default:
                        icon.setImageResource(R.drawable.ic_info);
                        background.setBackgroundResource(R.drawable.bg_toast_info);
                        break;
                }

                // Create and show toast
                Toast toast = new Toast(context);
                toast.setGravity(Gravity.CENTER, 0, 0);
                toast.setDuration(duration);
                toast.setView(layout);
                toast.show();
            } catch (Exception e) {
                // Fallback to standard toast if custom toast fails
                Toast.makeText(context, message, duration).show();
            }
        });
    }

    /**
     * Show a success toast
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showSuccessToast(Context context, String message) {
        showToast(context, message, TYPE_SUCCESS, LENGTH_SHORT);
    }

    /**
     * Show a success toast with resource string
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showSuccessToast(Context context, @StringRes int stringResId) {
        showSuccessToast(context, context.getString(stringResId));
    }

    /**
     * Show an error toast
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showErrorToast(Context context, String message) {
        showToast(context, message, TYPE_ERROR, LENGTH_LONG);
    }

    /**
     * Show an error toast with resource string
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showErrorToast(Context context, @StringRes int stringResId) {
        showErrorToast(context, context.getString(stringResId));
    }

    /**
     * Show a warning toast
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showWarningToast(Context context, String message) {
        showToast(context, message, TYPE_WARNING, LENGTH_LONG);
    }

    /**
     * Show a warning toast with resource string
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showWarningToast(Context context, @StringRes int stringResId) {
        showWarningToast(context, context.getString(stringResId));
    }

    /**
     * Show an info toast
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showInfoToast(Context context, String message) {
        showToast(context, message, TYPE_INFO, LENGTH_SHORT);
    }

    /**
     * Show an info toast with resource string
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showInfoToast(Context context, @StringRes int stringResId) {
        showInfoToast(context, context.getString(stringResId));
    }

    /**
     * Show an API response toast based on success/failure
     *
     * @param context Context
     * @param isSuccess Whether the API call was successful
     * @param message Message to display
     */
    public static void showApiResponseToast(Context context, boolean isSuccess, String message) {
        if (isSuccess) {
            showSuccessToast(context, message);
        } else {
            showErrorToast(context, message);
        }
    }

    /**
     * Show a toast for order status update
     *
     * @param context Context
     * @param status New order status
     * @param orderNumber Order number
     */
    public static void showOrderStatusToast(Context context, String status, String orderNumber) {
        String message = String.format("Order #%s: Status updated to %s", orderNumber, status);
        showInfoToast(context, message);
    }

    /**
     * Show a toast for successful reorder
     *
     * @param context Context
     * @param orderNumber Original order number
     */
    public static void showReorderToast(Context context, String orderNumber) {
        String message = String.format("Order #%s has been reordered successfully", orderNumber);
        showSuccessToast(context, message);
    }

    // Convenience methods with shorter names for backward compatibility

    /**
     * Show a success toast (convenience method)
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showSuccess(Context context, String message) {
        showSuccessToast(context, message);
    }

    /**
     * Show a success toast with resource string (convenience method)
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showSuccess(Context context, @StringRes int stringResId) {
        showSuccessToast(context, stringResId);
    }

    /**
     * Show an error toast (convenience method)
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showError(Context context, String message) {
        showErrorToast(context, message);
    }

    /**
     * Show an error toast with resource string (convenience method)
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showError(Context context, @StringRes int stringResId) {
        showErrorToast(context, stringResId);
    }

    /**
     * Show a warning toast (convenience method)
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showWarning(Context context, String message) {
        showWarningToast(context, message);
    }

    /**
     * Show a warning toast with resource string (convenience method)
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showWarning(Context context, @StringRes int stringResId) {
        showWarningToast(context, stringResId);
    }

    /**
     * Show an info toast (convenience method)
     *
     * @param context Context
     * @param message Message to display
     */
    public static void showInfo(Context context, String message) {
        showInfoToast(context, message);
    }

    /**
     * Show an info toast with resource string (convenience method)
     *
     * @param context Context
     * @param stringResId String resource ID
     */
    public static void showInfo(Context context, @StringRes int stringResId) {
        showInfoToast(context, stringResId);
    }
}
