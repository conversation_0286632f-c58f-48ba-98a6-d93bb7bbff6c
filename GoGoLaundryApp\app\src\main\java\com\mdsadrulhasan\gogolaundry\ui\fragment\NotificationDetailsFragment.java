package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.text.format.DateUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.MainActivity;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Notification;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.NotificationViewModel;

/**
 * Fragment for displaying detailed notification information
 */
public class NotificationDetailsFragment extends Fragment {
    private static final String TAG = "NotificationDetails";
    private static final String ARG_NOTIFICATION = "notification";

    private Notification notification;
    private NotificationViewModel viewModel;

    // Views
    private View backButton; // Changed from Toolbar to back button view
    private ImageView notificationIcon;
    private TextView notificationType;
    private TextView notificationTime;
    private TextView notificationTitle;
    private TextView notificationMessage;
    private ImageView notificationImage;
    private LinearLayout orderInfoLayout;
    private TextView orderNumber;
    private MaterialButton btnMarkRead;
    private MaterialButton btnViewOrder;

    /**
     * Create a new instance of NotificationDetailsFragment
     *
     * @param notification The notification to display
     * @return New instance of NotificationDetailsFragment
     */
    public static NotificationDetailsFragment newInstance(Notification notification) {
        NotificationDetailsFragment fragment = new NotificationDetailsFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_NOTIFICATION, notification);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Get notification from arguments
        if (getArguments() != null) {
            notification = (Notification) getArguments().getSerializable(ARG_NOTIFICATION);
        }

        if (notification == null) {
            Log.e(TAG, "No notification provided to NotificationDetailsFragment");
            // Navigate back if no notification
            if (getActivity() != null) {
                getActivity().onBackPressed();
            }
            return;
        }

        viewModel = new ViewModelProvider(this).get(NotificationViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_notification_details, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize views
        initViews(view);

        // Set up toolbar
        setupToolbar();

        // Populate notification data
        populateNotificationData();

        // Set up click listeners
        setupClickListeners();
    }

    /**
     * Initialize all views
     */
    private void initViews(View view) {
        backButton = view.findViewById(R.id.back_button);
        notificationIcon = view.findViewById(R.id.notification_icon);
        notificationType = view.findViewById(R.id.notification_type);
        notificationTime = view.findViewById(R.id.notification_time);
        notificationTitle = view.findViewById(R.id.notification_title);
        notificationMessage = view.findViewById(R.id.notification_message);
        notificationImage = view.findViewById(R.id.notification_image);
        orderInfoLayout = view.findViewById(R.id.order_info_layout);
        orderNumber = view.findViewById(R.id.order_number);
        btnMarkRead = view.findViewById(R.id.btn_mark_read);
        btnViewOrder = view.findViewById(R.id.btn_view_order);
    }

    /**
     * Set up back button navigation
     */
    private void setupToolbar() {
        // Set up back button click listener
        if (backButton != null) {
            backButton.setOnClickListener(v -> {
                if (getActivity() != null) {
                    getActivity().onBackPressed();
                }
            });
        }
    }

    /**
     * Populate notification data into views
     */
    private void populateNotificationData() {
        if (notification == null) return;

        // Set title and message
        notificationTitle.setText(notification.getTitle());
        notificationMessage.setText(notification.getMessage());

        // Set type
        notificationType.setText(notification.getTypeDisplayName().toUpperCase());

        // Set time
        if (notification.getCreatedAt() != null) {
            long timeMillis = notification.getCreatedAt().getTime();
            String relativeTime = DateUtils.getRelativeTimeSpanString(
                    timeMillis,
                    System.currentTimeMillis(),
                    DateUtils.MINUTE_IN_MILLIS,
                    DateUtils.FORMAT_ABBREV_RELATIVE
            ).toString();
            notificationTime.setText(relativeTime);
        }

        // Set icon based on notification type
        switch (notification.getType()) {
            case "order_status":
                notificationIcon.setImageResource(R.drawable.ic_receipt);
                break;
            case "promo":
                notificationIcon.setImageResource(R.drawable.ic_add_shopping_cart);
                break;
            case "system":
                notificationIcon.setImageResource(R.drawable.ic_info);
                break;
            default:
                notificationIcon.setImageResource(R.drawable.ic_notification);
                break;
        }

        // Handle notification image
        String imageUrl = notification.getImageUrl();
        View notificationImageContainer = getView().findViewById(R.id.notification_image_container);

        if (imageUrl != null && !imageUrl.trim().isEmpty()) {
            if (notificationImageContainer != null) {
                notificationImageContainer.setVisibility(View.VISIBLE);
            }
            notificationImage.setVisibility(View.VISIBLE);

            // Load image with Glide
            Glide.with(this)
                    .load(imageUrl)
                    .apply(new RequestOptions()
                            .placeholder(R.drawable.ic_notification)
                            .error(R.drawable.ic_notification)
                            .transform(new RoundedCorners(24)))
                    .into(notificationImage);
        } else {
            if (notificationImageContainer != null) {
                notificationImageContainer.setVisibility(View.GONE);
            }
            notificationImage.setVisibility(View.GONE);
        }

        // Handle order information
        View orderInfoContainer = getView().findViewById(R.id.order_info_container);

        if ("order_status".equals(notification.getType()) && notification.getOrderId() != null) {
            if (orderInfoContainer != null) {
                orderInfoContainer.setVisibility(View.VISIBLE);
            }
            orderInfoLayout.setVisibility(View.VISIBLE);
            btnViewOrder.setVisibility(View.VISIBLE);

            if (notification.getOrderNumber() != null && !notification.getOrderNumber().isEmpty()) {
                orderNumber.setText("Order #" + notification.getOrderNumber());
            } else {
                orderNumber.setText("Order ID: " + notification.getOrderId());
            }
        } else {
            if (orderInfoContainer != null) {
                orderInfoContainer.setVisibility(View.GONE);
            }
            orderInfoLayout.setVisibility(View.GONE);
            btnViewOrder.setVisibility(View.GONE);
        }

        // Update mark as read button based on read status
        if (notification.isRead()) {
            btnMarkRead.setText("Already Read");
            btnMarkRead.setEnabled(false);
        } else {
            btnMarkRead.setText("Mark as Read");
            btnMarkRead.setEnabled(true);
        }
    }

    /**
     * Set up click listeners for buttons
     */
    private void setupClickListeners() {
        // Mark as read button
        btnMarkRead.setOnClickListener(v -> markNotificationAsRead());

        // View order button
        btnViewOrder.setOnClickListener(v -> viewOrder());

        // Image click for full screen view
        notificationImage.setOnClickListener(v -> {
            String imageUrl = notification.getImageUrl();
            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                openFullScreenImage(imageUrl, notification.getTitle());
            }
        });
    }

    /**
     * Mark notification as read
     */
    private void markNotificationAsRead() {
        if (notification == null || notification.isRead()) return;

        Log.d(TAG, "Marking notification as read: " + notification.getId());

        viewModel.markAsRead(notification.getId()).observe(getViewLifecycleOwner(), count -> {
            if (count != null && count >= 0) {
                // Success
                notification.setRead(true);
                btnMarkRead.setText("Already Read");
                btnMarkRead.setEnabled(false);

                ToastUtils.showSuccessToast(requireContext(), "Notification marked as read");

                // Update badge in MainActivity
                if (getActivity() instanceof MainActivity) {
                    ((MainActivity) getActivity()).updateNotificationBadge(count);
                }
            } else {
                // Error
                ToastUtils.showErrorToast(requireContext(), "Failed to mark notification as read");
            }
        });
    }

    /**
     * View order details
     */
    private void viewOrder() {
        if (notification == null || notification.getOrderId() == null) return;

        try {
            // Create and navigate to OrderTrackingFragment
            OrderTrackingFragment trackingFragment = OrderTrackingFragment.newInstance(
                    notification.getOrderNumber(), notification.getOrderNumber());

            // Replace current fragment with OrderTrackingFragment
            if (getActivity() != null) {
                getActivity().getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_container, trackingFragment)
                        .addToBackStack(null)
                        .commit();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to order tracking: " + e.getMessage());
            Toast.makeText(getContext(), "Error viewing order: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Open full screen image viewer
     */
    private void openFullScreenImage(String imageUrl, String title) {
        try {
            // Create and navigate to FullScreenImageFragment
            FullScreenImageFragment fullScreenFragment = FullScreenImageFragment.newInstance(imageUrl, title);

            // Replace current fragment with FullScreenImageFragment
            if (getActivity() != null) {
                getActivity().getSupportFragmentManager().beginTransaction()
                        .replace(R.id.fragment_container, fullScreenFragment)
                        .addToBackStack(null)
                        .commit();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error opening full screen image: " + e.getMessage());
            Toast.makeText(getContext(), "Error opening image: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
}
