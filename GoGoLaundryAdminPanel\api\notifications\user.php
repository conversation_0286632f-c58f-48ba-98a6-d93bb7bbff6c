<?php
/**
 * API endpoint to get notifications for a user
 *
 * Required parameters:
 * - user_id: ID of the user to get notifications for
 *
 * Optional parameters:
 * - limit: Maximum number of notifications to return (default: 50)
 * - offset: Offset for pagination (default: 0)
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 * - data: Array of notification objects
 */

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/NotificationManager.php';
require_once '../../includes/UserManager.php';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => []
];

// Check if user_id is provided
if (!isset($_GET['user_id']) || empty($_GET['user_id'])) {
    $response['message'] = 'User ID is required';
    echo json_encode($response);
    exit();
}

// Get parameters
$user_id = intval($_GET['user_id']);
$limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
$offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

try {
    // Initialize managers
    $userManager = new UserManager($pdo);
    $notificationManager = new NotificationManager($pdo);

    // Validate user exists
    $user = $userManager->getUserById($user_id);
    if (!$user) {
        $response['message'] = 'User not found';
        echo json_encode($response);
        exit();
    }

    // Check if user is verified
    if (!$user['is_verified']) {
        $response['message'] = 'User account is not verified';
        echo json_encode($response);
        exit();
    }

    // Get only unread notifications for the user (is_read=0)
    $notifications = $notificationManager->getNotificationsForUser($user_id, $limit, $offset, true);

    // Process notifications for JSON response
    foreach ($notifications as &$notification) {
        // Convert is_read from 0/1 to boolean for JSON
        $notification['is_read'] = (bool)$notification['is_read'];

        // Format created_at as ISO 8601 for consistency
        $created_at = new DateTime($notification['created_at']);
        $notification['created_at'] = $created_at->format('c');
    }

    // Log the number of notifications returned
    error_log("Returning " . count($notifications) . " unread notifications for user: " . $user_id);

    // Set success response
    $response['success'] = true;
    $response['message'] = 'Notifications retrieved successfully';
    $response['data'] = $notifications;

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    // Log the error for server-side debugging
    error_log('Notification API Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
