<?php
/**
 * Upload Profile Picture API Endpoint
 *
 * This endpoint handles profile picture upload and update
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/UserManager.php';
require_once '../includes/SettingsManager.php';

// Log session information for debugging
error_log('Session ID: ' . session_id());
error_log('Session data: ' . json_encode($_SESSION));
error_log('Cookies: ' . json_encode($_COOKIE));
error_log('Headers: ' . json_encode(getallheaders()));

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Check if user is logged in via session or user_id parameter
$userId = null;

// First, check for session authentication
if (isset($_SESSION['user_id']) && isset($_SESSION['is_logged_in']) && $_SESSION['is_logged_in']) {
    $userId = $_SESSION['user_id'];
    error_log('User authenticated via session. User ID: ' . $userId);
}
// Then, check for user_id parameter (for mobile app)
else if (isset($_POST['user_id'])) {
    $userId = (int)$_POST['user_id'];

    // Validate the user exists
    $userManager = new UserManager($pdo);
    $user = $userManager->getUserById($userId);

    if (!$user) {
        error_log('Invalid user_id provided: ' . $userId);
        jsonResponse(false, 'Invalid user ID', [], 401);
    }

    error_log('User authenticated via user_id parameter. User ID: ' . $userId);
} else {
    error_log('Unauthorized access attempt. No valid authentication method. Session data: ' . json_encode($_SESSION));
    jsonResponse(false, 'Unauthorized. Please login first.', [], 401);
}

// Initialize managers (if not already initialized)
if (!isset($userManager)) {
    $userManager = new UserManager($pdo);
}
$settingsManager = new SettingsManager($pdo);

// Get settings
$maxSize = (int)$settingsManager->getSetting('profile_picture_max_size', '2097152'); // 2MB default
$allowedTypes = explode(',', $settingsManager->getSetting('profile_picture_allowed_types', 'jpg,jpeg,png'));
$uploadPath = $settingsManager->getSetting('profile_picture_path', 'uploads/profile_pictures/');

// Ensure upload directory exists
if (!file_exists('../' . $uploadPath)) {
    mkdir('../' . $uploadPath, 0755, true);
}

// Check if file was uploaded
if (!isset($_FILES['profile_picture']) || $_FILES['profile_picture']['error'] !== UPLOAD_ERR_OK) {
    $errorMessage = 'No file uploaded or upload error occurred';

    // Get more specific error message
    if (isset($_FILES['profile_picture'])) {
        switch ($_FILES['profile_picture']['error']) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errorMessage = 'File is too large';
                break;
            case UPLOAD_ERR_PARTIAL:
                $errorMessage = 'File was only partially uploaded';
                break;
            case UPLOAD_ERR_NO_FILE:
                $errorMessage = 'No file was uploaded';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $errorMessage = 'Missing temporary folder';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $errorMessage = 'Failed to write file to disk';
                break;
            case UPLOAD_ERR_EXTENSION:
                $errorMessage = 'A PHP extension stopped the file upload';
                break;
        }
    }

    jsonResponse(false, $errorMessage, [], 400);
}

// Get file information
$file = $_FILES['profile_picture'];
$fileName = $file['name'];
$fileSize = $file['size'];
$fileTmpName = $file['tmp_name'];
$fileType = $file['type'];

// Validate file size
if ($fileSize > $maxSize) {
    jsonResponse(false, 'File is too large. Maximum size is ' . ($maxSize / 1024 / 1024) . 'MB', [], 400);
}

// Get file extension
$fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

// Validate file type
if (!in_array($fileExtension, $allowedTypes)) {
    jsonResponse(false, 'Invalid file type. Allowed types: ' . implode(', ', $allowedTypes), [], 400);
}

// Generate unique filename
$newFileName = uniqid('profile_') . '.' . $fileExtension;
$uploadFilePath = '../' . $uploadPath . $newFileName;

// Move uploaded file
if (!move_uploaded_file($fileTmpName, $uploadFilePath)) {
    jsonResponse(false, 'Failed to upload file', [], 500);
}

// Get the public URL for the file
$profilePictureUrl = $uploadPath . $newFileName;

// Update user profile picture in database
$result = $userManager->updateProfilePicture($userId, $profilePictureUrl);

if (!$result) {
    // Delete the uploaded file if database update fails
    @unlink($uploadFilePath);
    jsonResponse(false, 'Failed to update profile picture', [], 500);
}

// Get updated user data
$user = $userManager->getUserById($userId);

// Remove sensitive data
unset($user['password']);

// Return success response
jsonResponse(true, 'Profile picture updated successfully', [
    'user' => $user
]);
