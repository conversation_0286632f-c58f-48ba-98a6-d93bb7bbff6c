<?php
/**
 * Admin Dashboard Home Page
 *
 * This page displays the admin dashboard with key metrics and statistics
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/UserManager.php';
require_once '../includes/SettingsManager.php';
require_once '../includes/OrderManager.php';

// Initialize managers
$userManager = new UserManager($pdo);
$settingsManager = new SettingsManager($pdo);
$orderManager = new OrderManager($pdo);

// Get user statistics
$totalUsers = $userManager->getTotalUsers();
$verifiedUsers = $userManager->getTotalVerifiedUsers();
$deletedUsers = $userManager->getTotalDeletedUsers();
$recentlyDeletedUsers = $userManager->getRecentlyDeletedUsers(5);

// Get order statistics
$totalOrders = $orderManager->getTotalOrders();
$pendingOrders = $orderManager->getOrderCountByStatus('placed');
$processingOrders = $orderManager->getOrderCountByStatus('processing');
$deliveredOrders = $orderManager->getOrderCountByStatus('delivered');
$todayOrders = $orderManager->getOrderCountByDate(date('Y-m-d'));
$yesterdayOrders = $orderManager->getOrderCountByDate(date('Y-m-d', strtotime('-1 day')));
$todayRevenue = $orderManager->getRevenue(date('Y-m-d'));
$yesterdayRevenue = $orderManager->getRevenue(date('Y-m-d', strtotime('-1 day')));
$weeklyRevenue = $orderManager->getRevenue(date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
$monthlyRevenue = $orderManager->getRevenue(date('Y-m-01'), date('Y-m-t'));

// Calculate percentage change for today vs yesterday
$revenuePercentageChange = 0;
if ($yesterdayRevenue > 0) {
    $revenuePercentageChange = (($todayRevenue - $yesterdayRevenue) / $yesterdayRevenue) * 100;
}

// Calculate percentage change for total orders vs last month
$lastMonthOrders = $orderManager->getTotalOrders(date('Y-m-d', strtotime('-1 month')), date('Y-m-d', strtotime('-1 day')));
$ordersPercentageChange = 0;
if ($lastMonthOrders > 0) {
    $ordersPercentageChange = (($totalOrders - $lastMonthOrders) / $lastMonthOrders) * 100;
}

// Get recent orders
$recentOrders = $orderManager->getRecentOrders(5);

// Get settings
$settings = $settingsManager->getAllSettings();
$otpEnabled = isset($settings['otp_enabled']) ? (bool)$settings['otp_enabled'] : true;

// We're removing the recent admin activity section as requested

// Page title and breadcrumbs
$pageTitle = 'Dashboard';
$breadcrumbs = [];

// Add scripts to page
$pageScripts = [
    '../assets/js/dashboard-charts.js',
    '../assets/js/dashboard.js'
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-sm-flex align-items-center justify-content-between">
            <h1 class="h3 mb-0 text-gray-800">Dashboard</h1>
            <div class="d-flex">
                <div class="dropdown me-2">
                    <button class="btn btn-sm btn-primary dropdown-toggle" type="button" id="dashboardActions" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cog"></i> Quick Actions
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dashboardActions">
                        <li><a class="dropdown-item" href="create_order.php"><i class="fas fa-plus-circle me-2"></i> New Order</a></li>
                        <li><a class="dropdown-item" href="users.php?action=add"><i class="fas fa-user-plus me-2"></i> Add User</a></li>
                        <li><a class="dropdown-item" href="services.php"><i class="fas fa-cogs me-2"></i> Manage Services</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="reports.php"><i class="fas fa-chart-bar me-2"></i> View Reports</a></li>
                    </ul>
                </div>
                <button class="btn btn-sm btn-outline-primary" id="refreshDashboard">
                    <i class="fas fa-sync-alt"></i> Refresh Data
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Order Stats Cards -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 rounded-3 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalOrders) ?></div>
                        <div class="text-xs text-muted mt-2">
                            <?php if ($ordersPercentageChange > 0): ?>
                                <span class="text-success"><i class="fas fa-arrow-up"></i> <?= number_format(abs($ordersPercentageChange), 1) ?>%</span> from last month
                            <?php elseif ($ordersPercentageChange < 0): ?>
                                <span class="text-danger"><i class="fas fa-arrow-down"></i> <?= number_format(abs($ordersPercentageChange), 1) ?>%</span> from last month
                            <?php else: ?>
                                <span class="text-muted">0.0%</span> change from last month
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-primary text-white">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 rounded-3 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Today's Revenue</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($todayRevenue, 2) ?> BDT</div>
                        <div class="text-xs text-muted mt-2">
                            <?php if ($revenuePercentageChange > 0): ?>
                                <span class="text-success"><i class="fas fa-arrow-up"></i> <?= number_format(abs($revenuePercentageChange), 1) ?>%</span> from yesterday
                            <?php elseif ($revenuePercentageChange < 0): ?>
                                <span class="text-danger"><i class="fas fa-arrow-down"></i> <?= number_format(abs($revenuePercentageChange), 1) ?>%</span> from yesterday
                            <?php else: ?>
                                <span class="text-muted">0.0%</span> change from yesterday
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-success text-white">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2 rounded-3 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Pending Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($pendingOrders) ?></div>
                        <div class="text-xs text-muted mt-2">
                            <a href="orders.php?status=placed" class="text-info">View all pending orders</a>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-info text-white">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 rounded-3 hover-shadow">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Processing Orders</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($processingOrders) ?></div>
                        <div class="text-xs text-muted mt-2">
                            <a href="orders.php?status=processing" class="text-warning">View all processing orders</a>
                        </div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle bg-warning text-white">
                            <i class="fas fa-spinner"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Revenue Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4 rounded-3 hover-shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-gradient-light">
                <h6 class="m-0 font-weight-bold text-primary">Revenue Overview</h6>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-primary active" data-period="week">7 Days</button>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-period="month">30 Days</button>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-period="year">This Year</button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="revenueChart"></canvas>
                    <div id="revenueChartError" class="text-danger text-center mt-3"></div>
                </div>
                <div class="text-center mt-3">
                    <div class="d-flex justify-content-around">
                        <div class="text-center">
                            <h6 class="font-weight-bold">Weekly Revenue</h6>
                            <h4 class="text-primary"><?= number_format($weeklyRevenue, 2) ?> BDT</h4>
                        </div>
                        <div class="text-center">
                            <h6 class="font-weight-bold">Monthly Revenue</h6>
                            <h4 class="text-success"><?= number_format($monthlyRevenue, 2) ?> BDT</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Status Pie Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4 rounded-3 hover-shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-gradient-light">
                <h6 class="m-0 font-weight-bold text-primary">Order Status Distribution</h6>
                <button class="btn btn-sm btn-outline-primary" id="refreshCharts">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2" style="position: relative; height: 240px;">
                    <canvas id="orderStatusChart"></canvas>
                    <div id="orderStatusChartError" class="text-danger text-center mt-3"></div>
                </div>
                <div class="mt-4 text-center small" id="orderStatusLegend">
                    <!-- Legend will be dynamically populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Recent Orders -->
    <div class="col-lg-7 mb-4">
        <div class="card shadow mb-4 rounded-3 hover-shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center bg-gradient-light">
                <h6 class="m-0 font-weight-bold text-primary">Recent Orders</h6>
                <div>
                    <a href="create_order.php" class="btn btn-sm btn-success me-2">
                        <i class="fas fa-plus"></i> New Order
                    </a>
                    <a href="orders.php" class="btn btn-sm btn-primary">
                        <i class="fas fa-list"></i> View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($recentOrders)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="lead">No recent orders found</p>
                        <a href="create_order.php" class="btn btn-primary">Create New Order</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover border-bottom" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>Order #</th>
                                    <th>Customer</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentOrders as $order): ?>
                                    <tr>
                                        <td><a href="order_details.php?id=<?= $order['id'] ?>" class="fw-bold text-primary"><?= $order['order_number'] ?></a></td>
                                        <td><?= $order['customer_name'] ?></td>
                                        <td class="fw-bold"><?= number_format($order['total'], 2) ?> BDT</td>
                                        <td>
                                            <span class="badge bg-<?= getStatusBadgeClass($order['status']) ?> rounded-pill">
                                                <?= formatStatus($order['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= date('M d, H:i', strtotime($order['created_at'])) ?></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-icon" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="dropdown-menu dropdown-menu-end">
                                                    <a class="dropdown-item" href="order_details.php?id=<?= $order['id'] ?>">
                                                        <i class="fas fa-eye me-2 text-primary"></i> View Details
                                                    </a>
                                                    <a class="dropdown-item" href="update_order.php?id=<?= $order['id'] ?>">
                                                        <i class="fas fa-edit me-2 text-info"></i> Update Status
                                                    </a>
                                                    <a class="dropdown-item" href="print_invoice.php?id=<?= $order['id'] ?>">
                                                        <i class="fas fa-print me-2 text-success"></i> Print Invoice
                                                    </a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- User Stats Cards -->
    <div class="col-lg-5 mb-4">
        <div class="card shadow mb-4 rounded-3 hover-shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center bg-gradient-light">
                <h6 class="m-0 font-weight-bold text-primary">User Statistics</h6>
                <a href="users.php" class="btn btn-sm btn-primary">
                    <i class="fas fa-users"></i> Manage Users
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-xl-6 col-md-6 mb-4">
                        <div class="card stats-card border-0 rounded-3 shadow-sm" style="border-left: 4px solid #0d6efd !important;">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <div class="stats-number"><?php echo $totalUsers; ?></div>
                                        <div class="stats-text">Total Users</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="icon-circle bg-primary text-white">
                                            <i class="fas fa-users"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-6 col-md-6 mb-4">
                        <div class="card stats-card border-0 rounded-3 shadow-sm" style="border-left: 4px solid #198754 !important;">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <div class="stats-number"><?php echo $verifiedUsers; ?></div>
                                        <div class="stats-text">Verified Users</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="icon-circle bg-success text-white">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-6 col-md-6 mb-4">
                        <div class="card stats-card border-0 rounded-3 shadow-sm" style="border-left: 4px solid #dc3545 !important;">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <div class="stats-number"><?php echo $deletedUsers; ?></div>
                                        <div class="stats-text">Deleted Users</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="icon-circle bg-danger text-white">
                                            <i class="fas fa-user-times"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-6 col-md-6 mb-4">
                        <div class="card stats-card border-0 rounded-3 shadow-sm" style="border-left: 4px solid #6c757d !important;">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <div class="stats-number"><?php echo $otpEnabled ? 'Enabled' : 'Disabled'; ?></div>
                                        <div class="stats-text">OTP Verification</div>
                                    </div>
                                    <div class="col-auto">
                                        <div class="icon-circle bg-secondary text-white">
                                            <i class="fas fa-<?php echo $otpEnabled ? 'lock' : 'lock-open'; ?>"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">System Status</h6>
                <div>
                    <button class="btn btn-sm btn-outline-primary refresh-status-btn">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="mb-4">
                            <h4 class="small font-weight-bold">OTP Verification <span class="float-end"><?php echo $otpEnabled ? 'Enabled' : 'Disabled'; ?></span></h4>
                            <div class="progress mb-4">
                                <div class="progress-bar <?php echo $otpEnabled ? 'bg-success' : 'bg-danger'; ?>" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4 class="small font-weight-bold">Pending Orders <span class="float-end"><?= $pendingOrders ?></span></h4>
                            <div class="progress mb-4">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: <?= min(100, ($pendingOrders / max(1, $totalOrders)) * 100) ?>%" aria-valuenow="<?= min(100, ($pendingOrders / max(1, $totalOrders)) * 100) ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="mb-4">
                            <h4 class="small font-weight-bold">Processing Orders <span class="float-end"><?= $processingOrders ?></span></h4>
                            <div class="progress mb-4">
                                <div class="progress-bar bg-info" role="progressbar" style="width: <?= min(100, ($processingOrders / max(1, $totalOrders)) * 100) ?>%" aria-valuenow="<?= min(100, ($processingOrders / max(1, $totalOrders)) * 100) ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4 class="small font-weight-bold">Delivered Orders <span class="float-end"><?= $deliveredOrders ?></span></h4>
                            <div class="progress mb-4">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?= min(100, ($deliveredOrders / max(1, $totalOrders)) * 100) ?>%" aria-valuenow="<?= min(100, ($deliveredOrders / max(1, $totalOrders)) * 100) ?>" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h4 class="small font-weight-bold">User Verification Rate <span class="float-end"><?= number_format(($verifiedUsers / max(1, $totalUsers)) * 100, 1) ?>%</span></h4>
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: <?= min(100, ($verifiedUsers / max(1, $totalUsers)) * 100) ?>%" aria-valuenow="<?= min(100, ($verifiedUsers / max(1, $totalUsers)) * 100) ?>" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
/**
 * Get status badge class
 *
 * @param string $status Order status
 * @return string Badge class
 */
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'placed': return 'secondary';
        case 'confirmed': return 'info';
        case 'pickup_scheduled': return 'primary';
        case 'picked_up': return 'warning';
        case 'processing': return 'warning';
        case 'ready_for_delivery': return 'info';
        case 'out_for_delivery': return 'primary';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

/**
 * Format status
 *
 * @param string $status Order status
 * @return string Formatted status
 */
function formatStatus($status) {
    return ucwords(str_replace('_', ' ', $status));
}
?>

<?php include 'includes/footer.php'; ?>
