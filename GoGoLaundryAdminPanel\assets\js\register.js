/**
 * Registration Page JavaScript
 *
 * This file handles the registration page functionality
 */

$(document).ready(function() {
    // Load divisions for dropdown
    loadDivisions();

    // Step 1: Basic Information
    $('#step1NextBtn').on('click', function() {
        // Get form data
        const fullName = $('#fullName').val().trim();
        const phone = $('#regPhone').val().trim();
        const email = $('#email').val().trim();

        // Validate full name
        if (fullName.length < 2) {
            showError('#registrationError', 'Please enter your full name');
            return;
        }

        // Validate phone number
        if (!validatePhone(phone)) {
            showError('#registrationError', 'Please enter a valid phone number');
            return;
        }

        // Validate email if provided
        if (email && !validateEmail(email)) {
            showError('#registrationError', 'Please enter a valid email address');
            return;
        }

        // Hide error message
        hideMessages();

        // Move to step 2
        $('.registration-step').addClass('d-none');
        $('#step2').removeClass('d-none');
    });

    // Step 2: Back Button
    $('#step2BackBtn').on('click', function() {
        // Move back to step 1
        $('.registration-step').addClass('d-none');
        $('#step1').removeClass('d-none');
    });

    // Send Registration OTP Button Click
    $('#sendRegOtpBtn').on('click', function() {
        const phone = $('#regPhone').val().trim();
        const email = $('#email').val().trim();

        // Validate phone number
        if (!validatePhone(phone)) {
            showError('#registrationError', 'Please enter a valid phone number');
            return;
        }

        // Disable button and show loading state
        const sendOtpBtn = $(this);
        sendOtpBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...');

        // Hide previous messages
        hideMessages();

        // Send OTP request
        $.ajax({
            url: 'api/send_otp.php',
            type: 'POST',
            dataType: 'json',
            data: {
                phone: phone,
                email: email,
                purpose: 'registration'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showSuccess('#registrationSuccess', response.message);

                    // Show OTP input section
                    $('.otp-section').removeClass('d-none');

                    // Start OTP timer
                    startOtpTimer('#regOtpTimer', response.data.expires_in || 600, function() {
                        // Timer expired callback
                        sendOtpBtn.prop('disabled', false).text('Resend OTP');
                    });

                    // Update button text
                    sendOtpBtn.prop('disabled', true).text('OTP Sent');
                } else {
                    // Show error message
                    showError('#registrationError', response.message);
                    sendOtpBtn.prop('disabled', false).text('Send OTP');
                }
            },
            error: function(xhr, status, error) {
                // Parse error response if possible
                try {
                    const response = JSON.parse(xhr.responseText);
                    showError('#registrationError', response.message || 'An error occurred. Please try again.');
                } catch (e) {
                    showError('#registrationError', 'An error occurred. Please try again.');
                }

                sendOtpBtn.prop('disabled', false).text('Send OTP');
            }
        });
    });

    // Step 2: Next Button (Verify OTP)
    $('#step2NextBtn').on('click', function() {
        const phone = $('#regPhone').val().trim();
        const otp = $('#regOtp').val().trim();

        // Validate OTP
        if (otp.length !== 6 || !/^\d+$/.test(otp)) {
            showError('#registrationError', 'Please enter a valid 6-digit OTP');
            return;
        }

        // Disable button and show loading state
        const nextBtn = $(this);
        nextBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...');

        // Hide previous messages
        hideMessages();

        // Send verify OTP request
        $.ajax({
            url: 'api/verify_otp.php',
            type: 'POST',
            dataType: 'json',
            data: {
                phone: phone,
                otp: otp,
                purpose: 'registration'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showSuccess('#registrationSuccess', response.message);

                    // Move to step 3
                    $('.registration-step').addClass('d-none');
                    $('#step3').removeClass('d-none');
                } else {
                    // Show error message
                    showError('#registrationError', response.message);
                    nextBtn.prop('disabled', false).text('Next');
                }
            },
            error: function(xhr, status, error) {
                // Parse error response if possible
                try {
                    const response = JSON.parse(xhr.responseText);
                    showError('#registrationError', response.message || 'An error occurred. Please try again.');
                } catch (e) {
                    showError('#registrationError', 'An error occurred. Please try again.');
                }

                nextBtn.prop('disabled', false).text('Next');
            }
        });
    });

    // Step 3: Back Button
    $('#step3BackBtn').on('click', function() {
        // Move back to step 2
        $('.registration-step').addClass('d-none');
        $('#step2').removeClass('d-none');
    });

    // Registration Form Submission
    $('#registrationForm').on('submit', function(e) {
        e.preventDefault();

        // Get form data
        const fullName = $('#fullName').val().trim();
        const phone = $('#regPhone').val().trim();
        const email = $('#email').val().trim();
        const password = $('#regPassword').val();
        const confirmPassword = $('#confirmPassword').val();
        const otp = $('#regOtp').val().trim();

        // Validate password
        if (password.length < 8) {
            showError('#registrationError', 'Password must be at least 8 characters long');
            return;
        }

        // Validate password confirmation
        if (password !== confirmPassword) {
            showError('#registrationError', 'Passwords do not match');
            return;
        }

        // Disable button and show loading state
        const registerBtn = $('#registerBtn');
        registerBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Registering...');

        // Hide previous messages
        hideMessages();

        // Get location data
        const address = $('#address').val().trim();
        const divisionId = $('#division').val() ? parseInt($('#division').val()) : null;
        const districtId = $('#district').val() ? parseInt($('#district').val()) : null;
        const upazillaId = $('#upazilla').val() ? parseInt($('#upazilla').val()) : null;

        // Send registration request
        $.ajax({
            url: 'api/register.php',
            type: 'POST',
            dataType: 'json',
            data: {
                full_name: fullName,
                phone: phone,
                email: email,
                password: password,
                otp: otp,
                address: address,
                division_id: divisionId,
                district_id: districtId,
                upazilla_id: upazillaId
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showSuccess('#registrationSuccess', response.message);

                    // Redirect to dashboard
                    setTimeout(function() {
                        window.location.href = 'dashboard.php';
                    }, 1000);
                } else {
                    // Show error message
                    showError('#registrationError', response.message);
                    registerBtn.prop('disabled', false).text('Register');
                }
            },
            error: function(xhr, status, error) {
                // Parse error response if possible
                try {
                    const response = JSON.parse(xhr.responseText);
                    showError('#registrationError', response.message || 'An error occurred. Please try again.');
                } catch (e) {
                    showError('#registrationError', 'An error occurred. Please try again.');
                }

                registerBtn.prop('disabled', false).text('Register');
            }
        });
    });

    // Helper Functions

    // Validate phone number
    function validatePhone(phone) {
        // Bangladesh phone number format: 01XXXXXXXXX
        const phoneRegex = /^(?:\+?880|0)1[3-9]\d{8}$/;
        return phoneRegex.test(phone);
    }

    // Validate email
    function validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Show error message
    function showError(selector, message) {
        $(selector).removeClass('d-none').text(message);
    }

    // Show success message
    function showSuccess(selector, message) {
        $(selector).removeClass('d-none').text(message);
    }

    // Hide all messages
    function hideMessages() {
        $('#registrationError, #registrationSuccess').addClass('d-none');
    }

    // Start OTP timer
    function startOtpTimer(selector, duration, callback) {
        let timer = duration;
        const timerElement = $(selector);

        const interval = setInterval(function() {
            const minutes = Math.floor(timer / 60);
            const seconds = timer % 60;

            timerElement.text(minutes + ':' + (seconds < 10 ? '0' : '') + seconds);

            if (--timer < 0) {
                clearInterval(interval);
                if (typeof callback === 'function') {
                    callback();
                }
            }
        }, 1000);
    }

    // Load divisions for dropdown
    function loadDivisions() {
        $.ajax({
            url: 'api/get_divisions.php',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success && response.data) {
                    const divisions = response.data;
                    let options = '<option value="">Select Division</option>';

                    divisions.forEach(function(division) {
                        options += `<option value="${division.id}">${division.name}</option>`;
                    });

                    $('#division').html(options);

                    // Setup change event for division dropdown
                    $('#division').on('change', function() {
                        const divisionId = $(this).val();
                        if (divisionId) {
                            loadDistricts(divisionId);
                            $('#district').prop('disabled', false);
                        } else {
                            $('#district').html('<option value="">Select District</option>').prop('disabled', true);
                            $('#upazilla').html('<option value="">Select Upazilla</option>').prop('disabled', true);
                        }
                    });
                }
            },
            error: function() {
                console.error('Failed to load divisions');
            }
        });
    }

    // Load districts for selected division
    function loadDistricts(divisionId) {
        $.ajax({
            url: 'api/get_districts.php',
            type: 'GET',
            dataType: 'json',
            data: {
                division_id: divisionId
            },
            success: function(response) {
                if (response.success && response.data) {
                    const districts = response.data;
                    let options = '<option value="">Select District</option>';

                    districts.forEach(function(district) {
                        options += `<option value="${district.id}">${district.name}</option>`;
                    });

                    $('#district').html(options);

                    // Setup change event for district dropdown
                    $('#district').on('change', function() {
                        const districtId = $(this).val();
                        if (districtId) {
                            loadUpazillas(districtId);
                            $('#upazilla').prop('disabled', false);
                        } else {
                            $('#upazilla').html('<option value="">Select Upazilla</option>').prop('disabled', true);
                        }
                    });
                }
            },
            error: function() {
                console.error('Failed to load districts');
            }
        });
    }

    // Load upazillas for selected district
    function loadUpazillas(districtId) {
        $.ajax({
            url: 'api/get_upazillas.php',
            type: 'GET',
            dataType: 'json',
            data: {
                district_id: districtId
            },
            success: function(response) {
                if (response.success && response.data) {
                    const upazillas = response.data;
                    let options = '<option value="">Select Upazilla</option>';

                    upazillas.forEach(function(upazilla) {
                        options += `<option value="${upazilla.id}">${upazilla.name}</option>`;
                    });

                    $('#upazilla').html(options);
                }
            },
            error: function() {
                console.error('Failed to load upazillas');
            }
        });
    }
});
