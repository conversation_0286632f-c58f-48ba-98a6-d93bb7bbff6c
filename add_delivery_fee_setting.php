<?php
/**
 * Add Delivery Fee Setting
 * 
 * This script adds the delivery_fee setting to the settings table
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'GoGoLaundryAdminPanel/config/db.php';

echo "<h2>Add Delivery Fee Setting</h2>";

try {
    // Check if settings table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
    $settingsTableExists = $stmt->rowCount() > 0;

    if (!$settingsTableExists) {
        echo "Settings table does not exist. Please run the database import script first.<br>";
        exit;
    }

    // Check if delivery_fee setting already exists
    $stmt = $pdo->prepare("SELECT * FROM settings WHERE setting_key = ?");
    $stmt->execute(['delivery_fee']);
    $existingSetting = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existingSetting) {
        echo "Delivery fee setting already exists with value: " . $existingSetting['setting_value'] . "<br>";
        echo "Updating to default value of 50.00 BDT...<br>";
    } else {
        echo "Adding new delivery fee setting...<br>";
    }

    // Add or update the delivery_fee setting
    $stmt = $pdo->prepare("
        INSERT INTO settings (setting_key, setting_value, description) 
        VALUES (?, ?, ?) 
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), description = VALUES(description)
    ");
    $stmt->execute(['delivery_fee', '50.00', 'Default delivery fee in BDT']);

    echo "Delivery fee setting added/updated successfully.<br>";
    echo "<a href='GoGoLaundryAdminPanel/admin/settings.php'>Go to Settings Page</a>";

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
?>
