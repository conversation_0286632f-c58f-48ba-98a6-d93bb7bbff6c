package com.mdsadrulhasan.gogolaundry.api;

import android.content.Context;
import android.util.Log;


import com.mdsadrulhasan.gogolaundry.utils.SessionManager;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Interceptor to handle cookies for session management
 */
public class CookieInterceptor implements Interceptor {
    private static final String TAG = "CookieInterceptor";
    private SessionManager sessionManager;

    public CookieInterceptor(Context context) {
        this.sessionManager = new SessionManager(context);
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request originalRequest = chain.request();
        Request.Builder builder = originalRequest.newBuilder();

        // Log the request URL for debugging
        Log.d(TAG, "Intercepting request to: " + originalRequest.url().toString());

        // Add stored cookies to request
        String cookies = sessionManager.getCookies();
        if (cookies != null && !cookies.isEmpty()) {
            Log.d(TAG, "Adding cookies to request: " + cookies);
            builder.addHeader("Cookie", cookies);
        } else {
            Log.d(TAG, "No cookies available to add to request");
        }

        // Add user-agent header to identify the app
        builder.addHeader("User-Agent", "OtpManagementApp-Android");

        // Build the modified request
        Request modifiedRequest = builder.build();

        // Log all headers for debugging
        Log.d(TAG, "Request headers:");
        for (String name : modifiedRequest.headers().names()) {
            Log.d(TAG, name + ": " + modifiedRequest.header(name));
        }

        // Execute the request
        Response response = chain.proceed(modifiedRequest);

        // Log response code and URL
        Log.d(TAG, "Response: " + response.code() + " for " + response.request().url());

        // Extract cookies from response
        if (response.headers("Set-Cookie").size() > 0) {
            Set<String> cookiesSet = new HashSet<>();

            // Get existing cookies
            if (cookies != null && !cookies.isEmpty()) {
                String[] existingCookies = cookies.split(";");
                for (String cookie : existingCookies) {
                    cookiesSet.add(cookie.trim());
                }
            }

            // Add new cookies
            List<String> newCookies = response.headers("Set-Cookie");
            Log.d(TAG, "Received " + newCookies.size() + " cookies from server");

            for (String cookie : newCookies) {
                Log.d(TAG, "Full cookie: " + cookie);
                // Extract just the name=value part (before the first ;)
                String simpleCookie = cookie.split(";")[0];
                Log.d(TAG, "Extracted cookie: " + simpleCookie);
                cookiesSet.add(simpleCookie);
            }

            // Build cookie string
            StringBuilder cookieBuilder = new StringBuilder();
            for (String cookie : cookiesSet) {
                if (cookieBuilder.length() > 0) {
                    cookieBuilder.append("; ");
                }
                cookieBuilder.append(cookie);
            }

            // Save cookies
            String finalCookies = cookieBuilder.toString();
            sessionManager.saveCookies(finalCookies);
            Log.d(TAG, "Saved cookies: " + finalCookies);
        } else {
            Log.d(TAG, "No cookies in response");
        }

        return response;
    }
}
