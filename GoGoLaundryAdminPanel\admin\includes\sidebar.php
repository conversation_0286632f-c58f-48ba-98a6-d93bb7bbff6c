<div class="sidebar">
    <div class="sidebar-header">
        <h3><?php echo APP_NAME; ?></h3>
        <div class="sidebar-subtitle">Admin Panel</div>
        <!-- Mobile close button -->
        <button type="button" class="btn-close btn-close-white d-md-none position-absolute top-0 end-0 mt-2 me-2" id="closeSidebarMobile" aria-label="Close"></button>
    </div>

    <ul class="list-unstyled components" id="sidebarMenu">
        <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : ''; ?>">
            <a href="index.php">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </li>

        <!-- Orders Management -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['orders.php', 'order_details.php', 'create_order.php']) ? 'active' : ''; ?>">
            <a href="#ordersSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-shopping-cart"></i> Orders
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['orders.php', 'order_details.php', 'create_order.php']) ? 'show' : ''; ?>" id="ordersSubmenu">
                <li>
                    <a href="orders.php">
                        <i class="fas fa-list"></i> All Orders
                    </a>
                </li>
                <li>
                    <a href="orders.php?status=placed">
                        <i class="fas fa-clock"></i> New Orders
                    </a>
                </li>
                <li>
                    <a href="orders.php?status=processing">
                        <i class="fas fa-spinner"></i> Processing
                    </a>
                </li>
                <li>
                    <a href="orders.php?status=out_for_delivery">
                        <i class="fas fa-truck"></i> Out for Delivery
                    </a>
                </li>
                <li>
                    <a href="create_order.php">
                        <i class="fas fa-plus-circle"></i> Create Order
                    </a>
                </li>
            </ul>
        </li>

        <!-- Services & Items Management -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['services.php', 'items.php', 'add_service.php', 'edit_service.php', 'add_item.php', 'edit_item.php']) ? 'active' : ''; ?>">
            <a href="#servicesSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-tags"></i> Services & Items
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['services.php', 'items.php', 'add_service.php', 'edit_service.php', 'add_item.php', 'edit_item.php']) ? 'show' : ''; ?>" id="servicesSubmenu">
                <li>
                    <a href="services.php">
                        <i class="fas fa-concierge-bell"></i> Services
                    </a>
                </li>
                <li>
                    <a href="items.php">
                        <i class="fas fa-tshirt"></i> Items
                    </a>
                </li>
            </ul>
        </li>

        <!-- Shops Management -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['shops.php', 'shop_details.php', 'edit_shop.php', 'add_shop.php']) ? 'active' : ''; ?>">
            <a href="shops.php">
                <i class="fas fa-store"></i> Nearest Shops
            </a>
        </li>

        <!-- Delivery Personnel -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['delivery_personnel.php', 'add_delivery_personnel.php', 'edit_delivery_personnel.php']) ? 'active' : ''; ?>">
            <a href="delivery_personnel.php">
                <i class="fas fa-people-carry"></i> Delivery Personnel
            </a>
        </li>

        <!-- Promotions -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['promo_codes.php', 'add_promo.php', 'edit_promo.php']) ? 'active' : ''; ?>">
            <a href="promo_codes.php">
                <i class="fas fa-percent"></i> Promo Codes
            </a>
        </li>

        <!-- Reports & Analytics -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['reports.php', 'sales_report.php', 'service_report.php', 'customer_report.php']) ? 'active' : ''; ?>">
            <a href="#reportsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-chart-bar"></i> Reports
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['reports.php', 'sales_report.php', 'service_report.php', 'customer_report.php']) ? 'show' : ''; ?>" id="reportsSubmenu">
                <li>
                    <a href="sales_report.php">
                        <i class="fas fa-chart-line"></i> Sales Report
                    </a>
                </li>
                <li>
                    <a href="service_report.php">
                        <i class="fas fa-chart-pie"></i> Service Analytics
                    </a>
                </li>
                <li>
                    <a href="customer_report.php">
                        <i class="fas fa-user-chart"></i> Customer Analytics
                    </a>
                </li>
            </ul>
        </li>

        <!-- Notifications -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['notifications.php', 'send_notification.php']) ? 'active' : ''; ?>">
            <a href="notifications.php">
                <i class="fas fa-bell"></i> Notifications
            </a>
        </li>

        <!-- Users Management -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['users.php', 'user_details.php', 'reset_password.php']) ? 'active' : ''; ?>">
            <a href="#usersSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-users"></i> Users
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['users.php', 'user_details.php', 'reset_password.php']) ? 'show' : ''; ?>" id="usersSubmenu">
                <li>
                    <a href="users.php">
                        <i class="fas fa-user-friends"></i> Manage Users
                    </a>
                </li>
                <li>
                    <a href="reset_password.php">
                        <i class="fas fa-key"></i> Reset Passwords
                    </a>
                </li>
            </ul>
        </li>

        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['deleted_accounts.php', 'restore_account.php']) ? 'active' : ''; ?>">
            <a href="deleted_accounts.php">
                <i class="fas fa-trash-restore"></i> Deleted Accounts
            </a>
        </li>

        <li class="<?php echo basename($_SERVER['PHP_SELF']) === 'activity_logs.php' ? 'active' : ''; ?>">
            <a href="activity_logs.php">
                <i class="fas fa-clipboard-list"></i> Activity Logs
            </a>
        </li>

        <!-- Settings -->
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['settings.php', 'settings_history.php']) ? 'active' : ''; ?>">
            <a href="#settingsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-cogs"></i> Settings
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['settings.php', 'settings_history.php']) ? 'show' : ''; ?>" id="settingsSubmenu">
                <li>
                    <a href="settings.php">
                        <i class="fas fa-sliders-h"></i> Manage Settings
                    </a>
                </li>
                <li>
                    <a href="settings_history.php">
                        <i class="fas fa-history"></i> Settings History
                    </a>
                </li>
            </ul>
        </li>

        <?php if ($adminData['role'] === 'super_admin'): ?>
        <li class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['admins.php', 'add_admin.php', 'edit_admin.php']) ? 'active' : ''; ?>">
            <a href="#adminsSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                <i class="fas fa-user-shield"></i> Administrators
            </a>
            <ul class="collapse list-unstyled <?php echo in_array(basename($_SERVER['PHP_SELF']), ['admins.php', 'add_admin.php', 'edit_admin.php']) ? 'show' : ''; ?>" id="adminsSubmenu">
                <li>
                    <a href="admins.php">
                        <i class="fas fa-users-cog"></i> Manage Admins
                    </a>
                </li>
                <li>
                    <a href="add_admin.php">
                        <i class="fas fa-user-plus"></i> Add Admin
                    </a>
                </li>
            </ul>
        </li>
        <?php endif; ?>
    </ul>

    <div class="sidebar-footer">
        <a href="logout.php" class="btn btn-danger btn-sm">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
</div>
