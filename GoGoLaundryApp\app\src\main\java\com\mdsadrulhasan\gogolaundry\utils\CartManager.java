package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mdsadrulhasan.gogolaundry.model.CartItem;
import com.mdsadrulhasan.gogolaundry.model.Item;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * Manager class for shopping cart operations
 */
public class CartManager {
    private static final String TAG = "CartManager";
    private static final String PREF_NAME = "cart_preferences";
    private static final String CART_ITEMS_KEY = "cart_items";
    
    private static CartManager instance;
    private final SharedPreferences preferences;
    private final Gson gson;
    private List<CartItem> cartItems;
    
    /**
     * Private constructor
     * 
     * @param context Application context
     */
    private CartManager(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        gson = new Gson();
        loadCartItems();
    }
    
    /**
     * Get singleton instance
     * 
     * @param context Application context
     * @return CartManager instance
     */
    public static synchronized CartManager getInstance(Context context) {
        if (instance == null) {
            instance = new CartManager(context.getApplicationContext());
        }
        return instance;
    }
    
    /**
     * Load cart items from SharedPreferences
     */
    private void loadCartItems() {
        String cartJson = preferences.getString(CART_ITEMS_KEY, null);
        if (cartJson != null) {
            try {
                Type type = new TypeToken<List<CartItem>>() {}.getType();
                cartItems = gson.fromJson(cartJson, type);
                Log.d(TAG, "Loaded " + cartItems.size() + " items from cart");
            } catch (Exception e) {
                Log.e(TAG, "Error loading cart items: " + e.getMessage());
                cartItems = new ArrayList<>();
            }
        } else {
            cartItems = new ArrayList<>();
        }
    }
    
    /**
     * Save cart items to SharedPreferences
     */
    private void saveCartItems() {
        String cartJson = gson.toJson(cartItems);
        preferences.edit().putString(CART_ITEMS_KEY, cartJson).apply();
        Log.d(TAG, "Saved " + cartItems.size() + " items to cart");
    }
    
    /**
     * Add item to cart
     * 
     * @param item Item to add
     * @param quantity Quantity
     * @return True if added successfully
     */
    public boolean addToCart(Item item, double quantity) {
        if (item == null || quantity <= 0) {
            return false;
        }
        
        // Check if item already exists in cart
        for (CartItem cartItem : cartItems) {
            if (cartItem.getItemId() == item.getId()) {
                // Update quantity
                cartItem.setQuantity(cartItem.getQuantity() + quantity);
                cartItem.updateSubtotal();
                saveCartItems();
                return true;
            }
        }
        
        // Add new item to cart
        CartItem newItem = new CartItem(
                item.getId(),
                item.getServiceId(),
                item.getName(),
                item.getImageUrl(),
                item.getPrice(),
                quantity,
                item.getServiceName()
        );
        cartItems.add(newItem);
        saveCartItems();
        return true;
    }
    
    /**
     * Update item quantity in cart
     * 
     * @param itemId Item ID
     * @param quantity New quantity
     * @return True if updated successfully
     */
    public boolean updateQuantity(int itemId, double quantity) {
        if (quantity <= 0) {
            return removeFromCart(itemId);
        }
        
        for (CartItem cartItem : cartItems) {
            if (cartItem.getItemId() == itemId) {
                cartItem.setQuantity(quantity);
                cartItem.updateSubtotal();
                saveCartItems();
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Remove item from cart
     * 
     * @param itemId Item ID
     * @return True if removed successfully
     */
    public boolean removeFromCart(int itemId) {
        for (int i = 0; i < cartItems.size(); i++) {
            if (cartItems.get(i).getItemId() == itemId) {
                cartItems.remove(i);
                saveCartItems();
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Clear cart
     */
    public void clearCart() {
        cartItems.clear();
        saveCartItems();
    }
    
    /**
     * Get all cart items
     * 
     * @return List of cart items
     */
    public List<CartItem> getCartItems() {
        return new ArrayList<>(cartItems);
    }
    
    /**
     * Get cart item count
     * 
     * @return Number of items in cart
     */
    public int getItemCount() {
        return cartItems.size();
    }
    
    /**
     * Get cart total
     * 
     * @return Total price of all items in cart
     */
    public double getCartTotal() {
        double total = 0;
        for (CartItem item : cartItems) {
            total += item.getSubtotal();
        }
        return total;
    }
}
