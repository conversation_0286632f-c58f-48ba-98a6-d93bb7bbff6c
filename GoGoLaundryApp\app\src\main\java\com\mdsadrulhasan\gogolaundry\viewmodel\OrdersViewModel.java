package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.repository.OrderRepository;
import com.mdsadrulhasan.gogolaundry.utils.AppExecutors;
import com.mdsadrulhasan.gogolaundry.utils.Resource;

import java.util.ArrayList;
import java.util.List;

import com.mdsadrulhasan.gogolaundry.utils.SessionManager;

/**
 * ViewModel for orders screen
 */
public class OrdersViewModel extends AndroidViewModel {

    private static final String TAG = "OrdersViewModel";

    private final OrderRepository orderRepository;
    private final SessionManager sessionManager;
    private final MutableLiveData<Boolean> forceRefresh = new MutableLiveData<>(false);
    private LiveData<Resource<List<OrderEntity>>> ordersData;

    public OrdersViewModel(@NonNull Application application) {
        super(application);
        orderRepository = OrderRepository.getInstance();
        sessionManager = new SessionManager(application);

        // Get user ID from session
        int userId = sessionManager.getUser().getId();

        // Initialize orders data
        ordersData = Transformations.switchMap(forceRefresh, refresh ->
            orderRepository.getOrdersByUserId(userId, refresh));
    }

    /**
     * Get orders LiveData
     *
     * @return LiveData of orders resource
     */
    public LiveData<Resource<List<OrderEntity>>> getOrders() {
        return ordersData;
    }

    /**
     * Get orders by user ID
     *
     * @param userId User ID
     * @param forceRefresh Whether to force refresh from network
     * @return LiveData of orders resource
     */
    public LiveData<Resource<List<OrderEntity>>> getOrdersByUserId(int userId, boolean forceRefresh) {
        return orderRepository.getOrdersByUserId(userId, forceRefresh);
    }

    /**
     * Get order details LiveData
     *
     * @param orderId Order ID
     * @return LiveData of order resource
     */
    public LiveData<Resource<OrderEntity>> getOrderDetails(int orderId) {
        return orderRepository.getOrderById(orderId, true);
    }

    /**
     * Get order by ID
     *
     * @param orderId Order ID
     * @return LiveData of order resource
     */
    public LiveData<Resource<OrderEntity>> getOrderById(int orderId) {
        return orderRepository.getOrderById(orderId, false);
    }

    /**
     * Get order items by order ID
     *
     * @param orderId Order ID
     * @return LiveData of order items resource
     */
    public LiveData<Resource<List<OrderItemEntity>>> getOrderItemsByOrderId(int orderId) {
        MutableLiveData<Resource<List<OrderItemEntity>>> result = new MutableLiveData<>();

        // Load order items from repository
        LiveData<List<OrderItemEntity>> orderItemsLiveData = orderRepository.getOrderItemsByOrderId(orderId);

        // Observe order items
        orderItemsLiveData.observeForever(orderItems -> {
            if (orderItems != null && !orderItems.isEmpty()) {
                result.setValue(Resource.success(orderItems));
            } else {
                result.setValue(Resource.error("No order items found", null));
            }
        });

        return result;
    }

    /**
     * Get order by tracking number
     *
     * @param trackingNumber Order tracking number
     * @return LiveData of order
     */
    public LiveData<Order> getOrderByTrackingNumber(String trackingNumber) {
        MutableLiveData<Order> result = new MutableLiveData<>();

        // Execute on background thread
        new Thread(() -> {
            // Get order from repository
            OrderEntity entity = orderRepository.getOrderByTrackingNumber(trackingNumber);

            // If not found by tracking number, try order number
            if (entity == null) {
                entity = orderRepository.getOrderByOrderNumber(trackingNumber);
            }

            // Convert to model if found
            if (entity != null) {
                Order order = convertToOrderModel(entity);
                result.postValue(order);
            } else {
                result.postValue(null);
            }
        }).start();

        return result;
    }

    /**
     * Refresh orders data
     */
    public void refreshOrders() {
        // Clear dummy data first
        clearDummyData();

        // Then load orders
        forceRefresh.setValue(true);
    }

    /**
     * Clear dummy data from the database
     */
    private void clearDummyData() {
        // We're not clearing any data now, as we want to show all orders
        Log.d("OrdersViewModel", "Not clearing any data, showing all orders");
    }

    /**
     * Convert OrderEntity to Order model
     *
     * @param entity OrderEntity from database
     * @return Order model for UI
     */
    private Order convertToOrderModel(OrderEntity entity) {
        if (entity == null) return null;

        Order order = new Order();
        order.setId(entity.getId());
        order.setOrderNumber(entity.getOrderNumber());
        order.setTrackingNumber(entity.getTrackingNumber());
        order.setUserId(entity.getUserId());
        order.setSubtotal(entity.getSubtotal());
        order.setDiscount(entity.getDiscount());
        order.setDeliveryFee(entity.getDeliveryFee());
        order.setTotalAmount(entity.getTotal());
        order.setStatus(entity.getStatus());
        order.setPaymentStatus(entity.getPaymentStatus());
        order.setPaymentMethod(entity.getPaymentMethod());

        // Format dates
        if (entity.getCreatedAt() != null) {
            order.setCreatedAt(entity.getCreatedAt().toString());
        }
        if (entity.getUpdatedAt() != null) {
            order.setUpdatedAt(entity.getUpdatedAt().toString());
        }

        // Set pickup and delivery dates
        if (entity.getPickupDate() != null) {
            order.setPickupDate(entity.getPickupDate().toString());
            order.setPickupTime(entity.getPickupTimeSlot());
        }
        if (entity.getDeliveryDate() != null) {
            order.setDeliveryDate(entity.getDeliveryDate().toString());
            order.setDeliveryTime(entity.getDeliveryTimeSlot());
        }

        // Set address
        order.setAddress(entity.getPickupAddress());
        order.setNotes(entity.getNotes());

        return order;
    }

    // The convertToOrderModel method is already defined above

    /**
     * Convert list of OrderEntity to list of Order models
     *
     * @param orderEntities List of OrderEntity from database
     * @return List of Order models for UI
     */
    private List<Order> convertToOrderModels(List<OrderEntity> orderEntities) {
        List<Order> orders = new ArrayList<>();

        Log.d("OrdersViewModel", "Converting " + orderEntities.size() + " order entities to models");

        for (OrderEntity entity : orderEntities) {
            Log.d("OrdersViewModel", "Converting order: " + entity.getOrderNumber() + ", Status: " + entity.getStatus());
            Order order = convertToOrderModel(entity);
            orders.add(order);
        }

        Log.d("OrdersViewModel", "Converted to " + orders.size() + " order models");

        return orders;
    }
}
