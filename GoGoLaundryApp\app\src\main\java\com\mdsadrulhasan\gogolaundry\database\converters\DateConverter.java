package com.mdsadrulhasan.gogolaundry.database.converters;

import androidx.room.TypeConverter;

import java.util.Date;

/**
 * Type converter for Date objects in Room database
 */
public class DateConverter {
    
    /**
     * Convert timestamp to Date
     * 
     * @param value Timestamp in milliseconds
     * @return Date object
     */
    @TypeConverter
    public static Date fromTimestamp(Long value) {
        return value == null ? null : new Date(value);
    }
    
    /**
     * Convert Date to timestamp
     * 
     * @param date Date object
     * @return Timestamp in milliseconds
     */
    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? null : date.getTime();
    }
}
