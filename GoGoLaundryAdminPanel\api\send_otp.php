<?php
/**
 * Send OTP API Endpoint
 *
 * This endpoint handles sending OTP to users
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OtpManager.php';
require_once '../includes/SmsService.php';
require_once '../includes/UserManager.php';
require_once '../includes/SettingsManager.php';

// Log request for debugging
error_log('send_otp.php called with method: ' . $_SERVER['REQUEST_METHOD']);
error_log('POST data: ' . json_encode($_POST));

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Log input data for debugging
error_log('Input data: ' . json_encode($inputData));
error_log('Raw POST data: ' . file_get_contents('php://input'));
error_log('$_POST: ' . json_encode($_POST));
error_log('$_GET: ' . json_encode($_GET));

// Validate input
if (empty($inputData['phone'])) {
    jsonResponse(false, 'Phone number is required', [], 400);
}

$phone = sanitize($inputData['phone']);
$email = !empty($inputData['email']) ? sanitize($inputData['email']) : null;
$purpose = !empty($inputData['purpose']) ? sanitize($inputData['purpose']) : 'registration';

// Log sanitized data
error_log('Sanitized phone: ' . $phone);
error_log('Sanitized email: ' . $email);
error_log('Sanitized purpose: ' . $purpose);

// Validate phone number
if (!validatePhone($phone)) {
    jsonResponse(false, 'Invalid phone number format', [], 400);
}

// Validate email if provided
if ($email && !validateEmail($email)) {
    jsonResponse(false, 'Invalid email format', [], 400);
}

// Validate purpose
$validPurposes = ['registration', 'login', 'reset_password'];
if (!in_array($purpose, $validPurposes)) {
    jsonResponse(false, 'Invalid purpose', [], 400);
}

// Initialize managers
$otpManager = new OtpManager($pdo);
$smsService = new SmsService();
$userManager = new UserManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Check if OTP verification is enabled
$otpEnabled = $settingsManager->isOtpEnabled();
error_log('OTP enabled: ' . ($otpEnabled ? 'true' : 'false'));

// If OTP is disabled, return success without sending OTP
if (!$otpEnabled) {
    jsonResponse(true, 'OTP verification is disabled. You can proceed without OTP.', [
        'otp_enabled' => false,
        'phone' => formatPhone($phone)
    ]);
}

// Check if user exists for registration purpose
if ($purpose === 'registration' && $userManager->userExistsByPhone($phone)) {
    jsonResponse(false, 'Phone number already registered', [], 400);
}

// Check if user exists for login/reset_password purpose
if (($purpose === 'login' || $purpose === 'reset_password') && !$userManager->userExistsByPhone($phone)) {
    jsonResponse(false, 'Phone number not registered', [], 404);
}

// Check if email is already registered
if ($purpose === 'registration' && $email && $userManager->userExistsByEmail($email)) {
    jsonResponse(false, 'Email already registered', [], 400);
}

// Check if there's already a valid OTP
if ($otpManager->hasValidOtp($phone, $purpose)) {
    $remainingTime = $otpManager->getRemainingTime($phone, $purpose);
    jsonResponse(false, 'An OTP has already been sent. Please wait ' . ceil($remainingTime / 60) . ' minutes before requesting a new one.', [
        'remaining_time' => $remainingTime
    ], 429);
}

// Generate OTP
$otp = $otpManager->generateOtp($phone, $email, $purpose);

if (!$otp) {
    jsonResponse(false, 'Failed to generate OTP. Please try again later.', [], 500);
}

// Send OTP via SMS
$smsResponse = $smsService->sendOtp($phone, $otp);

// Log SMS response for debugging
error_log('SMS API Response: ' . json_encode($smsResponse));

if (!isset($smsResponse['success']) || !$smsResponse['success']) {
    // Log the error but don't expose details to client
    error_log('SMS API Error: ' . json_encode($smsResponse));
    jsonResponse(false, 'Failed to send OTP. Please try again later.', [], 500);
}

// Return success response
jsonResponse(true, 'OTP sent successfully', [
    'expires_in' => OTP_EXPIRY,
    'phone' => formatPhone($phone),
    'otp_enabled' => true
]);
