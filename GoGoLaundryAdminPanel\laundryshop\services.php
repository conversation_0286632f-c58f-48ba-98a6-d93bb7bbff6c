<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'My Services';
$currentPage = 'services';

// Handle service actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'toggle_service':
                $serviceId = intval($_POST['service_id']);
                $isAvailable = intval($_POST['is_available']);
                
                // Check if service is already in shop_services
                $checkStmt = $pdo->prepare("SELECT id FROM shop_services WHERE shop_id = ? AND service_id = ?");
                $checkStmt->execute([$shopOwnerData['shop_id'], $serviceId]);
                
                if ($checkStmt->fetch()) {
                    // Update existing
                    $stmt = $pdo->prepare("UPDATE shop_services SET is_available = ? WHERE shop_id = ? AND service_id = ?");
                    $stmt->execute([$isAvailable, $shopOwnerData['shop_id'], $serviceId]);
                } else {
                    // Insert new
                    $stmt = $pdo->prepare("INSERT INTO shop_services (shop_id, service_id, is_available, estimated_hours) VALUES (?, ?, ?, 24)");
                    $stmt->execute([$shopOwnerData['shop_id'], $serviceId, $isAvailable]);
                }

                // Trigger cache invalidation for mobile app
                try {
                    $cacheInvalidateStmt = $pdo->prepare("
                        INSERT INTO shop_update_notifications (shop_id, update_type, created_at)
                        VALUES (?, 'service_update', NOW())
                        ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0
                    ");
                    $cacheInvalidateStmt->execute([$shopOwnerData['shop_id']]);
                } catch (PDOException $e) {
                    error_log('Cache invalidation error: ' . $e->getMessage());
                }

                $_SESSION['shop_success_message'] = 'Service availability updated successfully!';
                break;
        }
    }
}

// Get all services with shop availability status
try {
    $servicesStmt = $pdo->prepare("
        SELECT s.*, 
               ss.is_available as shop_is_available,
               ss.estimated_hours
        FROM services s
        LEFT JOIN shop_services ss ON s.id = ss.service_id AND ss.shop_id = ?
        WHERE s.is_active = 1
        ORDER BY s.sort_order ASC, s.name ASC
    ");
    $servicesStmt->execute([$shopOwnerData['shop_id']]);
    $services = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log('Services fetch error: ' . $e->getMessage());
    $services = [];
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">My Services</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-concierge-bell me-2"></i>Available Services
                </h5>
                <small class="text-muted">Enable or disable services for your shop</small>
            </div>
            <div class="card-body">
                <?php if (empty($services)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No services available.</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($services as $service): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 service-card">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="service-info flex-grow-1">
                                                <?php if ($service['image_url']): ?>
                                                    <img src="<?php echo htmlspecialchars($service['image_url']); ?>" 
                                                         class="service-image mb-2" alt="Service Image">
                                                <?php endif; ?>
                                                <h6 class="card-title"><?php echo htmlspecialchars($service['name']); ?></h6>
                                                <?php if ($service['bn_name']): ?>
                                                    <small class="text-muted d-block"><?php echo htmlspecialchars($service['bn_name']); ?></small>
                                                <?php endif; ?>
                                                <?php if ($service['description']): ?>
                                                    <p class="card-text small text-muted mt-2">
                                                        <?php echo htmlspecialchars($service['description']); ?>
                                                    </p>
                                                <?php endif; ?>
                                            </div>
                                            <div class="service-toggle">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input service-switch" 
                                                           type="checkbox" 
                                                           id="service_<?php echo $service['id']; ?>"
                                                           data-service-id="<?php echo $service['id']; ?>"
                                                           <?php echo ($service['shop_is_available'] == 1) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="service_<?php echo $service['id']; ?>">
                                                        <small><?php echo ($service['shop_is_available'] == 1) ? 'Available' : 'Disabled'; ?></small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if ($service['shop_is_available'] == 1): ?>
                                            <div class="service-details">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <small class="text-success">
                                                            <i class="fas fa-check-circle me-1"></i>
                                                            Active in your shop
                                                        </small>
                                                    </div>
                                                </div>
                                                <?php if ($service['estimated_hours']): ?>
                                                    <div class="row mt-2">
                                                        <div class="col-12">
                                                            <small class="text-muted">
                                                                <i class="fas fa-clock me-1"></i>
                                                                Est. completion: <?php echo $service['estimated_hours']; ?> hours
                                                            </small>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="service-details">
                                                <small class="text-muted">
                                                    <i class="fas fa-times-circle me-1"></i>
                                                    Not available in your shop
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.service-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
}

.service-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
}

.service-switch {
    transform: scale(1.2);
}

.form-check-input:checked {
    background-color: var(--shop-primary);
    border-color: var(--shop-primary);
}

.service-toggle {
    min-width: 80px;
    text-align: center;
}

.service-info {
    padding-right: 15px;
}
</style>

<script>
// Handle service toggle
document.addEventListener('DOMContentLoaded', function() {
    const serviceSwitches = document.querySelectorAll('.service-switch');
    
    serviceSwitches.forEach(function(switchElement) {
        switchElement.addEventListener('change', function() {
            const serviceId = this.dataset.serviceId;
            const isAvailable = this.checked ? 1 : 0;
            const label = this.nextElementSibling.querySelector('small');
            
            // Update label immediately
            label.textContent = isAvailable ? 'Available' : 'Disabled';
            
            // Send AJAX request
            const formData = new FormData();
            formData.append('action', 'toggle_service');
            formData.append('service_id', serviceId);
            formData.append('is_available', isAvailable);
            
            fetch('services.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Reload page to show updated status
                location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                // Revert switch on error
                this.checked = !this.checked;
                label.textContent = this.checked ? 'Available' : 'Disabled';
                alert('Failed to update service. Please try again.');
            });
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
