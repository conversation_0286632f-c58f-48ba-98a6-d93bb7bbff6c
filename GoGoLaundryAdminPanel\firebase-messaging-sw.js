// Firebase Messaging Service Worker for GoGoLaundry Admin Panel

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyASl3UlvsWsfylrHNHLWUOxc2Lcln6PI0g",
  authDomain: "gogolaundry-c4dd1.firebaseapp.com",
  projectId: "gogolaundry-c4dd1",
  storageBucket: "gogolaundry-c4dd1.firebasestorage.app",
  messagingSenderId: "523301621504",
  appId: "1:523301621504:android:59b7b8816b0e2b7604ede5"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  const notificationTitle = payload.notification?.title || payload.data?.title || 'GoGoLaundry Notification';
  const notificationOptions = {
    body: payload.notification?.body || payload.data?.message || 'You have a new notification',
    icon: payload.notification?.icon || '/assets/images/logo.png',
    badge: '/assets/images/badge.png',
    data: payload.data || {},
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'View Details',
        icon: '/assets/images/view-icon.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/assets/images/dismiss-icon.png'
      }
    ],
    vibrate: [200, 100, 200],
    tag: 'gogolaundry-notification'
  };

  // Show notification
  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  const notification = event.notification;
  const action = event.action;
  const data = notification.data || {};

  // Close the notification
  notification.close();

  if (action === 'dismiss') {
    // Just close the notification
    return;
  }

  // Handle notification click
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Try to find an existing window/tab
      for (const client of clientList) {
        if (client.url.includes('GoGoLaundryAdminPanel') && 'focus' in client) {
          // Focus existing window and navigate if needed
          client.focus();
          
          // Send message to the client to handle navigation
          client.postMessage({
            type: 'NOTIFICATION_CLICK',
            data: data,
            action: action
          });
          
          return;
        }
      }
      
      // If no existing window, open a new one
      const targetUrl = getTargetUrl(data, action);
      return clients.openWindow(targetUrl);
    })
  );
});

// Handle notification close events
self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Notification closed:', event);
  
  const data = event.notification.data || {};
  
  // Track notification dismissal if needed
  if (data.notification_id) {
    // You could send analytics or update read status here
    console.log('Notification dismissed:', data.notification_id);
  }
});

/**
 * Get target URL based on notification data and action
 */
function getTargetUrl(data, action) {
  const baseUrl = self.location.origin + '/GoGoLaundry/GoGoLaundryAdminPanel/admin/';
  
  if (action === 'view' || !action) {
    const type = data.type;
    const orderId = data.order_id;
    const userId = data.user_id;
    
    switch (type) {
      case 'new_order':
      case 'order_status':
        if (orderId) {
          return baseUrl + 'order_details.php?id=' + orderId;
        }
        return baseUrl + 'orders.php';
        
      case 'user_registration':
        if (userId) {
          return baseUrl + 'users.php?user_id=' + userId;
        }
        return baseUrl + 'users.php';
        
      case 'system':
        return baseUrl + 'notifications.php';
        
      default:
        return baseUrl + 'index.php';
    }
  }
  
  return baseUrl + 'index.php';
}

// Handle service worker installation
self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker installing...');
  self.skipWaiting();
});

// Handle service worker activation
self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker activating...');
  event.waitUntil(self.clients.claim());
});

// Handle messages from the main thread
self.addEventListener('message', (event) => {
  console.log('[firebase-messaging-sw.js] Message received:', event.data);
  
  const { type, data } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'GET_VERSION':
      event.ports[0].postMessage({ version: '1.0.0' });
      break;
      
    default:
      console.log('Unknown message type:', type);
  }
});

// Handle push events (backup for FCM)
self.addEventListener('push', (event) => {
  console.log('[firebase-messaging-sw.js] Push event received:', event);
  
  if (event.data) {
    try {
      const payload = event.data.json();
      console.log('Push payload:', payload);
      
      const title = payload.notification?.title || payload.data?.title || 'GoGoLaundry';
      const options = {
        body: payload.notification?.body || payload.data?.message || 'New notification',
        icon: '/assets/images/logo.png',
        badge: '/assets/images/badge.png',
        data: payload.data || {},
        requireInteraction: true,
        tag: 'gogolaundry-push'
      };
      
      event.waitUntil(
        self.registration.showNotification(title, options)
      );
    } catch (error) {
      console.error('Error parsing push payload:', error);
    }
  }
});

console.log('[firebase-messaging-sw.js] Service worker loaded successfully');
