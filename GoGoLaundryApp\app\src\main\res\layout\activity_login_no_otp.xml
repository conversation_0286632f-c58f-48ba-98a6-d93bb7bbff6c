<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".LoginActivity">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        android:title="@string/login"
        android:titleTextColor="@color/white"
        android:fontFamily="@font/bnlatxf"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="24dp">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/loginCard"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:background="#F4D4DE"
                    android:orientation="vertical"
                    android:padding="24dp">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        android:contentDescription="@string/app_name"
                        android:src="@drawable/profile" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/phoneLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_marginTop="16dp"
                        android:hint="@string/phone_number"
                        app:startIconDrawable="@drawable/baseline_phone_24">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/phoneEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/bnlatxf"
                            android:inputType="phone" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/passwordLayout"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:hint="@string/password"
                        app:endIconMode="password_toggle"
                        app:startIconDrawable="@drawable/baseline_password_24">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/passwordEditText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/bnlatxf"
                            android:inputType="textPassword" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:id="@+id/forgotPasswordText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:fontFamily="@font/bnlatxf"
                        android:gravity="end"
                        android:text="@string/forgot_password"
                        android:textColor="@color/primary"
                        android:textSize="14sp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/loginButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:backgroundTint="#BB3930"
                        android:fontFamily="@font/bnlatxf"
                        android:text="@string/login"
                        app:cornerRadius="12dp" />

                    <TextView
                        android:id="@+id/signupText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:fontFamily="@font/bnlatxf"
                        android:text="@string/dont_have_account"
                        android:textColor="@color/primary"
                        android:textSize="14sp" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>