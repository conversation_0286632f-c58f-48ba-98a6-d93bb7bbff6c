package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.card.MaterialCardView;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.ServiceAdapter;
import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;
import com.mdsadrulhasan.gogolaundry.model.Service;
import com.mdsadrulhasan.gogolaundry.viewmodel.ServicesViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for displaying all popular services in a grid layout
 */
public class PopularServiceFragment extends Fragment implements ServiceAdapter.OnServiceClickListener {

    private ServicesViewModel viewModel;
    private RecyclerView recyclerView;
    private ServiceAdapter adapter;
    private SwipeRefreshLayout swipeRefreshLayout;
    private MaterialCardView progressCard;
    private MaterialCardView emptyCard;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_popular_services, container, false);

        // Initialize views
        initViews(view);

        // Set up RecyclerView
        setupRecyclerView();

        // Set up ViewModel
        setupViewModel();

        // Load data
        loadServices();

        return view;
    }

    /**
     * Initialize views
     */
    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.services_recycler_view);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);
        progressCard = view.findViewById(R.id.progress_card);
        emptyCard = view.findViewById(R.id.empty_card);

        // Set up swipe refresh
        swipeRefreshLayout.setOnRefreshListener(this::loadServices);
        swipeRefreshLayout.setColorSchemeResources(R.color.primary);
    }

    /**
     * Set up RecyclerView
     */
    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new GridLayoutManager(requireContext(), 2));
        adapter = new ServiceAdapter(new ArrayList<>(), this);
        recyclerView.setAdapter(adapter);

        // Ensure proper scrolling behavior
        recyclerView.setNestedScrollingEnabled(true);
        recyclerView.setHasFixedSize(true);
    }

    /**
     * Set up ViewModel
     */
    private void setupViewModel() {
        viewModel = new ViewModelProvider(this).get(ServicesViewModel.class);
    }

    /**
     * Load services data
     */
    private void loadServices() {
        // Observe services data
        viewModel.getServices().observe(getViewLifecycleOwner(), resource -> {
            swipeRefreshLayout.setRefreshing(false);

            if (resource.isLoading()) {
                showLoading();
            } else if (resource.isSuccess()) {
                List<ServiceEntity> serviceEntities = resource.getData();
                if (serviceEntities != null && !serviceEntities.isEmpty()) {
                    // Convert entities to UI models
                    List<Service> services = convertToServiceModels(serviceEntities);
                    showServices(services);
                } else {
                    showEmpty();
                }
            } else if (resource.isError()) {
                showError(resource.getMessage());
            }
        });

        // Load services
        viewModel.refreshServices();
    }

    /**
     * Convert service entities to UI models
     */
    private List<Service> convertToServiceModels(List<ServiceEntity> entities) {
        List<Service> services = new ArrayList<>();
        for (ServiceEntity entity : entities) {
            Service service = new Service();
            service.setId(entity.getId());
            service.setName(entity.getName());
            service.setDescription(entity.getDescription());
            service.setIconUrl(entity.getImageUrl()); // Fixed: use getImageUrl() instead of getIconUrl()
            service.setPrice(0.0); // ServiceEntity doesn't have price, set default value
            services.add(service);
        }
        return services;
    }

    /**
     * Show loading state
     */
    private void showLoading() {
        progressCard.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.GONE);
    }

    /**
     * Show services data
     */
    private void showServices(List<Service> services) {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        emptyCard.setVisibility(View.GONE);
        adapter.updateServices(services);
    }

    /**
     * Show empty state
     */
    private void showEmpty() {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.VISIBLE);
    }

    /**
     * Show error state
     */
    private void showError(String message) {
        progressCard.setVisibility(View.GONE);
        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();

        // If adapter is empty, show empty view
        if (adapter.getItemCount() == 0) {
            recyclerView.setVisibility(View.GONE);
            emptyCard.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onServiceClick(Service service) {
        // Navigate to items screen for this service
        if (getActivity() != null) {
            ItemsFragment itemsFragment = ItemsFragment.newInstance(service.getId(), service.getName());
            getActivity().getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_container, itemsFragment)
                    .addToBackStack(null)
                    .commit();
        }
    }

    @Override
    public void onAddToCartClick(Service service) {
        // Services don't have add to cart functionality
        // Navigate to items screen instead
        onServiceClick(service);
    }
}
