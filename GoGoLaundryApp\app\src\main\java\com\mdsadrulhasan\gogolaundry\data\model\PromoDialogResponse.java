package com.mdsadrulhasan.gogolaundry.data.model;

import com.google.gson.annotations.SerializedName;
import java.io.Serializable;

/**
 * Response model for promotional dialog API
 */
public class PromoDialogResponse {

    @SerializedName("success")
    private boolean success;

    @SerializedName("has_dialog")
    private boolean hasDialog;

    @SerializedName("dialog")
    private PromoDialog dialog;

    @SerializedName("message")
    private String message;

    // Constructors
    public PromoDialogResponse() {}

    public PromoDialogResponse(boolean success, boolean hasDialog, PromoDialog dialog, String message) {
        this.success = success;
        this.hasDialog = hasDialog;
        this.dialog = dialog;
        this.message = message;
    }

    // Getters and setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public boolean isHasDialog() {
        return hasDialog;
    }

    public void setHasDialog(boolean hasDialog) {
        this.hasDialog = hasDialog;
    }

    public PromoDialog getDialog() {
        return dialog;
    }

    public void setDialog(PromoDialog dialog) {
        this.dialog = dialog;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * Promotional dialog data model
     */
    public static class PromoDialog implements Serializable {

        @SerializedName("id")
        private int id;

        @SerializedName("title")
        private String title;

        @SerializedName("subtitle")
        private String subtitle;

        @SerializedName("description")
        private String description;

        @SerializedName("discount_text")
        private String discountText;

        @SerializedName("promo_code")
        private String promoCode;

        @SerializedName("button_text")
        private String buttonText;

        @SerializedName("image_url")
        private String imageUrl;

        @SerializedName("background_color")
        private String backgroundColor;

        @SerializedName("text_color")
        private String textColor;

        @SerializedName("button_color")
        private String buttonColor;

        @SerializedName("background_type")
        private String backgroundType;

        @SerializedName("gradient_color1")
        private String gradientColor1;

        @SerializedName("gradient_color2")
        private String gradientColor2;

        @SerializedName("gradient_direction")
        private String gradientDirection;

        @SerializedName("is_active")
        private boolean isActive;

        @SerializedName("start_date")
        private String startDate;

        @SerializedName("end_date")
        private String endDate;

        @SerializedName("created_at")
        private String createdAt;

        @SerializedName("updated_at")
        private String updatedAt;

        // Constructors
        public PromoDialog() {}

        // Getters and setters
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getDiscountText() {
            return discountText;
        }

        public void setDiscountText(String discountText) {
            this.discountText = discountText;
        }

        public String getPromoCode() {
            return promoCode;
        }

        public void setPromoCode(String promoCode) {
            this.promoCode = promoCode;
        }

        public String getButtonText() {
            return buttonText;
        }

        public void setButtonText(String buttonText) {
            this.buttonText = buttonText;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getBackgroundColor() {
            return backgroundColor;
        }

        public void setBackgroundColor(String backgroundColor) {
            this.backgroundColor = backgroundColor;
        }

        public String getTextColor() {
            return textColor;
        }

        public void setTextColor(String textColor) {
            this.textColor = textColor;
        }

        public String getButtonColor() {
            return buttonColor;
        }

        public void setButtonColor(String buttonColor) {
            this.buttonColor = buttonColor;
        }

        public String getBackgroundType() {
            return backgroundType;
        }

        public void setBackgroundType(String backgroundType) {
            this.backgroundType = backgroundType;
        }

        public String getGradientColor1() {
            return gradientColor1;
        }

        public void setGradientColor1(String gradientColor1) {
            this.gradientColor1 = gradientColor1;
        }

        public String getGradientColor2() {
            return gradientColor2;
        }

        public void setGradientColor2(String gradientColor2) {
            this.gradientColor2 = gradientColor2;
        }

        public String getGradientDirection() {
            return gradientDirection;
        }

        public void setGradientDirection(String gradientDirection) {
            this.gradientDirection = gradientDirection;
        }

        public boolean isActive() {
            return isActive;
        }

        public void setActive(boolean active) {
            isActive = active;
        }

        public String getStartDate() {
            return startDate;
        }

        public void setStartDate(String startDate) {
            this.startDate = startDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }

        public String getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(String updatedAt) {
            this.updatedAt = updatedAt;
        }
    }
}
