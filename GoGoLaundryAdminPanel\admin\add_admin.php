<?php
/**
 * Add New Admin
 *
 * This page allows super admins to add new admin users
 */

// Include authentication middleware
require_once 'auth.php';

// Check if user is super admin
if ($adminData['role'] !== 'super_admin') {
    $_SESSION['error_message'] = 'You do not have permission to access this page.';
    header('Location: index.php');
    exit;
}

// Include required files
require_once '../includes/AdminManager.php';

// Initialize admin manager
$adminManager = new AdminManager($pdo);

// Handle form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Get form data
        $username = isset($_POST['username']) ? trim($_POST['username']) : '';
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $fullName = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
        $password = isset($_POST['password']) ? trim($_POST['password']) : '';
        $confirmPassword = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
        $role = isset($_POST['role']) ? trim($_POST['role']) : 'admin';
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        // Validate form data
        if (empty($username)) {
            $error = 'Username is required.';
        } elseif (empty($email)) {
            $error = 'Email is required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Invalid email format.';
        } elseif (empty($fullName)) {
            $error = 'Full name is required.';
        } elseif (empty($password)) {
            $error = 'Password is required.';
        } elseif (strlen($password) < 8) {
            $error = 'Password must be at least 8 characters long.';
        } elseif ($password !== $confirmPassword) {
            $error = 'Passwords do not match.';
        } elseif (!in_array($role, ['admin', 'super_admin'])) {
            $error = 'Invalid role selected.';
        } else {
            // Check if username already exists
            if ($adminManager->adminExistsByUsername($username)) {
                $error = 'Username already exists. Please choose a different username.';
            } 
            // Check if email already exists
            elseif ($adminManager->adminExistsByEmail($email)) {
                $error = 'Email already exists. Please use a different email address.';
            } else {
                // Add new admin
                $result = $adminManager->addAdmin($username, $email, $password, $fullName, $role, $isActive);
                
                if ($result) {
                    // Log action
                    $adminManager->logAdminAction(
                        $adminData['id'],
                        'admin_add',
                        'Added new admin: ' . $username . ' (' . $role . ')',
                        getClientIp()
                    );
                    
                    $success = 'Admin user added successfully.';
                    
                    // Clear form data
                    $username = $email = $fullName = $password = $confirmPassword = '';
                    $role = 'admin';
                    $isActive = 1;
                } else {
                    $error = 'Failed to add admin user. Please try again.';
                }
            }
        }
    }
}

// Page title and breadcrumbs
$pageTitle = 'Add New Admin';
$breadcrumbs = [
    'Administrators' => 'admins.php',
    'Add New Admin' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Add New Admin</h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="admins.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Admins
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i> <?php echo $success; ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">Admin User Information</h5>
    </div>
    <div class="card-body">
        <form action="add_admin.php" method="post">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" value="<?php echo isset($username) ? htmlspecialchars($username) : ''; ?>" required>
                    <div class="form-text">Username must be unique and will be used for login.</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo isset($fullName) ? htmlspecialchars($fullName) : ''; ?>" required>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="password" name="password" required>
                    <div class="form-text">Password must be at least 8 characters long.</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                    <select class="form-select" id="role" name="role" required>
                        <option value="admin" <?php echo (isset($role) && $role === 'admin') ? 'selected' : ''; ?>>Admin</option>
                        <option value="super_admin" <?php echo (isset($role) && $role === 'super_admin') ? 'selected' : ''; ?>>Super Admin</option>
                    </select>
                    <div class="form-text">Super admins can manage other admin users.</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="form-check form-switch mt-4">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo (!isset($isActive) || $isActive) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">Active Account</label>
                    </div>
                    <div class="form-text">Inactive accounts cannot log in.</div>
                </div>
            </div>
            
            <div class="text-end mt-4">
                <button type="reset" class="btn btn-secondary me-2">Reset</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-user-plus me-1"></i> Add Admin
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password validation
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    
    confirmPasswordInput.addEventListener('input', function() {
        if (passwordInput.value !== confirmPasswordInput.value) {
            confirmPasswordInput.setCustomValidity('Passwords do not match');
        } else {
            confirmPasswordInput.setCustomValidity('');
        }
    });
    
    passwordInput.addEventListener('input', function() {
        if (passwordInput.value.length < 8) {
            passwordInput.setCustomValidity('Password must be at least 8 characters long');
        } else {
            passwordInput.setCustomValidity('');
            
            if (confirmPasswordInput.value) {
                if (passwordInput.value !== confirmPasswordInput.value) {
                    confirmPasswordInput.setCustomValidity('Passwords do not match');
                } else {
                    confirmPasswordInput.setCustomValidity('');
                }
            }
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
