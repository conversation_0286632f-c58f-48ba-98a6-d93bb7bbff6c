package com.mdsadrulhasan.gogolaundry.util;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Item decoration for adding spacing between grid items in a RecyclerView
 */
public class GridSpacingItemDecoration extends RecyclerView.ItemDecoration {
    
    private final int spanCount;
    private final int spacing;
    private final boolean includeEdge;
    
    /**
     * Constructor
     * 
     * @param spanCount Number of columns in the grid
     * @param spacing Spacing between items in pixels
     * @param includeEdge Whether to include edge spacing
     */
    public GridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge) {
        this.spanCount = spanCount;
        this.spacing = spacing;
        this.includeEdge = includeEdge;
    }
    
    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, 
                              @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        int column = position % spanCount;
        
        if (includeEdge) {
            outRect.left = spacing - column * spacing / spanCount;
            outRect.right = (column + 1) * spacing / spanCount;
            
            if (position < spanCount) {
                outRect.top = spacing;
            }
            outRect.bottom = spacing;
        } else {
            outRect.left = column * spacing / spanCount;
            outRect.right = spacing - (column + 1) * spacing / spanCount;
            
            if (position >= spanCount) {
                outRect.top = spacing;
            }
        }
    }
}
