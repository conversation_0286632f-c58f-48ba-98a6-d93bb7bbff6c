package com.mdsadrulhasan.gogolaundry.model;

import com.google.gson.annotations.SerializedName;
import java.io.Serializable;
import java.util.Date;

/**
 * Model class for notifications
 */
public class Notification implements Serializable {
    private static final long serialVersionUID = 1L;
    private int id;

    @SerializedName("user_id")
    private int userId;

    @SerializedName("order_id")
    private Integer orderId;

    private String title;
    private String message;
    private String type;

    @SerializedName("image_url")
    private String imageUrl;

    @SerializedName("is_read")
    private boolean isRead;

    @SerializedName("created_at")
    private Date createdAt;

    @SerializedName("order_number")
    private String orderNumber;

    /**
     * Default constructor
     */
    public Notification() {
    }

    /**
     * Constructor with all fields
     *
     * @param id Notification ID
     * @param userId User ID
     * @param orderId Order ID (can be null)
     * @param title Notification title
     * @param message Notification message
     * @param type Notification type (order_status, promo, system, custom)
     * @param imageUrl Image URL (can be null)
     * @param isRead Whether the notification has been read
     * @param createdAt Date the notification was created
     * @param orderNumber Order number (can be null)
     */
    public Notification(int id, int userId, Integer orderId, String title, String message, String type, String imageUrl, boolean isRead, Date createdAt, String orderNumber) {
        this.id = id;
        this.userId = userId;
        this.orderId = orderId;
        this.title = title;
        this.message = message;
        this.type = type;
        this.imageUrl = imageUrl;
        this.isRead = isRead;
        this.createdAt = createdAt;
        this.orderNumber = orderNumber;
    }

    // Getters and setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isRead() {
        return isRead;
    }

    public void setRead(boolean read) {
        isRead = read;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    /**
     * Get the notification type display name
     *
     * @return Display name for the notification type
     */
    public String getTypeDisplayName() {
        switch (type) {
            case "order_status":
                return "Order Status";
            case "promo":
                return "Promotion";
            case "system":
                return "System";
            case "custom":
                return "Custom";
            default:
                return type;
        }
    }

    /**
     * Convert notification to string for logging
     *
     * @return String representation of notification
     */
    @Override
    public String toString() {
        return "{" +
                "id=" + id +
                ", user_id=" + userId +
                ", order_id=" + orderId +
                ", title=" + title +
                ", message=" + message +
                ", type=" + type +
                ", image_url=" + imageUrl +
                ", is_read=" + isRead +
                ", created_at=" + createdAt +
                "}";
    }
}
