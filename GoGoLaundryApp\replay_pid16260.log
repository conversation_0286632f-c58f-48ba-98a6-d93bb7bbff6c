version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 334 ciObject found
instanceKlass java/util/zip/InflaterInputStream
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811fcc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811fc800
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000001d0811fc400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0811fc000
# instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations_Decorated$$Lambda+0x000001d0811f8fa8
instanceKlass org/gradle/api/internal/cache/NoMarkingStrategy
instanceKlass org/gradle/api/internal/cache/CacheDirTagMarkingStrategy
instanceKlass org/gradle/api/internal/provider/TypeSanitizingTransformer
# instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations$ContextualErrorMessageProperty$$Lambda+0x000001d0811f4c00
# instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations$$Lambda+0x000001d0811f5ce8
# instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations$$Lambda+0x000001d0811f5638
# instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations$ContextualErrorMessageProperty$$Lambda+0x000001d0811f5418
instanceKlass org/gradle/internal/serialization/Cached
# instanceKlass org/gradle/internal/instantiation/generator/ManagedObjectFactory$$Lambda+0x000001d0811f7cf0
instanceKlass org/gradle/internal/instantiation/generator/ManagedObjectFactory$ManagedPropertyName
# instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations$DefaultCacheResourceConfiguration_Decorated$$Lambda+0x000001d0811f7888
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$4
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$3
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$2
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$1
instanceKlass org/gradle/api/internal/provider/ValueCollector
instanceKlass org/gradle/api/internal/provider/ValueSanitizer
instanceKlass org/gradle/api/internal/provider/ValueSanitizers
instanceKlass  @bci java/util/function/Function identity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/function/Function$$Lambda+0x000001d081171b00
instanceKlass org/gradle/api/internal/provider/ValueState
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Present
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Missing
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Value
instanceKlass org/gradle/api/NamedDomainObjectProvider
instanceKlass org/gradle/api/internal/provider/Providers
instanceKlass org/gradle/internal/Describables$AbstractDescribable
instanceKlass org/gradle/internal/Describables
instanceKlass org/gradle/api/internal/cache/CacheResourceConfigurationInternal$EntryRetention
instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations$DefaultCacheResourceConfiguration
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811f4800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811f4400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811f4000
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ObjectCreationDetails
instanceKlass org/gradle/internal/instantiation/generator/InjectUtil
instanceKlass java/util/TimSort
instanceKlass com/google/common/collect/Iterables
instanceKlass com/google/common/collect/Ordering
instanceKlass org/gradle/internal/instantiation/generator/ConstructorComparator
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$InvokeConstructorStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl$GeneratedConstructorImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedConstructor
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$SerializationConstructor
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass org/objectweb/asm/Handler
instanceKlass org/objectweb/asm/Attribute
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addConstructor (Ljava/lang/reflect/Constructor;Z)V 83 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001d0811ea000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0811e9c00
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addConstructor (Ljava/lang/reflect/Constructor;Z)V 83 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e9800
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addConstructor (Ljava/lang/reflect/Constructor;Z)V 83 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811ed8b8
instanceKlass  @cpi org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl 1176 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e9400
instanceKlass org/gradle/model/internal/asm/AsmClassGeneratorUtils
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addLazyGroovySupportSetterOverloads (Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata;Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata;)V 21 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811ecfd8
instanceKlass org/apache/groovy/util/BeanUtils
instanceKlass groovy/lang/MetaProperty
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addSetMethod (Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata;Ljava/lang/reflect/Method;)V 66 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811ec4c8
instanceKlass  @cpi org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl 1330 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e9000
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addActionMethod (Ljava/lang/reflect/Method;)V 98 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001d0811e8c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0811e8800
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addActionMethod (Ljava/lang/reflect/Method;)V 98 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e8400
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addActionMethod (Ljava/lang/reflect/Method;)V 98 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e1c70
instanceKlass  @cpi org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl 1343 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e8000
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addActionMethod (Ljava/lang/reflect/Method;)V 22 <appendix> argL0 ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e1a58
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSet$1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection$WrappedIterator
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addDynamicMethods ()V 82 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e7c08
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addDynamicMethods ()V 64 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e79e8
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addDynamicMethods ()V 49 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e7308
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addDynamicMethods ()V 34 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e6c28
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addSetter (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/gradle/model/internal/asm/BytecodeFragment;)V 7 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e6088
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl mixInGroovyObject ()V 50 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e5e68
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl mixInGroovyObject ()V 35 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e5788
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl mixInDynamicAware ()V 60 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e5088
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl mixInDynamicAware ()V 37 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811e49a8
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl applyConventionMappingToGetter (Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata;Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata;ZZ)V 55 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001d0811e1400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0811e1000
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl applyConventionMappingToGetter (Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata;Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata;ZZ)V 55 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e0c00
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl applyConventionMappingToGetter (Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata;Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata;ZZ)V 55 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811dfc48
instanceKlass  @cpi org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl 1328 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e0800
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addLazyGetter (Ljava/lang/String;Lorg/objectweb/asm/Type;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/objectweb/asm/Type;Lorg/gradle/model/internal/asm/BytecodeFragment;Lorg/gradle/model/internal/asm/BytecodeFragment;)V 15 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811df568
instanceKlass org/gradle/model/internal/asm/BytecodeFragment$1
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl mixInConventionAware ()V 32 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811df128
instanceKlass org/gradle/model/internal/asm/ClassVisitorScope$1
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addExtensionsProperty ()V 10 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811de360
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addNoDeprecationConventionPrivateGetter ()V 7 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811ddc60
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addLazyGetter (Ljava/lang/String;Lorg/objectweb/asm/Type;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lorg/objectweb/asm/Type;Lorg/gradle/model/internal/asm/BytecodeFragment;Lorg/gradle/model/internal/asm/BytecodeFragment;)V 15 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e0400
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addServiceGetter (Ljava/lang/String;Ljava/lang/String;Lorg/objectweb/asm/Type;Ljava/lang/String;Ljava/lang/String;)V 11 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811dd580
instanceKlass  @cpi org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl 1241 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811e0000
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateToStringSupport ()V 8 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811dcea0
instanceKlass  @bci java/util/stream/MatchOps makeRef (Ljava/util/function/Predicate;Ljava/util/stream/MatchOps$MatchKind;)Ljava/util/stream/TerminalOp; 20 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811d0400
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateModelObjectMethods ()V 104 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811dc7a0
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateModelObjectMethods ()V 88 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811dc0c0
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateModelObjectMethods ()V 72 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811db9e0
instanceKlass org/gradle/api/Task
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateModelObjectMethods ()V 46 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811db108
instanceKlass org/objectweb/asm/Edge
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateModelObjectMethods ()V 31 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811da820
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateGeneratedSubtypeMethods ()V 24 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811da140
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateGeneratedSubtypeMethods ()V 7 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811d9a60
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateInitMethod ()V 58 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811d9360
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateInitMethod ()V 43 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811d0c20
instanceKlass org/objectweb/asm/Label
instanceKlass org/objectweb/asm/Frame
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl generateInitMethod ()V 28 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda+0x000001d0811d1838
instanceKlass org/objectweb/asm/ByteVector
instanceKlass org/objectweb/asm/Symbol
instanceKlass org/objectweb/asm/SymbolTable
instanceKlass org/objectweb/asm/FieldVisitor
instanceKlass org/objectweb/asm/MethodVisitor
instanceKlass org/objectweb/asm/AnnotationVisitor
instanceKlass org/objectweb/asm/ModuleVisitor
instanceKlass org/objectweb/asm/RecordComponentVisitor
instanceKlass org/gradle/model/internal/asm/AsmClassGenerator
instanceKlass org/objectweb/asm/Handle
instanceKlass org/gradle/internal/DisplayName
instanceKlass org/gradle/api/Project
instanceKlass org/gradle/api/internal/provider/AbstractMinimalProvider
instanceKlass org/gradle/api/internal/provider/PropertyInternal
instanceKlass org/gradle/api/internal/provider/support/LazyGroovySupport
instanceKlass org/gradle/api/internal/provider/HasConfigurableValueInternal
instanceKlass org/gradle/api/internal/provider/ProviderInternal
instanceKlass org/gradle/api/internal/provider/EvaluationContext$EvaluationOwner
instanceKlass org/gradle/api/internal/provider/ValueSupplier
instanceKlass org/gradle/internal/instantiation/generator/ManagedObjectFactory
instanceKlass org/gradle/util/internal/ConfigureUtil
instanceKlass org/gradle/internal/metaobject/AbstractDynamicObject
instanceKlass org/gradle/api/plugins/Convention
instanceKlass org/gradle/api/plugins/ExtensionContainer
instanceKlass org/gradle/internal/metaobject/DynamicObject
instanceKlass org/gradle/internal/metaobject/PropertyAccess
instanceKlass org/gradle/internal/metaobject/MethodAccess
instanceKlass org/gradle/internal/extensibility/ConventionAwareHelper
instanceKlass org/gradle/api/internal/HasConvention
instanceKlass org/gradle/api/internal/IConventionAware
instanceKlass org/gradle/internal/state/OwnerAware
instanceKlass org/gradle/api/internal/GeneratedSubclass
instanceKlass org/gradle/api/internal/ConventionMapping
instanceKlass org/gradle/model/internal/asm/BytecodeFragment
instanceKlass  @bci org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata findAnnotation (Ljava/lang/Class;)Ljava/lang/annotation/Annotation; 10 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata$$Lambda+0x000001d0811cdb88
instanceKlass  @bci org/gradle/internal/instantiation/generator/AbstractClassGenerator hasNestedAnnotation (Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata;)Z 12 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AbstractClassGenerator$$Lambda+0x000001d0811cd938
instanceKlass  @cpi org/gradle/internal/instantiation/generator/AbstractClassGenerator 625 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811d0000
instanceKlass groovy/lang/GroovyObjectSupport
instanceKlass groovy/lang/GroovyCallable
instanceKlass org/gradle/api/IsolatedAction
instanceKlass  @bci java/util/stream/MatchOps makeRef (Ljava/util/function/Predicate;Ljava/util/stream/MatchOps$MatchKind;)Ljava/util/stream/TerminalOp; 20 <appendix> member <vmtarget> ; # java/util/stream/MatchOps$$Lambda+0x000001d081170c88
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass  @bci org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata isReadableWithoutSetterOfPropertyType ()Z 17 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata$$Lambda+0x000001d0811ccca8
instanceKlass jdk/internal/vm/annotation/IntrinsicCandidate
instanceKlass java/lang/Deprecated
instanceKlass org/gradle/api/internal/DynamicObjectAware
instanceKlass org/gradle/internal/extensibility/NoConventionMapping
instanceKlass org/gradle/api/Incubating
instanceKlass org/gradle/api/NonExtensible
instanceKlass org/gradle/api/cache/MarkingStrategy
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata
instanceKlass org/gradle/internal/reflect/PropertyAccessor
instanceKlass org/gradle/internal/reflect/PropertyMutator
instanceKlass org/gradle/internal/reflect/JavaPropertyReflectionUtil
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassMetadata
instanceKlass org/gradle/internal/reflect/MutablePropertyDetails
instanceKlass java/beans/Introspector$1
instanceKlass jdk/internal/access/JavaBeansAccess
instanceKlass java/beans/FeatureDescriptor
instanceKlass java/beans/Introspector
instanceKlass org/gradle/internal/reflect/MethodSet$MethodKey
instanceKlass org/gradle/api/cache/Cleanup
instanceKlass org/gradle/api/invocation/Gradle
instanceKlass org/gradle/api/plugins/ExtensionAware
instanceKlass org/gradle/api/plugins/PluginAware
instanceKlass org/gradle/api/internal/cache/CacheResourceConfigurationInternal
instanceKlass org/gradle/cache/CleanupFrequency
instanceKlass org/gradle/api/cache/CacheResourceConfiguration
instanceKlass org/gradle/internal/reflect/PropertyDetails
instanceKlass org/gradle/internal/reflect/MutableClassDetails
instanceKlass org/gradle/internal/reflect/ClassDetails
instanceKlass org/gradle/internal/reflect/ClassInspector
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationVisitor
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassInspectionVisitorImpl
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InjectionAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$DisabledAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassValidator
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass org/gradle/internal/reflect/MethodSet
instanceKlass com/google/common/collect/SetMultimap
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationHandler
instanceKlass  @bci org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector forType (Ljava/lang/Class;)Lorg/gradle/internal/instantiation/generator/ClassGenerator$GeneratedConstructor; 7 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$$Lambda+0x000001d0811c1388
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$CachedConstructor
instanceKlass org/gradle/api/internal/cache/DefaultCacheConfigurations
instanceKlass org/gradle/api/internal/model/DefaultObjectFactory
instanceKlass org/gradle/internal/model/BuildTreeObjectFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c7000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c6c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c6800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c6400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c6000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c5c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c5800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c5400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c5000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c4c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811c4800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0811c4400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0811c4000
instanceKlass  @bci org/gradle/api/internal/model/NamedObjectInstantiator <init> (Lorg/gradle/cache/internal/CrossBuildInMemoryCacheFactory;)V 6 <appendix> member <vmtarget> ; # org/gradle/api/internal/model/NamedObjectInstantiator$$Lambda+0x000001d0811c0618
instanceKlass org/gradle/internal/state/Managed
instanceKlass com/google/common/base/ExtraObjectsMethodsForWeb
instanceKlass org/gradle/model/internal/inspect/ValidationProblemCollector
instanceKlass org/gradle/api/internal/MutationGuards$1
instanceKlass org/gradle/api/internal/MutationGuard
instanceKlass org/gradle/api/internal/MutationGuards
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator$1
instanceKlass org/gradle/api/internal/collections/DefaultDomainObjectCollectionFactory
instanceKlass org/gradle/api/file/Directory
instanceKlass org/gradle/api/file/RegularFile
instanceKlass org/gradle/api/file/FileSystemLocation
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811be800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811be400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811be000
instanceKlass  @bci org/gradle/api/internal/file/DefaultFileCollectionFactory <init> (Lorg/gradle/internal/file/PathToFileResolver;Lorg/gradle/api/internal/tasks/TaskDependencyFactory;Lorg/gradle/api/internal/file/collections/DirectoryFileTreeFactory;Lorg/gradle/internal/Factory;Lorg/gradle/api/internal/provider/PropertyHost;Lorg/gradle/internal/nativeintegration/filesystem/FileSystem;)V 10 <appendix> argL0 ; # org/gradle/api/internal/file/DefaultFileCollectionFactory$$Lambda+0x000001d0811bba20
instanceKlass  @cpi org/gradle/api/internal/file/DefaultFileCollectionFactory 167 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0811bdc00
instanceKlass org/gradle/api/internal/file/collections/FileCollectionObservationListener
instanceKlass org/gradle/api/internal/tasks/DefaultTaskDependencyFactory
instanceKlass org/gradle/api/internal/file/collections/MinimalFileTree
instanceKlass org/gradle/api/internal/file/collections/MinimalFileCollection
instanceKlass org/gradle/api/internal/file/FileTreeInternal
instanceKlass org/gradle/api/internal/file/FileCollectionInternal
instanceKlass org/gradle/api/internal/tasks/TaskDependencyContainer
instanceKlass org/gradle/api/internal/file/DefaultFileCollectionFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811bd800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811bd400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811bd000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811bcc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811bc800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811bc400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811bc000
instanceKlass org/gradle/internal/exceptions/DiagnosticsVisitor
instanceKlass org/gradle/internal/typeconversion/ErrorHandlingNotationParser
instanceKlass org/gradle/internal/typeconversion/NotationConvertResult
instanceKlass org/gradle/internal/typeconversion/NotationConverterToNotationParserAdapter
instanceKlass org/gradle/internal/typeconversion/TypeInfo
instanceKlass org/gradle/internal/typeconversion/NotationParserBuilder
instanceKlass org/gradle/api/internal/file/FileOrUriNotationConverter
instanceKlass org/gradle/api/internal/file/AbstractFileResolver
instanceKlass org/gradle/api/internal/provider/DefaultPropertyFactory
instanceKlass  @bci org/gradle/api/internal/provider/PropertyHost <clinit> ()V 0 <appendix> argL0 ; # org/gradle/api/internal/provider/PropertyHost$$Lambda+0x000001d0811b8800
instanceKlass org/gradle/internal/state/ModelObject
instanceKlass org/gradle/api/internal/file/collections/DefaultDirectoryFileTreeFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets$PatternSetFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets
instanceKlass com/google/common/cache/LocalCache$AbstractReferenceEntry
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass com/google/common/cache/LocalCache$LoadingValueReference
instanceKlass com/google/common/cache/RemovalListener
instanceKlass com/google/common/cache/Weigher
instanceKlass com/google/common/base/Equivalence
instanceKlass java/util/function/BiPredicate
instanceKlass com/google/common/base/MoreObjects
instanceKlass com/google/common/cache/LocalCache$1
instanceKlass com/google/common/cache/ReferenceEntry
instanceKlass com/google/common/cache/LocalCache$ValueReference
instanceKlass com/google/common/cache/CacheLoader
instanceKlass com/google/common/cache/LocalCache$LocalManualCache
instanceKlass com/google/common/cache/CacheBuilder$2
instanceKlass com/google/common/cache/CacheStats
instanceKlass com/google/common/base/Suppliers$SupplierOfInstance
instanceKlass com/google/common/base/Suppliers
instanceKlass com/google/common/cache/CacheBuilder$1
instanceKlass com/google/common/cache/AbstractCache$StatsCounter
instanceKlass com/google/common/cache/LoadingCache
instanceKlass com/google/common/cache/Cache
instanceKlass com/google/common/base/Ticker
instanceKlass com/google/common/cache/CacheBuilder
instanceKlass org/gradle/cache/internal/HeapProportionalCacheSizer
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme$DefaultDeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/InstanceFactory
instanceKlass org/gradle/internal/instantiation/generator/DependencyInjectingInstantiator
instanceKlass org/gradle/internal/instantiation/DeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme
instanceKlass org/gradle/internal/instantiation/generator/ParamsMatchingConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector
instanceKlass com/google/common/collect/ImmutableMultimap$Builder
instanceKlass com/google/common/collect/Multiset
instanceKlass  @bci org/gradle/internal/instantiation/generator/AbstractClassGenerator <init> (Ljava/util/Collection;Ljava/util/Collection;Lorg/gradle/internal/instantiation/PropertyRoleAnnotationHandler;Lorg/gradle/cache/internal/CrossBuildInMemoryCache;)V 6 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/AbstractClassGenerator$$Lambda+0x000001d0811aa5b8
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedClass
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$AbstractCrossBuildInMemoryCache
instanceKlass org/gradle/internal/session/BuildSessionLifecycleListener
instanceKlass org/gradle/model/internal/asm/ClassGeneratorSuffixRegistry
instanceKlass org/gradle/api/artifacts/dsl/DependencyCollector
instanceKlass org/gradle/api/ExtensiblePolymorphicDomainObjectContainer
instanceKlass org/gradle/api/internal/rules/NamedDomainObjectFactoryRegistry
instanceKlass org/gradle/api/PolymorphicDomainObjectContainer
instanceKlass org/gradle/api/NamedDomainObjectContainer
instanceKlass org/gradle/util/Configurable
instanceKlass org/gradle/api/NamedDomainObjectSet
instanceKlass org/gradle/api/DomainObjectSet
instanceKlass org/gradle/api/NamedDomainObjectCollection
instanceKlass org/gradle/api/DomainObjectCollection
instanceKlass org/gradle/api/file/DirectoryProperty
instanceKlass org/gradle/api/file/RegularFileProperty
instanceKlass org/gradle/api/file/FileSystemLocationProperty
instanceKlass org/gradle/api/provider/Property
instanceKlass org/gradle/api/provider/MapProperty
instanceKlass org/gradle/api/provider/SetProperty
instanceKlass org/gradle/api/provider/ListProperty
instanceKlass org/gradle/api/provider/HasMultipleValues
instanceKlass org/gradle/api/provider/Provider
instanceKlass org/gradle/api/file/ConfigurableFileTree
instanceKlass org/gradle/api/file/DirectoryTree
instanceKlass org/gradle/api/file/FileTree
instanceKlass org/gradle/api/file/ConfigurableFileCollection
instanceKlass org/gradle/api/provider/SupportsConvention
instanceKlass org/gradle/api/provider/HasConfigurableValue
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InstantiationStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassInspectionVisitor
instanceKlass com/google/common/reflect/TypeCapture
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$UnclaimedPropertyHandler
instanceKlass com/google/common/collect/ListMultimap
instanceKlass com/google/common/collect/AbstractMultimap
instanceKlass com/google/common/collect/Multimap
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator
instanceKlass org/gradle/internal/service/ServiceRegistryBuilder$1
instanceKlass  @bci org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory defaultServiceRegistry ()Lorg/gradle/internal/service/ServiceRegistry; 9 <appendix> member <vmtarget> ; # org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory$$Lambda+0x000001d0811a6e48
instanceKlass org/gradle/internal/service/ServiceRegistrationAction
instanceKlass org/gradle/api/internal/tasks/properties/annotations/OutputPropertyRoleAnnotationHandler
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory$ManagedTypeFactory
instanceKlass org/gradle/internal/instantiation/InstantiationScheme
instanceKlass org/gradle/internal/instantiation/generator/ConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811a0400
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCache
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci org/gradle/internal/execution/model/annotations/ModifierAnnotationCategory annotationsOf ([Lorg/gradle/internal/execution/model/annotations/ModifierAnnotationCategory;)Lcom/google/common/collect/ImmutableSet; 24 <appendix> member <vmtarget> ; # org/gradle/internal/execution/model/annotations/ModifierAnnotationCategory$$Lambda+0x000001d0811a4e08
instanceKlass  @bci org/gradle/internal/execution/model/annotations/ModifierAnnotationCategory annotationsOf ([Lorg/gradle/internal/execution/model/annotations/ModifierAnnotationCategory;)Lcom/google/common/collect/ImmutableSet; 8 <appendix> argL0 ; # org/gradle/internal/execution/model/annotations/ModifierAnnotationCategory$$Lambda+0x000001d0811a4bd0
instanceKlass org/gradle/work/NormalizeLineEndings
instanceKlass org/gradle/api/tasks/IgnoreEmptyDirectories
instanceKlass org/gradle/api/tasks/Optional
instanceKlass org/gradle/api/tasks/PathSensitive
instanceKlass org/gradle/api/tasks/CompileClasspath
instanceKlass org/gradle/api/tasks/Classpath
instanceKlass org/gradle/api/tasks/SkipWhenEmpty
instanceKlass org/gradle/work/Incremental
instanceKlass  @bci org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices createDeleter (Lorg/gradle/internal/time/Clock;Lorg/gradle/internal/nativeintegration/filesystem/FileSystem;Lorg/gradle/internal/os/OperatingSystem;)Lorg/gradle/internal/file/Deleter; 21 <appendix> member <vmtarget> ; # org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda+0x000001d08119ed58
instanceKlass  @bci org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices createDeleter (Lorg/gradle/internal/time/Clock;Lorg/gradle/internal/nativeintegration/filesystem/FileSystem;Lorg/gradle/internal/os/OperatingSystem;)Lorg/gradle/internal/file/Deleter; 10 <appendix> member <vmtarget> ; # org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda+0x000001d08119eb38
instanceKlass org/gradle/internal/file/impl/DefaultDeleter
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0811a0000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119bc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119b800
instanceKlass org/gradle/cache/internal/scopes/DefaultCacheScopeMapping
instanceKlass org/gradle/cache/internal/CacheScopeMapping
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119b400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119b000
instanceKlass org/gradle/cache/CacheBuilder
instanceKlass org/gradle/cache/internal/DefaultUnscopedCacheBuilderFactory
instanceKlass org/gradle/cache/internal/ReferencablePersistentCache
instanceKlass org/gradle/cache/PersistentCache
instanceKlass org/gradle/cache/HasCleanupAction
instanceKlass org/gradle/cache/CleanableStore
instanceKlass org/gradle/cache/ExclusiveCacheAccessCoordinator
instanceKlass org/gradle/cache/internal/DefaultCacheFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08119a000
instanceKlass  @bci org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices createBuildOperationRunner (Lorg/gradle/internal/time/Clock;Lorg/gradle/internal/operations/CurrentBuildOperationRef;Lorg/gradle/internal/logging/progress/ProgressLoggerFactory;Lorg/gradle/internal/operations/BuildOperationIdFactory;Lorg/gradle/internal/operations/BuildOperationListenerManager;)Lorg/gradle/internal/operations/BuildOperationRunner; 31 <appendix> member <vmtarget> ; # org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda+0x000001d08119d210
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListenerFactory
instanceKlass  @bci org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices createBuildOperationRunner (Lorg/gradle/internal/time/Clock;Lorg/gradle/internal/operations/CurrentBuildOperationRef;Lorg/gradle/internal/logging/progress/ProgressLoggerFactory;Lorg/gradle/internal/operations/BuildOperationIdFactory;Lorg/gradle/internal/operations/BuildOperationListenerManager;)Lorg/gradle/internal/operations/BuildOperationRunner; 20 <appendix> member <vmtarget> ; # org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda+0x000001d08119cdf8
instanceKlass  @cpi org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices 211 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d081199c00
instanceKlass org/gradle/internal/operations/BuildOperationTimeSupplier
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext
instanceKlass org/gradle/internal/operations/BuildOperationContext
instanceKlass org/gradle/internal/operations/BuildOperation
instanceKlass org/gradle/internal/operations/BuildOperationWorker
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081199800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081199400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081199000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081198c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081198800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081198400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081198000
instanceKlass org/gradle/internal/logging/services/ProgressLoggingBridge
instanceKlass org/gradle/internal/logging/progress/ProgressLogger
instanceKlass org/gradle/internal/logging/progress/DefaultProgressLoggerFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081195c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081195800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081195400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081195000
instanceKlass org/gradle/internal/operations/DefaultBuildOperationIdFactory
instanceKlass  @bci org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1 createGradleUserHomeDirProvider ()Lorg/gradle/initialization/GradleUserHomeDirProvider; 4 <appendix> member <vmtarget> ; # org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1$$Lambda+0x000001d081197130
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d081194c00
instanceKlass org/gradle/cache/internal/UsedGradleVersions
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupServices
instanceKlass org/gradle/api/internal/cache/CacheConfigurationsInternal
instanceKlass org/gradle/api/cache/CacheConfigurations
instanceKlass org/gradle/cache/internal/scopes/AbstractScopedCacheBuilderFactory
instanceKlass org/gradle/initialization/layout/GlobalCacheDir
instanceKlass org/gradle/cache/internal/LegacyCacheCleanupEnablement
instanceKlass org/gradle/internal/vfs/FileSystemAccess
instanceKlass org/gradle/cache/internal/DefaultGeneratedGradleJarCache
instanceKlass org/gradle/cache/internal/GeneratedGradleJarCache
instanceKlass org/gradle/groovy/scripts/internal/CrossBuildInMemoryCachingScriptClassCache
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistryListenerManager
instanceKlass org/gradle/cache/scopes/GlobalScopedCacheBuilderFactory
instanceKlass org/gradle/execution/plan/ToPlannedNodeConverterRegistry
instanceKlass org/gradle/process/internal/worker/child/WorkerProcessClassPathProvider
instanceKlass org/gradle/internal/classloader/ClasspathHasher
instanceKlass org/gradle/internal/jvm/JavaModuleDetector
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1
instanceKlass org/gradle/internal/session/BuildSessionState
instanceKlass org/gradle/internal/buildoption/DefaultInternalOptions
instanceKlass groovy/json/DefaultJsonGenerator
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace$JsonThrowableConverter
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace$JsonClassConverter
instanceKlass groovy/json/JsonGenerator
instanceKlass groovy/json/JsonGenerator$Options
instanceKlass org/gradle/internal/buildoption/StringInternalOption
instanceKlass groovy/json/JsonGenerator$Converter
instanceKlass org/gradle/internal/buildoption/InternalOptions
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081194800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081194400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081194000
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager
instanceKlass org/gradle/configuration/internal/DefaultDynamicCallContextTracker
instanceKlass org/gradle/configuration/internal/DynamicCallContextTracker
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLeaseCompletion
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLease
instanceKlass org/gradle/internal/resources/ResourceLock
instanceKlass com/google/common/base/Supplier
instanceKlass org/gradle/internal/work/Synchronizer
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService
instanceKlass org/gradle/internal/work/ProjectParallelExecutionController
instanceKlass org/gradle/internal/resources/ResourceLockState
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService
instanceKlass org/gradle/internal/resources/ResourceLockCoordinationService
instanceKlass org/gradle/internal/work/WorkerLeaseService
instanceKlass org/gradle/internal/work/WorkerThreadRegistry
instanceKlass org/gradle/internal/resources/ProjectLeaseRegistry
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry
instanceKlass org/gradle/internal/operations/logging/LoggingBuildOperationProgressBroadcaster
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace
instanceKlass org/gradle/internal/service/scopes/CrossBuildSessionParameters
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationValve
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationListenerRegistrar
instanceKlass org/gradle/internal/work/WorkerLimits
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator
instanceKlass org/gradle/internal/operations/BuildOperationExecutor
instanceKlass org/gradle/internal/operations/BuildOperationQueueFactory
instanceKlass org/gradle/internal/code/UserCodeApplicationContext
instanceKlass org/gradle/configuration/internal/ListenerBuildOperationDecorator
instanceKlass org/gradle/internal/service/scopes/CoreCrossBuildSessionServices
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081188c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081188800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081188400
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectionService
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectingVisitor
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass org/gradle/internal/session/CrossBuildSessionState$Services
instanceKlass org/gradle/internal/session/CrossBuildSessionState
instanceKlass org/gradle/internal/buildprocess/execution/BuildSessionLifecycleBuildActionExecutor$ActionImpl
instanceKlass  @bci org/gradle/internal/buildprocess/execution/StartParamsValidatingActionExecutor execute (Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult; 127 <appendix> member <vmtarget> ; # org/gradle/internal/buildprocess/execution/StartParamsValidatingActionExecutor$$Lambda+0x000001d08118ea48
instanceKlass  @bci org/gradle/internal/buildprocess/execution/StartParamsValidatingActionExecutor execute (Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult; 15 <appendix> member <vmtarget> ; # org/gradle/internal/buildprocess/execution/StartParamsValidatingActionExecutor$$Lambda+0x000001d08118e828
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$3
instanceKlass org/gradle/internal/logging/sink/ProgressLogEventGenerator
instanceKlass org/gradle/internal/logging/console/BuildLogLevelFilterRenderer
instanceKlass org/gradle/launcher/daemon/server/exec/ExecuteBuild$1
instanceKlass org/gradle/initialization/DefaultBuildRequestContext
instanceKlass org/gradle/initialization/DefaultBuildRequestMetaData
instanceKlass org/gradle/configuration/DefaultBuildClientMetaData
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection$1
instanceKlass org/gradle/internal/featurelifecycle/LoggingIncubatingFeatureHandler
instanceKlass org/gradle/util/internal/IncubationLogger
instanceKlass org/gradle/internal/daemon/clientinput/ClientInputForwarder$2
instanceKlass org/gradle/internal/daemon/clientinput/ClientInputForwarder$1
instanceKlass  @bci org/gradle/launcher/daemon/server/exec/ForwardClientInput execute (Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V 5 <appendix> member <vmtarget> ; # org/gradle/launcher/daemon/server/exec/ForwardClientInput$$Lambda+0x000001d08118c200
instanceKlass java/math/MathContext
instanceKlass org/gradle/internal/util/NumberUtil
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher$1
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass com/google/common/collect/AbstractIterator$1
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass  @bci org/gradle/launcher/daemon/server/DaemonStateCoordinator runCommand (Ljava/lang/Runnable;Ljava/lang/String;)V 11 <appendix> member <vmtarget> ; # org/gradle/launcher/daemon/server/DaemonStateCoordinator$$Lambda+0x000001d081186af0
instanceKlass  @cpi org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory 194 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d081188000
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$5
instanceKlass jdk/internal/math/MathUtils
instanceKlass jdk/internal/math/DoubleToDecimal
instanceKlass org/gradle/launcher/daemon/server/exec/StartBuildOrRespondWithBusy$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue$1
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel$1
instanceKlass com/google/common/collect/Platform
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandExecution
instanceKlass org/gradle/launcher/exec/DefaultBuildActionParameters
instanceKlass org/gradle/configuration/GradleLauncherMetaData
instanceKlass com/google/common/collect/AbstractMapEntry
instanceKlass com/google/common/collect/ImmutableMap$Builder
instanceKlass  @bci org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer read (Lorg/gradle/internal/serialize/Decoder;)Lorg/gradle/api/internal/StartParameterInternal; 153 <appendix> member <vmtarget> ; # org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer$$Lambda+0x000001d081181158
instanceKlass org/gradle/internal/deprecation/DeprecationMessageBuilder$WithDocumentation
instanceKlass org/gradle/internal/deprecation/Documentation
instanceKlass org/gradle/internal/deprecation/DeprecationTimeline
instanceKlass org/gradle/internal/deprecation/Documentation$AbstractBuilder
instanceKlass org/gradle/internal/deprecation/DeprecationLogger$4
instanceKlass org/gradle/internal/problems/NoOpProblemDiagnosticsFactory$2
instanceKlass org/gradle/internal/problems/NoOpProblemDiagnosticsFactory$1
instanceKlass org/gradle/problems/buildtree/ProblemStream
instanceKlass org/gradle/problems/ProblemDiagnostics
instanceKlass org/gradle/internal/problems/NoOpProblemDiagnosticsFactory
instanceKlass org/gradle/problems/buildtree/ProblemStream$StackTraceTransformer
instanceKlass org/gradle/internal/featurelifecycle/LoggingDeprecatedFeatureHandler
instanceKlass org/gradle/internal/featurelifecycle/FeatureHandler
instanceKlass org/gradle/internal/deprecation/DeprecationMessageBuilder
instanceKlass org/gradle/internal/deprecation/DeprecationLogger
instanceKlass  @bci org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer read (Lorg/gradle/internal/serialize/Decoder;)Lorg/gradle/api/internal/StartParameterInternal; 125 <appendix> member <vmtarget> ; # org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer$$Lambda+0x000001d08113ece8
instanceKlass org/gradle/internal/deprecation/DeprecationLogger$ThrowingRunnable
instanceKlass com/google/common/collect/Lists
instanceKlass org/gradle/internal/DefaultTaskExecutionRequest
instanceKlass org/gradle/internal/buildoption/Option$Value
instanceKlass org/gradle/internal/RunDefaultTasksExecutionRequest
instanceKlass org/gradle/TaskExecutionRequest
instanceKlass org/gradle/api/launcher/cli/WelcomeMessageConfiguration
instanceKlass org/gradle/internal/concurrent/DefaultParallelismConfiguration
instanceKlass org/gradle/internal/logging/DefaultLoggingConfiguration
instanceKlass org/gradle/initialization/BuildLayoutParameters
instanceKlass java/nio/channels/spi/AbstractSelector$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$ReceiveQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$DisconnectQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection
instanceKlass org/gradle/launcher/daemon/server/api/DaemonConnection
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler$ConnectionWorker
instanceKlass org/gradle/launcher/daemon/server/SynchronizedDispatchConnection
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$2
instanceKlass org/gradle/internal/serialize/PositionAwareEncoder
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress$Serializer
instanceKlass org/gradle/internal/io/BufferCaster
instanceKlass java/lang/invoke/ConstantBootstraps
instanceKlass java/nio/channels/SelectionKey
instanceKlass java/nio/BufferMismatch
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass com/sun/security/sasl/Provider$1
instanceKlass  @bci sun/security/provider/certpath/ldap/JdkLDAP <init> ()V 15 <appendix> member <vmtarget> ; # sun/security/provider/certpath/ldap/JdkLDAP$$Lambda+0x000001d081168510
instanceKlass sun/security/smartcardio/SunPCSC$1
instanceKlass  @bci sun/security/jgss/SunProvider <init> ()V 15 <appendix> member <vmtarget> ; # sun/security/jgss/SunProvider$$Lambda+0x000001d081137d98
instanceKlass org/jcp/xml/dsig/internal/dom/XMLDSigRI$2
instanceKlass org/jcp/xml/dsig/internal/dom/XMLDSigRI$1
instanceKlass  @bci sun/security/pkcs11/SunPKCS11 register (Lsun/security/pkcs11/SunPKCS11$Descriptor;)V 27 <appendix> argL0 ; # sun/security/pkcs11/SunPKCS11$$Lambda+0x000001d081136948
instanceKlass sun/security/pkcs11/SunPKCS11$Descriptor
instanceKlass javax/security/auth/callback/CallbackHandler
instanceKlass javax/security/auth/Subject
instanceKlass  @bci sun/security/ssl/SunJSSE registerAlgorithms ()V 1 <appendix> member <vmtarget> ; # sun/security/ssl/SunJSSE$$Lambda+0x000001d0811669a0
instanceKlass java/security/spec/ECFieldF2m
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerEncoder
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/ec/SunEC$1
instanceKlass sun/security/mscapi/SunMSCAPI$2
instanceKlass sun/security/mscapi/SunMSCAPI$1
instanceKlass com/sun/security/sasl/gsskerb/JdkSASL$1
instanceKlass sun/security/jca/ProviderConfig$ProviderLoader
instanceKlass sun/security/jca/ProviderConfig$3
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass sun/net/NetProperties$1
instanceKlass sun/net/NetProperties
instanceKlass  @bci sun/nio/ch/UnixDomainSocketsUtil getTempDir ()Ljava/lang/String; 0 <appendix> argL0 ; # sun/nio/ch/UnixDomainSocketsUtil$$Lambda+0x000001d081162b60
instanceKlass sun/nio/ch/UnixDomainSocketsUtil
instanceKlass sun/nio/ch/UnixDomainSockets
instanceKlass sun/nio/ch/PipeImpl$Initializer$LoopbackConnector
instanceKlass sun/nio/ch/PipeImpl$Initializer
instanceKlass java/nio/channels/Pipe
instanceKlass sun/nio/ch/WEPoll
instanceKlass sun/nio/ch/Util$2
instanceKlass sun/nio/ch/Util
instanceKlass java/nio/channels/Selector
instanceKlass org/gradle/internal/remote/internal/KryoBackedMessageSerializer
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator$1
instanceKlass org/gradle/internal/event/DefaultListenerManager$ExclusiveEventBroadcast$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection
instanceKlass org/gradle/launcher/daemon/server/Daemon$DefaultDaemonExpirationListener
instanceKlass org/gradle/internal/serialize/ObjectWriter
instanceKlass org/gradle/launcher/daemon/server/Daemon$DaemonExpirationPeriodicCheck
instanceKlass org/gradle/internal/serialize/ObjectReader
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter
instanceKlass org/gradle/internal/serialize/StatefulSerializer
instanceKlass org/gradle/internal/serialize/Serializers
instanceKlass org/gradle/launcher/daemon/server/expiry/AnyDaemonExpirationStrategy
instanceKlass org/gradle/internal/remote/internal/RemoteConnection
instanceKlass org/gradle/internal/remote/internal/Connection
instanceKlass org/gradle/internal/dispatch/Receive
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUnavailableExpirationStrategy
instanceKlass org/gradle/internal/remote/internal/MessageSerializer
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnectCompletion
instanceKlass org/gradle/internal/remote/internal/ConnectCompletion
instanceKlass java/net/Socket
instanceKlass  @bci org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl addSetMethod (Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata;Ljava/lang/reflect/Method;)V 66 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d08112d000
instanceKlass  @bci sun/reflect/annotation/AnnotationParser parseEnumArray (ILjava/lang/Class;Ljava/nio/ByteBuffer;Ljdk/internal/reflect/ConstantPool;Ljava/lang/Class;)Ljava/lang/Object; 16 <appendix> member <vmtarget> ; # sun/reflect/annotation/AnnotationParser$$Lambda+0x000001d08115efa8
instanceKlass sun/nio/ch/IOStatus
instanceKlass org/gradle/internal/reflect/JavaReflectionUtil
instanceKlass org/gradle/internal/service/scopes/ParallelListener
instanceKlass org/gradle/internal/event/DefaultListenerManager$ListenerDetails
instanceKlass org/gradle/launcher/daemon/server/health/LowMemoryDaemonExpirationStrategy
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatusListener
instanceKlass org/gradle/launcher/daemon/server/NotMostRecentlyUsedDaemonExpirationStrategy
instanceKlass com/google/common/base/Functions$ConstantFunction
instanceKlass com/google/common/base/Functions
instanceKlass org/gradle/launcher/daemon/server/DaemonIdleTimeoutExpirationStrategy
instanceKlass org/gradle/launcher/daemon/toolchain/DaemonJvmCriteria$JavaHome
instanceKlass org/gradle/launcher/daemon/context/DaemonRequestContext
instanceKlass org/gradle/launcher/daemon/context/DaemonCompatibilitySpec
instanceKlass org/gradle/api/internal/specs/ExplainingSpec
instanceKlass org/gradle/launcher/daemon/server/CompatibleDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/expiry/AllDaemonExpirationStrategy
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08112cc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08112c800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08112c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08112c000
instanceKlass org/gradle/internal/stream/EncodedStream
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonStartupCommunication
instanceKlass  @bci org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock close ()V 32 <appendix> member <vmtarget> ; # org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$$Lambda+0x000001d081128a48
instanceKlass  @bci org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock close ()V 21 <appendix> member <vmtarget> ; # org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$$Lambda+0x000001d081128828
instanceKlass  @bci org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock close ()V 10 <appendix> member <vmtarget> ; # org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$$Lambda+0x000001d081128608
instanceKlass java/io/FileOutputStream$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress
instanceKlass org/gradle/internal/serialize/AbstractEncoder
instanceKlass org/gradle/internal/serialize/FlushableEncoder
instanceKlass  @bci org/gradle/launcher/daemon/registry/DaemonRegistryContent removeInfo (I)V 10 <appendix> member <vmtarget> ; # org/gradle/launcher/daemon/registry/DaemonRegistryContent$$Lambda+0x000001d081123d70
instanceKlass  @cpi org/gradle/launcher/daemon/registry/DaemonRegistryContent 118 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d081121400
instanceKlass org/gradle/launcher/daemon/registry/DaemonStopEvent$Serializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonStopEvent
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo$Serializer
instanceKlass org/gradle/cache/internal/filelock/LockInfo
instanceKlass  @bci org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock lockInformationRegion (Lorg/gradle/cache/FileLockManager$LockMode;Lorg/gradle/internal/time/ExponentialBackoff;)Lorg/gradle/cache/internal/filelock/FileLockOutcome; 3 <appendix> member <vmtarget> ; # org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$$Lambda+0x000001d081122f00
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer$SequenceNumberLockState
instanceKlass org/gradle/internal/time/ExponentialBackoff$Result
instanceKlass org/gradle/cache/internal/filelock/FileLockOutcome
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$1
instanceKlass org/gradle/internal/time/ExponentialBackoff
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$AwaitableFileLockReleasedSignal
instanceKlass org/gradle/cache/FileLockReleasedSignal
instanceKlass org/gradle/cache/internal/filelock/LockInfoSerializer
instanceKlass org/gradle/cache/internal/filelock/LockInfoAccess
instanceKlass org/gradle/cache/internal/filelock/LockStateAccess
instanceKlass org/gradle/cache/internal/filelock/LockFileAccess
instanceKlass org/gradle/cache/internal/filelock/LockState
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer
instanceKlass java/nio/file/FileVisitor
instanceKlass org/apache/commons/io/filefilter/IOFileFilter
instanceKlass java/nio/file/PathMatcher
instanceKlass org/apache/commons/io/file/PathFilter
instanceKlass java/io/FilenameFilter
instanceKlass org/apache/commons/io/FileUtils
instanceKlass org/gradle/internal/time/ExponentialBackoff$Query
instanceKlass org/gradle/cache/FileLock$State
instanceKlass org/gradle/cache/internal/filelock/LockStateSerializer
instanceKlass sun/nio/ch/ExtendedSocketOption$1
instanceKlass sun/nio/ch/ExtendedSocketOption
instanceKlass sun/nio/ch/OptionKey
instanceKlass sun/nio/ch/SocketOptionRegistry$LazyInitialization
instanceKlass sun/nio/ch/SocketOptionRegistry$RegistryKey
instanceKlass sun/nio/ch/SocketOptionRegistry
instanceKlass sun/nio/ch/DatagramChannelImpl$DefaultOptionsHolder
instanceKlass java/net/StandardSocketOptions$StdSocketOption
instanceKlass java/net/StandardSocketOptions
instanceKlass  @bci sun/nio/ch/DatagramSocketAdaptor$DatagramSockets <clinit> ()V 0 <appendix> argL0 ; # sun/nio/ch/DatagramSocketAdaptor$DatagramSockets$$Lambda+0x000001d08115c210
instanceKlass sun/nio/ch/DatagramSocketAdaptor$DatagramSockets
instanceKlass  @bci sun/nio/ch/DatagramChannelImpl releaserFor (Ljava/io/FileDescriptor;[Lsun/nio/ch/NativeSocketAddress;)Ljava/lang/Runnable; 2 <appendix> member <vmtarget> ; # sun/nio/ch/DatagramChannelImpl$$Lambda+0x000001d08115ba28
instanceKlass sun/nio/ch/NativeSocketAddress
instanceKlass sun/net/ResourceManager
instanceKlass java/nio/channels/MulticastChannel
instanceKlass java/net/DatagramSocket
instanceKlass org/gradle/cache/internal/locklistener/FileLockCommunicator
instanceKlass org/gradle/cache/internal/filelock/DefaultLockOptions
instanceKlass org/gradle/cache/internal/FileBackedObjectHolder$1Updater
instanceKlass  @bci org/gradle/cache/internal/FileIntegrityViolationSuppressingObjectHolderDecorator update (Lorg/gradle/cache/ObjectHolder$UpdateAction;)Ljava/lang/Object; 4 <appendix> member <vmtarget> ; # org/gradle/cache/internal/FileIntegrityViolationSuppressingObjectHolderDecorator$$Lambda+0x000001d08111f7a0
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$8
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo
instanceKlass org/gradle/launcher/daemon/context/DaemonConnectDetails
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo$TZCanonicalIDMapHolder
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass  @bci java/time/ZoneOffset ofTotalSeconds (I)Ljava/time/ZoneOffset; 37 <appendix> argL0 ; # java/time/ZoneOffset$$Lambda+0x80000000e
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass java/time/ZoneId
instanceKlass sun/util/resources/provider/NonBaseLocaleDataMetaInfo
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter createSupportedLocaleString (Ljava/lang/String;)Ljava/lang/String; 6 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x000001d081155eb0
instanceKlass sun/util/locale/provider/BaseLocaleDataMetaInfo
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getTimeZoneNameProvider ()Ljava/util/spi/TimeZoneNameProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x000001d081155a40
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getTimeZoneNameProvider ()Ljava/util/spi/TimeZoneNameProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x000001d0811553b0
instanceKlass sun/util/locale/provider/TimeZoneNameUtility$TimeZoneNameGetter
instanceKlass sun/util/locale/provider/TimeZoneNameUtility
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$1
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$Receiver
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddress
instanceKlass org/gradle/internal/remote/internal/inet/InetEndpoint
instanceKlass java/util/UUID$Holder
instanceKlass java/util/UUID
instanceKlass sun/net/NetHooks
instanceKlass java/net/InetSocketAddress$InetSocketAddressHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081121000
instanceKlass org/gradle/internal/remote/internal/inet/InetAddresses
instanceKlass java/net/SocketImpl
instanceKlass java/net/SocketOptions
instanceKlass  @bci sun/nio/ch/ServerSocketAdaptor create (Lsun/nio/ch/ServerSocketChannelImpl;)Ljava/net/ServerSocket; 1 <appendix> member <vmtarget> ; # sun/nio/ch/ServerSocketAdaptor$$Lambda+0x000001d0811530a8
instanceKlass java/net/ServerSocket
instanceKlass jdk/net/ExtendedSocketOptions$2
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions
instanceKlass jdk/net/ExtendedSocketOptions$ExtSocketOption
instanceKlass java/net/SocketOption
instanceKlass jdk/net/ExtendedSocketOptions
instanceKlass sun/net/ext/ExtendedSocketOptions
instanceKlass sun/nio/ch/Net$1
instanceKlass java/net/ProtocolFamily
instanceKlass sun/nio/ch/Net
instanceKlass sun/nio/ch/SelChImpl
instanceKlass  @bci sun/nio/ch/DefaultSelectorProvider <clinit> ()V 0 <appendix> argL0 ; # sun/nio/ch/DefaultSelectorProvider$$Lambda+0x000001d08114fbf0
instanceKlass sun/nio/ch/DefaultSelectorProvider
instanceKlass  @bci java/nio/channels/spi/SelectorProvider$Holder provider ()Ljava/nio/channels/spi/SelectorProvider; 0 <appendix> argL0 ; # java/nio/channels/spi/SelectorProvider$Holder$$Lambda+0x000001d08114f338
instanceKlass java/nio/channels/spi/SelectorProvider$Holder
instanceKlass java/nio/channels/spi/SelectorProvider
instanceKlass java/nio/channels/NetworkChannel
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector$1
instanceKlass org/gradle/launcher/daemon/server/Daemon$5
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler
instanceKlass java/util/LinkedList$Node
instanceKlass org/gradle/initialization/DefaultBuildCancellationToken
instanceKlass org/gradle/initialization/BuildCancellationToken
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator
instanceKlass org/gradle/launcher/daemon/server/Daemon$4
instanceKlass org/gradle/launcher/daemon/server/Daemon$3
instanceKlass org/gradle/launcher/daemon/server/Daemon$2
instanceKlass org/gradle/launcher/daemon/server/Daemon$1
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUpdater
instanceKlass sun/security/provider/AbstractDrbg$NonceProvider
instanceKlass  @bci sun/security/provider/AbstractDrbg$SeederHolder <clinit> ()V 42 <appendix> member <vmtarget> ; # sun/security/provider/AbstractDrbg$SeederHolder$$Lambda+0x000001d08114d8e8
instanceKlass  @cpi sun/security/provider/AbstractDrbg$SeederHolder 91 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d081120c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d081120800
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/DefaultInterface
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InetAddress$PlatformResolver
instanceKlass java/net/spi/InetAddressResolver
instanceKlass java/net/spi/InetAddressResolver$LookupPolicy
instanceKlass java/net/Inet4AddressImpl
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass jdk/internal/access/JavaNetInetAddressAccess
instanceKlass java/net/InetAddress
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/AbstractDrbg$SeederHolder
instanceKlass java/security/DrbgParameters$NextBytes
instanceKlass  @bci sun/security/provider/AbstractDrbg <clinit> ()V 12 <appendix> argL0 ; # sun/security/provider/AbstractDrbg$$Lambda+0x000001d081149970
instanceKlass  @cpi sun/security/provider/AbstractDrbg 383 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d081120400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d081120000
instanceKlass sun/security/provider/EntropySource
instanceKlass sun/security/provider/AbstractDrbg
instanceKlass java/security/DrbgParameters$Instantiation
instanceKlass java/security/DrbgParameters
instanceKlass sun/security/provider/MoreDrbgParameters
instanceKlass  @bci sun/security/provider/DRBG <init> (Ljava/security/SecureRandomParameters;)V 26 <appendix> argL0 ; # sun/security/provider/DRBG$$Lambda+0x000001d081148460
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/SecureRandomParameters
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass com/google/common/base/Joiner
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonCommandExecuter
instanceKlass org/gradle/internal/remote/ConnectionAcceptor
instanceKlass org/gradle/internal/remote/Address
instanceKlass java/net/SocketAddress
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$OutputMessageSerializer
instanceKlass org/gradle/internal/logging/serializer/LogLevelChangeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressCompleteEventSerializer
instanceKlass org/gradle/internal/operations/BuildOperationMetadata
instanceKlass org/gradle/internal/logging/serializer/ProgressStartEventSerializer
instanceKlass org/gradle/internal/logging/serializer/SpanSerializer
instanceKlass org/gradle/internal/logging/serializer/StyledTextOutputEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ReadStdInEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputResumeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/SelectOptionPromptEventSerializer
instanceKlass org/gradle/internal/logging/serializer/IntQuestionPromptEventSerializer
instanceKlass org/gradle/internal/logging/serializer/TextQuestionPromptEventSerializer
instanceKlass org/gradle/internal/logging/serializer/BooleanQuestionPromptEventSerializer
instanceKlass org/gradle/internal/logging/serializer/YesNoQuestionPromptEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputRequestEventSerializer
instanceKlass org/gradle/internal/logging/serializer/LogEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CloseInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$UserResponseSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$ForwardInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FinishedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$SuccessSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FailureSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildStartedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$DaemonUnavailableSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CancelSerializer
instanceKlass org/gradle/launcher/exec/BuildActionParameters
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildActionParametersSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector
instanceKlass org/gradle/launcher/daemon/server/IncomingConnectionHandler
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStateControl
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081116400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081116000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081115c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081115800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081115400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081115000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081114c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081114800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081114400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d081114000
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddressSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent$Serializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent
instanceKlass org/gradle/cache/LockOptions
instanceKlass org/gradle/cache/internal/AbstractFileAccess
instanceKlass org/gradle/internal/serialize/Encoder
instanceKlass org/gradle/cache/internal/FileBackedObjectHolder
instanceKlass org/gradle/cache/internal/FileIntegrityViolationSuppressingObjectHolderDecorator
instanceKlass org/gradle/cache/ObjectHolder$UpdateAction
instanceKlass org/gradle/cache/ObjectHolder
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry
instanceKlass  @bci org/gradle/cache/internal/CacheAccessSerializer get (Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object; 7 <appendix> member <vmtarget> ; # org/gradle/cache/internal/CacheAccessSerializer$$Lambda+0x000001d08110fd40
instanceKlass  @bci org/gradle/cache/Cache get (Ljava/lang/Object;Ljava/util/function/Supplier;)Ljava/lang/Object; 3 <appendix> member <vmtarget> ; # org/gradle/cache/Cache$$Lambda+0x000001d08110fb00
instanceKlass  @bci org/gradle/launcher/daemon/registry/DaemonRegistryServices createDaemonRegistry (Lorg/gradle/launcher/daemon/registry/DaemonDir;Lorg/gradle/cache/FileLockManager;Lorg/gradle/internal/file/Chmod;)Lorg/gradle/launcher/daemon/registry/DaemonRegistry; 16 <appendix> member <vmtarget> ; # org/gradle/launcher/daemon/registry/DaemonRegistryServices$$Lambda+0x000001d08110f8e0
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08110ac00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08110a800
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FallbackStat
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/EmptyChmod
instanceKlass org/gradle/internal/nativeintegration/filesystem/jdk7/Jdk7Symlink
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08110a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08110a000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081109c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081109800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081109400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081109000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081108c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d081108800
instanceKlass net/rubygrapefruit/platform/file/PosixFileInfo
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$BrokenService
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/UnavailablePosixFiles
instanceKlass net/rubygrapefruit/platform/memory/WindowsMemory
instanceKlass net/rubygrapefruit/platform/terminal/Terminals
instanceKlass org/gradle/api/internal/file/temp/GradleUserHomeTemporaryFileProvider$1
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$2
instanceKlass net/rubygrapefruit/platform/file/WindowsFileInfo
instanceKlass net/rubygrapefruit/platform/file/FileInfo
instanceKlass net/rubygrapefruit/platform/internal/DirList
instanceKlass net/rubygrapefruit/platform/internal/AbstractFiles
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/NativePlatformBackedFileMetadataAccessor
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$RandomLongIdGenerator
instanceKlass org/gradle/cache/internal/DefaultProcessMetaDataProvider
instanceKlass org/gradle/internal/time/ExponentialBackoff$Signal
instanceKlass org/gradle/cache/FileLock
instanceKlass org/gradle/cache/FileAccess
instanceKlass java/util/function/LongSupplier
instanceKlass org/gradle/cache/internal/DefaultFileLockManager
instanceKlass org/gradle/internal/service/scopes/BasicGlobalScopeServices$1
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081108400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081108000
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$TypeInfo
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Iter
instanceKlass org/gradle/tooling/internal/protocol/test/InternalTaskSpec
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$InternalTaskSpecSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$TestExecutionRequestActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedPhasedActionSerializer
instanceKlass org/gradle/tooling/internal/provider/serialization/SerializedPayloadSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildEventSubscriptionsSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildModelActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/SubscribableBuildAction
instanceKlass java/util/concurrent/atomic/Striped64$1
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$InstanceBasedSerializerFactory
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ValueSerializer
instanceKlass org/gradle/internal/serialize/AbstractSerializer
instanceKlass org/gradle/internal/serialize/BaseSerializerFactory
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
instanceKlass org/gradle/internal/serialize/AbstractCollectionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$NullableFileSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ExecuteBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/ExecuteBuildAction
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$HierarchySerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$StrictSerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$SerializerClassMatcherStrategy
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Node
instanceKlass java/util/concurrent/ConcurrentSkipListMap$Index
instanceKlass java/util/concurrent/ConcurrentNavigableMap
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$1
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$SerializerFactory
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry
instanceKlass org/gradle/internal/serialize/SerializerRegistry
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer
instanceKlass org/gradle/initialization/BuildRequestContext
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection
instanceKlass org/gradle/launcher/daemon/server/exec/ResetDeprecationLogger
instanceKlass org/gradle/launcher/daemon/server/exec/RequestStopIfSingleUsedDaemon
instanceKlass org/gradle/internal/daemon/clientinput/StdinHandler
instanceKlass org/gradle/internal/daemon/clientinput/ClientInputForwarder
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput
instanceKlass org/gradle/launcher/daemon/server/exec/LogAndCheckHealth
instanceKlass org/gradle/launcher/daemon/server/exec/ReturnResult
instanceKlass java/util/concurrent/LinkedTransferQueue$DualNode
instanceKlass java/util/concurrent/TransferQueue
instanceKlass java/util/concurrent/ForkJoinTask
instanceKlass java/util/concurrent/CompletableFuture$AsynchronousCompletionTask
instanceKlass java/util/concurrent/ForkJoinPool$2
instanceKlass jdk/internal/access/JavaUtilConcurrentFJPAccess
instanceKlass java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/ForkJoinPool$WorkQueue
instanceKlass java/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory
instanceKlass java/util/concurrent/CompletableFuture$AltResult
instanceKlass java/util/concurrent/CompletableFuture
instanceKlass java/util/concurrent/CompletionStage
instanceKlass org/gradle/launcher/daemon/server/exec/BuildCommandOnly
instanceKlass org/gradle/launcher/daemon/server/api/HandleReportStatus
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel
instanceKlass org/gradle/launcher/daemon/server/api/HandleInvalidateVirtualFileSystem
instanceKlass org/gradle/launcher/daemon/protocol/Message
instanceKlass org/gradle/launcher/daemon/server/api/HandleStop
instanceKlass org/gradle/launcher/daemon/diagnostics/DaemonDiagnostics
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810fa800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810fa400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810fa000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810f9c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f9800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810f9400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f9000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810f8c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f8800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810f8400
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f8000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f7c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f7800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f7400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f7000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f6c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f6800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f6400
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000001d0810f6000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810f5c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810f5800
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationResult
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationListener
instanceKlass java/lang/Thread$ThreadNumbering
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/Executors
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass org/gradle/internal/concurrent/AbstractManagedExecutor$1
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionCheck
instanceKlass  @bci org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor pollForValues ()V 4 <appendix> member <vmtarget> ; # org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor$$Lambda+0x000001d0810f2988
instanceKlass java/util/concurrent/BlockingDeque
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultSlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/SlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionInfo
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy$CatchAndRecordFailures
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass org/gradle/internal/concurrent/ThreadFactoryImpl
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; 92 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001d0810f5400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f5000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f4c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f4800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810f4400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810f4000
instanceKlass org/gradle/internal/concurrent/ManagedThreadPoolExecutor
instanceKlass org/gradle/internal/concurrent/ManagedScheduledExecutor
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass org/gradle/internal/concurrent/ManagedExecutor
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executor
instanceKlass org/gradle/internal/concurrent/AsyncStoppable
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy
instanceKlass org/gradle/internal/concurrent/DefaultExecutorFactory
instanceKlass  @bci org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy determineGcStrategy ()Lorg/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy; 52 <appendix> argL0 ; # org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$$Lambda+0x000001d0810efa20
instanceKlass sun/management/Sensor
instanceKlass sun/management/MemoryPoolImpl
instanceKlass java/lang/management/MemoryPoolMXBean
instanceKlass  @bci org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy determineGcStrategy ()Lorg/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy; 16 <appendix> member <vmtarget> ; # org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$$Lambda+0x000001d0810ef800
instanceKlass  @bci org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy determineGcStrategy ()Lorg/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy; 3 <appendix> argL0 ; # org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$$Lambda+0x000001d0810ebd08
instanceKlass  @cpi org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy 147 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0810ef400
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 63 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x000001d081073928
instanceKlass  @bci sun/management/spi/PlatformMBeanProvider$PlatformComponent getMBeans (Ljava/lang/Class;)Ljava/util/List; 47 <appendix> member <vmtarget> ; # sun/management/spi/PlatformMBeanProvider$PlatformComponent$$Lambda+0x000001d0810736d8
instanceKlass com/sun/jmx/mbeanserver/Util
instanceKlass javax/management/ObjectName$Property
instanceKlass com/sun/jmx/mbeanserver/GetPropertyAction
instanceKlass javax/management/ObjectName
instanceKlass javax/management/QueryExp
instanceKlass  @bci sun/management/Util newObjectName (Ljava/lang/String;Ljava/lang/String;)Ljavax/management/ObjectName; 2 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001d0810ef000
instanceKlass java/lang/invoke/LambdaFormEditor$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810eec00
instanceKlass  @bci sun/management/Util newObjectName (Ljava/lang/String;Ljava/lang/String;)Ljavax/management/ObjectName; 2 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001d0810ee800
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810ee400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810ee000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810edc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810ed800
instanceKlass  @bci sun/management/Util newObjectName (Ljava/lang/String;Ljava/lang/String;)Ljavax/management/ObjectName; 2 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001d0810ed400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810ed000
instanceKlass  @bci sun/management/Util newObjectName (Ljava/lang/String;Ljava/lang/String;)Ljavax/management/ObjectName; 2 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001d0810ecc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810ec800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810ec400
instanceKlass java/lang/Long$LongCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810ec000
instanceKlass sun/management/Util
instanceKlass com/sun/management/GarbageCollectorMXBean
instanceKlass java/lang/management/MemoryMXBean
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000001d081070f48
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000001d081070d20
instanceKlass  @bci java/util/stream/Collectors toList ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x000001d081070b08
instanceKlass  @bci java/lang/management/ManagementFactory getPlatformMXBeans (Ljava/lang/Class;)Ljava/util/List; 35 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$$Lambda+0x000001d0810708c8
instanceKlass  @bci java/lang/management/ManagementFactory$PlatformMBeanFinder findFirst (Ljava/lang/Class;)Lsun/management/spi/PlatformMBeanProvider$PlatformComponent; 19 <appendix> member <vmtarget> ; # java/lang/management/ManagementFactory$PlatformMBeanFinder$$Lambda+0x000001d081070678
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass jdk/management/jfr/internal/FlightRecorderMXBeanProvider$SingleMBeanComponent
instanceKlass jdk/management/jfr/FlightRecorderMXBean
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$11
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$10
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$9
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess$1
instanceKlass sun/management/ManagementFactoryHelper$LoggingMXBeanAccess
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$8
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$7
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$6
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$5
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$4
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$3
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$2
instanceKlass java/lang/management/DefaultPlatformMBeanProvider$1
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$5
instanceKlass sun/management/VMManagementImpl
instanceKlass sun/management/VMManagement
instanceKlass sun/management/ManagementFactoryHelper
instanceKlass sun/management/NotificationEmitterSupport
instanceKlass javax/management/NotificationEmitter
instanceKlass javax/management/NotificationBroadcaster
instanceKlass com/sun/management/DiagnosticCommandMBean
instanceKlass javax/management/DynamicMBean
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$4
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$3
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$2
instanceKlass com/sun/management/internal/PlatformMBeanProviderImpl$1
instanceKlass sun/management/spi/PlatformMBeanProvider$PlatformComponent
instanceKlass  @bci com/sun/management/internal/PlatformMBeanProviderImpl <clinit> ()V 8 <appendix> argL0 ; # com/sun/management/internal/PlatformMBeanProviderImpl$$Lambda+0x000001d08106b870
instanceKlass java/util/concurrent/Callable
instanceKlass sun/management/spi/PlatformMBeanProvider
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder$1
instanceKlass java/lang/management/ManagementFactory$PlatformMBeanFinder
instanceKlass java/lang/management/GarbageCollectorMXBean
instanceKlass java/lang/management/MemoryManagerMXBean
instanceKlass java/lang/management/PlatformManagedObject
instanceKlass  @bci java/lang/management/ManagementFactory loadNativeLib ()V 0 <appendix> argL0 ; # java/lang/management/ManagementFactory$$Lambda+0x000001d08106a430
instanceKlass java/lang/management/ManagementFactory
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionMonitor
instanceKlass org/gradle/internal/time/DefaultTimer
instanceKlass com/google/common/collect/ElementTypesAreNonnullByDefault
instanceKlass com/google/errorprone/annotations/DoNotMock
instanceKlass com/google/common/collect/ObjectArrays
instanceKlass org/gradle/internal/service/scopes/ListenerService
instanceKlass org/gradle/internal/service/scopes/StatefulListener
instanceKlass org/gradle/internal/event/DefaultListenerManager$EventBroadcast
instanceKlass org/gradle/internal/event/DefaultListenerManager
instanceKlass org/gradle/internal/buildprocess/execution/BuildSessionLifecycleBuildActionExecutor
instanceKlass org/gradle/internal/buildprocess/execution/StartParamsValidatingActionExecutor
instanceKlass org/gradle/initialization/BuildRequestMetaData
instanceKlass org/gradle/initialization/exception/ExceptionAnalyser
instanceKlass org/gradle/initialization/exception/ExceptionCollector
instanceKlass org/gradle/problems/buildtree/ProblemDiagnosticsFactory
instanceKlass org/gradle/internal/buildprocess/execution/SessionFailureReportingActionExecutor
instanceKlass org/gradle/StartParameter
instanceKlass org/gradle/concurrent/ParallelismConfiguration
instanceKlass org/gradle/internal/buildprocess/execution/SetupLoggingActionExecutor
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e6c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e6800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e6400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e6000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e5c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e5800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810e5400
instanceKlass org/gradle/groovy/scripts/internal/ScriptSourceHasher
instanceKlass org/gradle/internal/jvm/inspection/JvmMetadataDetector
instanceKlass org/gradle/api/internal/initialization/loadercache/ClassLoaderCache
instanceKlass org/gradle/internal/jvm/inspection/JvmVersionDetector
instanceKlass org/gradle/process/internal/worker/WorkerProcessFactory
instanceKlass org/gradle/internal/execution/timeout/TimeoutHandler
instanceKlass org/gradle/cache/GlobalCacheLocations
instanceKlass org/gradle/cache/internal/FileContentCacheFactory
instanceKlass org/gradle/cache/scopes/ScopedCacheBuilderFactory
instanceKlass org/gradle/internal/file/FileAccessTimeJournal
instanceKlass org/gradle/execution/plan/ToPlannedNodeConverter
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistry
instanceKlass org/gradle/internal/hash/ClassLoaderHierarchyHasher
instanceKlass org/gradle/internal/classloader/HashingClassLoaderFactory
instanceKlass org/gradle/internal/isolation/IsolatableFactory
instanceKlass org/gradle/cache/UnscopedCacheBuilderFactory
instanceKlass org/gradle/internal/service/scopes/WorkerSharedUserHomeScopeServices
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutputFactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e5000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e4c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810e4800
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass org/gradle/internal/instrumentation/agent/DefaultClassFileTransformer
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$StateContext
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass java/text/Format$FieldDelegate
instanceKlass java/util/Date
instanceKlass java/text/DigitList
instanceKlass java/text/FieldPosition
instanceKlass java/lang/StringUTF16$CharsSpliterator
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004b
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfInt <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfInt$$Lambda+0x80000004c
instanceKlass java/util/OptionalInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass  @bci java/text/DecimalFormatSymbols findNonFormatChar (Ljava/lang/String;C)C 4 <appendix> argL0 ; # java/text/DecimalFormatSymbols$$Lambda+0x80000000d
instanceKlass java/util/function/IntPredicate
instanceKlass java/util/stream/IntStream
instanceKlass java/lang/StringLatin1$CharsSpliterator
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDecimalFormatSymbolsProvider ()Ljava/text/spi/DecimalFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006b
instanceKlass java/text/DecimalFormatSymbols
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getNumberFormatProvider ()Ljava/text/spi/NumberFormatProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006c
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatSymbolsProvider ()Ljava/text/spi/DateFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x80000006a
instanceKlass java/text/DateFormatSymbols
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000012
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000064
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass java/util/stream/Nodes$ArrayNode
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 16 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006e
instanceKlass  @bci sun/util/locale/provider/LocaleProviderAdapter toLocaleArray (Ljava/util/Set;)[Ljava/util/Locale; 6 <appendix> argL0 ; # sun/util/locale/provider/LocaleProviderAdapter$$Lambda+0x80000006d
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter getCalendarDataProvider ()Ljava/util/spi/CalendarDataProvider; 8 <appendix> member <vmtarget> ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000067
instanceKlass java/util/ResourceBundle
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass java/util/Calendar$Builder
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getCalendarProvider ()Lsun/util/spi/CalendarProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000068
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000066
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass jdk/internal/util/ByteArray
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/Calendar
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/Format
instanceKlass org/gradle/internal/logging/sink/LogEventDispatcher
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$SeenFromEol
instanceKlass org/gradle/internal/SystemProperties
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$4
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$3
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$2
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$1
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$State
instanceKlass org/gradle/internal/logging/text/StreamBackedStandardOutputListener
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutput
instanceKlass org/gradle/internal/logging/console/StyledTextOutputBackedRenderer
instanceKlass org/slf4j/helpers/FormattingTuple
instanceKlass org/slf4j/helpers/MessageFormatter
instanceKlass net/rubygrapefruit/platform/internal/FunctionResult
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$PrintStreamDestination
instanceKlass java/util/logging/ErrorManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter$SnapshotImpl
instanceKlass org/gradle/internal/logging/events/OutputEventListener$1
instanceKlass org/gradle/internal/dispatch/MethodInvocation
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$SnapshotImpl
instanceKlass org/gradle/process/internal/shutdown/ShutdownHooks
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonMain$1
instanceKlass com/google/common/io/Files$2
instanceKlass com/google/common/io/LineProcessor
instanceKlass com/google/common/io/ByteSink
instanceKlass com/google/common/io/ByteSource
instanceKlass com/google/common/base/Predicate
instanceKlass com/google/common/graph/SuccessorsFunction
instanceKlass com/google/common/io/Files
instanceKlass org/gradle/util/internal/GFileUtils
instanceKlass  @bci java/util/regex/CharPredicates ctype (I)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/CharPredicates$$Lambda+0x000001d081066a10
instanceKlass org/gradle/util/GradleVersion
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810dc800
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810dc400
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000001d0810dc000
instanceKlass net/rubygrapefruit/platform/internal/jni/PosixProcessFunctions
instanceKlass org/gradle/jvm/toolchain/internal/DefaultJavaLanguageVersion
instanceKlass org/gradle/jvm/toolchain/JavaLanguageVersion
instanceKlass com/google/common/base/Optional
instanceKlass org/gradle/internal/FileUtils$1
instanceKlass org/gradle/internal/FileUtils
instanceKlass com/google/common/collect/CollectPreconditions
instanceKlass com/google/common/collect/ImmutableMap
instanceKlass com/google/common/collect/Maps$EntryTransformer
instanceKlass com/google/common/collect/BiMap
instanceKlass com/google/common/base/Converter
instanceKlass com/google/common/collect/SortedMapDifference
instanceKlass com/google/common/collect/MapDifference
instanceKlass com/google/common/collect/Maps
instanceKlass com/google/common/collect/Sets
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext$Serializer
instanceKlass org/gradle/launcher/daemon/toolchain/DaemonJvmCriteria
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810d4c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810d4800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810d4400
instanceKlass org/gradle/internal/nativeintegration/ReflectiveEnvironment
instanceKlass org/gradle/internal/nativeintegration/processenvironment/AbstractProcessEnvironment
instanceKlass net/rubygrapefruit/platform/internal/DefaultProcess
instanceKlass net/rubygrapefruit/platform/internal/WrapperProcess
instanceKlass net/rubygrapefruit/platform/file/WindowsFiles
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810d4000
instanceKlass org/gradle/internal/invocation/BuildAction
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandAction
instanceKlass org/gradle/launcher/daemon/server/DaemonLogFile
instanceKlass org/gradle/launcher/daemon/registry/DaemonDir
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthCheck
instanceKlass org/gradle/launcher/daemon/server/stats/DaemonRunningStats
instanceKlass org/gradle/launcher/daemon/server/MasterExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/health/HealthExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthStats
instanceKlass org/gradle/launcher/daemon/server/Daemon
instanceKlass org/gradle/internal/serialize/Serializer
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy
instanceKlass org/gradle/tooling/internal/provider/runner/OperationDependencyLookup
instanceKlass org/gradle/tooling/internal/provider/runner/ToolingApiBuildEventListenerFactory
instanceKlass org/gradle/problems/buildtree/ProblemReporter
instanceKlass org/gradle/internal/serialize/beans/services/BeanConstructors
instanceKlass org/gradle/nativeplatform/NativeBinarySpec
instanceKlass org/gradle/platform/base/BinarySpec
instanceKlass org/gradle/platform/base/Binary
instanceKlass org/gradle/api/CheckableComponentSpec
instanceKlass org/gradle/api/BuildableComponentSpec
instanceKlass org/gradle/platform/base/ComponentSpec
instanceKlass org/gradle/model/ModelElement
instanceKlass org/gradle/internal/resource/transport/sftp/SftpClientFactory
instanceKlass org/gradle/internal/resource/transport/sftp/SftpResourcesServices$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ResourcesServices$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/gcp/gcs/GcsResourcesServices$GlobalScopeServices
instanceKlass org/gradle/nativeplatform/TargetMachineBuilder
instanceKlass org/gradle/nativeplatform/TargetMachine
instanceKlass org/gradle/nativeplatform/internal/DefaultTargetMachineFactory
instanceKlass org/gradle/nativeplatform/TargetMachineFactory
instanceKlass org/gradle/nativeplatform/internal/NativePlatformResolver
instanceKlass org/gradle/platform/base/internal/PlatformResolver
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatformInternal
instanceKlass org/gradle/nativeplatform/platform/NativePlatform
instanceKlass org/gradle/platform/base/Platform
instanceKlass org/gradle/nativeplatform/platform/internal/OperatingSystemInternal
instanceKlass org/gradle/nativeplatform/platform/OperatingSystem
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatforms
instanceKlass org/gradle/internal/logging/text/DiagnosticsVisitor
instanceKlass org/gradle/buildinit/plugins/internal/action/InitBuiltInCommand
instanceKlass org/gradle/internal/build/event/OperationResultPostProcessorFactory
instanceKlass org/gradle/initialization/BuildEventConsumer
instanceKlass org/gradle/internal/build/event/BuildEventSubscriptions
instanceKlass org/gradle/language/java/internal/JavaLanguageServices$JavaGlobalScopeServices
instanceKlass org/gradle/api/component/SoftwareComponentFactory
instanceKlass org/gradle/api/publish/internal/service/PublishServices$GlobalScopeServices
instanceKlass org/gradle/platform/base/internal/registry/ComponentModelBaseServices$GlobalScopeServices
instanceKlass org/gradle/reporting/ReportRenderer
instanceKlass org/gradle/api/reporting/components/internal/DiagnosticsServices$1
instanceKlass org/gradle/api/plugins/internal/HelpBuiltInCommand
instanceKlass org/gradle/configuration/project/BuiltInCommand
instanceKlass org/gradle/plugin/internal/PluginUseServices$GlobalScopeServices
instanceKlass org/gradle/internal/fingerprint/FileNormalizer
instanceKlass org/gradle/internal/reflect/annotations/AnnotationCategory
instanceKlass org/gradle/api/problems/ProblemSpec
instanceKlass org/gradle/api/problems/internal/DocLink
instanceKlass org/gradle/internal/model/CalculatedValue
instanceKlass org/gradle/internal/component/local/model/LocalVariantGraphResolveMetadata
instanceKlass org/gradle/internal/component/model/VariantGraphResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationInternal$VariantVisitor
instanceKlass org/gradle/api/artifacts/Configuration
instanceKlass org/gradle/api/Named
instanceKlass org/gradle/api/attributes/HasConfigurableAttributes
instanceKlass org/gradle/api/attributes/HasAttributes
instanceKlass org/gradle/api/file/FileCollection
instanceKlass org/gradle/api/Buildable
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultLocalVariantMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/LocalVariantMetadataBuilder
instanceKlass org/gradle/internal/component/model/ExcludeMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultExcludeRuleConverter
instanceKlass org/gradle/api/internal/artifacts/DefaultImmutableModuleIdentifierFactory
instanceKlass org/apache/ivy/util/MessageLogger
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultIvyContextManager
instanceKlass org/gradle/api/internal/artifacts/ivyservice/IvyContextManager
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/Version
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionParser
instanceKlass org/gradle/api/Transformer
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass org/gradle/api/artifacts/component/ComponentSelector
instanceKlass org/gradle/internal/resource/ExternalResourceName
instanceKlass org/gradle/api/Describable
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/ExcludeRuleConverter
instanceKlass org/gradle/internal/typeconversion/NotationParser
instanceKlass org/gradle/api/internal/artifacts/ImmutableModuleIdentifierFactory
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport
instanceKlass org/gradle/cache/internal/ProducerGuard
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DependencyMetadataFactory
instanceKlass org/gradle/internal/typeconversion/NotationConverter
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGlobalScopeServices
instanceKlass java/lang/FunctionalInterface
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper$Factory
instanceKlass org/gradle/internal/resource/connector/ResourceConnectorFactory
instanceKlass org/gradle/internal/resource/transport/http/SslContextFactory
instanceKlass org/gradle/internal/resource/transport/http/HttpResourcesServices$GlobalScopeServices
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingGlobalScopeServices
instanceKlass org/gradle/tooling/internal/provider/ExecuteBuildActionRunner
instanceKlass org/gradle/internal/buildtree/BuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/serialization/ClassLoaderCache
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputReader$UserInput
instanceKlass org/gradle/api/internal/tasks/userinput/DefaultUserInputReader
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputReader
instanceKlass kotlin/annotation/Target
instanceKlass kotlin/annotation/Retention
instanceKlass kotlin/Metadata
instanceKlass org/gradle/kotlin/dsl/support/ImplicitImports
instanceKlass org/gradle/kotlin/dsl/support/GlobalServices
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810c0000
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotterStatistics$Collector
instanceKlass org/gradle/api/internal/changedetection/state/FileHasherStatistics$Collector
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GlobalScopeServices
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistration
instanceKlass org/gradle/internal/properties/bean/PropertyWalker
instanceKlass org/gradle/api/internal/tasks/properties/AbstractTypeScheme
instanceKlass org/gradle/api/internal/tasks/properties/TypeScheme
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistar
instanceKlass org/gradle/api/internal/tasks/properties/InspectionSchemeFactory
instanceKlass org/gradle/api/model/ReplacedBy
instanceKlass org/gradle/api/tasks/Internal
instanceKlass org/gradle/api/internal/plugins/software/SoftwareType
instanceKlass org/gradle/api/services/ServiceReference
instanceKlass org/gradle/api/tasks/OutputFiles
instanceKlass org/gradle/api/tasks/OutputFile
instanceKlass org/gradle/api/tasks/OutputDirectory
instanceKlass org/gradle/api/tasks/OutputDirectories
instanceKlass org/gradle/api/tasks/options/OptionValues
instanceKlass org/gradle/api/tasks/Nested
instanceKlass org/gradle/api/tasks/LocalState
instanceKlass org/gradle/api/tasks/InputFiles
instanceKlass org/gradle/api/tasks/InputFile
instanceKlass org/gradle/api/tasks/InputDirectory
instanceKlass org/gradle/api/artifacts/transform/InputArtifactDependencies
instanceKlass org/gradle/api/artifacts/transform/InputArtifact
instanceKlass org/gradle/api/tasks/Input
instanceKlass org/gradle/api/tasks/Destroys
instanceKlass org/gradle/api/tasks/Console
instanceKlass org/gradle/internal/reflect/annotations/TypeAnnotationMetadataStore
instanceKlass org/gradle/internal/execution/WorkInputListeners
instanceKlass org/gradle/api/internal/project/taskfactory/TaskClassInfoStore
instanceKlass org/gradle/internal/execution/WorkExecutionTracker
instanceKlass org/gradle/internal/execution/history/ImmutableWorkspaceMetadataStore
instanceKlass org/gradle/internal/properties/annotations/TypeAnnotationHandler
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices
instanceKlass org/gradle/internal/file/BufferProvider
instanceKlass org/gradle/caching/internal/BuildCacheServices$1
instanceKlass org/gradle/internal/operations/BuildOperationAncestryTracker
instanceKlass org/gradle/internal/build/event/BuildEventServices$1
instanceKlass org/gradle/internal/build/event/BuildEventListenerFactory
instanceKlass org/gradle/internal/build/event/DefaultBuildEventsListenerRegistry
instanceKlass org/gradle/internal/build/event/BuildEventListenerRegistryInternal
instanceKlass org/gradle/build/event/BuildEventsListenerRegistry
instanceKlass org/gradle/internal/buildoption/IntegerInternalOption
instanceKlass org/gradle/internal/buildoption/InternalFlag
instanceKlass org/gradle/internal/buildoption/InternalOption
instanceKlass org/gradle/internal/buildoption/Option
instanceKlass org/gradle/internal/service/DefaultServiceLocator$ServiceFactory
instanceKlass org/gradle/internal/service/scopes/AbstractGradleModuleServices
instanceKlass org/gradle/internal/service/scopes/GradleModuleServices
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810a9800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810a9400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810a9000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d0810a8c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810a8800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d0810a8400
instanceKlass org/gradle/internal/classpath/intercept/JvmBytecodeInterceptorFactoryProvider$CompositeJvmBytecodeInterceptorFactoryProvider
instanceKlass org/gradle/internal/classpath/GroovyCallInterceptorsProvider$CompositeGroovyCallInterceptorsProvider
instanceKlass  @bci org/gradle/internal/classpath/GroovyCallInterceptorsProvider$ClassLoaderSourceGroovyCallInterceptorsProvider <init> (Ljava/lang/ClassLoader;Ljava/lang/String;)V 10 <appendix> member <vmtarget> ; # org/gradle/internal/classpath/GroovyCallInterceptorsProvider$ClassLoaderSourceGroovyCallInterceptorsProvider$$Lambda+0x000001d0810aee70
instanceKlass org/gradle/internal/classpath/GroovyCallInterceptorsProvider$ClassLoaderSourceGroovyCallInterceptorsProvider
instanceKlass  @bci org/gradle/internal/classpath/intercept/JvmBytecodeInterceptorFactoryProvider$ClassLoaderSourceJvmBytecodeInterceptorFactoryProvider <init> (Ljava/lang/ClassLoader;Ljava/lang/String;)V 10 <appendix> member <vmtarget> ; # org/gradle/internal/classpath/intercept/JvmBytecodeInterceptorFactoryProvider$ClassLoaderSourceJvmBytecodeInterceptorFactoryProvider$$Lambda+0x000001d0810aea18
instanceKlass org/gradle/internal/classpath/intercept/JvmBytecodeInterceptorFactoryProvider$ClassLoaderSourceJvmBytecodeInterceptorFactoryProvider
instanceKlass org/gradle/internal/classpath/intercept/JvmBytecodeInterceptorSet
instanceKlass org/gradle/internal/classpath/intercept/DefaultJvmBytecodeInterceptorFactorySet
instanceKlass  @bci org/gradle/internal/classpath/GroovyCallInterceptorsProvider$ClassSourceGroovyCallInterceptorsProvider <init> (Ljava/lang/String;)V 9 <appendix> member <vmtarget> ; # org/gradle/internal/classpath/GroovyCallInterceptorsProvider$ClassSourceGroovyCallInterceptorsProvider$$Lambda+0x000001d0810ae1a0
instanceKlass org/gradle/internal/classpath/Instrumented
instanceKlass org/gradle/internal/classpath/GroovyCallInterceptorsProvider$ClassSourceGroovyCallInterceptorsProvider
instanceKlass org/gradle/internal/classpath/intercept/DefaultCallSiteInterceptorSet
instanceKlass org/gradle/internal/classpath/intercept/CallSiteDecorator
instanceKlass org/gradle/internal/classpath/intercept/JvmBytecodeInterceptorFactoryProvider
instanceKlass org/gradle/internal/classpath/GroovyCallInterceptorsProvider
instanceKlass org/gradle/internal/classpath/intercept/JvmBytecodeInterceptorFactorySet
instanceKlass org/gradle/internal/classpath/intercept/CallSiteInterceptorSet
instanceKlass org/gradle/internal/classpath/intercept/CallInterceptorRegistry
instanceKlass org/gradle/internal/classpath/TransformedClassPath
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass org/gradle/internal/IoActions
instanceKlass org/gradle/util/internal/GUtil
instanceKlass  @bci org/gradle/api/internal/classpath/DefaultModuleRegistry loadOptionalModule (Ljava/lang/String;)Lorg/gradle/api/internal/classpath/Module; 4 <appendix> member <vmtarget> ; # org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda+0x000001d0810ac1f0
instanceKlass groovy/lang/MetaClass
instanceKlass groovy/lang/MetaObjectProtocol
instanceKlass groovy/lang/GroovySystem
instanceKlass groovy/lang/MetaClassRegistry
instanceKlass groovy/lang/GroovyObject
instanceKlass org/objectweb/asm/ClassVisitor
instanceKlass org/gradle/internal/classloader/InstrumentingClassLoader
instanceKlass java/util/ComparableTimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass org/gradle/internal/util/Trie$Builder
instanceKlass org/gradle/internal/util/Trie
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$TrieSet
instanceKlass  @bci java/lang/ClassLoader definePackage (Ljava/lang/String;Ljava/lang/Module;)Ljava/lang/Package; 73 <appendix> member <vmtarget> ; # java/lang/ClassLoader$$Lambda+0x000001d081065098
instanceKlass  @bci jdk/internal/loader/BootLoader$PackageHelper findModule (Ljava/lang/String;)Ljava/lang/Module; 90 <appendix> member <vmtarget> ; # jdk/internal/loader/BootLoader$PackageHelper$$Lambda+0x000001d081064e78
instanceKlass jdk/internal/loader/BootLoader$PackageHelper
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator forEachRemaining (Ljava/util/function/Consumer;)V 33 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x000001d0810649d0
instanceKlass java/util/stream/StreamSpliterators
instanceKlass  @cpi java/util/stream/StreamSpliterators$WrappingSpliterator 151 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d0810a8000
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci java/lang/ClassLoader getPackages ()[Ljava/lang/Package; 38 <appendix> argL0 ; # java/lang/ClassLoader$$Lambda+0x000001d081063d10
instanceKlass java/util/function/IntFunction
instanceKlass  @bci jdk/internal/loader/BootLoader packages ()Ljava/util/stream/Stream; 6 <appendix> argL0 ; # jdk/internal/loader/BootLoader$$Lambda+0x000001d0810636f8
instanceKlass java/util/stream/Streams$2
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass  @bci java/util/stream/AbstractPipeline spliterator ()Ljava/util/Spliterator; 103 <appendix> member <vmtarget> ; # java/util/stream/AbstractPipeline$$Lambda+0x000001d081062d90
instanceKlass java/util/stream/Streams$ConcatSpliterator
instanceKlass  @bci java/lang/ClassLoader packages ()Ljava/util/stream/Stream; 13 <appendix> member <vmtarget> ; # java/lang/ClassLoader$$Lambda+0x000001d081062620
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$Java9PackagesFetcher
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$AbstractClassLoaderLookuper
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassLoaderPackagesFetcher
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassDefiner
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils
instanceKlass org/gradle/initialization/GradleApiSpecAggregator$DefaultSpec
instanceKlass kotlin/jvm/internal/Intrinsics
instanceKlass kotlin/collections/SetsKt__SetsJVMKt
instanceKlass com/google/common/collect/PeekingIterator
instanceKlass com/google/common/collect/UnmodifiableIterator
instanceKlass com/google/common/collect/Iterators
instanceKlass com/google/common/collect/Hashing
instanceKlass com/google/common/math/IntMath$1
instanceKlass com/google/common/math/MathPreconditions
instanceKlass com/google/common/math/IntMath
instanceKlass com/google/common/base/Preconditions
instanceKlass org/apache/groovy/json/DefaultFastStringServiceFactory
instanceKlass org/apache/groovy/json/FastStringServiceFactory
instanceKlass org/gradle/internal/reflect/ReflectionCache$CacheEntry
instanceKlass com/google/common/collect/ImmutableCollection$Builder
instanceKlass com/google/common/collect/ImmutableSet$SetBuilderImpl
instanceKlass org/gradle/kotlin/dsl/provider/KotlinGradleApiSpecProvider
instanceKlass org/gradle/initialization/GradleApiSpecProvider$SpecAdapter
instanceKlass org/gradle/initialization/GradleApiSpecProvider
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/initialization/GradleApiSpecProvider$Spec
instanceKlass org/gradle/initialization/GradleApiSpecAggregator
instanceKlass com/google/common/base/Function
instanceKlass org/gradle/internal/reflect/CachedInvokable
instanceKlass org/gradle/internal/reflect/ReflectionCache
instanceKlass org/gradle/internal/reflect/DirectInstantiator
instanceKlass org/gradle/initialization/DefaultClassLoaderRegistry
instanceKlass org/gradle/internal/installation/GradleRuntimeShadedJarDetector
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081099c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081099800
instanceKlass java/nio/charset/CoderResult
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLClassLoader$2
instanceKlass org/objectweb/asm/Type
instanceKlass org/gradle/initialization/DefaultLegacyTypesSupport
instanceKlass org/gradle/api/internal/jvm/JavaVersionParser
instanceKlass org/gradle/api/internal/DynamicModulesClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081099400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081099000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081098c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d081098800
instanceKlass org/gradle/api/internal/classpath/DefaultPluginModuleRegistry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081098400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081098000
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList$Builder
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass  @bci org/gradle/api/internal/classpath/DefaultModuleRegistry <clinit> ()V 0 <appendix> argL0 ; # org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda+0x000001d081095300
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/internal/buildevents/BuildLoggerFactory
instanceKlass org/gradle/execution/DefaultWorkValidationWarningRecorder
instanceKlass org/gradle/execution/WorkValidationWarningReporter
instanceKlass org/gradle/internal/execution/steps/ValidateStep$ValidationWarningRecorder
instanceKlass javax/inject/Inject
instanceKlass org/gradle/initialization/layout/BuildLayoutFactory
instanceKlass org/gradle/internal/service/scopes/EventScope
instanceKlass org/gradle/internal/scripts/DefaultScriptFileResolverListeners
instanceKlass org/gradle/internal/scripts/ScriptFileResolverListeners
instanceKlass org/gradle/internal/id/UUIDGenerator
instanceKlass org/gradle/internal/remote/MessagingServer
instanceKlass org/gradle/internal/remote/MessagingClient
instanceKlass org/gradle/internal/remote/internal/IncomingConnector
instanceKlass org/gradle/internal/remote/internal/OutgoingConnector
instanceKlass org/gradle/internal/id/IdGenerator
instanceKlass org/gradle/internal/remote/services/MessagingServices
instanceKlass org/gradle/api/internal/file/DefaultFileLookup
instanceKlass org/gradle/internal/service/scopes/Scope$Settings
instanceKlass java/lang/annotation/Documented
instanceKlass javax/annotation/meta/TypeQualifierDefault
instanceKlass javax/annotation/Nonnull
instanceKlass org/gradle/api/NonNullApi
instanceKlass org/gradle/internal/service/scopes/Scope$Project
instanceKlass org/gradle/internal/service/scopes/Scope$Gradle
instanceKlass org/gradle/internal/service/scopes/Scope$Build
instanceKlass org/gradle/internal/service/scopes/Scope$BuildTree
instanceKlass org/gradle/internal/service/scopes/Scope$BuildSession
instanceKlass org/gradle/internal/service/scopes/Scope$CrossBuildSession
instanceKlass org/gradle/internal/service/scopes/Scope$UserHome
instanceKlass org/gradle/internal/service/ServiceScopeValidatorWorkarounds
instanceKlass org/gradle/api/internal/DocumentationRegistry
instanceKlass org/gradle/internal/remote/internal/inet/InetAddressFactory
instanceKlass org/gradle/api/internal/file/FileLookup
instanceKlass org/gradle/api/internal/file/DefaultFilePropertyFactory
instanceKlass org/gradle/api/internal/file/FileResolver
instanceKlass org/gradle/internal/file/PathToFileResolver
instanceKlass org/gradle/internal/file/RelativeFilePathResolver
instanceKlass org/gradle/api/internal/provider/PropertyHost
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/internal/state/ManagedFactoryRegistry
instanceKlass org/gradle/api/internal/file/FileFactory
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass org/gradle/cache/GlobalCache
instanceKlass org/gradle/api/internal/classpath/GlobalCacheRootsProvider
instanceKlass org/gradle/internal/operations/BuildOperationListener
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractionStrategy
instanceKlass org/gradle/internal/properties/annotations/AbstractPropertyAnnotationHandler
instanceKlass org/gradle/internal/properties/annotations/PropertyAnnotationHandler
instanceKlass org/gradle/internal/instantiation/InjectAnnotationHandler
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractionStrategy
instanceKlass org/gradle/api/tasks/util/PatternSet
instanceKlass org/gradle/api/tasks/util/PatternFilterable
instanceKlass org/gradle/api/tasks/AntBuilderAware
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass org/gradle/model/internal/inspect/MethodModelRuleExtractor
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass org/gradle/model/internal/inspect/ModelRuleSourceDetector
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/internal/instrumentation/agent/AgentInitializer
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractor
instanceKlass org/gradle/api/internal/cache/StringInterner
instanceKlass com/google/common/collect/Interner
instanceKlass org/gradle/api/internal/model/NamedObjectInstantiator
instanceKlass org/gradle/internal/state/ManagedFactory
instanceKlass org/gradle/api/internal/tasks/TaskDependencyFactory
instanceKlass org/gradle/api/internal/file/FilePropertyFactory
instanceKlass org/gradle/model/internal/inspect/ModelRuleExtractor
instanceKlass org/gradle/internal/scripts/ScriptFileResolvedListener
instanceKlass org/gradle/model/internal/manage/instance/ManagedProxyFactory
instanceKlass org/gradle/internal/instantiation/InstanceGenerator
instanceKlass org/gradle/internal/operations/CurrentBuildOperationRef
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$RegistrationWrapper
instanceKlass java/lang/Class$AnnotationData
instanceKlass org/gradle/internal/service/scopes/ServiceScope
instanceKlass org/gradle/internal/service/ServiceScopeValidator
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CompositeServiceProvider
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ParentServices
instanceKlass org/gradle/cache/internal/Synchronizer
instanceKlass org/gradle/cache/internal/CacheSupport
instanceKlass org/gradle/cache/internal/CacheAccessSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry
instanceKlass org/gradle/cache/Cache
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryServices
instanceKlass org/gradle/launcher/daemon/server/scaninfo/DaemonScanInfo
instanceKlass org/gradle/launcher/daemon/context/DaemonContext
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/DaemonServerConnector
instanceKlass org/gradle/launcher/daemon/server/DaemonServices
instanceKlass org/gradle/launcher/exec/BuildExecutor
instanceKlass org/gradle/launcher/exec/BuildActionExecutor
instanceKlass org/gradle/internal/buildprocess/BuildProcessScopeServices
instanceKlass  @bci org/gradle/internal/service/scopes/GlobalScopeServices <init> (ZLorg/gradle/internal/instrumentation/agent/AgentStatus;Lorg/gradle/internal/classpath/ClassPath;)V 12 <appendix> member <vmtarget> ; # org/gradle/internal/service/scopes/GlobalScopeServices$$Lambda+0x000001d081087820
instanceKlass org/gradle/internal/environment/GradleBuildEnvironment
instanceKlass org/gradle/cache/CacheCleanupStrategyFactory
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/cache/internal/InMemoryCacheDecoratorFactory
instanceKlass org/gradle/internal/execution/history/OverlappingOutputDetector
instanceKlass org/gradle/internal/execution/history/changes/ExecutionStateChangeDetector
instanceKlass org/gradle/internal/instantiation/InstantiatorFactory
instanceKlass org/gradle/internal/instantiation/PropertyRoleAnnotationHandler
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/model/internal/manage/binding/StructBindingsStore
instanceKlass org/gradle/initialization/ClassLoaderRegistry
instanceKlass org/gradle/api/internal/classpath/PluginModuleRegistry
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractor
instanceKlass org/gradle/api/tasks/util/internal/PatternSpecFactory
instanceKlass org/gradle/internal/file/excludes/FileSystemDefaultExcludesListener
instanceKlass org/gradle/process/internal/health/memory/JvmMemoryInfo
instanceKlass org/gradle/configuration/ImportsReader
instanceKlass org/gradle/api/model/ObjectFactory
instanceKlass org/gradle/internal/reflect/Instantiator
instanceKlass org/gradle/internal/problems/failure/FailureFactory
instanceKlass org/gradle/model/internal/manage/schema/ModelSchemaStore
instanceKlass org/gradle/initialization/JdkToolsInitializer
instanceKlass org/gradle/internal/scripts/ScriptFileResolver
instanceKlass org/gradle/process/internal/health/memory/OsMemoryInfo
instanceKlass org/gradle/internal/service/scopes/GradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/operations/BuildOperationProgressEventEmitter
instanceKlass org/gradle/api/internal/collections/DomainObjectCollectionFactory
instanceKlass org/gradle/process/internal/health/memory/MemoryManager
instanceKlass org/gradle/api/internal/provider/PropertyFactory
instanceKlass org/gradle/internal/operations/BuildOperationRunner
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/initialization/LegacyTypesSupport
instanceKlass org/gradle/internal/file/Deleter
instanceKlass org/gradle/cache/internal/CacheFactory
instanceKlass org/gradle/internal/hash/StreamHasher
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener
instanceKlass org/gradle/internal/operations/BuildOperationListenerManager
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCacheFactory
instanceKlass org/gradle/internal/operations/BuildOperationIdFactory
instanceKlass org/gradle/internal/logging/progress/ProgressLoggerFactory
instanceKlass org/gradle/internal/logging/progress/ProgressListener
instanceKlass org/gradle/internal/event/ScopedListenerManager
instanceKlass org/gradle/internal/event/ListenerManager
instanceKlass org/gradle/internal/concurrent/ExecutorFactory
instanceKlass org/gradle/cache/FileLockManager
instanceKlass org/gradle/cache/internal/ProcessMetaDataProvider
instanceKlass org/gradle/cache/internal/locklistener/FileLockContentionHandler
instanceKlass org/gradle/cache/internal/locklistener/InetAddressProvider
instanceKlass org/gradle/api/internal/file/collections/DirectoryFileTreeFactory
instanceKlass org/gradle/api/internal/file/FileCollectionFactory
instanceKlass org/gradle/process/internal/ExecFactory
instanceKlass org/gradle/api/internal/ProcessOperations
instanceKlass org/gradle/process/internal/JavaForkOptionsFactory
instanceKlass org/gradle/process/internal/JavaExecHandleFactory
instanceKlass org/gradle/process/internal/ExecHandleFactory
instanceKlass org/gradle/process/internal/ExecActionFactory
instanceKlass org/gradle/internal/service/scopes/BasicGlobalScopeServices
instanceKlass org/gradle/internal/service/scopes/Scope$Global
instanceKlass org/gradle/internal/service/scopes/Scope
instanceKlass  @bci org/gradle/internal/instrumentation/agent/DefaultAgentStatus <clinit> ()V 3 <appendix> argL0 ; # org/gradle/internal/instrumentation/agent/DefaultAgentStatus$$Lambda+0x000001d08103d688
instanceKlass  @cpi org/gradle/internal/instrumentation/agent/DefaultAgentStatus 38 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d08103cc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d08103c800
instanceKlass org/gradle/internal/instrumentation/agent/AgentControl
instanceKlass  @bci org/gradle/internal/lazy/Lazy locking ()Lorg/gradle/internal/lazy/Lazy$Factory; 0 <appendix> argL0 ; # org/gradle/internal/lazy/Lazy$$Lambda+0x000001d08103d270
instanceKlass org/gradle/internal/lazy/LockingLazy
instanceKlass org/gradle/internal/lazy/Lazy$Factory
instanceKlass org/gradle/internal/lazy/Lazy
instanceKlass org/gradle/internal/instrumentation/agent/DefaultAgentStatus
instanceKlass org/gradle/internal/instrumentation/agent/AgentStatus
instanceKlass org/gradle/api/specs/Spec
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/internal/buildprocess/BuildProcessState
instanceKlass org/gradle/launcher/daemon/server/DaemonProcessState
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$SingletonService$NonFactoryMarker
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingSystem
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingRouter
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem
instanceKlass org/gradle/internal/logging/slf4j/Slf4jLoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter
instanceKlass org/gradle/internal/logging/LoggingManagerInternal
instanceKlass org/gradle/internal/logging/StandardOutputCapture
instanceKlass org/gradle/api/logging/LoggingManager
instanceKlass org/gradle/internal/logging/source/StdErrLoggingSystem
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$OutputEventDestination
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$1
instanceKlass org/gradle/internal/logging/events/operations/StyledTextBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/StyledTextBuildOperationProgressDetails
instanceKlass org/gradle/internal/io/TextStream
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem
instanceKlass org/gradle/internal/logging/source/StdOutLoggingSystem
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08103c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08103c000
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManagerFactory
instanceKlass org/gradle/internal/logging/services/TextStreamOutputEventListener
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager$1
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager
instanceKlass org/gradle/internal/logging/services/LoggingServiceRegistry$1
instanceKlass org/gradle/internal/logging/config/LoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSourceSystem
instanceKlass org/gradle/internal/logging/services/LoggingServiceRegistry
instanceKlass org/gradle/launcher/daemon/configuration/DefaultDaemonServerConfiguration
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorage
instanceKlass org/fusesource/jansi/Ansi
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibrary
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory$1
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass java/lang/System$Logger
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass  @bci java/util/logging/Level$KnownLevel findByName (Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Optional; 29 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000025
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass  @bci java/util/logging/Level findLevel (Ljava/lang/String;)Ljava/util/logging/Level; 13 <appendix> argL0 ; # java/util/logging/Level$$Lambda+0x800000013
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass java/util/Collections$3
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass  @bci sun/management/MemoryPoolImpl <init> (Ljava/lang/String;ZJJ)V 73 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001d081030400
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/System$LoggerFinder
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 49 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000024
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 19 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000023
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLogger
instanceKlass net/rubygrapefruit/platform/file/FileEvents
instanceKlass java/util/RegularEnumSet$EnumSetIterator
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLibraryFunctions
instanceKlass jdk/internal/loader/NativeLibraries$Unloader
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass java/util/Formatter
instanceKlass net/rubygrapefruit/platform/internal/LibraryDef
instanceKlass java/util/Arrays$ArrayItr
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLocator
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLoader
instanceKlass net/rubygrapefruit/platform/Process
instanceKlass net/rubygrapefruit/platform/internal/Platform
instanceKlass net/rubygrapefruit/platform/Native
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/gradle/api/internal/file/temp/DefaultTemporaryFileProvider
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081030000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08102ec00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08102e800
instanceKlass java/lang/Class$EnclosingMethodInfo
instanceKlass  @cpi org/gradle/internal/execution/model/annotations/ModifierAnnotationCategory 147 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d08102e400
instanceKlass net/rubygrapefruit/platform/WindowsRegistry
instanceKlass org/gradle/internal/jvm/Jvm
instanceKlass org/gradle/internal/jvm/JavaInfo
instanceKlass net/rubygrapefruit/platform/memory/Memory
instanceKlass net/rubygrapefruit/platform/SystemInfo
instanceKlass net/rubygrapefruit/platform/file/FileSystems
instanceKlass org/gradle/internal/file/StatStatistics
instanceKlass org/gradle/internal/file/StatStatistics$Collector
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem
instanceKlass org/gradle/internal/service/InjectUtil
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08102e000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08102dc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08102d800
instanceKlass  @cpi org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata 164 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d08102d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08102d000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d08102cc00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d08102c800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000001d08102c400
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d08102c000
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$1
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector$ClassDetails
instanceKlass org/gradle/util/internal/CollectionUtils
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$SingletonService$1
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/gradle/internal/service/PrivateService
instanceKlass org/gradle/internal/reflect/JavaMethod
instanceKlass org/gradle/util/internal/ArrayUtils
instanceKlass com/google/errorprone/annotations/Keep
instanceKlass java/lang/annotation/Target
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass  @bci sun/reflect/annotation/AnnotationParser parseClassArray (ILjava/nio/ByteBuffer;Ljdk/internal/reflect/ConstantPool;Ljava/lang/Class;)Ljava/lang/Object; 10 <appendix> member <vmtarget> ; # sun/reflect/annotation/AnnotationParser$$Lambda+0x000001d081053608
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass org/gradle/internal/service/Provides
instanceKlass org/gradle/internal/service/AbstractServiceMethod
instanceKlass org/gradle/internal/file/FileCanonicalizer
instanceKlass org/gradle/api/internal/file/temp/TemporaryFileProvider
instanceKlass net/rubygrapefruit/platform/file/PosixFiles
instanceKlass net/rubygrapefruit/platform/file/Files
instanceKlass org/gradle/internal/os/OperatingSystem
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem$Factory
instanceKlass org/gradle/internal/service/TypeStringFormatter
instanceKlass org/gradle/internal/service/RelevantMethods$RelevantMethodsBuilder
instanceKlass org/gradle/internal/Cast
instanceKlass org/gradle/internal/service/ServiceMethod
instanceKlass org/gradle/internal/service/MethodHandleBasedServiceMethodFactory
instanceKlass org/gradle/internal/service/DefaultServiceMethodFactory
instanceKlass org/gradle/internal/service/ServiceMethodFactory
instanceKlass org/gradle/internal/service/RelevantMethods
instanceKlass org/gradle/internal/service/DefaultServiceAccessToken
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector
instanceKlass org/gradle/internal/service/ServiceAccess$1
instanceKlass org/gradle/internal/service/ServiceAccessToken
instanceKlass org/gradle/internal/service/ServiceAccessScope
instanceKlass org/gradle/internal/service/ServiceAccess
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ThisAsService
instanceKlass org/gradle/internal/concurrent/CompositeStoppable$1
instanceKlass org/gradle/internal/concurrent/CompositeStoppable
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler$Registration
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$OwnServices
instanceKlass org/gradle/internal/service/ServiceRegistration
instanceKlass org/gradle/internal/service/ServiceProvider$Visitor
instanceKlass org/gradle/internal/InternalTransformer
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ManagedObjectServiceProvider
instanceKlass org/gradle/internal/service/Service
instanceKlass org/gradle/internal/service/ServiceProvider
instanceKlass org/gradle/internal/concurrent/Stoppable
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$1
instanceKlass org/gradle/internal/nativeintegration/filesystem/Symlink
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileSystem
instanceKlass org/gradle/internal/file/FileSystem
instanceKlass org/gradle/internal/file/Stat
instanceKlass org/gradle/internal/file/Chmod
instanceKlass org/gradle/internal/file/FileModeMutator
instanceKlass org/gradle/internal/file/FileModeAccessor
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FileSystemServices
instanceKlass org/gradle/internal/service/DefaultServiceRegistry
instanceKlass org/gradle/internal/service/ContainsServices
instanceKlass org/gradle/internal/service/CloseableServiceRegistry
instanceKlass org/gradle/internal/service/ServiceRegistryBuilder
instanceKlass org/gradle/internal/nativeintegration/jansi/DefaultJansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorageLocator
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiBootPathConfigurer
instanceKlass net/rubygrapefruit/platform/ProcessLauncher
instanceKlass net/rubygrapefruit/platform/NativeIntegration
instanceKlass org/gradle/internal/file/FileMetadataAccessor
instanceKlass org/gradle/internal/nativeintegration/NativeCapabilities
instanceKlass org/gradle/internal/nativeintegration/ProcessEnvironment
instanceKlass org/gradle/internal/nativeintegration/network/HostnameLookup
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleDetector
instanceKlass org/gradle/initialization/GradleUserHomeDirProvider
instanceKlass org/gradle/internal/service/ServiceRegistry
instanceKlass org/gradle/internal/service/ServiceLookup
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices
instanceKlass org/gradle/internal/service/ServiceRegistrationProvider
instanceKlass org/gradle/internal/serialize/AbstractDecoder
instanceKlass org/gradle/internal/serialize/Decoder
instanceKlass org/gradle/launcher/bootstrap/EntryPoint$RecordingExecutionListener
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081018c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081018800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081018400
instanceKlass org/gradle/internal/logging/events/operations/LogEventBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/LogEventBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/slf4j/BuildOperationAwareLogger
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$2
instanceKlass org/gradle/internal/dispatch/ReflectionDispatch
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$1
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$LazyListener
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081018000
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000001d08104f030
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000001d08104edf8
instanceKlass java/util/StringJoiner
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x000001d08104e460
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass jdk/internal/module/Checks
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x000001d08104e030
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x000001d08104cd90
instanceKlass java/lang/reflect/Proxy
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter$DispatchingInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter
instanceKlass org/gradle/internal/logging/events/operations/ProgressStartBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/ProgressStartBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/sink/OutputEventTransformer
instanceKlass org/gradle/internal/exceptions/NonGradleCauseExceptionsHolder
instanceKlass org/gradle/internal/exceptions/MultiCauseException
instanceKlass org/gradle/internal/exceptions/ResolutionProvider
instanceKlass org/gradle/internal/event/AbstractBroadcastDispatch
instanceKlass org/gradle/internal/event/ListenerBroadcast
instanceKlass org/gradle/internal/dispatch/Dispatch
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleMetaData
instanceKlass org/gradle/internal/logging/console/ColorMap
instanceKlass org/gradle/internal/Factory
instanceKlass org/gradle/internal/logging/format/LogHeaderFormatter
instanceKlass org/gradle/api/logging/StandardOutputListener
instanceKlass org/gradle/internal/logging/text/StyledTextOutput
instanceKlass org/gradle/internal/logging/config/LoggingSystem$Snapshot
instanceKlass org/gradle/internal/logging/events/InteractiveEvent
instanceKlass org/gradle/internal/logging/events/OutputEvent
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer
instanceKlass org/gradle/internal/logging/config/LoggingRouter
instanceKlass org/gradle/internal/logging/LoggingOutputInternal
instanceKlass org/gradle/api/logging/LoggingOutput
instanceKlass org/gradle/internal/logging/config/LoggingSystem
instanceKlass org/gradle/internal/logging/console/UserInputReceiver$Normalizer
instanceKlass org/gradle/internal/logging/console/DefaultUserInputReceiver
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext$NoOpLogger
instanceKlass org/gradle/api/logging/Logger
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/gradle/internal/time/TimeSource$1
instanceKlass org/gradle/internal/time/TimeSource
instanceKlass org/gradle/internal/time/MonotonicClock
instanceKlass org/gradle/internal/time/CountdownTimer
instanceKlass org/gradle/internal/time/Timer
instanceKlass org/gradle/internal/time/Clock
instanceKlass org/gradle/internal/time/Time
instanceKlass org/gradle/internal/logging/events/OutputEventListener
instanceKlass org/gradle/internal/logging/console/GlobalUserInputReceiver
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext
instanceKlass org/slf4j/impl/StaticLoggerBinder
instanceKlass org/slf4j/spi/LoggerFactoryBinder
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/slf4j/Logger
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/LoggerFactory
instanceKlass org/slf4j/helpers/BasicMarker
instanceKlass org/slf4j/Marker
instanceKlass org/slf4j/helpers/BasicMarkerFactory
instanceKlass org/slf4j/IMarkerFactory
instanceKlass org/slf4j/MarkerFactory
instanceKlass org/gradle/api/logging/Logging
instanceKlass org/gradle/launcher/daemon/configuration/DaemonServerConfiguration
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/logging/text/StyledTextOutputFactory
instanceKlass org/gradle/api/logging/configuration/LoggingConfiguration
instanceKlass org/gradle/initialization/BuildClientMetaData
instanceKlass org/gradle/launcher/bootstrap/ExecutionCompleter
instanceKlass org/gradle/launcher/bootstrap/ExecutionListener
instanceKlass org/gradle/launcher/bootstrap/EntryPoint
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/TreeMap$Entry
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x000001d08104a150
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass java/net/URLClassLoader$1
instanceKlass org/gradle/internal/classloader/InstrumentingClassLoader
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/io/RandomAccessFile$1
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/IoActions
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000033
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002b
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass  @bci java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x000001d081048288
instanceKlass org/gradle/internal/InternalTransformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass  @bci org/gradle/api/internal/classpath/DefaultModuleRegistry loadOptionalModule (Ljava/lang/String;)Lorg/gradle/api/internal/classpath/Module; 4 <appendix> member <vmtarget> ; # org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda+0x000001d081009640
instanceKlass  @cpi org/gradle/api/internal/classpath/DefaultModuleRegistry 421 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d08100c000
instanceKlass org/gradle/internal/classpath/TransformedClassPath
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/Collections$1
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x000001d081046ce8
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/CharPredicates
instanceKlass java/util/regex/ASCII
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass java/io/Reader
instanceKlass java/lang/Readable
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/internal/classloader/DefaultClassLoaderFactory
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/Cast
instanceKlass java/util/AbstractList$Itr
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList$Builder
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass java/io/FileFilter
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass java/net/URI$Parser
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass  @bci org/gradle/api/internal/classpath/DefaultModuleRegistry <clinit> ()V 0 <appendix> argL0 ; # org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda+0x000001d0810046a8
instanceKlass  @cpi org/gradle/api/internal/classpath/DefaultModuleRegistry 504 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d081006000
instanceKlass org/gradle/api/specs/Spec
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/api/internal/classpath/GlobalCacheRootsProvider
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/launcher/bootstrap/ProcessBootstrap
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass org/gradle/launcher/daemon/bootstrap/GradleDaemon
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/util/jar/JarFile$ThreadTrackHolder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass jdk/internal/vm/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass jdk/internal/vm/PostVMInitHook$2
instanceKlass jdk/internal/vm/PostVMInitHook
instanceKlass  @bci jdk/internal/reflect/DirectConstructorHandleAccessor invokeImpl ([Ljava/lang/Object;)Ljava/lang/Object; 88 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000001d081002800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081002400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081002000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081001c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081001800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081001400
instanceKlass  @cpi sun/nio/ch/DatagramChannelImpl 1342 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000001d081001000
instanceKlass java/lang/instrument/ClassFileTransformer
instanceKlass org/gradle/instrumentation/agent/Agent
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass jdk/internal/loader/Resource
instanceKlass java/util/StringTokenizer
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/lang/StringCoding
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/instrument/TransformerManager$TransformerInfo
instanceKlass sun/instrument/TransformerManager
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass  @bci sun/instrument/InstrumentationImpl <clinit> ()V 16 <appendix> argL0 ; # sun/instrument/InstrumentationImpl$$Lambda+0x000001d081042458
instanceKlass sun/instrument/InstrumentationImpl
instanceKlass java/lang/instrument/Instrumentation
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass jdk/internal/module/ModuleBootstrap$SafeModuleFinder
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x000001d081041cf8
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x000001d0810413c0
instanceKlass  @bci jdk/internal/module/ModuleBootstrap decode (Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/Map; 193 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000001d081041188
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/util/SequencedMap
instanceKlass java/util/SequencedSet
instanceKlass java/lang/ModuleLayer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/module/ModuleFinder$1
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/module/Resolver
instanceKlass java/lang/module/Configuration
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004d
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004f
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004e
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000050
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass  @bci jdk/internal/module/DefaultRoots exportsAPI (Ljava/lang/module/ModuleDescriptor;)Z 9 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000056
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003a
instanceKlass java/util/function/BiConsumer
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 42 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000053
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 32 <appendix> member <vmtarget> ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000057
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 21 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000054
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass  @bci org/gradle/api/internal/file/DefaultFileCollectionFactory <init> (Lorg/gradle/internal/file/PathToFileResolver;Lorg/gradle/api/internal/tasks/TaskDependencyFactory;Lorg/gradle/api/internal/file/collections/DirectoryFileTreeFactory;Lorg/gradle/internal/Factory;Lorg/gradle/api/internal/provider/PropertyHost;Lorg/gradle/internal/nativeintegration/filesystem/FileSystem;)V 10 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000001d081000800
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 11 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000055
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/function/Predicate
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/invoke/LambdaMetafactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000001d081000400
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/Void
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterator
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/module/DefaultRoots
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass sun/nio/cs/SingleByte
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciMethod java/lang/Object equals (Ljava/lang/Object;)Z 1024 0 7744 0 -1
ciMethod java/lang/Object hashCode ()I 512 0 256 0 -1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 834 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; org/gradle/internal/daemon/clientinput/StdInStream
staticfield java/lang/System out Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
staticfield java/lang/System err Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 100 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 100 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 7 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
instanceKlass java/lang/CloneNotSupportedException
instanceKlass com/google/common/collect/RegularImmutableMap$BucketOverflowException
instanceKlass java/security/PrivilegedActionException
instanceKlass sun/security/pkcs11/wrapper/PKCS11Exception
instanceKlass java/security/GeneralSecurityException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/text/ParseException
instanceKlass java/lang/InterruptedException
instanceKlass java/net/URISyntaxException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1443 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 7 1 100 1 10 12 7 1 10 10 7 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/VM 1 1 320 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 9 12 1 1 9 12 1 9 12 1 3 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 11 7 12 1 1 1 7 1 10 100 12 1 1 1 10 12 1 8 1 8 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 5 0 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 100 1 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 5 0 10 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 7 1 1 1 1
staticfield jdk/internal/misc/VM lock Ljava/lang/Object; java/lang/Object
ciInstanceKlass java/lang/InternalError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass com/google/common/util/concurrent/ExecutionError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Set 1 1 144 100 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 7 1 7 1 11 7 12 1 1 1 11 12 1 1 7 1 10 12 1 10 12 1 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map$Entry 1 0 178 18 12 1 1 7 1 100 1 18 10 100 12 1 1 1 18 12 1 18 100 1 11 7 12 1 1 1 11 12 1 11 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 8 10 7 1 10 12 1 8 10 12 1 8 1 10 12 1 8 10 12 1 8 1 10 12 1 1 8 1 100 1 8 1 10 12 1 1 11 12 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 16 3 3 15 11 12 15 11 12 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection$SocketInputStream
instanceKlass org/gradle/internal/file/RandomAccessFileInputStream
instanceKlass org/gradle/internal/daemon/clientinput/StdInStream
instanceKlass com/esotericsoftware/kryo/io/Input
instanceKlass org/gradle/internal/serialize/kryo/KryoBackedDecoder$1
instanceKlass org/gradle/internal/serialize/AbstractDecoder$DecoderStream
instanceKlass org/gradle/internal/stream/EncodedStream$EncodedInput
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 3 7 1 8 1 10 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/zip/CheckedInputStream
instanceKlass java/io/DataInputStream
instanceKlass sun/net/www/protocol/jar/JarURLConnection$JarURLInputStream
instanceKlass java/util/jar/Manifest$FastInputStream
instanceKlass java/util/zip/InflaterInputStream
instanceKlass java/io/BufferedInputStream
ciInstanceKlass java/io/FilterInputStream 1 1 62 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$RetrieveSystemPackagesClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 7 12 1 1 1 7 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 7 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 7 12 1 1 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 577 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer$ForwardEvents
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 7 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/System$2 1 1 643 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 1 11 100 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 1 100 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass jdk/internal/access/JavaLangAccess 1 0 213 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
ciMethod java/lang/System$2 currentCarrierThread ()Ljava/lang/Thread; 508 0 5398 0 0
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 10 9 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 8 1 100 1 10 10 7 12 1 1 1 10 12 1 8 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 460 7 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 5 0 5 0 7 1 3 5 0 3 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 100 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 10 12 1 4 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 9 12 1 1 10 12 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass java/math/BigDecimal
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/Striped64
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 100 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 100 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 890 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 18 10 12 1 1 10 12 1 1 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 10 12 1 7 1 7 1 8 1 10 10 12 1 1 10 12 1 10 7 12 1 1 8 1 10 12 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 34 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
ciMethod java/lang/ref/Reference get ()Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/ref/Reference refersToImpl (Ljava/lang/Object;)Z 760 0 5626 0 96
ciMethod java/lang/ref/Reference refersTo0 (Ljava/lang/Object;)Z 770 0 385 0 -1
ciMethod java/lang/ref/Reference refersTo (Ljava/lang/Object;)Z 518 0 6318 0 0
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass java/util/stream/MatchOps$MatchKind
instanceKlass org/gradle/internal/reflect/PropertyAccessorType
instanceKlass com/google/common/cache/LocalCache$EntryFactory
instanceKlass com/google/common/cache/CacheBuilder$NullListener
instanceKlass com/google/common/cache/CacheBuilder$OneWeigher
instanceKlass com/google/common/cache/LocalCache$Strength
instanceKlass org/gradle/api/tasks/util/internal/PatternSpecFactory$CaseSensitivity
instanceKlass org/gradle/internal/file/TreeType
instanceKlass org/gradle/internal/properties/OutputFilePropertyType
instanceKlass org/gradle/internal/properties/annotations/PropertyAnnotationHandler$Kind
instanceKlass org/gradle/internal/execution/model/annotations/ModifierAnnotationCategory
instanceKlass org/gradle/internal/properties/InputFilePropertyType
instanceKlass org/gradle/internal/nativeintegration/EnvironmentModificationResult
instanceKlass com/google/common/collect/AbstractIterator$State
instanceKlass org/gradle/internal/deprecation/DeprecatedFeatureUsage$Type
instanceKlass org/gradle/initialization/StartParameterBuildOptions$ConfigurationCacheProblemsOption$Value
instanceKlass org/gradle/internal/watch/registry/WatchMode
instanceKlass org/gradle/api/launcher/cli/WelcomeMessageDisplayMode
instanceKlass org/gradle/api/artifacts/verification/DependencyVerificationMode
instanceKlass java/lang/annotation/ElementType
instanceKlass org/gradle/launcher/daemon/toolchain/DaemonJvmCriteria$JavaHome$Source
instanceKlass org/gradle/cache/FileLockManager$LockMode
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/time/temporal/ChronoField
instanceKlass java/net/StandardProtocolFamily
instanceKlass jdk/internal/util/OperatingSystem
instanceKlass org/gradle/launcher/daemon/server/api/DaemonState
instanceKlass java/security/DrbgParameters$Capability
instanceKlass sun/security/util/KnownOIDs
instanceKlass org/gradle/internal/operations/BuildOperationCategory
instanceKlass org/gradle/tooling/events/OperationType
instanceKlass org/gradle/api/logging/configuration/WarningMode
instanceKlass org/gradle/api/logging/configuration/ConsoleOutput
instanceKlass org/gradle/api/logging/configuration/ShowStacktrace
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationStatus
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass org/gradle/internal/logging/text/StyledTextOutput$Style
instanceKlass net/rubygrapefruit/platform/internal/FunctionResult$Failure
instanceKlass java/math/RoundingMode
instanceKlass org/gradle/api/JavaVersion
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiOperatingSystemSupport
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass net/rubygrapefruit/platform/terminal/Terminals$Output
instanceKlass java/util/Locale$Category
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccess
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$SingletonService$BindState
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$State
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$NativeFeatures
instanceKlass org/gradle/launcher/daemon/configuration/DaemonPriority
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$NativeServicesMode
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass org/gradle/api/logging/LogLevel
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/util/stream/StreamShape
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
instanceKlass java/lang/ref/ReferenceQueue$Null
instanceKlass java/lang/ref/NativeReferenceQueue
ciInstanceKlass java/lang/ref/ReferenceQueue 1 1 183 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 9 7 12 1 1 1 11 12 1 10 7 12 1 1 9 12 1 1 7 1 10 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 7 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 5 0 10 10 12 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 11 100 12 1 1 1 10 7 12 1 1 7 1 10 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/ReferenceQueue NULL Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue$Null
staticfield java/lang/ref/ReferenceQueue ENQUEUED Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue$Null
staticfield java/lang/ref/ReferenceQueue $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass org/gradle/internal/classloader/VisitableURLClassLoader
instanceKlass org/gradle/internal/classloader/VisitableURLClassLoader
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 117 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 3 10 100 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/ByteArrayInputStream $assertionsDisabled Z 1
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciMethod java/nio/Buffer position ()I 258 0 129 0 -1
ciMethod java/nio/Buffer limit ()I 280 0 140 0 -1
ciMethod java/nio/Buffer hasRemaining ()Z 466 0 3872 0 0
ciInstanceKlass java/util/Objects 1 1 184 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 11 100 12 1 1 1 100 1 10 10 12 1 8 1 10 12 1 8 1 100 1 11 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/util/Preconditions 1 1 194 10 7 12 1 1 1 11 7 12 1 1 1 11 100 12 1 1 1 7 1 100 1 10 7 12 1 1 1 10 12 1 8 1 7 1 10 7 12 1 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 7 1 10 12 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 11 12 1 8 1 8 1 11 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 7 1 10 10 12 1 1 9 12 1 1 7 1 10 9 12 1 7 1 10 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/util/Preconditions SIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions AIOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield jdk/internal/util/Preconditions IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
ciMethod java/util/Objects checkFromIndexSize (III)I 512 0 16310 0 160
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/AbstractSequentialList
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/Vector
instanceKlass java/util/Arrays$ArrayList
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 7 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 100 12 1 1 1 100 1 11 7 12 1 1 1 10 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection
instanceKlass java/util/TreeMap$Values
instanceKlass com/google/common/collect/ImmutableCollection
instanceKlass java/util/IdentityHashMap$Values
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/AbstractQueue
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/gradle/api/internal/provider/AbstractProperty$PropertyQueryException
instanceKlass java/util/ConcurrentModificationException
instanceKlass java/lang/TypeNotPresentException
instanceKlass org/gradle/internal/reflect/NoSuchPropertyException
instanceKlass org/gradle/internal/typeconversion/TypeConversionException
instanceKlass com/google/common/util/concurrent/UncheckedExecutionException
instanceKlass com/google/common/cache/CacheLoader$InvalidCacheLoadException
instanceKlass groovy/json/JsonException
instanceKlass org/gradle/internal/work/NoAvailableWorkerLeaseException
instanceKlass org/gradle/launcher/daemon/server/BadlyFormedRequestException
instanceKlass java/security/ProviderException
instanceKlass org/gradle/internal/remote/internal/MessageIOException
instanceKlass org/gradle/cache/InsufficientLockModeException
instanceKlass org/gradle/cache/LockTimeoutException
instanceKlass org/gradle/cache/internal/locklistener/GracefullyStoppedException
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry$EmptyRegistryException
instanceKlass org/gradle/cache/FileIntegrityViolationException
instanceKlass org/gradle/internal/file/FileException
instanceKlass java/io/UncheckedIOException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStoppedException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonUnavailableException
instanceKlass java/util/MissingResourceException
instanceKlass org/gradle/internal/jvm/JavaHomeException
instanceKlass kotlin/UninitializedPropertyAccessException
instanceKlass org/gradle/api/reflect/ObjectInstantiationException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass java/util/NoSuchElementException
instanceKlass org/gradle/internal/reflect/NoSuchMethodException
instanceKlass org/gradle/internal/nativeintegration/NativeIntegrationException
instanceKlass net/rubygrapefruit/platform/NativeException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass com/esotericsoftware/kryo/KryoException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass org/gradle/internal/operations/BuildOperationInvocationException
instanceKlass org/gradle/internal/UncheckedException
instanceKlass org/gradle/api/GradleException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/SecurityException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/gradle/api/GradleException
instanceKlass java/lang/IllegalStateException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass jdk/net/UnixDomainPrincipal
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/module/ModuleReferenceImpl$CachedHash
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 736 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 8
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
instanceKlass com/google/common/cache/LocalCache
instanceKlass java/util/concurrent/ConcurrentSkipListMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 100 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass kotlin/KotlinNullPointerException
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 1 1 26 10 7 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; null
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleReferences$Array
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
instanceKlass java/lang/invoke/VarHandleLongs$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 0 0 271 9 7 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker$StackFrame 0 0 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 375 100 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 7 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 7 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
ciInstanceKlass jdk/internal/misc/Blocker 1 1 106 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 12 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Blocker JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/misc/Blocker $assertionsDisabled Z 1
ciMethod jdk/internal/misc/Blocker currentCarrierThread ()Ljava/lang/Thread; 508 0 5398 0 0
ciMethod jdk/internal/misc/Blocker begin ()J 512 0 5401 0 160
ciMethod jdk/internal/misc/Blocker end (J)V 524 0 5398 0 120
instanceKlass java/nio/MappedByteBuffer
instanceKlass java/nio/HeapByteBuffer
ciInstanceKlass java/nio/ByteBuffer 1 1 446 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 100 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 100 1 5 0 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 9 12 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 10 12 1 9 12 100 1 10 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 7 1 9 12 1 8 1 10 12 1 8 1 8 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 10 12 1 1 10 12 10 12 10 12 10 12 10 12 10 12 10 12 1 1 10 12 1 9 12 1 1 7 10 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1
staticfield java/nio/ByteBuffer ARRAY_BASE_OFFSET J 16
staticfield java/nio/ByteBuffer $assertionsDisabled Z 1
ciMethod java/nio/ByteBuffer isDirect ()Z 0 0 1 0 -1
ciMethod java/nio/ByteBuffer position (I)Ljava/nio/ByteBuffer; 518 0 1770 0 -1
instanceKlass com/google/common/collect/Sets$ImprovedAbstractSet
instanceKlass java/lang/ProcessEnvironment$CheckedKeySet
instanceKlass java/util/concurrent/ConcurrentSkipListSet
instanceKlass java/util/concurrent/ConcurrentSkipListMap$EntrySet
instanceKlass java/util/concurrent/CopyOnWriteArraySet
instanceKlass java/util/TreeMap$EntrySet
instanceKlass java/util/LinkedHashMap$LinkedEntrySet
instanceKlass com/google/common/collect/Sets$SetView
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet
instanceKlass java/util/TreeMap$KeySet
instanceKlass java/util/TreeSet
instanceKlass java/util/LinkedHashMap$LinkedKeySet
instanceKlass java/util/Collections$SingletonSet
instanceKlass java/util/IdentityHashMap$KeySet
instanceKlass java/util/EnumSet
instanceKlass java/util/HashMap$KeySet
instanceKlass java/util/WeakHashMap$KeySet
instanceKlass java/util/Collections$SetFromMap
instanceKlass java/util/HashSet
instanceKlass java/util/ImmutableCollections$MapN$1
instanceKlass java/util/Collections$EmptySet
instanceKlass java/util/HashMap$EntrySet
ciInstanceKlass java/util/AbstractSet 1 1 96 10 7 12 1 1 1 100 1 100 1 11 12 1 1 10 100 1 10 12 1 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 12 1 1 10 100 12 1 1 10 100 12 1 1 1 11 10 12 1 1 11 12 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/gradle/internal/reflect/ReflectionCache$WeaklyClassReferencingCache
instanceKlass java/lang/ClassValue$ClassValueMap
ciInstanceKlass java/util/WeakHashMap 1 1 399 7 1 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 3 10 7 12 1 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 4 10 12 1 11 7 12 1 1 1 6 0 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 9 12 1 9 12 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 7 1 3 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 100 1 11 12 1 11 12 1 10 12 1 1 10 10 100 12 1 1 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 9 12 100 1 10 10 100 12 1 1 10 12 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 8 1 10 12 1 10 12 10 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/WeakHashMap NULL_KEY Ljava/lang/Object; java/lang/Object
ciMethod java/util/WeakHashMap remove (Ljava/lang/Object;)Ljava/lang/Object; 768 22 5504 0 0
ciMethod java/util/WeakHashMap hash (Ljava/lang/Object;)I 516 0 6405 0 240
ciMethod java/util/WeakHashMap indexFor (II)I 768 0 8543 0 0
ciMethod java/util/WeakHashMap expungeStaleEntries ()V 768 0 6534 0 176
ciMethod java/util/WeakHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 514 0 8456 0 0
ciMethod java/util/WeakHashMap getTable ()[Ljava/util/WeakHashMap$Entry; 768 0 8459 0 0
ciMethod java/util/WeakHashMap matchesKey (Ljava/util/WeakHashMap$Entry;Ljava/lang/Object;)Z 404 0 2825 0 0
ciInstanceKlass java/util/concurrent/ForkJoinPool 1 1 1205 7 1 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 3 100 1 9 7 12 1 1 1 9 3 9 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 9 7 12 1 1 1 9 12 1 3 5 0 5 0 5 0 5 0 5 0 10 12 1 1 3 10 12 1 1 9 12 1 9 12 1 10 12 1 3 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 3 10 12 1 1 9 12 1 1 10 7 12 1 1 5 0 3 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 1 9 12 1 5 0 5 0 5 0 9 12 1 1 11 100 12 1 1 1 100 1 8 1 10 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 9 12 1 1 10 12 1 10 12 1 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 10 12 1 1 3 10 12 1 3 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 12 1 5 0 9 100 12 1 1 1 10 12 1 10 7 1 10 12 100 1 10 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 3 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 7 1 7 1 8 1 10 12 1 1 10 12 1 1 100 1 10 12 1 100 1 10 12 1 100 1 10 12 1 100 1 10 100 1 8 1 10 10 12 1 7 1 11 7 12 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 100 1 100 1 10 10 12 1 10 10 12 1 1 10 12 1 10 100 1 10 12 1 10 12 1 9 12 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 8 1 8 1 8 1 8 1 10 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 1 3 10 12 1 1 100 1 10 10 12 1 1 11 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 7 12 1 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 8 10 12 1 1 8 8 8 1 7 1 10 10 12 1 100 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 3 1 3 1 3 1 1 3 1 1 3 1 3 1 3 1 1 1 3 1 1 1 1 1 1 1 3 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ForkJoinPool defaultForkJoinWorkerThreadFactory Ljava/util/concurrent/ForkJoinPool$ForkJoinWorkerThreadFactory; java/util/concurrent/ForkJoinPool$DefaultForkJoinWorkerThreadFactory
staticfield java/util/concurrent/ForkJoinPool common Ljava/util/concurrent/ForkJoinPool; java/util/concurrent/ForkJoinPool
staticfield java/util/concurrent/ForkJoinPool U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ForkJoinPool CTL J 216
staticfield java/util/concurrent/ForkJoinPool RUNSTATE J 48
staticfield java/util/concurrent/ForkJoinPool PARALLELISM J 224
staticfield java/util/concurrent/ForkJoinPool THREADIDS J 24
staticfield java/util/concurrent/ForkJoinPool POOLIDS_BASE Ljava/lang/Object; java/lang/Class
staticfield java/util/concurrent/ForkJoinPool POOLIDS J 320
ciInstanceKlass jdk/internal/misc/CarrierThread 0 0 141 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 7 1 7 1 7 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1
instanceKlass jdk/internal/misc/CarrierThread
ciInstanceKlass java/util/concurrent/ForkJoinWorkerThread 0 0 127 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1
ciMethod java/util/concurrent/ForkJoinWorkerThread getPool ()Ljava/util/concurrent/ForkJoinPool; 0 0 1 0 -1
ciMethod jdk/internal/misc/CarrierThread inBlocking ()Z 0 0 1 0 -1
ciMethod jdk/internal/misc/CarrierThread beginBlocking ()V 0 0 1 0 -1
ciMethod jdk/internal/misc/CarrierThread endBlocking ()V 0 0 1 0 -1
ciInstanceKlass java/util/function/BiFunction 1 1 65 10 100 12 1 1 1 18 12 1 1 11 7 12 1 1 11 100 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass sun/nio/ch/DirectBuffer 1 1 13 100 1 100 1 1 1 1 1 1 1 1 1
ciMethod sun/nio/ch/DirectBuffer address ()J 0 0 1 0 -1
ciInstanceKlass java/util/zip/ZipConstants 1 0 78 100 1 100 1 1 1 1 5 0 1 5 0 1 5 0 1 5 0 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 3 1 3 1 1 1 3 1 1 1 1 1 1 1 3 1 3 1 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1
instanceKlass java/util/jar/JarFile
ciInstanceKlass java/util/zip/ZipFile 1 1 551 7 1 7 1 7 1 10 12 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 7 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 8 10 12 1 1 10 12 1 1 8 1 9 12 1 1 9 12 1 9 10 12 1 1 9 12 1 1 7 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 5 0 5 0 5 0 5 0 7 1 10 12 1 100 1 8 1 10 100 1 9 12 1 10 12 1 100 1 18 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 7 1 10 12 1 18 18 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 7 1 9 12 1 1 11 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 3 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 9 12 1 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 8 1 8 1 9 12 1 1 9 12 1 7 1 10 10 12 1 11 7 1 11 12 1 1 9 12 1 9 12 1 8 1 10 7 12 1 1 1 8 1 10 12 1 100 1 10 12 1 9 12 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 16 1 15 10 12 16 15 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/util/zip/ZipFile DISABLE_ZIP64_EXTRA_VALIDATION Z 0
ciMethod java/util/zip/ZipFile ensureOpenOrZipException ()V 1024 0 2845 0 0
instanceKlass com/google/common/cache/LocalCache$Segment
instanceKlass jdk/internal/loader/NativeLibraries$CountedLock
instanceKlass java/util/concurrent/ConcurrentHashMap$Segment
ciInstanceKlass java/util/concurrent/locks/ReentrantLock 1 1 177 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 100 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 8 1 10 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 100 1 1
ciMethod java/util/concurrent/locks/ReentrantLock lock ()V 12 0 10118 0 -1
ciMethod java/util/concurrent/locks/ReentrantLock unlock ()V 10 0 10148 0 -1
ciInstanceKlass jdk/internal/access/JavaNioAccess 1 0 47 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
ciMethod jdk/internal/access/JavaNioAccess acquireSession (Ljava/nio/Buffer;)V 0 0 1 0 -1
ciMethod jdk/internal/access/JavaNioAccess releaseSession (Ljava/nio/Buffer;)V 0 0 1 0 -1
ciInstanceKlass java/io/RandomAccessFile 1 1 421 7 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 12 1 1 9 12 1 1 7 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 8 1 8 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 1 1 100 1 8 1 10 7 1 10 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 9 12 1 10 7 12 1 1 100 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 8 1 10 10 12 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 7 1 7 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 7 1 10 10 7 12 1 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/io/DataInput 1 0 36 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/DataOutput 1 0 30 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/io/RandomAccessFile read ([BII)I 516 0 8251 0 0
ciMethod java/io/RandomAccessFile seek (J)V 512 0 6408 0 248
ciMethod java/io/RandomAccessFile readBytes ([BII)I 518 0 5413 0 232
ciMethod java/io/RandomAccessFile readFully ([BII)V 540 0 5403 0 304
ciMethod java/io/RandomAccessFile readBytes0 ([BII)I 770 0 385 0 -1
ciMethod java/io/RandomAccessFile seek0 (J)V 512 0 256 0 -1
ciMethod java/lang/ref/ReferenceQueue poll ()Ljava/lang/ref/Reference; 514 0 6433 0 112
ciInstanceKlass jdk/internal/util/Preconditions$4 1 1 61 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 100 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 1 1 1 1
ciInstanceKlass java/util/Collections$SetFromMap 1 1 182 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 9 12 1 1 11 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 9 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 1 11 12 1 11 12 1 11 12 1 1 11 12 1 11 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 100 1 8 1 10 12 1 7 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 100 1 1 1
ciMethod java/util/Collections$SetFromMap remove (Ljava/lang/Object;)Z 512 0 5504 0 0
ciInstanceKlass java/util/WeakHashMap$Entry 1 1 112 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 7 1 10 12 1 11 10 7 12 1 1 1 10 12 1 11 10 100 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
ciInstanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry 1 1 37 10 7 12 1 1 1 9 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
ciInstanceKlass java/util/zip/ZipUtils 1 1 331 7 1 7 1 100 1 10 7 12 1 1 1 5 0 5 0 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 5 0 5 0 5 0 5 0 5 0 5 0 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 1 5 0 100 1 5 0 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 9 12 1 10 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 1 1 1 5 0 1 5 0 1 1 3 1 3 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/zip/ZipUtils NIO_ACCESS Ljdk/internal/access/JavaNioAccess; java/nio/Buffer$2
staticfield java/util/zip/ZipUtils defaultBuf Ljava/nio/ByteBuffer; java/nio/HeapByteBuffer
staticfield java/util/zip/ZipUtils unsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/zip/ZipUtils byteBufferArrayOffset J 48
staticfield java/util/zip/ZipUtils byteBufferOffsetOffset J 40
ciMethod java/util/zip/ZipUtils LOCSIG ([B)J 512 0 3259 0 0
ciMethod java/util/zip/ZipUtils LOCNAM ([B)I 1024 0 2801 0 0
ciMethod java/util/zip/ZipUtils LOCEXT ([B)I 1024 0 2801 0 0
ciMethod java/util/zip/ZipUtils getBufferArray (Ljava/nio/ByteBuffer;)[B 0 0 1 0 -1
ciMethod java/util/zip/ZipUtils getBufferOffset (Ljava/nio/ByteBuffer;)I 0 0 1 0 -1
ciMethod java/util/zip/ZipUtils SH ([BI)I 1024 0 501563 0 176
ciMethod java/util/zip/ZipUtils LG ([BI)J 634 0 61525 0 224
ciInstanceKlass java/util/zip/ZipFile$CleanableResource 1 1 167 100 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 10 7 12 1 1 1 9 12 1 1 7 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 11 7 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 11 12 1 1 10 12 1 11 7 12 1 1 1 100 1 11 12 1 1 100 1 11 12 1 10 12 1 100 1 10 12 1 1 10 12 1 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/zip/ZipFile$Source 1 1 688 100 1 100 1 7 1 7 1 9 7 12 1 1 1 10 12 1 1 5 0 8 1 10 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 3 9 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 7 1 8 1 3 8 1 8 1 7 1 10 12 1 8 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 5 0 8 1 10 12 1 1 10 12 1 8 1 8 1 7 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 100 1 100 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 10 9 12 1 9 12 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 1 11 100 12 1 1 1 9 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 9 12 1 10 9 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 5 0 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 5 0 10 12 1 9 12 1 8 1 5 0 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 100 1 10 12 10 12 1 1 9 12 1 9 12 1 8 1 8 1 5 0 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 10 12 1 1 10 12 1 7 1 10 11 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 9 12 1 1 9 12 1 9 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 10 7 12 1 1 10 12 1 1 9 12 1 1 10 10 7 12 1 1 1 1 1 3 1 1 1 3 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/zip/ZipFile$Source JUJA Ljdk/internal/access/JavaUtilJarAccess; java/util/jar/JavaUtilJarAccessImpl
staticfield java/util/zip/ZipFile$Source EMPTY_META_VERSIONS [I 0
staticfield java/util/zip/ZipFile$Source files Ljava/util/HashMap; java/util/HashMap
staticfield java/util/zip/ZipFile$Source builtInFS Ljava/nio/file/FileSystem; sun/nio/fs/WindowsFileSystem
staticfield java/util/zip/ZipFile$Source $assertionsDisabled Z 1
ciMethod java/util/zip/ZipFile$Source readFullyAt ([BIIJ)I 512 666 4559 0 0
ciMethod java/util/zip/ZipFile$Source readAt ([BIIJ)I 1024 0 2845 0 0
ciInstanceKlass java/util/zip/ZipFile$ZipFileInputStream 1 1 180 100 1 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 9 12 1 5 0 10 12 1 1 9 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 5 0 8 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 7 1 5 0 3 9 12 1 1 9 12 1 1 11 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/zip/ZipFile$ZipFileInputStream read ([BII)I 1024 0 2845 0 0
ciMethod java/util/zip/ZipFile$ZipFileInputStream close ()V 512 0 5400 0 1784
ciMethod java/util/zip/ZipFile$ZipFileInputStream initDataOffset ()J 1024 0 2845 0 0
ciInstanceKlass java/util/zip/ZipFile$ZipFileInflaterInputStream 1 1 155 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 9 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 11 7 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 5 0 3 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/zip/ZipFile$ZipFileInflaterInputStream
ciInstanceKlass java/util/zip/InflaterInputStream 1 1 180 9 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 10 12 1 100 1 8 1 10 9 12 1 1 9 12 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 8 1 7 1 5 0 10 100 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 100 1 8 1 10 10 12 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/zip/InflaterInputStream read ([BII)I 514 0 5377 0 -1
ciMethod java/util/zip/InflaterInputStream fill ()V 0 0 1 0 -1
ciMethod java/util/zip/InflaterInputStream ensureOpen ()V 514 0 5377 0 0
ciMethod java/util/zip/ZipFile$ZipFileInflaterInputStream fill ()V 1024 0 2844 0 0
ciInstanceKlass java/util/zip/Inflater 1 1 280 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 7 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 11 100 12 1 1 1 100 1 11 10 12 1 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 12 1 1 100 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 5 0 9 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 7 12 1 1 1 100 1 10 100 1 8 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/zip/Inflater $assertionsDisabled Z 1
ciMethod java/util/zip/Inflater inflate ([BII)I 324 0 4676 0 -1
ciMethod java/util/zip/Inflater setInput ([BII)V 1024 0 2845 0 0
ciMethod java/util/zip/Inflater finished ()Z 514 0 5314 0 0
ciMethod java/util/zip/Inflater needsInput ()Z 324 0 4676 0 0
ciMethod java/util/zip/Inflater needsDictionary ()Z 324 0 4676 0 0
ciMethod java/util/zip/Inflater hasPendingOutput ()Z 222 0 111 0 0
ciMethod java/util/zip/Inflater ensureOpen ()V 512 0 5378 0 -1
ciMethod java/util/zip/Inflater inflateBytesBytes (J[BII[BII)J 770 0 385 0 -1
ciMethod java/util/zip/Inflater inflateBufferBytes (JJI[BII)J 0 0 1 0 -1
ciInstanceKlass java/util/zip/Inflater$InflaterZStreamRef 1 1 60 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 11 7 12 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/zip/Inflater$InflaterZStreamRef address ()J 268 0 134 0 -1
ciMethod jdk/internal/util/Preconditions outOfBoundsCheckFromIndexSize (Ljava/util/function/BiFunction;III)Ljava/lang/RuntimeException; 0 0 1 0 -1
ciMethod jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 512 0 39206 0 168
ciMethod java/lang/ref/ReferenceQueue headIsNull ()Z 778 0 12075 0 0
ciMethod java/lang/ref/ReferenceQueue poll0 ()Ljava/lang/ref/Reference; 4 0 240 0 -1
ciMethod java/lang/Math max (II)I 516 0 74420 0 -1
ciMethod java/lang/Math min (II)I 520 0 49699 0 -1
ciMethod jdk/internal/access/JavaLangAccess currentCarrierThread ()Ljava/lang/Thread; 0 0 1 0 -1
ciMethod java/lang/Thread currentCarrierThread ()Ljava/lang/Thread; 512 0 256 0 -1
ciMethod java/io/InputStream read ([BII)I 2 360 1 0 -1
ciMethod java/util/Set remove (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod jdk/internal/misc/VM isBooted ()Z 512 0 5448 0 0
ciMethod java/util/Map remove (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
instanceKlass org/apache/commons/io/FileExistsException
instanceKlass java/nio/channels/ClosedChannelException
instanceKlass java/net/SocketException
instanceKlass java/io/FileNotFoundException
instanceKlass java/io/ObjectStreamException
instanceKlass java/net/UnknownHostException
instanceKlass java/io/EOFException
instanceKlass java/util/zip/ZipException
instanceKlass java/net/MalformedURLException
ciInstanceKlass java/io/IOException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionError 0 0 79 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/zip/ZipException 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/EOFException 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/ref/ReferenceQueue headIsNull ()Z 2 11686 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40007 0xb0 0x38 0x2cf6 0x80003 0x2cf6 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipUtils SH ([BI)I 2 501051 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipUtils LG ([BI)J 2 61208 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x20002 0xef18 0x90002 0xef19 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/misc/VM isBooted ()Z 2 5192 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x40007 0x0 0x38 0x1448 0x80003 0x1448 0x18 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 2 38950 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 22 0x50007 0x0 0x40 0x9826 0xc0007 0x9826 0x30 0x0 0x130002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData jdk/internal/misc/Blocker begin ()J 2 5145 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 95 0x2 0x1419 0x30007 0x0 0x2a8 0x1419 0x60002 0x1419 0xb0004 0xffffffffffffebe7 0x0 0x1d0ee281348 0x6 0x0 0x0 0xe0007 0x1419 0x240 0x0 0x120004 0x0 0x0 0x0 0x0 0x0 0x0 0x170005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a0007 0x0 0x1b0 0x0 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x240005 0x0 0x0 0x0 0x0 0x0 0x0 0x270002 0x0 0x2e0007 0x0 0x60 0x0 0x310002 0x0 0x350007 0x0 0x30 0x0 0x3c0002 0x0 0x460007 0x0 0x58 0x0 0x4a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x530007 0x0 0x58 0x0 0x570005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x0 oops 1 11 java/lang/Thread methods 0
ciMethodData jdk/internal/misc/Blocker currentCarrierThread ()Ljava/lang/Thread; 2 5144 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x30005 0x0 0x0 0x1d0ee85da68 0x1418 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x0 oops 1 3 java/lang/System$2 methods 0
ciMethodData java/lang/System$2 currentCarrierThread ()Ljava/lang/Thread; 2 5144 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x2 0x1418 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData jdk/internal/misc/Blocker end (J)V 2 5136 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 83 0x30007 0x1410 0x248 0x0 0x90007 0x0 0x160 0x0 0xc0002 0x0 0x110004 0x0 0x0 0x0 0x0 0x0 0x0 0x140007 0x0 0xc8 0x0 0x180004 0x0 0x0 0x0 0x0 0x0 0x0 0x1d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x200007 0x0 0x38 0x0 0x240003 0x0 0x18 0x280007 0x0 0x30 0x0 0x2f0002 0x0 0x330002 0x0 0x360004 0x0 0x0 0x0 0x0 0x0 0x0 0x3b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f0002 0x0 0x430005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/ref/ReferenceQueue poll ()Ljava/lang/ref/Reference; 2 6176 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 48 0x10005 0x1820 0x0 0x0 0x0 0x0 0x0 0x40007 0x0 0x20 0x1820 0xd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x110005 0x0 0x0 0x0 0x0 0x0 0x0 0x190005 0x0 0x0 0x0 0x0 0x0 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 2 8199 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10007 0x2007 0x38 0x0 0x70003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap hash (Ljava/lang/Object;)I 2 6147 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x10005 0x87 0x0 0x1d0ee82e320 0xfa8 0x1d0ee7da040 0x7d4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/util/zip/ZipFile$ZipFileInflaterInputStream 5 java/util/zip/ZipFile$ZipFileInputStream methods 0
ciMethodData java/io/RandomAccessFile read ([BII)I 2 7993 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x40005 0x1f39 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/io/RandomAccessFile readBytes ([BII)I 2 5154 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 25 0x2 0x1422 0x90005 0x1422 0x0 0x0 0x0 0x0 0x0 0x100002 0x1422 0x1a0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/io/RandomAccessFile seek (J)V 2 6152 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 33 0x30007 0x1808 0x30 0x0 0xc0002 0x0 0x100002 0x1808 0x160005 0x1808 0x0 0x0 0x0 0x0 0x0 0x1a0002 0x1808 0x1d0003 0x1808 0x28 0x230002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/io/RandomAccessFile readFully ([BII)V 2 5133 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0xd0005 0x0 0x0 0x1d0ee43cff0 0x140d 0x0 0x0 0x140007 0x140d 0x30 0x0 0x1b0002 0x0 0x290007 0x0 0xffffffffffffff98 0x140d 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 3 java/io/RandomAccessFile methods 0
ciMethodData java/lang/ref/Reference refersTo (Ljava/lang/Object;)Z 2 6059 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20005 0x7 0x0 0x1d0ee29f278 0xaff 0x1d0ee3eaf78 0xca5 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/util/WeakHashMap$Entry 5 java/lang/ThreadLocal$ThreadLocalMap$Entry methods 0
ciMethodData java/util/Objects checkFromIndexSize (III)I 2 16054 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40002 0x3eb6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipFile$Source readFullyAt ([BIIJ)I 2 4304 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 37 0xe0005 0x0 0x0 0x1d0ee43cff0 0x10d0 0x0 0x0 0x160007 0x10d0 0x80 0x1547 0x1e0002 0x1547 0x2b0005 0x1547 0x0 0x0 0x0 0x0 0x0 0x3a0003 0x1547 0xffffffffffffff98 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 1 3 java/io/RandomAccessFile methods 0
ciMethodData java/util/zip/InflaterInputStream read ([BII)I 2 5120 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 109 0x10005 0x1400 0x0 0x0 0x0 0x0 0x0 0x50007 0x1400 0x30 0x0 0xc0002 0x0 0x140002 0x1400 0x190007 0x13c1 0x20 0x3f 0x220005 0x0 0x0 0x1d0ee82e270 0x13c1 0x0 0x0 0x250007 0x21f 0x78 0x11a2 0x2c0005 0x0 0x0 0x1d0ee82e270 0x11a2 0x0 0x0 0x2f0007 0x11a2 0x20 0x0 0x3d0005 0x0 0x0 0x1d0ee82e270 0x11a2 0x0 0x0 0x400007 0x6f5 0xb0 0xaad 0x470005 0x0 0x0 0x1d0ee82e270 0xaad 0x0 0x0 0x4a0007 0x0 0x58 0xaad 0x4e0005 0x0 0x0 0x1d0ee82e320 0xaad 0x0 0x0 0x580005 0x0 0x0 0x1d0ee82e270 0x11a2 0x0 0x0 0x5e0007 0x0 0xfffffffffffffe30 0x11a2 0x680005 0x0 0x0 0x0 0x0 0x0 0x0 0x730007 0x0 0x38 0x0 0x780003 0x0 0x18 0x7d0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0xffffffffffffffff 0xffffffffffffffff 0x0 0x0 oops 6 22 java/util/zip/Inflater 33 java/util/zip/Inflater 44 java/util/zip/Inflater 55 java/util/zip/Inflater 66 java/util/zip/ZipFile$ZipFileInflaterInputStream 73 java/util/zip/Inflater methods 0
ciMethodData java/util/zip/InflaterInputStream ensureOpen ()V 2 5120 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40007 0x1400 0x30 0x0 0xd0002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/Inflater finished ()Z 2 5057 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/Inflater needsDictionary ()Z 2 4514 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/Inflater needsInput ()Z 2 4516 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 37 0xd0007 0xa82 0x70 0x722 0x180007 0x6f6 0x38 0x2c 0x1c0003 0x2c 0xa0 0x200003 0x6f6 0x88 0x240005 0xa82 0x0 0x0 0x0 0x0 0x0 0x270007 0x0 0x38 0xa82 0x2b0003 0xa82 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/Inflater inflate ([BII)I 2 4530 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 208 0x70002 0x11b2 0x140005 0x11b2 0x0 0x0 0x0 0x0 0x0 0x1f0007 0x0 0xa8 0x11b2 0x2d0005 0x0 0x0 0x1d0f32f5690 0x11b2 0x0 0x0 0x400005 0x11b2 0x0 0x0 0x0 0x0 0x0 0x450003 0x11b2 0x388 0x5a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x610005 0x0 0x0 0x0 0x0 0x0 0x0 0x680002 0x0 0x6f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x720007 0x0 0x1d8 0x0 0x7a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x810004 0x0 0x0 0x0 0x0 0x0 0x0 0x840005 0x0 0x0 0x0 0x0 0x0 0x0 0x900005 0x0 0x0 0x0 0x0 0x0 0x0 0x9e0005 0x0 0x0 0x0 0x0 0x0 0x0 0xa80005 0x0 0x0 0x0 0x0 0x0 0x0 0xad0003 0x0 0x50 0xb70005 0x0 0x0 0x0 0x0 0x0 0x0 0xbf0003 0x0 0xa8 0xc40002 0x0 0xcb0002 0x0 0xd50005 0x0 0x0 0x0 0x0 0x0 0x0 0xe40005 0x0 0x0 0x0 0x0 0x0 0x0 0xe90003 0x0 0x50 0xf70005 0x0 0x0 0x0 0x0 0x0 0x0 0xfe0003 0x11b2 0x18 0x14e0007 0x72b 0x20 0xa87 0x1590007 0x18e 0x58 0x1024 0x1600007 0x925 0x38 0x6ff 0x1680003 0x6ff 0x18 0x1790007 0x11b2 0x20 0x0 0x1830007 0x11b2 0x90 0x0 0x1880007 0x0 0x70 0x0 0x1920005 0x0 0x0 0x0 0x0 0x0 0x0 0x1960003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 16 java/util/zip/Inflater$InflaterZStreamRef methods 0
ciMethodData java/lang/ref/Reference refersToImpl (Ljava/lang/Object;)Z 2 5246 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x20005 0x147e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap getTable ()[Ljava/util/WeakHashMap$Entry; 2 8075 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x10005 0x1f8b 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap expungeStaleEntries ()V 2 6150 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 64 0x40005 0x0 0x0 0x1d0ee871ef8 0x1806 0x0 0x0 0x90007 0x1806 0x178 0x0 0x140004 0x0 0x0 0x0 0x0 0x0 0x0 0x210002 0x0 0x350007 0x0 0xe0 0x0 0x420007 0x0 0xa8 0x0 0x480007 0x0 0x70 0x0 0x530004 0x0 0x0 0x0 0x0 0x0 0x0 0x540003 0x0 0x18 0x6d0003 0x0 0x30 0x780003 0x0 0xffffffffffffff38 0x7d0003 0x0 0x18 0x870003 0x0 0xfffffffffffffe68 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/lang/ref/ReferenceQueue methods 0
ciMethodData java/util/WeakHashMap indexFor (II)I 2 8161 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 5 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipFile$ZipFileInputStream close ()V 2 5144 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x40007 0xa0c 0x20 0xa0c 0x2a0005 0x0 0x0 0x1d0ee29b720 0xa0c 0x0 0x0 0x320003 0xa0c 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 7 java/util/Collections$SetFromMap methods 0
ciMethodData java/util/Collections$SetFromMap remove (Ljava/lang/Object;)Z 2 5248 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 24 0x50005 0x0 0x0 0x1d0ee29d6a8 0x1480 0x0 0x0 0xa0007 0xa0c 0x38 0xa74 0xe0003 0xa74 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 1 3 java/util/WeakHashMap methods 0
ciMethodData java/util/zip/ZipUtils LOCSIG ([B)J 2 3005 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x20002 0xbbd 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap remove (Ljava/lang/Object;)Ljava/lang/Object; 2 5120 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 64 0x10002 0x1400 0x70005 0x1400 0x0 0x0 0x0 0x0 0x0 0xc0005 0x1400 0x0 0x0 0x0 0x0 0x0 0x150002 0x1400 0x270007 0x9cc 0x120 0xad2 0x370007 0x9e 0xe8 0xa34 0x3e0005 0xa34 0x0 0x0 0x0 0x0 0x0 0x410007 0x0 0x90 0xa34 0x5c0007 0x0 0x70 0xa34 0x650104 0x0 0x0 0x0 0x0 0x0 0x0 0x660003 0xa34 0x18 0x7e0003 0x9e 0xfffffffffffffef8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/WeakHashMap matchesKey (Ljava/util/WeakHashMap$Entry;Ljava/lang/Object;)Z 2 2624 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 47 0x20005 0xa40 0x0 0x0 0x0 0x0 0x0 0x50007 0x0 0x20 0xa40 0xb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x100007 0x0 0x90 0x0 0x150005 0x0 0x0 0x0 0x0 0x0 0x0 0x180007 0x0 0x38 0x0 0x1c0003 0x0 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/nio/Buffer hasRemaining ()Z 2 3639 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x80007 0xa4e 0x38 0x3e9 0xc0003 0x3e9 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipFile$ZipFileInflaterInputStream fill ()V 2 2332 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x40007 0x91e 0x30 0x0 0xd0002 0x0 0x200005 0x0 0x0 0x1d0ee7da040 0x91d 0x0 0x0 0x2b0007 0x91e 0x20 0x0 0x4c0005 0x0 0x0 0x1d0ee82e270 0x91e 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 9 java/util/zip/ZipFile$ZipFileInputStream 20 java/util/zip/Inflater methods 0
ciMethodData java/util/zip/ZipFile$ZipFileInputStream read ([BII)I 2 2333 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0xc0005 0x91e 0x0 0x0 0x0 0x0 0x0 0x100005 0x91e 0x0 0x0 0x0 0x0 0x0 0x1a0007 0x91e 0x20 0x0 0x290007 0x71 0x20 0x8ad 0x330007 0x91e 0x20 0x0 0x4c0005 0x91e 0x0 0x0 0x0 0x0 0x0 0x510007 0x0 0x20 0x91e 0x6d0003 0x91e 0x18 0x7e0007 0x2c 0x58 0x8f2 0x820005 0x0 0x0 0x1d0ee7da040 0x8f2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 1 47 java/util/zip/ZipFile$ZipFileInputStream methods 0
ciMethodData java/util/zip/ZipFile ensureOpenOrZipException ()V 2 2333 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 13 0x40007 0x91e 0x30 0x0 0xe0002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipFile$ZipFileInputStream initDataOffset ()J 2 2334 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 41 0x60007 0x2c 0xe8 0x8f3 0x290005 0x8f3 0x0 0x0 0x0 0x0 0x0 0x300007 0x8f3 0x30 0x0 0x390002 0x0 0x3e0002 0x8f3 0x450007 0x8f3 0x30 0x0 0x4e0002 0x0 0x5a0002 0x8f3 0x5f0002 0x8f3 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipUtils LOCNAM ([B)I 2 2291 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x30002 0x8f4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipUtils LOCEXT ([B)I 2 2291 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x30002 0x8f4 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/zip/ZipFile$Source readAt ([BIIJ)I 2 2336 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0xe0005 0x0 0x0 0x1d0ee43cff0 0x921 0x0 0x0 0x180005 0x0 0x0 0x1d0ee43cff0 0x921 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x6 0x0 0x0 0x0 0x0 0x0 0x0 oops 2 3 java/io/RandomAccessFile 10 java/io/RandomAccessFile methods 0
ciMethodData java/util/zip/Inflater setInput ([BII)V 2 2337 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 17 0x70002 0x922 0x2c0003 0x922 0x18 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
compile java/util/zip/InflaterInputStream read ([BII)I -1 4 inline 65 0 -1 0 java/util/zip/InflaterInputStream read ([BII)I 1 1 0 java/util/zip/InflaterInputStream ensureOpen ()V 1 20 0 java/util/Objects checkFromIndexSize (III)I 2 4 0 jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I 1 34 0 java/util/zip/Inflater finished ()Z 1 44 0 java/util/zip/Inflater needsDictionary ()Z 1 61 0 java/util/zip/Inflater needsInput ()Z 2 36 0 java/nio/Buffer hasRemaining ()Z 1 71 0 java/util/zip/Inflater hasPendingOutput ()Z 1 78 0 java/util/zip/ZipFile$ZipFileInflaterInputStream fill ()V 2 32 0 java/util/zip/ZipFile$ZipFileInputStream read ([BII)I 3 12 0 java/util/zip/ZipFile ensureOpenOrZipException ()V 3 16 0 java/util/zip/ZipFile$ZipFileInputStream initDataOffset ()J 4 41 0 java/util/zip/ZipFile$Source readFullyAt ([BIIJ)I 5 14 0 java/io/RandomAccessFile seek (J)V 6 16 0 jdk/internal/misc/Blocker begin ()J 7 0 0 jdk/internal/misc/VM isBooted ()Z 7 6 0 jdk/internal/misc/Blocker currentCarrierThread ()Ljava/lang/Thread; 8 3 0 java/lang/System$2 currentCarrierThread ()Ljava/lang/Thread; 6 26 0 jdk/internal/misc/Blocker end (J)V 5 43 0 java/io/RandomAccessFile readFully ([BII)V 6 13 0 java/io/RandomAccessFile read ([BII)I 7 4 0 java/io/RandomAccessFile readBytes ([BII)I 8 0 0 jdk/internal/misc/Blocker begin ()J 9 0 0 jdk/internal/misc/VM isBooted ()Z 9 6 0 jdk/internal/misc/Blocker currentCarrierThread ()Ljava/lang/Thread; 10 3 0 java/lang/System$2 currentCarrierThread ()Ljava/lang/Thread; 8 16 0 jdk/internal/misc/Blocker end (J)V 4 62 0 java/util/zip/ZipUtils LOCSIG ([B)J 5 2 0 java/util/zip/ZipUtils LG ([BI)J 6 2 0 java/util/zip/ZipUtils SH ([BI)I 6 9 0 java/util/zip/ZipUtils SH ([BI)I 4 90 0 java/util/zip/ZipUtils LOCNAM ([B)I 5 3 0 java/util/zip/ZipUtils SH ([BI)I 4 95 0 java/util/zip/ZipUtils LOCEXT ([B)I 5 3 0 java/util/zip/ZipUtils SH ([BI)I 3 76 0 java/util/zip/ZipFile$Source readAt ([BIIJ)I 4 14 0 java/io/RandomAccessFile seek (J)V 5 16 0 jdk/internal/misc/Blocker begin ()J 6 0 0 jdk/internal/misc/VM isBooted ()Z 6 6 0 jdk/internal/misc/Blocker currentCarrierThread ()Ljava/lang/Thread; 7 3 0 java/lang/System$2 currentCarrierThread ()Ljava/lang/Thread; 5 26 0 jdk/internal/misc/Blocker end (J)V 4 24 0 java/io/RandomAccessFile read ([BII)I 5 4 0 java/io/RandomAccessFile readBytes ([BII)I 6 0 0 jdk/internal/misc/Blocker begin ()J 7 0 0 jdk/internal/misc/VM isBooted ()Z 7 6 0 jdk/internal/misc/Blocker currentCarrierThread ()Ljava/lang/Thread; 8 3 0 java/lang/System$2 currentCarrierThread ()Ljava/lang/Thread; 6 16 0 jdk/internal/misc/Blocker end (J)V 3 130 0 java/util/zip/ZipFile$ZipFileInputStream close ()V 4 42 0 java/util/Collections$SetFromMap remove (Ljava/lang/Object;)Z 5 5 0 java/util/WeakHashMap remove (Ljava/lang/Object;)Ljava/lang/Object; 6 1 0 java/util/WeakHashMap maskNull (Ljava/lang/Object;)Ljava/lang/Object; 6 7 0 java/util/WeakHashMap hash (Ljava/lang/Object;)I 6 12 0 java/util/WeakHashMap getTable ()[Ljava/util/WeakHashMap$Entry; 7 1 0 java/util/WeakHashMap expungeStaleEntries ()V 8 4 0 java/lang/ref/ReferenceQueue poll ()Ljava/lang/ref/Reference; 9 1 0 java/lang/ref/ReferenceQueue headIsNull ()Z 6 21 0 java/util/WeakHashMap indexFor (II)I 6 62 0 java/util/WeakHashMap matchesKey (Ljava/util/WeakHashMap$Entry;Ljava/lang/Object;)Z 7 2 0 java/lang/ref/Reference refersTo (Ljava/lang/Object;)Z 8 2 0 java/lang/ref/Reference refersToImpl (Ljava/lang/Object;)Z 2 76 0 java/util/zip/Inflater setInput ([BII)V 3 7 0 jdk/internal/util/Preconditions checkFromIndexSize (IIILjava/util/function/BiFunction;)I
