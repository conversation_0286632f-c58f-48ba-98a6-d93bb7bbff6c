package com.mdsadrulhasan.gogolaundry.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * Adapter for displaying shop items in RecyclerView
 */
public class ShopItemAdapter extends RecyclerView.Adapter<ShopItemAdapter.ItemViewHolder> {

    private List<ShopItemEntity> items;
    private final OnItemClickListener listener;

    /**
     * Interface for handling item clicks
     */
    public interface OnItemClickListener {
        void onItemClick(ShopItemEntity item);
        void onAddToCartClick(ShopItemEntity item);
    }

    /**
     * Constructor
     *
     * @param items List of items
     * @param listener Click listener
     */
    public ShopItemAdapter(List<ShopItemEntity> items, OnItemClickListener listener) {
        this.items = items != null ? items : new ArrayList<>();
        this.listener = listener;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_shop_item, parent, false);
        return new ItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        ShopItemEntity item = items.get(position);
        holder.bind(item, listener);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    /**
     * Update items list
     *
     * @param newItems New list of items
     */
    public void updateItems(List<ShopItemEntity> newItems) {
        this.items = newItems != null ? newItems : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * ViewHolder for item
     */
    static class ItemViewHolder extends RecyclerView.ViewHolder {
        private final ImageView itemImage;
        private final TextView itemName;
        private final TextView itemPrice;
        private final TextView serviceName;
        private final TextView estimatedTime;
        private final Button addToCartButton;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            itemImage = itemView.findViewById(R.id.itemImageView);
            itemName = itemView.findViewById(R.id.itemNameTextView);
            itemPrice = itemView.findViewById(R.id.priceTextView);
            serviceName = itemView.findViewById(R.id.serviceCategoryTextView);
            estimatedTime = itemView.findViewById(R.id.estimatedTimeTextView);
            addToCartButton = itemView.findViewById(R.id.addToCartButton);
        }

        public void bind(ShopItemEntity item, OnItemClickListener listener) {
            // Set item name (prefer Bengali name if available)
            String displayName = item.getItemBnName() != null && !item.getItemBnName().isEmpty()
                    ? item.getItemBnName()
                    : item.getItemName();
            itemName.setText(displayName);

            // Set price (custom price if available, otherwise default price)
            double price = item.getCustomPrice() != null ? item.getCustomPrice() : item.getDefaultPrice();
            itemPrice.setText(itemView.getContext().getString(R.string.price_format, price));

            // Set service name
            serviceName.setText(item.getServiceName());

            // Set estimated time
            estimatedTime.setText(itemView.getContext().getString(R.string.estimated_time_hours, item.getEstimatedHours()));

            // Load item image
            if (item.getItemImageUrl() != null && !item.getItemImageUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(item.getItemImageUrl())
                        .placeholder(R.drawable.ic_item_placeholder)
                        .error(R.drawable.ic_item_placeholder)
                        .into(itemImage);
            } else {
                itemImage.setImageResource(R.drawable.ic_item_placeholder);
            }

            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(item);
                }
            });

            addToCartButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onAddToCartClick(item);
                }
            });
        }
    }
}
