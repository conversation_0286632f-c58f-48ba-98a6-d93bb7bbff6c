package com.mdsadrulhasan.gogolaundry.adapter;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Service;

import java.util.List;

/**
 * Adapter for displaying popular services in a horizontal RecyclerView
 * with enhanced sizing to match popular items
 */
public class PopularServiceAdapter extends RecyclerView.Adapter<PopularServiceAdapter.ServiceViewHolder> {
    
    private List<Service> services;
    private final ServiceClickListener listener;
    
    /**
     * Interface for handling service clicks
     */
    public interface ServiceClickListener {
        void onServiceClicked(Service service);
        void onViewItemsClicked(Service service);
    }
    
    /**
     * Constructor
     * 
     * @param services List of services
     * @param listener Click listener
     */
    public PopularServiceAdapter(List<Service> services, ServiceClickListener listener) {
        this.services = services;
        this.listener = listener;
    }
    
    /**
     * Update services list and refresh adapter
     * 
     * @param services New list of services
     */
    public void updateServices(List<Service> services) {
        this.services = services;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public ServiceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_popular_service, parent, false);
        return new ServiceViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ServiceViewHolder holder, int position) {
        Service service = services.get(position);
        holder.bind(service, listener);
    }
    
    @Override
    public int getItemCount() {
        return services != null ? services.size() : 0;
    }
    
    /**
     * ViewHolder for service items
     */
    static class ServiceViewHolder extends RecyclerView.ViewHolder {
        private final ImageView serviceIcon;
        private final TextView serviceName;
        private final TextView serviceDescription;
        private final MaterialButton btnViewItems;

        public ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            serviceIcon = itemView.findViewById(R.id.service_icon);
            serviceName = itemView.findViewById(R.id.service_name);
            serviceDescription = itemView.findViewById(R.id.service_description);
            btnViewItems = itemView.findViewById(R.id.btn_view_items);
        }
        
        /**
         * Bind service data to views
         * 
         * @param service Service to display
         * @param listener Click listener
         */
        public void bind(Service service, ServiceClickListener listener) {
            // Set service name
            serviceName.setText(service.getName());

            // Set service description
            if (service.getDescription() != null && !service.getDescription().isEmpty()) {
                serviceDescription.setText(service.getDescription());
                serviceDescription.setVisibility(View.VISIBLE);
            } else {
                serviceDescription.setText("Professional laundry service");
                serviceDescription.setVisibility(View.VISIBLE);
            }
            
            // Load service icon
            if (service.getIconUrl() != null && !service.getIconUrl().isEmpty()) {
                String imageUrl = service.getIconUrl();
                
                // Log the image URL for debugging
                Log.d("PopularServiceAdapter", "Loading service icon from: " + imageUrl);
                
                // Load the image with Glide
                Glide.with(itemView.getContext())
                        .load(imageUrl)
                        .apply(new RequestOptions()
                                .placeholder(getDefaultIcon(service))
                                .error(getDefaultIcon(service)))
                        .into(serviceIcon);
            } else {
                // Set default icon based on service name
                int defaultIcon = getDefaultIcon(service);
                serviceIcon.setImageResource(defaultIcon);
                Log.d("PopularServiceAdapter", "No icon URL available for service: " + service.getName());
            }
            
            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onServiceClicked(service);
                }
            });
            
            btnViewItems.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onViewItemsClicked(service);
                }
            });
        }
        
        /**
         * Get default icon based on service name
         * 
         * @param service Service
         * @return Icon resource ID
         */
        private int getDefaultIcon(Service service) {
            String serviceName = service.getName().toLowerCase();
            
            if (serviceName.contains("wash") || serviceName.contains("laundry")) {
                return R.drawable.ic_washing_machine;
            } else if (serviceName.contains("dry") || serviceName.contains("clean")) {
                return R.drawable.ic_dry_cleaning;
            } else if (serviceName.contains("iron") || serviceName.contains("press")) {
                return R.drawable.ic_iron;
            } else if (serviceName.contains("fold")) {
                return R.drawable.ic_folding;
            } else if (serviceName.contains("pickup") || serviceName.contains("delivery")) {
                return R.drawable.ic_delivery;
            } else {
                return R.drawable.ic_washing_machine; // Default fallback
            }
        }
    }
}
