<?php
/**
 * Order Details API
 *
 * This file handles the API endpoint for retrieving order details
 */

// Include required files
require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/OrderManager.php';

// Log access to this endpoint for debugging
error_log("API endpoint accessed: details.php");

// Set headers
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle different request methods
if ($method !== 'GET') {
    // Method not allowed
    header("HTTP/1.1 405 Method Not Allowed");
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if order ID is provided
if (!isset($_GET['order_id'])) {
    jsonResponse(false, 'Missing required parameter: order_id', [], 400);
}

$orderId = (int)$_GET['order_id'];

try {
    // Add more detailed error logging
    error_log("Starting order details retrieval for order ID: " . $orderId);

    // Initialize OrderManager
    $orderManager = new OrderManager($pdo);
    error_log("OrderManager initialized");

    // Get order details
    error_log("Attempting to get order details for ID: " . $orderId);
    $order = $orderManager->getOrderById($orderId);
    error_log("Order query executed");

    if (!$order) {
        error_log("Order not found for ID: " . $orderId);
        jsonResponse(false, 'Order not found', [], 404);
    }

    // Get order items
    error_log("Attempting to get order items for ID: " . $orderId);
    $orderItems = $orderManager->getOrderItems($orderId);
    error_log("Order items query executed");

    // Get order status history
    error_log("Attempting to get order status history for ID: " . $orderId);
    $statusHistory = $orderManager->getOrderStatusHistory($orderId);
    error_log("Order status history query executed");

    // Debug information
    error_log("Order ID: " . $orderId);
    error_log("Order: " . print_r($order, true));
    error_log("Order Items: " . print_r($orderItems, true));
    error_log("Status History: " . print_r($statusHistory, true));

    // Prepare response data
    $responseData = [
        'order' => $order,
        'items' => $orderItems,
        'status_history' => $statusHistory
    ];

    // Return response
    error_log("Sending successful response for order ID: " . $orderId);
    jsonResponse(true, 'Order details retrieved successfully', $responseData);
} catch (PDOException $e) {
    // Log detailed error
    error_log('Database Error in details.php: ' . $e->getMessage());
    error_log('Error code: ' . $e->getCode());
    error_log('Error trace: ' . $e->getTraceAsString());

    // Return error response
    jsonResponse(false, 'An error occurred while retrieving the order details: ' . $e->getMessage(), [], 500);
} catch (Exception $e) {
    // Log any other exceptions
    error_log('General Error in details.php: ' . $e->getMessage());
    error_log('Error trace: ' . $e->getTraceAsString());

    // Return error response
    jsonResponse(false, 'An unexpected error occurred: ' . $e->getMessage(), [], 500);
}

// Note: jsonResponse function is already defined in functions.php, so we don't need to redefine it here
