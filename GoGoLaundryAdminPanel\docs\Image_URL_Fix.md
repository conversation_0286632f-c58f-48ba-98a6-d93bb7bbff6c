# Image URL Fix for Notifications

## Problem Description
When sending notifications with uploaded images, the Android app was failing to load images with the error:
```
Error loading image: no protocol: /GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/notification_1748059154_683144123a712.png
```

## Root Cause
The issue was that the admin panel was storing **relative URLs** instead of **absolute URLs** for uploaded images. The Android app's image loading mechanism requires full URLs with protocol (http/https).

### Before Fix:
- **Stored URL**: `/GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/image.png`
- **Android app receives**: Relative path without protocol
- **Result**: Image loading fails with "no protocol" error

### After Fix:
- **Stored URL**: `http://*************/GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/image.png`
- **Android app receives**: Full absolute URL
- **Result**: Image loads successfully

## Solution Implemented

### 1. Admin Panel Fix (notifications.php)
**File**: `GoGoLaundryAdminPanel/admin/notifications.php`

**Before**:
```php
if (move_uploaded_file($fileTmpName, $targetPath)) {
    $imageUrl = '/GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/' . $newFileName;
}
```

**After**:
```php
if (move_uploaded_file($fileTmpName, $targetPath)) {
    // Create full URL instead of relative path
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $imageUrl = $protocol . '://' . $host . '/GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/' . $newFileName;
}
```

### 2. Android App Enhancement (GoGoLaundryFirebaseMessagingService.java)
**File**: `GoGoLaundryApp/app/src/main/java/com/mdsadrulhasan/gogolaundry/fcm/GoGoLaundryFirebaseMessagingService.java`

Added fallback mechanism to handle both relative and absolute URLs:

```java
private String convertToAbsoluteUrl(String imageUrl) {
    if (TextUtils.isEmpty(imageUrl)) {
        return imageUrl;
    }
    
    // If already absolute URL, return as is
    if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
        return imageUrl;
    }
    
    // Get base URL from ApiClient and remove '/api/' suffix
    String apiBaseUrl = ApiClient.getBaseUrl();
    String baseUrl = apiBaseUrl.replace("/api/", "");
    
    // Convert relative to absolute URL
    if (imageUrl.startsWith("/")) {
        return baseUrl + imageUrl;
    } else {
        return baseUrl + "/" + imageUrl;
    }
}
```

### 3. Enhanced Error Logging
Added comprehensive logging to help debug image loading issues:

```java
protected Bitmap doInBackground(String... urls) {
    String imageUrl = urls[0];
    Log.d(TAG, "Attempting to load image from: " + imageUrl);
    
    try {
        // ... image loading code ...
        int responseCode = connection.getResponseCode();
        Log.d(TAG, "HTTP response code: " + responseCode);
        
        if (bitmap != null) {
            Log.d(TAG, "Image loaded successfully. Original size: " + bitmap.getWidth() + "x" + bitmap.getHeight());
        }
    } catch (Exception e) {
        Log.e(TAG, "Error loading image from URL: " + imageUrl + " - " + e.getMessage());
        e.printStackTrace();
    }
}
```

## Benefits of the Fix

### 1. Automatic Protocol Detection
- **HTTP**: For local development
- **HTTPS**: For production environments
- **Dynamic**: Based on server configuration

### 2. Backward Compatibility
- **New uploads**: Generate absolute URLs automatically
- **Old relative URLs**: Converted to absolute URLs in Android app
- **External URLs**: Work without modification

### 3. Robust Error Handling
- **Detailed logging**: Better debugging capabilities
- **Graceful fallback**: Falls back to text notification if image fails
- **Network timeout**: 10-second timeout prevents hanging

### 4. Performance Optimizations
- **Image resizing**: Prevents memory issues
- **Asynchronous loading**: Doesn't block UI
- **Connection management**: Proper cleanup

## Testing Results

### Before Fix:
- ❌ Uploaded images: Failed to load (no protocol error)
- ✅ External URLs: Worked correctly
- ❌ Error messages: Unclear

### After Fix:
- ✅ Uploaded images: Load correctly with full URLs
- ✅ External URLs: Continue to work
- ✅ Old relative URLs: Automatically converted
- ✅ Error messages: Detailed and helpful

## URL Examples

### Generated URLs:
- **Local development**: `http://*************/GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/image.png`
- **Production HTTPS**: `https://yourdomain.com/GoGoLaundry/GoGoLaundryAdminPanel/uploads/notifications/image.png`

### Supported URL formats:
- **Absolute HTTP**: `http://example.com/image.png`
- **Absolute HTTPS**: `https://example.com/image.png`
- **Relative (converted)**: `/path/to/image.png` → `http://*************/path/to/image.png`

## Configuration

The Android app automatically uses the same base URL as the API client, ensuring consistency across the application. No manual configuration required.

## Future Enhancements

1. **CDN Support**: Easy to add CDN URLs
2. **Image Caching**: Can implement local caching
3. **Compression**: Server-side image optimization
4. **Multiple Formats**: Support for WebP, AVIF, etc.

## Verification Steps

1. **Upload image** in admin panel notification form
2. **Check database**: Verify full URL is stored
3. **Send notification**: Verify Android app receives full URL
4. **Check logs**: Verify successful image loading
5. **View notification**: Verify image displays correctly
