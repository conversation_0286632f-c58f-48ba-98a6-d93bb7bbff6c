package com.mdsadrulhasan.gogolaundry.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Utility class for date operations
 */
public class DateUtils {

    /**
     * Format a date using the specified pattern
     *
     * @param date Date to format
     * @param pattern Format pattern (e.g., "yyyy-MM-dd")
     * @return Formatted date string
     */
    public static String formatDate(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.getDefault());
        return sdf.format(date);
    }

    /**
     * Format a date using the default pattern (yyyy-MM-dd)
     *
     * @param date Date to format
     * @return Formatted date string
     */
    public static String formatDate(Date date) {
        return formatDate(date, "yyyy-MM-dd");
    }

    /**
     * Format a date and time using the specified pattern
     *
     * @param date Date to format
     * @param pattern Format pattern (e.g., "yyyy-MM-dd HH:mm:ss")
     * @return Formatted date and time string
     */
    public static String formatDateTime(Date date, String pattern) {
        return formatDate(date, pattern);
    }

    /**
     * Format a date and time using the default pattern (yyyy-MM-dd HH:mm:ss)
     *
     * @param date Date to format
     * @return Formatted date and time string
     */
    public static String formatDateTime(Date date) {
        return formatDate(date, "yyyy-MM-dd HH:mm:ss");
    }
}
