<?php
/**
 * Location Manager Class
 *
 * This class handles location data (divisions, districts, upazillas)
 */

class LocationManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get all divisions
     *
     * @return array Divisions
     */
    public function getAllDivisions() {
        $stmt = $this->pdo->query("
            SELECT *
            FROM divisions
            ORDER BY name ASC
        ");
        return $stmt->fetchAll();
    }

    /**
     * Get division by ID
     *
     * @param int $divisionId Division ID
     * @return array|bool Division data or false if not found
     */
    public function getDivisionById($divisionId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM divisions
            WHERE id = ?
        ");
        $stmt->execute([$divisionId]);
        return $stmt->fetch();
    }

    /**
     * Get all districts
     *
     * @return array Districts
     */
    public function getAllDistricts() {
        $stmt = $this->pdo->query("
            SELECT *
            FROM districts
            ORDER BY name ASC
        ");
        return $stmt->fetchAll();
    }

    /**
     * Get districts by division ID
     *
     * @param int $divisionId Division ID
     * @return array Districts
     */
    public function getDistrictsByDivision($divisionId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM districts
            WHERE division_id = ?
            ORDER BY name ASC
        ");
        $stmt->execute([$divisionId]);
        return $stmt->fetchAll();
    }

    /**
     * Get district by ID
     *
     * @param int $districtId District ID
     * @return array|bool District data or false if not found
     */
    public function getDistrictById($districtId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM districts
            WHERE id = ?
        ");
        $stmt->execute([$districtId]);
        return $stmt->fetch();
    }

    // Method moved to avoid duplication

    /**
     * Get all upazillas
     *
     * @return array Upazillas
     */
    public function getAllUpazillas() {
        $stmt = $this->pdo->query("
            SELECT *
            FROM upazillas
            ORDER BY name ASC
        ");
        return $stmt->fetchAll();
    }

    /**
     * Get upazillas by district ID
     *
     * @param int $districtId District ID
     * @return array Upazillas
     */
    public function getUpazillasByDistrict($districtId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM upazillas
            WHERE district_id = ?
            ORDER BY name ASC
        ");
        $stmt->execute([$districtId]);
        return $stmt->fetchAll();
    }

    /**
     * Get upazilla by ID
     *
     * @param int $upazillaId Upazilla ID
     * @return array|bool Upazilla data or false if not found
     */
    public function getUpazillaById($upazillaId) {
        $stmt = $this->pdo->prepare("
            SELECT *
            FROM upazillas
            WHERE id = ?
        ");
        $stmt->execute([$upazillaId]);
        return $stmt->fetch();
    }

    /**
     * Get full location path (Division > District > Upazilla)
     *
     * @param int $divisionId Division ID
     * @param int $districtId District ID
     * @param int $upazillaId Upazilla ID
     * @return string Full location path
     */
    public function getFullLocationPath($divisionId, $districtId, $upazillaId) {
        $path = [];

        if ($divisionId) {
            $division = $this->getDivisionById($divisionId);
            if ($division) {
                $path[] = $division['name'];
            }
        }

        if ($districtId) {
            $district = $this->getDistrictById($districtId);
            if ($district) {
                $path[] = $district['name'];
            }
        }

        if ($upazillaId) {
            $upazilla = $this->getUpazillaById($upazillaId);
            if ($upazilla) {
                $path[] = $upazilla['name'];
            }
        }

        return implode(' > ', $path);
    }
}
