package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.mdsadrulhasan.gogolaundry.MainActivity;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.repository.NotificationRepository;
import com.mdsadrulhasan.gogolaundry.utils.LanguageManager;

/**
 * Settings Fragment for app settings and preferences
 */
public class SettingsFragment extends Fragment {
    private static final String TAG = "SettingsFragment";

    private RadioGroup languageRadioGroup;
    private RadioButton englishRadioButton;
    private RadioButton banglaRadioButton;
    private RadioButton arabicRadioButton;
    private MaterialButton applyButton;
    private MaterialCardView languageCard;
    private LanguageManager languageManager;

    public SettingsFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        languageManager = new LanguageManager(requireContext());
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_settings, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize views
        initViews(view);

        // Set up listeners
        setupListeners();

        // Set initial state
        setInitialState();
    }

    /**
     * Initialize views
     *
     * @param view Root view
     */
    private void initViews(View view) {
        // Language settings
        languageCard = view.findViewById(R.id.language_card);
        languageRadioGroup = view.findViewById(R.id.language_radio_group);
        englishRadioButton = view.findViewById(R.id.radio_english);
        banglaRadioButton = view.findViewById(R.id.radio_bangla);
        arabicRadioButton = view.findViewById(R.id.radio_arabic);
        applyButton = view.findViewById(R.id.apply_button);

    }

    /**
     * Set up listeners
     */
    private void setupListeners() {
        // Language settings
        applyButton.setOnClickListener(v -> applyLanguageChange());


    }

    /**
     * Set initial state based on current settings
     */
    private void setInitialState() {
        // Set language state
        String currentLanguage = languageManager.getLanguage();

        switch (currentLanguage) {
            case LanguageManager.LANGUAGE_BANGLA:
                banglaRadioButton.setChecked(true);
                break;
            case LanguageManager.LANGUAGE_ARABIC:
                arabicRadioButton.setChecked(true);
                break;
            case LanguageManager.LANGUAGE_ENGLISH:
            default:
                englishRadioButton.setChecked(true);
                break;
        }


    }

    /**
     * Apply language change
     */
    private void applyLanguageChange() {
        int selectedId = languageRadioGroup.getCheckedRadioButtonId();
        String selectedLanguage;

        if (selectedId == R.id.radio_bangla) {
            selectedLanguage = LanguageManager.LANGUAGE_BANGLA;
        } else if (selectedId == R.id.radio_arabic) {
            selectedLanguage = LanguageManager.LANGUAGE_ARABIC;
        } else {
            selectedLanguage = LanguageManager.LANGUAGE_ENGLISH;
        }

        // Check if language has changed
        if (!selectedLanguage.equals(languageManager.getLanguage())) {
            // Save the selected language
            languageManager.setLanguage(selectedLanguage);

            // Show success message
            Toast.makeText(requireContext(), R.string.language_changed, Toast.LENGTH_SHORT).show();

            // Restart the activity to apply changes
            restartApp();
        } else {
            // Show message that language is already selected
            Toast.makeText(requireContext(), R.string.language_already_selected, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Restart the app to apply language changes
     */
    private void restartApp() {
        Intent intent = new Intent(requireContext(), MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        requireActivity().finish();
    }
}
