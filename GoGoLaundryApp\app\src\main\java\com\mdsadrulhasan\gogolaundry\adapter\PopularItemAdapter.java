package com.mdsadrulhasan.gogolaundry.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Item;

import java.util.ArrayList;
import java.util.List;

/**
 * Adapter for displaying popular items in a horizontal RecyclerView
 */
public class PopularItemAdapter extends RecyclerView.Adapter<PopularItemAdapter.ItemViewHolder> {
    
    private List<Item> items;
    private final ItemClickListener listener;
    
    /**
     * Interface for handling item clicks
     */
    public interface ItemClickListener {
        void onItemClicked(Item item);
        void onAddToCartClicked(Item item, int position);
    }
    
    /**
     * Constructor
     * 
     * @param items List of items
     * @param listener Click listener
     */
    public PopularItemAdapter(List<Item> items, ItemClickListener listener) {
        this.items = items;
        this.listener = listener;
    }
    
    /**
     * Update items list
     * 
     * @param newItems New list of items
     */
    public void updateItems(List<Item> newItems) {
        this.items = new ArrayList<>(newItems);
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_popular_item_horizontal, parent, false);
        return new ItemViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        Item item = items.get(position);
        holder.bind(item, position);
    }
    
    @Override
    public int getItemCount() {
        return items.size();
    }
    
    /**
     * ViewHolder for popular items
     */
    class ItemViewHolder extends RecyclerView.ViewHolder {

        private final ImageView itemImage;
        private final TextView itemName;
        private final TextView itemPrice;
        private final MaterialButton addToCartButton;
        private final FrameLayout outOfStockOverlay;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);

            itemImage = itemView.findViewById(R.id.item_image);
            itemName = itemView.findViewById(R.id.item_name);
            itemPrice = itemView.findViewById(R.id.item_price);
            addToCartButton = itemView.findViewById(R.id.add_to_cart_button);
            outOfStockOverlay = itemView.findViewById(R.id.out_of_stock_overlay);
        }
        
        /**
         * Bind item data to views
         * 
         * @param item Item to bind
         * @param position Position in adapter
         */
        public void bind(final Item item, final int position) {
            // Set item name
            itemName.setText(item.getName());

            // Set price
            itemPrice.setText(item.getFormattedPrice());

            // Handle out of stock state
            if (item.isInStock()) {
                outOfStockOverlay.setVisibility(View.GONE);
                addToCartButton.setEnabled(true);
                addToCartButton.setAlpha(1.0f);
            } else {
                outOfStockOverlay.setVisibility(View.VISIBLE);
                addToCartButton.setEnabled(false);
                addToCartButton.setAlpha(0.5f);
            }

            // Load image if available
            if (item.getImageUrl() != null && !item.getImageUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(item.getImageUrl())
                        .apply(new RequestOptions()
                                .placeholder(R.drawable.placeholder_image)
                                .error(R.drawable.placeholder_image))
                        .into(itemImage);
            } else {
                // Set default image
                itemImage.setImageResource(R.drawable.placeholder_image);
            }

            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClicked(item);
                }
            });
            
            addToCartButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onAddToCartClicked(item, position);
                }
            });
        }
    }
}
