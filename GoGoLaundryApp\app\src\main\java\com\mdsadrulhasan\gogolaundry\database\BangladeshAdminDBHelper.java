package com.mdsadrulhasan.gogolaundry.database; // Use your actual package name

import android.content.ContentValues;
import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log; // Added for potential logging

public class BangladeshAdminDBHelper extends SQLiteOpenHelper {

    private static final String TAG = "AdminDBHelper";

    // Database Version
    private static final int DATABASE_VERSION = 1; // Increment if schema changes AND data needs reprocessing

    // Database Name
    private static final String DATABASE_NAME = "bangladesh_admin_db";

    // Table Names
    public static final String TABLE_DIVISIONS = "divisions";
    public static final String TABLE_DISTRICTS = "districts";
    public static final String TABLE_UPAZILLAS = "upazillas"; // Spelling consistency (or use Upazilas)

    // Common column names
    public static final String KEY_ID = "id";
    public static final String KEY_NAME = "name";
    public static final String KEY_BN_NAME = "bn_name"; // Optional: Add Bengali names if available


    // DISTRICTS Table - column names
    public static final String KEY_DIVISION_ID = "division_id";

    // UPAZILLAS Table - column names
    public static final String KEY_DISTRICT_ID = "district_id";

    // Table Create Statements
    // Divisions table create statement
    private static final String CREATE_TABLE_DIVISIONS = "CREATE TABLE "
            + TABLE_DIVISIONS + "("
            + KEY_ID + " INTEGER PRIMARY KEY,"
            + KEY_NAME + " TEXT"
            // + "," + KEY_BN_NAME + " TEXT" // Uncomment if adding Bengali names
            + ")";

    // Districts table create statement
    private static final String CREATE_TABLE_DISTRICTS = "CREATE TABLE "
            + TABLE_DISTRICTS + "("
            + KEY_ID + " INTEGER PRIMARY KEY,"
            + KEY_DIVISION_ID + " INTEGER,"
            + KEY_NAME + " TEXT,"
            // + "," + KEY_BN_NAME + " TEXT," // Uncomment if adding Bengali names
            + "FOREIGN KEY(" + KEY_DIVISION_ID + ") REFERENCES " + TABLE_DIVISIONS + "(" + KEY_ID + ")"
            + ")";

    // Upazillas table create statement
    private static final String CREATE_TABLE_UPAZILLAS = "CREATE TABLE "
            + TABLE_UPAZILLAS + "(" // Consistent spelling 'upazillas'
            + KEY_ID + " INTEGER PRIMARY KEY,"
            + KEY_DISTRICT_ID + " INTEGER,"
            + KEY_NAME + " TEXT,"
            // + "," + KEY_BN_NAME + " TEXT," // Uncomment if adding Bengali names
            + "FOREIGN KEY(" + KEY_DISTRICT_ID + ") REFERENCES " + TABLE_DISTRICTS + "(" + KEY_ID + ")"
            + ")";

    public BangladeshAdminDBHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.d(TAG, "Creating database tables...");
        // Creating required tables
        db.execSQL(CREATE_TABLE_DIVISIONS);
        db.execSQL(CREATE_TABLE_DISTRICTS);
        db.execSQL(CREATE_TABLE_UPAZILLAS); // Corrected table name

        // Populate the database with initial data
        // Use transactions for performance boost during bulk inserts
        db.beginTransaction();
        try {
            Log.d(TAG, "Populating Divisions...");
            populateDivisions(db);
            Log.d(TAG, "Populating Districts...");
            populateDistricts(db);
            Log.d(TAG, "Populating Upazillas...");
            populateUpazillas(db); // Corrected method name
            db.setTransactionSuccessful();
            Log.d(TAG, "Database population successful.");
        } catch (Exception e) {
            Log.e(TAG, "Error populating database", e);
        } finally {
            db.endTransaction();
        }
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.w(TAG, "Upgrading database from version " + oldVersion + " to " + newVersion);
        // On upgrade drop older tables
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_UPAZILLAS); // Corrected table name
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_DISTRICTS);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_DIVISIONS);

        // Create new tables
        onCreate(db);
    }

    // --- Data Population Methods ---

    private void insertDivision(SQLiteDatabase db, int id, String name) {
        ContentValues values = new ContentValues();
        values.put(KEY_ID, id);
        values.put(KEY_NAME, name);
        db.insert(TABLE_DIVISIONS, null, values);
    }

    private void insertDistrict(SQLiteDatabase db, int id, int divisionId, String name) {
        ContentValues values = new ContentValues();
        values.put(KEY_ID, id);
        values.put(KEY_DIVISION_ID, divisionId);
        values.put(KEY_NAME, name);
        db.insert(TABLE_DISTRICTS, null, values);
    }

    // Changed to use consistent spelling 'upazillas'
    private void insertUpazilla(SQLiteDatabase db, int id, int districtId, String name) {
        ContentValues values = new ContentValues();
        values.put(KEY_ID, id);
        values.put(KEY_DISTRICT_ID, districtId);
        values.put(KEY_NAME, name);
        db.insert(TABLE_UPAZILLAS, null, values); // Corrected table name
    }


    private void populateDivisions(SQLiteDatabase db) {
        insertDivision(db, 1, "চট্টগ্রাম"); // Chattogram
        insertDivision(db, 2, "রাজশাহী"); // Rajshahi
        insertDivision(db, 3, "খুলনা");     // Khulna
        insertDivision(db, 4, "বরিশাল");  // Barishal
        insertDivision(db, 5, "সিলেট");     // Sylhet
        insertDivision(db, 6, "ঢাকা");      // Dhaka
        insertDivision(db, 7, "রংপুর");     // Rangpur
        insertDivision(db, 8, "ময়মনসিংহ"); // Mymensingh
    }

    private void populateDistricts(SQLiteDatabase db) {
        // Division IDs match the order in populateDivisions:
        // 1: Chattogram, 2: Rajshahi, 3: Khulna, 4: Barishal, 5: Sylhet, 6: Dhaka, 7: Rangpur, 8: Mymensingh

        // Chattogram Division (ID: 1)
        insertDistrict(db, 1, 1, "কুমিল্লা");     // Cumilla (Formerly Comilla)
        insertDistrict(db, 2, 1, "ফেনী");        // Feni
        insertDistrict(db, 3, 1, "ব্রাহ্মণবাড়িয়া"); // Brahmanbaria
        insertDistrict(db, 4, 1, "রাঙ্গামাটি");    // Rangamati
        insertDistrict(db, 5, 1, "নোয়াখালী");    // Noakhali
        insertDistrict(db, 6, 1, "চাঁদপুর");      // Chandpur
        insertDistrict(db, 7, 1, "লক্ষ্মীপুর");    // Lakshmipur
        insertDistrict(db, 8, 1, "চট্টগ্রাম");     // Chattogram (Formerly Chittagong)
        insertDistrict(db, 9, 1, "কক্সবাজার");    // Cox's Bazar
        insertDistrict(db, 10, 1, "খাগড়াছড়ি");  // Khagrachhari
        insertDistrict(db, 11, 1, "বান্দরবান");    // Bandarban

        // Rajshahi Division (ID: 2)
        insertDistrict(db, 12, 2, "সিরাজগঞ্জ");    // Sirajganj
        insertDistrict(db, 13, 2, "পাবনা");      // Pabna
        insertDistrict(db, 14, 2, "বগুড়া");      // Bogura (Formerly Bogra)
        insertDistrict(db, 15, 2, "রাজশাহী");     // Rajshahi
        insertDistrict(db, 16, 2, "নাটোর");      // Natore
        insertDistrict(db, 17, 2, "জয়পুরহাট");    // Joypurhat
        insertDistrict(db, 18, 2, "চাঁপাইনবাবগঞ্জ"); // Chapai Nawabganj
        insertDistrict(db, 19, 2, "নওগাঁ");       // Naogaon

        // Khulna Division (ID: 3)
        insertDistrict(db, 20, 3, "যশোর");       // Jashore (Formerly Jessore)
        insertDistrict(db, 21, 3, "সাতক্ষীরা");    // Satkhira
        insertDistrict(db, 22, 3, "মেহেরপুর");    // Meherpur
        insertDistrict(db, 23, 3, "নড়াইল");      // Narail
        insertDistrict(db, 24, 3, "চুয়াডাঙ্গা");  // Chuadanga
        insertDistrict(db, 25, 3, "কুষ্টিয়া");    // Kushtia
        insertDistrict(db, 26, 3, "মাগুরা");      // Magura
        insertDistrict(db, 27, 3, "খুলনা");      // Khulna
        insertDistrict(db, 28, 3, "বাগেরহাট");    // Bagerhat
        insertDistrict(db, 29, 3, "ঝিনাইদহ");     // Jhenaidah

        // Barishal Division (ID: 4)
        insertDistrict(db, 30, 4, "ঝালকাঠি");    // Jhalokati
        insertDistrict(db, 31, 4, "পটুয়াখালী");  // Patuakhali
        insertDistrict(db, 32, 4, "পিরোজপুর");   // Pirojpur
        insertDistrict(db, 33, 4, "বরিশাল");     // Barishal (Formerly Barisal)
        insertDistrict(db, 34, 4, "ভোলা");       // Bhola
        insertDistrict(db, 35, 4, "বরগুনা");     // Barguna

        // Sylhet Division (ID: 5)
        insertDistrict(db, 36, 5, "সিলেট");      // Sylhet
        insertDistrict(db, 37, 5, "মৌলভীবাজার");  // Moulvibazar
        insertDistrict(db, 38, 5, "হবিগঞ্জ");     // Habiganj
        insertDistrict(db, 39, 5, "সুনামগঞ্জ");    // Sunamganj

        // Dhaka Division (ID: 6)
        insertDistrict(db, 40, 6, "নরসিংদী");     // Narsingdi
        insertDistrict(db, 41, 6, "গাজীপুর");     // Gazipur
        insertDistrict(db, 42, 6, "শরীয়তপুর");   // Shariatpur
        insertDistrict(db, 43, 6, "নারায়ণগঞ্জ"); // Narayanganj
        insertDistrict(db, 44, 6, "টাঙ্গাইল");    // Tangail
        insertDistrict(db, 45, 6, "কিশোরগঞ্জ");   // Kishoreganj
        insertDistrict(db, 46, 6, "মানিকগঞ্জ");   // Manikganj
        insertDistrict(db, 47, 6, "ঢাকা");       // Dhaka
        insertDistrict(db, 48, 6, "মুন্সিগঞ্জ");   // Munshiganj
        insertDistrict(db, 49, 6, "রাজবাড়ী");    // Rajbari
        insertDistrict(db, 50, 6, "মাদারীপুর");   // Madaripur
        insertDistrict(db, 51, 6, "গোপালগঞ্জ");   // Gopalganj
        insertDistrict(db, 52, 6, "ফরিদপুর");     // Faridpur

        // Rangpur Division (ID: 7)
        insertDistrict(db, 53, 7, "পঞ্চগড়");     // Panchagarh
        insertDistrict(db, 54, 7, "দিনাজপুর");    // Dinajpur
        insertDistrict(db, 55, 7, "লালমনিরহাট");  // Lalmonirhat
        insertDistrict(db, 56, 7, "নীলফামারী");   // Nilphamari
        insertDistrict(db, 57, 7, "গাইবান্ধা");    // Gaibandha
        insertDistrict(db, 58, 7, "ঠাকুরগাঁও");    // Thakurgaon
        insertDistrict(db, 59, 7, "রংপুর");      // Rangpur
        insertDistrict(db, 60, 7, "কুড়িগ্রাম");    // Kurigram

        // Mymensingh Division (ID: 8)
        insertDistrict(db, 61, 8, "শেরপুর");      // Sherpur
        insertDistrict(db, 62, 8, "ময়মনসিংহ");   // Mymensingh
        insertDistrict(db, 63, 8, "জামালপুর");    // Jamalpur
        insertDistrict(db, 64, 8, "নেত্রকোণা");   // Netrokona
    }


    private void populateUpazillas(SQLiteDatabase db) { // Corrected spelling
        int upazillaId = 1; // Start Upazilla IDs from 1

        // --- Chattogram Division --- District IDs: 1-11 ---

        // Cumilla (District ID: 1)
        insertUpazilla(db, upazillaId++, 1, "দেবিদ্বার"); // Debidwar
        insertUpazilla(db, upazillaId++, 1, "বরুড়া"); // Barura
        insertUpazilla(db, upazillaId++, 1, "ব্রাহ্মণপাড়া"); // Brahmanpara
        insertUpazilla(db, upazillaId++, 1, "চান্দিনা"); // Chandina
        insertUpazilla(db, upazillaId++, 1, "চৌদ্দগ্রাম"); // Chauddagram
        insertUpazilla(db, upazillaId++, 1, "দাউদকান্দি"); // Daudkandi
        insertUpazilla(db, upazillaId++, 1, "হোমনা"); // Homna
        insertUpazilla(db, upazillaId++, 1, "লাকসাম"); // Laksam
        insertUpazilla(db, upazillaId++, 1, "মুরাদনগর"); // Muradnagar
        insertUpazilla(db, upazillaId++, 1, "নাঙ্গলকোট"); // Nangalkot
        insertUpazilla(db, upazillaId++, 1, "কুমিল্লা সদর"); // Cumilla Sadar
        insertUpazilla(db, upazillaId++, 1, "মেঘনা"); // Meghna
        insertUpazilla(db, upazillaId++, 1, "মনোহরগঞ্জ"); // Monohorgonj
        insertUpazilla(db, upazillaId++, 1, "সদর দক্ষিণ"); // Sadar Dakshin
        insertUpazilla(db, upazillaId++, 1, "তিতাস"); // Titas
        insertUpazilla(db, upazillaId++, 1, "বুড়িচং"); // Burichang
        insertUpazilla(db, upazillaId++, 1, "লালমাই"); // Lalmai

        // Feni (District ID: 2)
        insertUpazilla(db, upazillaId++, 2, "ছাগলনাইয়া"); // Chhagalnaiya
        insertUpazilla(db, upazillaId++, 2, "ফেনী সদর"); // Feni Sadar
        insertUpazilla(db, upazillaId++, 2, "সোনাগাজী"); // Sonagazi
        insertUpazilla(db, upazillaId++, 2, "ফুলগাজী"); // Fulgazi
        insertUpazilla(db, upazillaId++, 2, "পরশুরাম"); // Parshuram
        insertUpazilla(db, upazillaId++, 2, "দাগনভূঞা"); // Daganbhuiyan

        // Brahmanbaria (District ID: 3)
        insertUpazilla(db, upazillaId++, 3, "ব্রাহ্মণবাড়িয়া সদর"); // Brahmanbaria Sadar
        insertUpazilla(db, upazillaId++, 3, "কসবা"); // Kasba
        insertUpazilla(db, upazillaId++, 3, "নাসিরনগর"); // Nasirnagar
        insertUpazilla(db, upazillaId++, 3, "সরাইল"); // Sarail
        insertUpazilla(db, upazillaId++, 3, "আশুগঞ্জ"); // Ashuganj
        insertUpazilla(db, upazillaId++, 3, "আখাউড়া"); // Akhaura
        insertUpazilla(db, upazillaId++, 3, "নবীনগর"); // Nabinagar
        insertUpazilla(db, upazillaId++, 3, "বাঞ্ছারামপুর"); // Bancharampur
        insertUpazilla(db, upazillaId++, 3, "বিজয়নগর"); // Bijoynagar

        // Rangamati (District ID: 4)
        insertUpazilla(db, upazillaId++, 4, "রাঙ্গামাটি সদর"); // Rangamati Sadar
        insertUpazilla(db, upazillaId++, 4, "কাপ্তাই"); // Kaptai
        insertUpazilla(db, upazillaId++, 4, "কাউখালী"); // Kawkhali
        insertUpazilla(db, upazillaId++, 4, "বাঘাইছড়ি"); // Baghaichari
        insertUpazilla(db, upazillaId++, 4, "বরকল"); // Barkal
        insertUpazilla(db, upazillaId++, 4, "লংগদু"); // Langadu
        insertUpazilla(db, upazillaId++, 4, "রাজস্থলী"); // Rajasthali
        insertUpazilla(db, upazillaId++, 4, "বিলাইছড়ি"); // Belaichari
        insertUpazilla(db, upazillaId++, 4, "জুরাছড়ি"); // Juraichari
        insertUpazilla(db, upazillaId++, 4, "নানিয়ারচর"); // Naniarchar

        // Noakhali (District ID: 5)
        insertUpazilla(db, upazillaId++, 5, "নোয়াখালী সদর"); // Noakhali Sadar
        insertUpazilla(db, upazillaId++, 5, "কোম্পানীগঞ্জ"); // Companiganj
        insertUpazilla(db, upazillaId++, 5, "বেগমগঞ্জ"); // Begumganj
        insertUpazilla(db, upazillaId++, 5, "হাতিয়া"); // Hatia
        insertUpazilla(db, upazillaId++, 5, "সুবর্ণচর"); // Subarnachar
        insertUpazilla(db, upazillaId++, 5, "কবিরহাট"); // Kabirhat
        insertUpazilla(db, upazillaId++, 5, "সেনবাগ"); // Senbug
        insertUpazilla(db, upazillaId++, 5, "চাটখিল"); // Chatkhil
        insertUpazilla(db, upazillaId++, 5, "সোনাইমুড়ী"); // Sonaimuri

        // Chandpur (District ID: 6)
        insertUpazilla(db, upazillaId++, 6, "হাইমচর"); // Haimchar
        insertUpazilla(db, upazillaId++, 6, "কচুয়া"); // Kachua
        insertUpazilla(db, upazillaId++, 6, "শাহরাস্তি"); // Shahrasti
        insertUpazilla(db, upazillaId++, 6, "চাঁদপুর সদর"); // Chandpur Sadar
        insertUpazilla(db, upazillaId++, 6, "মতলব উত্তর"); // Matlab Uttar
        insertUpazilla(db, upazillaId++, 6, "হাজীগঞ্জ"); // Hajiganj
        insertUpazilla(db, upazillaId++, 6, "মতলব দক্ষিণ"); // Matlab Dakshin
        insertUpazilla(db, upazillaId++, 6, "ফরিদগঞ্জ"); // Faridganj

        // Lakshmipur (District ID: 7)
        insertUpazilla(db, upazillaId++, 7, "লক্ষ্মীপুর সদর"); // Lakshmipur Sadar
        insertUpazilla(db, upazillaId++, 7, "কমলনগর"); // Kamalnagar
        insertUpazilla(db, upazillaId++, 7, "রায়পুর"); // Raipur
        insertUpazilla(db, upazillaId++, 7, "রামগতি"); // Ramgati
        insertUpazilla(db, upazillaId++, 7, "রামগঞ্জ"); // Ramganj

        // Chattogram (District ID: 8)
        insertUpazilla(db, upazillaId++, 8, "রাঙ্গুনিয়া"); // Rangunia
        insertUpazilla(db, upazillaId++, 8, "সীতাকুণ্ড"); // Sitakunda
        insertUpazilla(db, upazillaId++, 8, "মীরসরাই"); // Mirsharai
        insertUpazilla(db, upazillaId++, 8, "পটিয়া"); // Patiya
        insertUpazilla(db, upazillaId++, 8, "সন্দ্বীপ"); // Sandwip
        insertUpazilla(db, upazillaId++, 8, "বাঁশখালী"); // Banshkhali
        insertUpazilla(db, upazillaId++, 8, "বোয়ালখালী"); // Boalkhali
        insertUpazilla(db, upazillaId++, 8, "আনোয়ারা"); // Anwara
        insertUpazilla(db, upazillaId++, 8, "চন্দনাইশ"); // Chandanaish
        insertUpazilla(db, upazillaId++, 8, "সাতকানিয়া"); // Satkania
        insertUpazilla(db, upazillaId++, 8, "লোহাগাড়া"); // Lohagara
        insertUpazilla(db, upazillaId++, 8, "হাটহাজারী"); // Hathazari
        insertUpazilla(db, upazillaId++, 8, "ফটিকছড়ি"); // Fatikchhari
        insertUpazilla(db, upazillaId++, 8, "রাউজান"); // Raozan
        insertUpazilla(db, upazillaId++, 8, "কর্ণফুলী"); // Karnafuli
        // Chattogram City Thanas (often treated like Upazilas administratively)
        insertUpazilla(db, upazillaId++, 8, "কোতোয়ালী থানা"); // Kotwali Thana
        insertUpazilla(db, upazillaId++, 8, "পাঁচলাইশ থানা"); // Panchlaish Thana
        insertUpazilla(db, upazillaId++, 8, "চান্দগাঁও থানা"); // Chandgaon Thana
        // Add other Chattogram city thanas if needed for your app's context

        // Cox's Bazar (District ID: 9)
        insertUpazilla(db, upazillaId++, 9, "কক্সবাজার সদর"); // Cox's Bazar Sadar
        insertUpazilla(db, upazillaId++, 9, "চকরিয়া"); // Chakaria
        insertUpazilla(db, upazillaId++, 9, "কুতুবদিয়া"); // Kutubdia
        insertUpazilla(db, upazillaId++, 9, "উখিয়া"); // Ukhiya
        insertUpazilla(db, upazillaId++, 9, "মহেশখালী"); // Moheshkhali
        insertUpazilla(db, upazillaId++, 9, "পেকুয়া"); // Pekua
        insertUpazilla(db, upazillaId++, 9, "রামু"); // Ramu
        insertUpazilla(db, upazillaId++, 9, "টেকনাফ"); // Teknaf

        // Khagrachhari (District ID: 10)
        insertUpazilla(db, upazillaId++, 10, "খাগড়াছড়ি সদর"); // Khagrachhari Sadar
        insertUpazilla(db, upazillaId++, 10, "দীঘিনালা"); // Dighinala
        insertUpazilla(db, upazillaId++, 10, "পানছড়ি"); // Panchari
        insertUpazilla(db, upazillaId++, 10, "লক্ষ্মীছড়ি"); // Laxmichhari
        insertUpazilla(db, upazillaId++, 10, "মহালছড়ি"); // Mohalchari
        insertUpazilla(db, upazillaId++, 10, "মানিকছড়ি"); // Manikchari
        insertUpazilla(db, upazillaId++, 10, "রামগড়"); // Ramgarh
        insertUpazilla(db, upazillaId++, 10, "মাটিরাঙ্গা"); // Matiranga
        insertUpazilla(db, upazillaId++, 10, "গুইমারা"); // Guimara

        // Bandarban (District ID: 11)
        insertUpazilla(db, upazillaId++, 11, "বান্দরবান সদর"); // Bandarban Sadar
        insertUpazilla(db, upazillaId++, 11, "আলীকদম"); // Alikadam
        insertUpazilla(db, upazillaId++, 11, "নাইক্ষ্যংছড়ি"); // Naikhongchhari
        insertUpazilla(db, upazillaId++, 11, "রোয়াংছড়ি"); // Rowangchhari
        insertUpazilla(db, upazillaId++, 11, "লামা"); // Lama
        insertUpazilla(db, upazillaId++, 11, "রুমা"); // Ruma
        insertUpazilla(db, upazillaId++, 11, "থানচি"); // Thanchi

        // --- Rajshahi Division --- District IDs: 12-19 ---

        // Sirajganj (District ID: 12)
        insertUpazilla(db, upazillaId++, 12, "বেলকুচি"); // Belkuchi
        insertUpazilla(db, upazillaId++, 12, "চৌহালী"); // Chauhali
        insertUpazilla(db, upazillaId++, 12, "কামারখন্দ"); // Kamarkhanda
        insertUpazilla(db, upazillaId++, 12, "কাজিপুর"); // Kazipur
        insertUpazilla(db, upazillaId++, 12, "রায়গঞ্জ"); // Raiganj
        insertUpazilla(db, upazillaId++, 12, "শাহজাদপুর"); // Shahjadpur
        insertUpazilla(db, upazillaId++, 12, "সিরাজগঞ্জ সদর"); // Sirajganj Sadar
        insertUpazilla(db, upazillaId++, 12, "তাড়াশ"); // Tarash
        insertUpazilla(db, upazillaId++, 12, "উল্লাপাড়া"); // Ullahpara

        // Pabna (District ID: 13)
        insertUpazilla(db, upazillaId++, 13, "সুজানগর"); // Sujanagar
        insertUpazilla(db, upazillaId++, 13, "ঈশ্বরদী"); // Ishurdi
        insertUpazilla(db, upazillaId++, 13, "ভাঙ্গুড়া"); // Bhangura
        insertUpazilla(db, upazillaId++, 13, "পাবনা সদর"); // Pabna Sadar
        insertUpazilla(db, upazillaId++, 13, "বেড়া"); // Bera
        insertUpazilla(db, upazillaId++, 13, "আটঘরিয়া"); // Atgharia
        insertUpazilla(db, upazillaId++, 13, "চাটমোহর"); // Chatmohar
        insertUpazilla(db, upazillaId++, 13, "সাঁথিয়া"); // Santhia
        insertUpazilla(db, upazillaId++, 13, "ফরিদপুর"); // Faridpur (Note: Faridpur Upazila in Pabna District)

        // Bogura (District ID: 14)
        insertUpazilla(db, upazillaId++, 14, "কাহালু"); // Kahaloo
        insertUpazilla(db, upazillaId++, 14, "বগুড়া সদর"); // Bogura Sadar
        insertUpazilla(db, upazillaId++, 14, "সারিয়াকান্দি"); // Shariakandi
        insertUpazilla(db, upazillaId++, 14, "শাজাহানপুর"); // Shajahanpur
        insertUpazilla(db, upazillaId++, 14, "দুপচাঁচিয়া"); // Dupchanchia
        insertUpazilla(db, upazillaId++, 14, "আদমদিঘী"); // Adamdighi
        insertUpazilla(db, upazillaId++, 14, "নন্দীগ্রাম"); // Nondigram
        insertUpazilla(db, upazillaId++, 14, "সোনাতলা"); // Sonatala
        insertUpazilla(db, upazillaId++, 14, "ধুনট"); // Dhunot
        insertUpazilla(db, upazillaId++, 14, "গাবতলী"); // Gabtali
        insertUpazilla(db, upazillaId++, 14, "শেরপুর"); // Sherpur
        insertUpazilla(db, upazillaId++, 14, "শিবগঞ্জ"); // Shibganj

        // Rajshahi (District ID: 15)
        insertUpazilla(db, upazillaId++, 15, "পাবা"); // Paba
        insertUpazilla(db, upazillaId++, 15, "দুর্গাপুর"); // Durgapur
        insertUpazilla(db, upazillaId++, 15, "মোহনপুর"); // Mohonpur
        insertUpazilla(db, upazillaId++, 15, "চারঘাট"); // Charghat
        insertUpazilla(db, upazillaId++, 15, "পুঠিয়া"); // Puthia
        insertUpazilla(db, upazillaId++, 15, "বাঘা"); // Bagha
        insertUpazilla(db, upazillaId++, 15, "গোদাগাড়ী"); // Godagari
        insertUpazilla(db, upazillaId++, 15, "তানোর"); // Tanore
        insertUpazilla(db, upazillaId++, 15, "বাগমারা"); // Bagmara
        // Rajshahi City Thanas (similar to Upazilas)
        // insertUpazilla(db, upazillaId++, 15, "বোয়ালিয়া থানা"); // Boalia Thana (Example if needed)
        // insertUpazilla(db, upazillaId++, 15, "মতিহার থানা"); // Motihar Thana
        // ... etc.

        // Natore (District ID: 16)
        insertUpazilla(db, upazillaId++, 16, "নাটোর সদর"); // Natore Sadar
        insertUpazilla(db, upazillaId++, 16, "সিংড়া"); // Singra
        insertUpazilla(db, upazillaId++, 16, "বড়াইগ্রাম"); // Baraigram
        insertUpazilla(db, upazillaId++, 16, "বাগাতিপাড়া"); // Bagatipara
        insertUpazilla(db, upazillaId++, 16, "লালপুর"); // Lalpur
        insertUpazilla(db, upazillaId++, 16, "গুরুদাসপুর"); // Gurudaspur
        insertUpazilla(db, upazillaId++, 16, "নলডাঙ্গা"); // Naldanga

        // Joypurhat (District ID: 17)
        insertUpazilla(db, upazillaId++, 17, "আক্কেলপুর"); // Akkelpur
        insertUpazilla(db, upazillaId++, 17, "কালাই"); // Kalai
        insertUpazilla(db, upazillaId++, 17, "ক্ষেতলাল"); // Khetlal
        insertUpazilla(db, upazillaId++, 17, "পাঁচবিবি"); // Panchbibi
        insertUpazilla(db, upazillaId++, 17, "জয়পুরহাট সদর"); // Joypurhat Sadar

        // Chapai Nawabganj (District ID: 18)
        insertUpazilla(db, upazillaId++, 18, "চাঁপাইনবাবগঞ্জ সদর"); // Chapai Nawabganj Sadar
        insertUpazilla(db, upazillaId++, 18, "গোমস্তাপুর"); // Gomostapur
        insertUpazilla(db, upazillaId++, 18, "নাচোল"); // Nachole
        insertUpazilla(db, upazillaId++, 18, "ভোলাহাট"); // Bholahat
        insertUpazilla(db, upazillaId++, 18, "শিবগঞ্জ"); // Shibganj

        // Naogaon (District ID: 19)
        insertUpazilla(db, upazillaId++, 19, "মহাদেবপুর"); // Mohadevpur
        insertUpazilla(db, upazillaId++, 19, "বদলগাছী"); // Badalgachi
        insertUpazilla(db, upazillaId++, 19, "পত্নীতলা"); // Patnitala
        insertUpazilla(db, upazillaId++, 19, "ধামইরহাট"); // Dhamoirhat
        insertUpazilla(db, upazillaId++, 19, "নিয়ামতপুর"); // Niamatpur
        insertUpazilla(db, upazillaId++, 19, "মান্দা"); // Manda
        insertUpazilla(db, upazillaId++, 19, "আত্রাই"); // Atrai
        insertUpazilla(db, upazillaId++, 19, "রানীনগর"); // Raninagar
        insertUpazilla(db, upazillaId++, 19, "নওগাঁ সদর"); // Naogaon Sadar
        insertUpazilla(db, upazillaId++, 19, "পোরশা"); // Porsha
        insertUpazilla(db, upazillaId++, 19, "সাপাহার"); // Sapahar

        // --- Khulna Division --- District IDs: 20-29 ---

        // Jashore (District ID: 20)
        insertUpazilla(db, upazillaId++, 20, "মণিরামপুর"); // Manirampur
        insertUpazilla(db, upazillaId++, 20, "অভয়নগর"); // Abhaynagar
        insertUpazilla(db, upazillaId++, 20, "বাঘারপাড়া"); // Bagherpara
        insertUpazilla(db, upazillaId++, 20, "চৌগাছা"); // Chougachha
        insertUpazilla(db, upazillaId++, 20, "ঝিকরগাছা"); // Jhikargachha
        insertUpazilla(db, upazillaId++, 20, "কেশবপুর"); // Keshabpur
        insertUpazilla(db, upazillaId++, 20, "যশোর সদর"); // Jashore Sadar
        insertUpazilla(db, upazillaId++, 20, "শার্শা"); // Sharsha

        // Satkhira (District ID: 21)
        insertUpazilla(db, upazillaId++, 21, "আশাশুনি"); // Assasuni
        insertUpazilla(db, upazillaId++, 21, "দেবহাটা"); // Debhata
        insertUpazilla(db, upazillaId++, 21, "কলারোয়া"); // Kalaroa
        insertUpazilla(db, upazillaId++, 21, "সাতক্ষীরা সদর"); // Satkhira Sadar
        insertUpazilla(db, upazillaId++, 21, "শ্যামনগর"); // Shyamnagar
        insertUpazilla(db, upazillaId++, 21, "তালা"); // Tala
        insertUpazilla(db, upazillaId++, 21, "কালিগঞ্জ"); // Kaliganj

        // Meherpur (District ID: 22)
        insertUpazilla(db, upazillaId++, 22, "মুজিবনগর"); // Mujibnagar
        insertUpazilla(db, upazillaId++, 22, "মেহেরপুর সদর"); // Meherpur Sadar
        insertUpazilla(db, upazillaId++, 22, "গাংনী"); // Gangni

        // Narail (District ID: 23)
        insertUpazilla(db, upazillaId++, 23, "নড়াইল সদর"); // Narail Sadar
        insertUpazilla(db, upazillaId++, 23, "লোহাগড়া"); // Lohagara
        insertUpazilla(db, upazillaId++, 23, "কালিয়া"); // Kalia

        // Chuadanga (District ID: 24)
        insertUpazilla(db, upazillaId++, 24, "চুয়াডাঙ্গা সদর"); // Chuadanga Sadar
        insertUpazilla(db, upazillaId++, 24, "আলমডাঙ্গা"); // Alamdanga
        insertUpazilla(db, upazillaId++, 24, "দামুড়হুদা"); // Damurhuda
        insertUpazilla(db, upazillaId++, 24, "জীবননগর"); // Jibannagar

        // Kushtia (District ID: 25)
        insertUpazilla(db, upazillaId++, 25, "কুষ্টিয়া সদর"); // Kushtia Sadar
        insertUpazilla(db, upazillaId++, 25, "কুমারখালী"); // Kumarkhali
        insertUpazilla(db, upazillaId++, 25, "খোকসা"); // Khoksa
        insertUpazilla(db, upazillaId++, 25, "মিরপুর"); // Mirpur
        insertUpazilla(db, upazillaId++, 25, "দৌলতপুর"); // Daulatpur
        insertUpazilla(db, upazillaId++, 25, "ভেড়ামারা"); // Bheramara

        // Magura (District ID: 26)
        insertUpazilla(db, upazillaId++, 26, "শালিখা"); // Shalikha
        insertUpazilla(db, upazillaId++, 26, "শ্রীপুর"); // Sreepur
        insertUpazilla(db, upazillaId++, 26, "মাগুরা সদর"); // Magura Sadar
        insertUpazilla(db, upazillaId++, 26, "মহম্মদপুর"); // Mohammadpur

        // Khulna (District ID: 27)
        insertUpazilla(db, upazillaId++, 27, "পাইকগাছা"); // Paikgachha
        insertUpazilla(db, upazillaId++, 27, "ফুলতলা"); // Fultola (Phultala often)
        insertUpazilla(db, upazillaId++, 27, "দিঘলিয়া"); // Digholia
        insertUpazilla(db, upazillaId++, 27, "রূপসা"); // Rupsha
        insertUpazilla(db, upazillaId++, 27, "তেরখাদা"); // Terokhada
        insertUpazilla(db, upazillaId++, 27, "ডুমুরিয়া"); // Dumuria
        insertUpazilla(db, upazillaId++, 27, "বটিয়াঘাটা"); // Botiaghata
        insertUpazilla(db, upazillaId++, 27, "দাকোপ"); // Dacope
        insertUpazilla(db, upazillaId++, 27, "কয়রা"); // Koyra
        // Khulna City Thanas (similar to Upazilas)
        // insertUpazilla(db, upazillaId++, 27, "খুলনা সদর থানা"); // Khulna Sadar Thana (Example if needed)
        // insertUpazilla(db, upazillaId++, 27, "সোনাডাঙ্গা থানা"); // Sonadanga Thana
        // ... etc.

        // Bagerhat (District ID: 28)
        insertUpazilla(db, upazillaId++, 28, "ফকিরহাট"); // Fakirhat
        insertUpazilla(db, upazillaId++, 28, "বাগেরহাট সদর"); // Bagerhat Sadar
        insertUpazilla(db, upazillaId++, 28, "মোল্লাহাট"); // Mollahat
        insertUpazilla(db, upazillaId++, 28, "শরণখোলা"); // Sarankhola
        insertUpazilla(db, upazillaId++, 28, "রামপাল"); // Rampal
        insertUpazilla(db, upazillaId++, 28, "মোড়েলগঞ্জ"); // Morrelganj
        insertUpazilla(db, upazillaId++, 28, "কচুয়া"); // Kachua
        insertUpazilla(db, upazillaId++, 28, "মংলা"); // Mongla
        insertUpazilla(db, upazillaId++, 28, "চিতলমারী"); // Chitalmari

        // Jhenaidah (District ID: 29)
        insertUpazilla(db, upazillaId++, 29, "ঝিনাইদহ সদর"); // Jhenaidah Sadar
        insertUpazilla(db, upazillaId++, 29, "শৈলকুপা"); // Shailkupa
        insertUpazilla(db, upazillaId++, 29, "হরিণাকুন্ড"); // Harinakundu
        insertUpazilla(db, upazillaId++, 29, "কালীগঞ্জ"); // Kaliganj
        insertUpazilla(db, upazillaId++, 29, "কোটচাঁদপুর"); // Kotchandpur
        insertUpazilla(db, upazillaId++, 29, "মহেশপুর"); // Moheshpur

        // --- Barishal Division --- District IDs: 30-35 ---

        // Jhalokati (District ID: 30)
        insertUpazilla(db, upazillaId++, 30, "ঝালকাঠি সদর"); // Jhalokati Sadar
        insertUpazilla(db, upazillaId++, 30, "কাঁঠালিয়া"); // Kathalia
        insertUpazilla(db, upazillaId++, 30, "নলছিটি"); // Nalchity
        insertUpazilla(db, upazillaId++, 30, "রাজাপুর"); // Rajapur

        // Patuakhali (District ID: 31)
        insertUpazilla(db, upazillaId++, 31, "বাউফল"); // Bauphal
        insertUpazilla(db, upazillaId++, 31, "পটুয়াখালী সদর"); // Patuakhali Sadar
        insertUpazilla(db, upazillaId++, 31, "দুমকি"); // Dumki
        insertUpazilla(db, upazillaId++, 31, "দশমিনা"); // Dashmina
        insertUpazilla(db, upazillaId++, 31, "কলাপাড়া"); // Kalapara
        insertUpazilla(db, upazillaId++, 31, "মির্জাগঞ্জ"); // Mirzaganj
        insertUpazilla(db, upazillaId++, 31, "গলাচিপা"); // Galachipa
        insertUpazilla(db, upazillaId++, 31, "রাঙ্গাবালী"); // Rangabali

        // Pirojpur (District ID: 32)
        insertUpazilla(db, upazillaId++, 32, "পিরোজপুর সদর"); // Pirojpur Sadar
        insertUpazilla(db, upazillaId++, 32, "নাজিরপুর"); // Nazirpur
        insertUpazilla(db, upazillaId++, 32, "কাউখালী"); // Kawkhali
        insertUpazilla(db, upazillaId++, 32, "ইন্দুরকানী"); // Indurkani (Formerly Zianagar)
        insertUpazilla(db, upazillaId++, 32, "ভান্ডারিয়া"); // Bhandaria
        insertUpazilla(db, upazillaId++, 32, "মঠবাড়িয়া"); // Mathbaria
        insertUpazilla(db, upazillaId++, 32, "নেছারাবাদ (স্বরূপকাঠী)"); // Nesarabad (Swarupkathi)

        // Barishal (District ID: 33)
        insertUpazilla(db, upazillaId++, 33, "বরিশাল সদর"); // Barishal Sadar
        insertUpazilla(db, upazillaId++, 33, "বাকেরগঞ্জ"); // Bakerganj
        insertUpazilla(db, upazillaId++, 33, "বাবুগঞ্জ"); // Babuganj
        insertUpazilla(db, upazillaId++, 33, "উজিরপুর"); // Wazirpur
        insertUpazilla(db, upazillaId++, 33, "বানারীপাড়া"); // Banaripara
        insertUpazilla(db, upazillaId++, 33, "গৌরনদী"); // Gournadi
        insertUpazilla(db, upazillaId++, 33, "আগৈলঝাড়া"); // Agailjhara
        insertUpazilla(db, upazillaId++, 33, "মেহেন্দিগঞ্জ"); // Mehendiganj
        insertUpazilla(db, upazillaId++, 33, "মুলাদী"); // Muladi
        insertUpazilla(db, upazillaId++, 33, "হিজলা"); // Hizla

        // Bhola (District ID: 34)
        insertUpazilla(db, upazillaId++, 34, "ভোলা সদর"); // Bhola Sadar
        insertUpazilla(db, upazillaId++, 34, "বোরহানউদ্দিন"); // Borhanuddin
        insertUpazilla(db, upazillaId++, 34, "চরফ্যাশন"); // Char Fasson
        insertUpazilla(db, upazillaId++, 34, "দৌলতখান"); // Daulatkhan
        insertUpazilla(db, upazillaId++, 34, "মনপুরা"); // Monpura
        insertUpazilla(db, upazillaId++, 34, "তজুমদ্দিন"); // Tazumuddin
        insertUpazilla(db, upazillaId++, 34, "লালমোহন"); // Lalmohan

        // Barguna (District ID: 35)
        insertUpazilla(db, upazillaId++, 35, "আমতলী"); // Amtali
        insertUpazilla(db, upazillaId++, 35, "বরগুনা সদর"); // Barguna Sadar
        insertUpazilla(db, upazillaId++, 35, "বেতাগী"); // Betagi
        insertUpazilla(db, upazillaId++, 35, "পাথরঘাটা"); // Patharghata
        insertUpazilla(db, upazillaId++, 35, "তালতলী"); // Taltali
        insertUpazilla(db, upazillaId++, 35, "বামনা"); // Bamna

        // --- Sylhet Division --- District IDs: 36-39 ---

        // Sylhet (District ID: 36)
        insertUpazilla(db, upazillaId++, 36, "বালাগঞ্জ"); // Balaganj
        insertUpazilla(db, upazillaId++, 36, "বিয়ানীবাজার"); // Beanibazar
        insertUpazilla(db, upazillaId++, 36, "বিশ্বনাথ"); // Bishwanath
        insertUpazilla(db, upazillaId++, 36, "কোম্পানীগঞ্জ"); // Companiganj
        insertUpazilla(db, upazillaId++, 36, "ফেঞ্চুগঞ্জ"); // Fenchuganj
        insertUpazilla(db, upazillaId++, 36, "গোলাপগঞ্জ"); // Golapganj
        insertUpazilla(db, upazillaId++, 36, "গোয়াইনঘাট"); // Gowainghat
        insertUpazilla(db, upazillaId++, 36, "জৈন্তাপুর"); // Jaintiapur
        insertUpazilla(db, upazillaId++, 36, "কানাইঘাট"); // Kanaighat
        insertUpazilla(db, upazillaId++, 36, "সিলেট সদর"); // Sylhet Sadar
        insertUpazilla(db, upazillaId++, 36, "জকিগঞ্জ"); // Zakiganj
        insertUpazilla(db, upazillaId++, 36, "দক্ষিণ সুরমা"); // Dakshin Surma
        insertUpazilla(db, upazillaId++, 36, "ওসমানীনগর"); // Osmani Nagar

        // Moulvibazar (District ID: 37)
        insertUpazilla(db, upazillaId++, 37, "বড়লেখা"); // Barlekha
        insertUpazilla(db, upazillaId++, 37, "কমলগঞ্জ"); // Kamalganj
        insertUpazilla(db, upazillaId++, 37, "কুলাউড়া"); // Kulaura
        insertUpazilla(db, upazillaId++, 37, "মৌলভীবাজার সদর"); // Moulvibazar Sadar
        insertUpazilla(db, upazillaId++, 37, "রাজনগর"); // Rajnagar
        insertUpazilla(db, upazillaId++, 37, "শ্রীমঙ্গল"); // Sreemangal
        insertUpazilla(db, upazillaId++, 37, "জুড়ী"); // Juri

        // Habiganj (District ID: 38)
        insertUpazilla(db, upazillaId++, 38, "নবীগঞ্জ"); // Nabiganj
        insertUpazilla(db, upazillaId++, 38, "বাহুবল"); // Bahubal
        insertUpazilla(db, upazillaId++, 38, "আজমিরীগঞ্জ"); // Ajmiriganj
        insertUpazilla(db, upazillaId++, 38, "বানিয়াচং"); // Baniachong
        insertUpazilla(db, upazillaId++, 38, "লাখাই"); // Lakhai
        insertUpazilla(db, upazillaId++, 38, "চুনারুঘাট"); // Chunarughat
        insertUpazilla(db, upazillaId++, 38, "হবিগঞ্জ সদর"); // Habiganj Sadar
        insertUpazilla(db, upazillaId++, 38, "মাধবপুর"); // Madhabpur
        insertUpazilla(db, upazillaId++, 38, "শায়েস্তাগঞ্জ"); // Shayestaganj (Newer Upazila)

        // Sunamganj (District ID: 39)
        insertUpazilla(db, upazillaId++, 39, "সুনামগঞ্জ সদর"); // Sunamganj Sadar
        insertUpazilla(db, upazillaId++, 39, "দক্ষিণ সুনামগঞ্জ"); // South Sunamganj (Dakshin Sunamganj)
        insertUpazilla(db, upazillaId++, 39, "বিশ্বম্ভরপুর"); // Bishwamvarpur
        insertUpazilla(db, upazillaId++, 39, "ছাতক"); // Chhatak
        insertUpazilla(db, upazillaId++, 39, "জগন্নাথপুর"); // Jagannathpur
        insertUpazilla(db, upazillaId++, 39, "দোয়ারাবাজার"); // Dowarabazar
        insertUpazilla(db, upazillaId++, 39, "তাহিরপুর"); // Tahirpur
        insertUpazilla(db, upazillaId++, 39, "ধর্মপাশা"); // Dharmapasha
        insertUpazilla(db, upazillaId++, 39, "জামালগঞ্জ"); // Jamalganj
        insertUpazilla(db, upazillaId++, 39, "শাল্লা"); // Shalla
        insertUpazilla(db, upazillaId++, 39, "দিরাই"); // Derai

        // --- Dhaka Division --- District IDs: 40-52 ---

        // Narsingdi (District ID: 40)
        insertUpazilla(db, upazillaId++, 40, "বেলাবো"); // Belabo
        insertUpazilla(db, upazillaId++, 40, "মনোহরদী"); // Monohardi
        insertUpazilla(db, upazillaId++, 40, "নরসিংদী সদর"); // Narsingdi Sadar
        insertUpazilla(db, upazillaId++, 40, "পলাশ"); // Palash
        insertUpazilla(db, upazillaId++, 40, "রায়পুরা"); // Raipura
        insertUpazilla(db, upazillaId++, 40, "শিবপুর"); // Shibpur

        // Gazipur (District ID: 41)
        insertUpazilla(db, upazillaId++, 41, "কালীগঞ্জ"); // Kaliganj
        insertUpazilla(db, upazillaId++, 41, "কালিয়াকৈর"); // Kaliakair
        insertUpazilla(db, upazillaId++, 41, "কাপাসিয়া"); // Kapasia
        insertUpazilla(db, upazillaId++, 41, "গাজীপুর সদর"); // Gazipur Sadar
        insertUpazilla(db, upazillaId++, 41, "শ্রীপুর"); // Sreepur
        // Gazipur City Corporation areas might be relevant depending on needs

        // Shariatpur (District ID: 42)
        insertUpazilla(db, upazillaId++, 42, "শরীয়তপুর সদর"); // Shariatpur Sadar
        insertUpazilla(db, upazillaId++, 42, "নড়িয়া"); // Naria
        insertUpazilla(db, upazillaId++, 42, "জাজিরা"); // Zajira
        insertUpazilla(db, upazillaId++, 42, "গোসাইরহাট"); // Gosairhat
        insertUpazilla(db, upazillaId++, 42, "ভেদরগঞ্জ"); // Bhedarganj
        insertUpazilla(db, upazillaId++, 42, "ডামুড্যা"); // Damudya

        // Narayanganj (District ID: 43)
        insertUpazilla(db, upazillaId++, 43, "আড়াইহাজার"); // Araihazar
        insertUpazilla(db, upazillaId++, 43, "বন্দর"); // Bandar
        insertUpazilla(db, upazillaId++, 43, "নারায়ণগঞ্জ সদর"); // Narayanganj Sadar
        insertUpazilla(db, upazillaId++, 43, "রূপগঞ্জ"); // Rupganj
        insertUpazilla(db, upazillaId++, 43, "সোনারগাঁ"); // Sonargaon
        // Narayanganj City Corporation areas might be relevant

        // Tangail (District ID: 44)
        insertUpazilla(db, upazillaId++, 44, "বাসাইল"); // Basail
        insertUpazilla(db, upazillaId++, 44, "ভুয়াপুর"); // Bhuapur
        insertUpazilla(db, upazillaId++, 44, "দেলদুয়ার"); // Delduar
        insertUpazilla(db, upazillaId++, 44, "ঘাটাইল"); // Ghatail
        insertUpazilla(db, upazillaId++, 44, "গোপালপুর"); // Gopalpur
        insertUpazilla(db, upazillaId++, 44, "মধুপুর"); // Madhupur
        insertUpazilla(db, upazillaId++, 44, "মির্জাপুর"); // Mirzapur
        insertUpazilla(db, upazillaId++, 44, "নাগরপুর"); // Nagarpur
        insertUpazilla(db, upazillaId++, 44, "সখিপুর"); // Sakhipur
        insertUpazilla(db, upazillaId++, 44, "টাঙ্গাইল সদর"); // Tangail Sadar
        insertUpazilla(db, upazillaId++, 44, "কালিহাতী"); // Kalihati
        insertUpazilla(db, upazillaId++, 44, "ধনবাড়ী"); // Dhanbari

        // Kishoreganj (District ID: 45)
        insertUpazilla(db, upazillaId++, 45, "ইটনা"); // Itna
        insertUpazilla(db, upazillaId++, 45, "কটিয়াদী"); // Katiadi
        insertUpazilla(db, upazillaId++, 45, "ভৈরব"); // Bhairab
        insertUpazilla(db, upazillaId++, 45, "তাড়াইল"); // Tarail
        insertUpazilla(db, upazillaId++, 45, "হোসেনপুর"); // Hossainpur
        insertUpazilla(db, upazillaId++, 45, "পাকুন্দিয়া"); // Pakundia
        insertUpazilla(db, upazillaId++, 45, "কুলিয়ারচর"); // Kuliarchar
        insertUpazilla(db, upazillaId++, 45, "কিশোরগঞ্জ সদর"); // Kishoreganj Sadar
        insertUpazilla(db, upazillaId++, 45, "করিমগঞ্জ"); // Karimganj
        insertUpazilla(db, upazillaId++, 45, "বাজিতপুর"); // Bajitpur
        insertUpazilla(db, upazillaId++, 45, "অষ্টগ্রাম"); // Austagram
        insertUpazilla(db, upazillaId++, 45, "মিঠামইন"); // Mithamain
        insertUpazilla(db, upazillaId++, 45, "নিকলী"); // Nikli

        // Manikganj (District ID: 46)
        insertUpazilla(db, upazillaId++, 46, "হরিরামপুর"); // Harirampur
        insertUpazilla(db, upazillaId++, 46, "সাটুরিয়া"); // Saturia
        insertUpazilla(db, upazillaId++, 46, "মানিকগঞ্জ সদর"); // Manikganj Sadar
        insertUpazilla(db, upazillaId++, 46, "ঘিওর"); // Ghior
        insertUpazilla(db, upazillaId++, 46, "শিবালয়"); // Shibalaya
        insertUpazilla(db, upazillaId++, 46, "দৌলতপুর"); // Daulatpur
        insertUpazilla(db, upazillaId++, 46, "সিঙ্গাইর"); // Singair

        // Dhaka (District ID: 47)
        insertUpazilla(db, upazillaId++, 47, "সাভার"); // Savar
        insertUpazilla(db, upazillaId++, 47, "ধামরাই"); // Dhamrai
        insertUpazilla(db, upazillaId++, 47, "কেরানীগঞ্জ"); // Keraniganj
        insertUpazilla(db, upazillaId++, 47, "নবাবগঞ্জ"); // Nawabganj
        insertUpazilla(db, upazillaId++, 47, "দোহার"); // Dohar
        // Dhaka City Thanas (these are numerous and act like sub-districts)
        // Examples - Add more if needed for your specific application:
        insertUpazilla(db, upazillaId++, 47, "তেজগাঁও থানা"); // Tejgaon Thana
        insertUpazilla(db, upazillaId++, 47, "মোহাম্মদপুর থানা"); // Mohammadpur Thana
        insertUpazilla(db, upazillaId++, 47, "মিরপুর থানা"); // Mirpur Thana
        insertUpazilla(db, upazillaId++, 47, "গুলশান থানা"); // Gulshan Thana
        insertUpazilla(db, upazillaId++, 47, "ধানমন্ডি থানা"); // Dhanmondi Thana
        insertUpazilla(db, upazillaId++, 47, "রমনা থানা"); // Ramna Thana
        insertUpazilla(db, upazillaId++, 47, "মতিঝিল থানা"); // Motijheel Thana
        // ... many more ...

        // Munshiganj (District ID: 48)
        insertUpazilla(db, upazillaId++, 48, "মুন্সিগঞ্জ সদর"); // Munshiganj Sadar
        insertUpazilla(db, upazillaId++, 48, "শ্রীনগর"); // Sreenagar
        insertUpazilla(db, upazillaId++, 48, "সিরাজদিখান"); // Sirajdikhan
        insertUpazilla(db, upazillaId++, 48, "লৌহজং"); // Louhajanj
        insertUpazilla(db, upazillaId++, 48, "গজারিয়া"); // Gazaria
        insertUpazilla(db, upazillaId++, 48, "টংগিবাড়ী"); // Tongibari

        // Rajbari (District ID: 49)
        insertUpazilla(db, upazillaId++, 49, "রাজবাড়ী সদর"); // Rajbari Sadar
        insertUpazilla(db, upazillaId++, 49, "গোয়ালন্দ"); // Goalanda
        insertUpazilla(db, upazillaId++, 49, "পাংশা"); // Pangsha
        insertUpazilla(db, upazillaId++, 49, "বালিয়াকান্দি"); // Baliakandi
        insertUpazilla(db, upazillaId++, 49, "কালুখালী"); // Kalukhali

        // Madaripur (District ID: 50)
        insertUpazilla(db, upazillaId++, 50, "মাদারীপুর সদর"); // Madaripur Sadar
        insertUpazilla(db, upazillaId++, 50, "শিবচর"); // Shibchar
        insertUpazilla(db, upazillaId++, 50, "কালকিনি"); // Kalkini
        insertUpazilla(db, upazillaId++, 50, "রাজৈর"); // Rajoir
        insertUpazilla(db, upazillaId++, 50, "ডাসার"); // Dasar (Newer upazila)

        // Gopalganj (District ID: 51)
        insertUpazilla(db, upazillaId++, 51, "গোপালগঞ্জ সদর"); // Gopalganj Sadar
        insertUpazilla(db, upazillaId++, 51, "কাশিয়ানী"); // Kashiani
        insertUpazilla(db, upazillaId++, 51, "টুংগীপাড়া"); // Tungipara
        insertUpazilla(db, upazillaId++, 51, "কোটালীপাড়া"); // Kotalipara
        insertUpazilla(db, upazillaId++, 51, "মুকসুদপুর"); // Muksudpur

        // Faridpur (District ID: 52)
        insertUpazilla(db, upazillaId++, 52, "ফরিদপুর সদর"); // Faridpur Sadar
        insertUpazilla(db, upazillaId++, 52, "আলফাডাঙ্গা"); // Alfadanga
        insertUpazilla(db, upazillaId++, 52, "বোয়ালমারী"); // Boalmari
        insertUpazilla(db, upazillaId++, 52, "সদরপুর"); // Sadarpur
        insertUpazilla(db, upazillaId++, 52, "নগরকান্দা"); // Nagarkanda
        insertUpazilla(db, upazillaId++, 52, "ভাঙ্গা"); // Bhanga
        insertUpazilla(db, upazillaId++, 52, "চরভদ্রাসন"); // Charbhadrasan
        insertUpazilla(db, upazillaId++, 52, "মধুখালী"); // Madhukhali
        insertUpazilla(db, upazillaId++, 52, "সালথা"); // Saltha

        // --- Rangpur Division --- District IDs: 53-60 ---

        // Panchagarh (District ID: 53)
        insertUpazilla(db, upazillaId++, 53, "পঞ্চগড় সদর"); // Panchagarh Sadar
        insertUpazilla(db, upazillaId++, 53, "দেবীগঞ্জ"); // Debiganj
        insertUpazilla(db, upazillaId++, 53, "বোদা"); // Boda
        insertUpazilla(db, upazillaId++, 53, "আটোয়ারী"); // Atwari
        insertUpazilla(db, upazillaId++, 53, "তেঁতুলিয়া"); // Tetulia

        // Dinajpur (District ID: 54)
        insertUpazilla(db, upazillaId++, 54, "নবাবগঞ্জ"); // Nawabganj
        insertUpazilla(db, upazillaId++, 54, "বীরগঞ্জ"); // Birganj
        insertUpazilla(db, upazillaId++, 54, "ঘোড়াঘাট"); // Ghoraghat
        insertUpazilla(db, upazillaId++, 54, "বিরামপুর"); // Birampur
        insertUpazilla(db, upazillaId++, 54, "পার্বতীপুর"); // Parbatipur
        insertUpazilla(db, upazillaId++, 54, "বোচাগঞ্জ"); // Bochaganj
        insertUpazilla(db, upazillaId++, 54, "কাহারোল"); // Kaharole
        insertUpazilla(db, upazillaId++, 54, "ফুলবাড়ী"); // Fulbari
        insertUpazilla(db, upazillaId++, 54, "দিনাজপুর সদর"); // Dinajpur Sadar
        insertUpazilla(db, upazillaId++, 54, "হাকিমপুর"); // Hakimpur
        insertUpazilla(db, upazillaId++, 54, "খানসামা"); // Khansama
        insertUpazilla(db, upazillaId++, 54, "বিরল"); // Birol
        insertUpazilla(db, upazillaId++, 54, "চিরিরবন্দর"); // Chirirbandar

        // Lalmonirhat (District ID: 55)
        insertUpazilla(db, upazillaId++, 55, "লালমনিরহাট সদর"); // Lalmonirhat Sadar
        insertUpazilla(db, upazillaId++, 55, "কালীগঞ্জ"); // Kaliganj
        insertUpazilla(db, upazillaId++, 55, "হাতীবান্ধা"); // Hatibandha
        insertUpazilla(db, upazillaId++, 55, "পাটগ্রাম"); // Patgram
        insertUpazilla(db, upazillaId++, 55, "আদিতমারী"); // Aditmari

        // Nilphamari (District ID: 56)
        insertUpazilla(db, upazillaId++, 56, "নীলফামারী সদর"); // Nilphamari Sadar
        insertUpazilla(db, upazillaId++, 56, "সৈয়দপুর"); // Saidpur
        insertUpazilla(db, upazillaId++, 56, "জলঢাকা"); // Jaldhaka
        insertUpazilla(db, upazillaId++, 56, "কিশোরগঞ্জ"); // Kishoreganj (Note: Kishoreganj Upazila in Nilphamari District)
        insertUpazilla(db, upazillaId++, 56, "ডোমার"); // Domar
        insertUpazilla(db, upazillaId++, 56, "ডিমলা"); // Dimla

        // Gaibandha (District ID: 57)
        insertUpazilla(db, upazillaId++, 57, "সাদুল্লাপুর"); // Sadullapur
        insertUpazilla(db, upazillaId++, 57, "গাইবান্ধা সদর"); // Gaibandha Sadar
        insertUpazilla(db, upazillaId++, 57, "পলাশবাড়ী"); // Palashbari
        insertUpazilla(db, upazillaId++, 57, "সাঘাটা"); // Saghata
        insertUpazilla(db, upazillaId++, 57, "গোবিন্দগঞ্জ"); // Gobindaganj
        insertUpazilla(db, upazillaId++, 57, "সুন্দরগঞ্জ"); // Sundarganj
        insertUpazilla(db, upazillaId++, 57, "ফুলছড়ি"); // Phulchhari

        // Thakurgaon (District ID: 58)
        insertUpazilla(db, upazillaId++, 58, "ঠাকুরগাঁও সদর"); // Thakurgaon Sadar
        insertUpazilla(db, upazillaId++, 58, "পীরগঞ্জ"); // Pirganj
        insertUpazilla(db, upazillaId++, 58, "রাণীশংকৈল"); // Ranisankail
        insertUpazilla(db, upazillaId++, 58, "হরিপুর"); // Haripur
        insertUpazilla(db, upazillaId++, 58, "বালিয়াডাঙ্গী"); // Baliadangi

        // Rangpur (District ID: 59)
        insertUpazilla(db, upazillaId++, 59, "রংপুর সদর"); // Rangpur Sadar
        insertUpazilla(db, upazillaId++, 59, "গংগাচড়া"); // Gangachara
        insertUpazilla(db, upazillaId++, 59, "তারাগঞ্জ"); // Taraganj
        insertUpazilla(db, upazillaId++, 59, "বদরগঞ্জ"); // Badarganj
        insertUpazilla(db, upazillaId++, 59, "মিঠাপুকুর"); // Mithapukur
        insertUpazilla(db, upazillaId++, 59, "পীরগঞ্জ"); // Pirganj
        insertUpazilla(db, upazillaId++, 59, "কাউনিয়া"); // Kaunia
        insertUpazilla(db, upazillaId++, 59, "পীরগাছা"); // Pirgachha
        // Rangpur City Corporation areas might be relevant

        // Kurigram (District ID: 60)
        insertUpazilla(db, upazillaId++, 60, "কুড়িগ্রাম সদর"); // Kurigram Sadar
        insertUpazilla(db, upazillaId++, 60, "নাগেশ্বরী"); // Nageshwari
        insertUpazilla(db, upazillaId++, 60, "ভুরুঙ্গামারী"); // Bhurungamari
        insertUpazilla(db, upazillaId++, 60, "ফুলবাড়ী"); // Phulbari
        insertUpazilla(db, upazillaId++, 60, "রাজারহাট"); // Rajarhat
        insertUpazilla(db, upazillaId++, 60, "উলিপুর"); // Ulipur
        insertUpazilla(db, upazillaId++, 60, "চিলমারী"); // Chilmari
        insertUpazilla(db, upazillaId++, 60, "রৌমারী"); // Rowmari
        insertUpazilla(db, upazillaId++, 60, "চর রাজিবপুর"); // Char Rajibpur

        // --- Mymensingh Division --- District IDs: 61-64 ---

        // Sherpur (District ID: 61)
        insertUpazilla(db, upazillaId++, 61, "শেরপুর সদর"); // Sherpur Sadar
        insertUpazilla(db, upazillaId++, 61, "নালিতাবাড়ী"); // Nalitabari
        insertUpazilla(db, upazillaId++, 61, "শ্রীবরদী"); // Sreebardi
        insertUpazilla(db, upazillaId++, 61, "নকলা"); // Nakla
        insertUpazilla(db, upazillaId++, 61, "ঝিনাইগাতী"); // Jhenaigati

        // Mymensingh (District ID: 62)
        insertUpazilla(db, upazillaId++, 62, "ফুলবাড়ীয়া"); // Fulbaria
        insertUpazilla(db, upazillaId++, 62, "ত্রিশাল"); // Trishal
        insertUpazilla(db, upazillaId++, 62, "ভালুকা"); // Bhaluka
        insertUpazilla(db, upazillaId++, 62, "মুক্তাগাছা"); // Muktagachha
        insertUpazilla(db, upazillaId++, 62, "ময়মনসিংহ সদর"); // Mymensingh Sadar
        insertUpazilla(db, upazillaId++, 62, "ধোবাউড়া"); // Dhobaura
        insertUpazilla(db, upazillaId++, 62, "ফুলপুর"); // Phulpur
        insertUpazilla(db, upazillaId++, 62, "হালুয়াঘাট"); // Haluaghat
        insertUpazilla(db, upazillaId++, 62, "গৌরীপুর"); // Gouripur
        insertUpazilla(db, upazillaId++, 62, "গফরগাঁও"); // Gafargaon
        insertUpazilla(db, upazillaId++, 62, "ঈশ্বরগঞ্জ"); // Iswarganj
        insertUpazilla(db, upazillaId++, 62, "নান্দাইল"); // Nandail
        insertUpazilla(db, upazillaId++, 62, "তারাকান্দা"); // Tarakanda (Newer Upazila)

        // Jamalpur (District ID: 63)
        insertUpazilla(db, upazillaId++, 63, "জামালপুর সদর"); // Jamalpur Sadar
        insertUpazilla(db, upazillaId++, 63, "মেলান্দহ"); // Melandah
        insertUpazilla(db, upazillaId++, 63, "ইসলামপুর"); // Islampur
        insertUpazilla(db, upazillaId++, 63, "দেওয়ানগঞ্জ"); // Dewanganj
        insertUpazilla(db, upazillaId++, 63, "সরিষাবাড়ী"); // Sarishabari
        insertUpazilla(db, upazillaId++, 63, "মাদারগঞ্জ"); // Madarganj
        insertUpazilla(db, upazillaId++, 63, "বকশীগঞ্জ"); // Baksiganj

        // Netrokona (District ID: 64)
        insertUpazilla(db, upazillaId++, 64, "বারহাট্টা"); // Barhatta
        insertUpazilla(db, upazillaId++, 64, "দুর্গাপুর"); // Durgapur
        insertUpazilla(db, upazillaId++, 64, "কেন্দুয়া"); // Kendua
        insertUpazilla(db, upazillaId++, 64, "আটপাড়া"); // Atpara
        insertUpazilla(db, upazillaId++, 64, "মদন"); // Madan
        insertUpazilla(db, upazillaId++, 64, "খালিয়াজুড়ি"); // Khaliajuri
        insertUpazilla(db, upazillaId++, 64, "কলমাকান্দা"); // Kalmakanda
        insertUpazilla(db, upazillaId++, 64, "মোহনগঞ্জ"); // Mohanganj
        insertUpazilla(db, upazillaId++, 64, "পূর্বধলা"); // Purbadhala
        insertUpazilla(db, upazillaId++, 64, "নেত্রকোণা সদর"); // Netrokona Sadar


        Log.d(TAG, "Finished populating Upazillas. Total count: " + (upazillaId - 1));
    }

}