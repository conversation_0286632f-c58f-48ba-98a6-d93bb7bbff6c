<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="160dp"
    android:background="#1976D0"
    android:padding="12dp"
    android:theme="@style/ThemeOverlay.MaterialComponents.Dark">

    <!-- Glass container for profile image -->
    <FrameLayout
        android:id="@+id/profile_image_container"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/glass_card_background"
        android:padding="3dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/profile_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="2dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_person"
            app:shapeAppearanceOverlay="@style/CircleImageView"
            app:strokeColor="@android:color/white"
            app:strokeWidth="2dp" />

        <ImageView
            android:id="@+id/edit_profile_image"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="0dp"
            android:layout_marginBottom="0dp"
            android:background="@drawable/glass_card_background"
            android:elevation="4dp"
            android:padding="4dp"
            android:src="@drawable/ic_edit"
            app:tint="@android:color/white" />

    </FrameLayout>

    <!-- User information container -->
    <LinearLayout
        android:id="@+id/user_info_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toStartOf="@id/decorative_container"
        app:layout_constraintStart_toEndOf="@id/profile_image_container"
        app:layout_constraintTop_toTopOf="@id/profile_image_container">

        <TextView
            android:id="@+id/nav_header_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="User Name"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
            android:textColor="@android:color/white"
            android:textSize="17sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/nav_header_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="+8801XXXXXXXXX"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#E0FFFFFF"
            android:textSize="13sp"
            android:fontFamily="sans-serif" />

        <TextView
            android:id="@+id/nav_header_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="<EMAIL>"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="#C0FFFFFF"
            android:textSize="11sp"
            android:fontFamily="sans-serif" />

    </LinearLayout>

    <!-- Decorative container -->
    <LinearLayout
        android:id="@+id/decorative_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:layout_marginBottom="12dp"
        android:orientation="vertical"
        android:gravity="end"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <View
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginBottom="6dp"
            android:alpha="0.3"
            android:background="@drawable/glass_card_background" />

        <View
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:alpha="0.2"
            android:background="@drawable/glass_card_background" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
