<?php
/**
 * Admin Settings Page
 *
 * This page allows administrators to manage system settings
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/SettingsManager.php';
require_once 'update_settings_hook.php';

// Initialize settings manager
$settingsManager = new SettingsManager($pdo);

// Get all settings
$settings = $settingsManager->getAllSettings();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !$adminManager->verifyCsrfToken($_POST['csrf_token'])) {
        $_SESSION['error_message'] = 'Invalid request. Please try again.';
        header('Location: settings.php');
        exit;
    }

    // Get input data
    $otpEnabled = isset($_POST['otp_enabled']) ? 1 : 0;
    $otpLength = isset($_POST['otp_length']) ? (int)$_POST['otp_length'] : 6;
    $otpExpiry = isset($_POST['otp_expiry']) ? (int)$_POST['otp_expiry'] : 600;
    $otpMaxAttempts = isset($_POST['otp_max_attempts']) ? (int)$_POST['otp_max_attempts'] : 3;
    $otpDailyLimit = isset($_POST['otp_daily_limit']) ? (int)$_POST['otp_daily_limit'] : 5;
    $minPasswordLength = isset($_POST['min_password_length']) ? (int)$_POST['min_password_length'] : 8;
    $adminWhatsapp = isset($_POST['admin_whatsapp']) ? trim($_POST['admin_whatsapp']) : '';
    $deliveryFee = isset($_POST['delivery_fee']) ? (float)$_POST['delivery_fee'] : 50.00;

    // Validate input
    $errors = [];

    if ($otpLength < 4 || $otpLength > 8) {
        $errors[] = 'OTP length must be between 4 and 8 digits.';
    }

    if ($otpExpiry < 60 || $otpExpiry > 3600) {
        $errors[] = 'OTP expiry time must be between 60 and 3600 seconds.';
    }

    if ($otpMaxAttempts < 1 || $otpMaxAttempts > 10) {
        $errors[] = 'OTP max attempts must be between 1 and 10.';
    }

    if ($otpDailyLimit < 1 || $otpDailyLimit > 20) {
        $errors[] = 'OTP daily limit must be between 1 and 20.';
    }

    if ($minPasswordLength < 6 || $minPasswordLength > 16) {
        $errors[] = 'Minimum password length must be between 6 and 16 characters.';
    }

    if (empty($adminWhatsapp)) {
        $errors[] = 'Admin WhatsApp number is required.';
    } elseif (!preg_match('/^\+[0-9]{10,15}$/', $adminWhatsapp)) {
        $errors[] = 'Admin WhatsApp number must be in international format (e.g., +1234567890).';
    }

    if ($deliveryFee < 0 || $deliveryFee > 1000) {
        $errors[] = 'Delivery fee must be between 0 and 1000 BDT.';
    }

    if (!empty($errors)) {
        $_SESSION['error_message'] = implode('<br>', $errors);
        header('Location: settings.php');
        exit;
    }

    // Update settings
    $settingsToUpdate = [
        'otp_enabled' => $otpEnabled,
        'otp_length' => $otpLength,
        'otp_expiry' => $otpExpiry,
        'otp_max_attempts' => $otpMaxAttempts,
        'otp_daily_limit' => $otpDailyLimit,
        'min_password_length' => $minPasswordLength,
        'admin_whatsapp' => $adminWhatsapp,
        'delivery_fee' => $deliveryFee
    ];

    $changedSettings = [];

    foreach ($settingsToUpdate as $key => $value) {
        $oldValue = isset($settings[$key]) ? $settings[$key] : null;

        // Convert both values to strings for proper comparison
        $oldValueStr = (string)$oldValue;
        $newValueStr = (string)$value;

        // Debug output to see what's happening
        error_log("Setting: $key, Old: '$oldValueStr', New: '$newValueStr', Equal: " . ($oldValueStr === $newValueStr ? 'Yes' : 'No'));

        if ($oldValueStr !== $newValueStr) {
            $result = $settingsManager->updateSetting($key, $value);

            if ($result) {
                $changedSettings[$key] = [
                    'old' => $oldValue,
                    'new' => $value
                ];
            }
        }
    }

    // Log settings changes
    if (!empty($changedSettings)) {
        foreach ($changedSettings as $key => $values) {
            $adminManager->logAdminAction(
                $adminData['id'],
                'settings_update',
                'Updated setting: ' . $key . ' from "' . $values['old'] . '" to "' . $values['new'] . '"',
                getClientIp()
            );

            // Log to settings history
            $stmt = $pdo->prepare("
                INSERT INTO settings_history (setting_key, old_value, new_value, admin_id)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$key, $values['old'], $values['new'], $adminData['id']]);
        }

        // Update config.php file
        updateConfigFile($otpEnabled);

        $_SESSION['success_message'] = 'Settings updated successfully.';
    } else {
        $_SESSION['info_message'] = 'No settings were changed.';
    }

    header('Location: settings.php');
    exit;
}

// Page title and breadcrumbs
$pageTitle = 'System Settings';
$breadcrumbs = [
    'Settings' => false
];

// Include header
include 'includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">System Settings</h1>
            <a href="settings_history.php" class="btn btn-info">
                <i class="fas fa-history me-2"></i> Settings History
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">Configure System Settings</h5>
    </div>
    <div class="card-body">
        <form method="post" action="settings.php" id="settingsForm">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

            <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="otp-tab" data-bs-toggle="tab" data-bs-target="#otp" type="button" role="tab" aria-controls="otp" aria-selected="true">
                        <i class="fas fa-shield-alt me-2"></i> OTP Settings
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                        <i class="fas fa-lock me-2"></i> Security Settings
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">
                        <i class="fas fa-address-book me-2"></i> Contact Settings
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="order-tab" data-bs-toggle="tab" data-bs-target="#order" type="button" role="tab" aria-controls="order" aria-selected="false">
                        <i class="fas fa-shopping-cart me-2"></i> Order Settings
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="settingsTabsContent">
                <!-- OTP Settings Tab -->
                <div class="tab-pane fade show active" id="otp" role="tabpanel" aria-labelledby="otp-tab">
                    <div class="mb-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="otp_enabled" name="otp_enabled" value="1" <?php echo (isset($settings['otp_enabled']) && $settings['otp_enabled'] == 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="otp_enabled">Enable OTP Verification</label>
                        </div>
                        <div class="form-text">When disabled, users will be directed to contact an administrator via WhatsApp for account verification and password resets.</div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="otp_length" class="form-label">OTP Length</label>
                                <input type="number" class="form-control" id="otp_length" name="otp_length" min="4" max="8" value="<?php echo isset($settings['otp_length']) ? $settings['otp_length'] : '6'; ?>">
                                <div class="form-text">Number of digits in the OTP (4-8).</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="otp_expiry" class="form-label">OTP Expiry Time (seconds)</label>
                                <input type="number" class="form-control" id="otp_expiry" name="otp_expiry" min="60" max="3600" value="<?php echo isset($settings['otp_expiry']) ? $settings['otp_expiry'] : '600'; ?>">
                                <div class="form-text">Time in seconds before an OTP expires (60-3600).</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="otp_max_attempts" class="form-label">Max OTP Attempts</label>
                                <input type="number" class="form-control" id="otp_max_attempts" name="otp_max_attempts" min="1" max="10" value="<?php echo isset($settings['otp_max_attempts']) ? $settings['otp_max_attempts'] : '3'; ?>">
                                <div class="form-text">Maximum number of attempts allowed for OTP verification (1-10).</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="otp_daily_limit" class="form-label">Daily OTP Limit</label>
                                <input type="number" class="form-control" id="otp_daily_limit" name="otp_daily_limit" min="1" max="20" value="<?php echo isset($settings['otp_daily_limit']) ? $settings['otp_daily_limit'] : '5'; ?>">
                                <div class="form-text">Maximum number of OTPs a user can request per day (1-20).</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Settings Tab -->
                <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="min_password_length" class="form-label">Minimum Password Length</label>
                                <input type="number" class="form-control" id="min_password_length" name="min_password_length" min="6" max="16" value="<?php echo isset($settings['min_password_length']) ? $settings['min_password_length'] : '8'; ?>">
                                <div class="form-text">Minimum number of characters required for user passwords (6-16).</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Settings Tab -->
                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_whatsapp" class="form-label">Admin WhatsApp Number</label>
                                <input type="text" class="form-control" id="admin_whatsapp" name="admin_whatsapp" value="<?php echo isset($settings['admin_whatsapp']) ? $settings['admin_whatsapp'] : '+1234567890'; ?>">
                                <div class="form-text">WhatsApp number for users to contact when OTP verification is disabled. Include country code (e.g., +1234567890).</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Settings Tab -->
                <div class="tab-pane fade" id="order" role="tabpanel" aria-labelledby="order-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="delivery_fee" class="form-label">Delivery Fee (BDT)</label>
                                <input type="number" class="form-control" id="delivery_fee" name="delivery_fee" min="0" max="1000" step="0.01" value="<?php echo isset($settings['delivery_fee']) ? $settings['delivery_fee'] : '50.00'; ?>">
                                <div class="form-text">Default delivery fee in BDT. This will be applied to all new orders.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 pt-3 border-top">
                <button type="submit" class="btn btn-primary" id="saveSettingsBtn">
                    <i class="fas fa-save me-2"></i> Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmationModalLabel">Confirm Settings Change</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> You are about to change critical system settings. This may affect user experience and application functionality.
                </div>
                <p>Are you sure you want to save these changes?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmSaveBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle form submission with confirmation
        const settingsForm = document.getElementById('settingsForm');
        const saveSettingsBtn = document.getElementById('saveSettingsBtn');
        const confirmSaveBtn = document.getElementById('confirmSaveBtn');
        const otpEnabledCheckbox = document.getElementById('otp_enabled');
        const originalOtpEnabled = otpEnabledCheckbox.checked;

        saveSettingsBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Check if OTP setting has changed
            if (otpEnabledCheckbox.checked !== originalOtpEnabled) {
                // Show confirmation modal
                const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
                confirmationModal.show();
            } else {
                // Submit form directly
                settingsForm.submit();
            }
        });

        confirmSaveBtn.addEventListener('click', function() {
            // Submit form
            settingsForm.submit();
        });
    });
</script>

<?php include 'includes/footer.php'; ?>
