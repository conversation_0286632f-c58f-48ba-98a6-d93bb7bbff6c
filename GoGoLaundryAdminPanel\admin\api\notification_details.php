<?php
/**
 * Notification Details API
 *
 * This endpoint returns detailed information about a specific notification
 */

header('Content-Type: application/json');

// Include required files
require_once '../../config/db.php';

// Start session first
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check admin authentication
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Authentication required']);
    exit;
}

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get notification ID
$notificationId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Debug logging
error_log("Notification Details API - ID received: " . $notificationId);

// Validate input
if ($notificationId <= 0) {
    error_log("Notification Details API - Invalid ID: " . $notificationId);
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Invalid notification ID']);
    exit;
}

try {
    // Check what columns exist in all relevant tables
    $stmt = $pdo->query("DESCRIBE notifications");
    $notificationColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    error_log("Available columns in notifications table: " . implode(', ', $notificationColumns));

    // Check users table columns
    $userColumns = [];
    try {
        $stmt = $pdo->query("DESCRIBE users");
        $userColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        error_log("Available columns in users table: " . implode(', ', $userColumns));
    } catch (Exception $e) {
        error_log("Users table not accessible: " . $e->getMessage());
    }

    // Check orders table columns
    $orderColumns = [];
    try {
        $stmt = $pdo->query("DESCRIBE orders");
        $orderColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        error_log("Available columns in orders table: " . implode(', ', $orderColumns));
    } catch (Exception $e) {
        error_log("Orders table not accessible: " . $e->getMessage());
    }

    // Build the SELECT query based on available columns
    $selectFields = ['n.id', 'n.user_id', 'n.title', 'n.message', 'n.type', 'n.is_read', 'n.created_at'];

    // Add optional notification columns if they exist
    if (in_array('order_id', $notificationColumns)) {
        $selectFields[] = 'n.order_id';
    }
    if (in_array('image_url', $notificationColumns)) {
        $selectFields[] = 'n.image_url';
    }
    if (in_array('fcm_sent', $notificationColumns)) {
        $selectFields[] = 'n.fcm_sent';
    }
    if (in_array('sms_sent', $notificationColumns)) {
        $selectFields[] = 'n.sms_sent';
    }

    // Add user fields if users table is accessible
    $userFields = [];
    if (!empty($userColumns)) {
        if (in_array('full_name', $userColumns)) {
            $userFields[] = 'u.full_name as user_name';
        }
        if (in_array('phone', $userColumns)) {
            $userFields[] = 'u.phone as user_phone';
        }
        if (in_array('email', $userColumns)) {
            $userFields[] = 'u.email as user_email';
        }
        if (in_array('address', $userColumns)) {
            $userFields[] = 'u.address as user_address';
        }
    }

    // Add order fields if orders table is accessible
    $orderFields = [];
    if (!empty($orderColumns)) {
        if (in_array('order_number', $orderColumns)) {
            $orderFields[] = 'o.order_number';
        }
        if (in_array('status', $orderColumns)) {
            $orderFields[] = 'o.status as order_status';
        }
        if (in_array('total_amount', $orderColumns)) {
            $orderFields[] = 'o.total_amount as order_total';
        } elseif (in_array('total', $orderColumns)) {
            $orderFields[] = 'o.total as order_total';
        }
        if (in_array('created_at', $orderColumns)) {
            $orderFields[] = 'o.created_at as order_date';
        }
    }

    // Combine all fields
    $allFields = array_merge($selectFields, $userFields, $orderFields);

    // Build the FROM and JOIN clauses
    $fromClause = "FROM notifications n";
    if (!empty($userColumns)) {
        $fromClause .= " LEFT JOIN users u ON n.user_id = u.id";
    }
    if (!empty($orderColumns) && in_array('order_id', $notificationColumns)) {
        $fromClause .= " LEFT JOIN orders o ON n.order_id = o.id";
    }

    // Get notification details
    $sql = "SELECT " . implode(', ', $allFields) . " " . $fromClause . " WHERE n.id = ?";
    error_log("Final SQL query: " . $sql);

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$notificationId]);
    $notification = $stmt->fetch();

    error_log("Notification Details API - Query executed, result: " . ($notification ? 'found' : 'not found'));

    if (!$notification) {
        error_log("Notification Details API - Notification not found for ID: " . $notificationId);
        ob_clean();
        echo json_encode(['success' => false, 'error' => 'Notification not found']);
        exit;
    }

    // Format notification type
    function formatNotificationType($type) {
        switch ($type) {
            case 'order_status': return 'Order Status';
            case 'promo': return 'Promotion';
            case 'system': return 'System';
            case 'custom': return 'Custom';
            default: return ucfirst($type);
        }
    }

    // Generate HTML content
    $html = '<div class="notification-details">';

    // Basic Information
    $html .= '<div class="row mb-4">';
    $html .= '<div class="col-md-6">';
    $html .= '<h6 class="text-primary">Basic Information</h6>';
    $html .= '<table class="table table-sm">';
    $html .= '<tr><td><strong>ID:</strong></td><td>' . htmlspecialchars($notification['id']) . '</td></tr>';
    $html .= '<tr><td><strong>Title:</strong></td><td>' . htmlspecialchars($notification['title']) . '</td></tr>';
    $html .= '<tr><td><strong>Type:</strong></td><td><span class="badge bg-primary">' . formatNotificationType($notification['type']) . '</span></td></tr>';
    $html .= '<tr><td><strong>Created:</strong></td><td>' . date('M d, Y H:i:s', strtotime($notification['created_at'])) . '</td></tr>';
    $html .= '</table>';
    $html .= '</div>';

    // Status Information
    $html .= '<div class="col-md-6">';
    $html .= '<h6 class="text-primary">Status Information</h6>';
    $html .= '<table class="table table-sm">';
    $html .= '<tr><td><strong>Read Status:</strong></td><td>';
    if ($notification['is_read']) {
        $html .= '<span class="badge bg-success">Read</span>';
    } else {
        $html .= '<span class="badge bg-warning">Unread</span>';
    }
    $html .= '</td></tr>';

    // Only show FCM status if column exists
    if (isset($notification['fcm_sent'])) {
        $html .= '<tr><td><strong>FCM Status:</strong></td><td>';
        if ($notification['fcm_sent'] === '1') {
            $html .= '<span class="badge bg-success">Sent</span>';
        } elseif ($notification['fcm_sent'] === '0') {
            $html .= '<span class="badge bg-danger">Pending</span>';
        } else {
            $html .= '<span class="badge bg-secondary">N/A</span>';
        }
        $html .= '</td></tr>';
    }

    // Only show SMS status if column exists
    if (isset($notification['sms_sent'])) {
        $html .= '<tr><td><strong>SMS Status:</strong></td><td>';
        if ($notification['sms_sent'] === '1') {
            $html .= '<span class="badge bg-success">Sent</span>';
        } elseif ($notification['sms_sent'] === '0') {
            $html .= '<span class="badge bg-danger">Pending</span>';
        } else {
            $html .= '<span class="badge bg-secondary">N/A</span>';
        }
        $html .= '</td></tr>';
    }
    $html .= '</table>';
    $html .= '</div>';
    $html .= '</div>';

    // Message Content
    $html .= '<div class="mb-4">';
    $html .= '<h6 class="text-primary">Message Content</h6>';
    $html .= '<div class="card">';
    $html .= '<div class="card-body">';
    $html .= '<p class="mb-0">' . nl2br(htmlspecialchars($notification['message'])) . '</p>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';

    // Image (if available and column exists)
    if (isset($notification['image_url']) && !empty($notification['image_url'])) {
        $html .= '<div class="mb-4">';
        $html .= '<h6 class="text-primary">Attached Image</h6>';
        $html .= '<div class="text-center">';
        $html .= '<img src="' . htmlspecialchars($notification['image_url']) . '" alt="Notification Image" class="img-fluid" style="max-height: 200px; border: 1px solid #ddd; border-radius: 8px;">';
        $html .= '<br><small class="text-muted mt-2 d-block">Click image to view full size</small>';
        $html .= '</div>';
        $html .= '</div>';
    }

    // User Information
    if (isset($notification['user_name']) && $notification['user_name']) {
        $html .= '<div class="row mb-4">';
        $html .= '<div class="col-md-6">';
        $html .= '<h6 class="text-primary">User Information</h6>';
        $html .= '<table class="table table-sm">';
        $html .= '<tr><td><strong>Name:</strong></td><td>' . htmlspecialchars($notification['user_name']) . '</td></tr>';
        if (isset($notification['user_phone']) && $notification['user_phone']) {
            $html .= '<tr><td><strong>Phone:</strong></td><td>' . htmlspecialchars($notification['user_phone']) . '</td></tr>';
        }
        if (isset($notification['user_email']) && $notification['user_email']) {
            $html .= '<tr><td><strong>Email:</strong></td><td>' . htmlspecialchars($notification['user_email']) . '</td></tr>';
        }
        $html .= '</table>';
        $html .= '</div>';
        $html .= '</div>';
    }

    // Order Information (if available and order_id column exists)
    if (isset($notification['order_id']) && $notification['order_id'] && isset($notification['order_number']) && $notification['order_number']) {
        $html .= '<div class="mb-4">';
        $html .= '<h6 class="text-primary">Related Order</h6>';
        $html .= '<table class="table table-sm">';
        $html .= '<tr><td><strong>Order Number:</strong></td><td><a href="order_details.php?id=' . $notification['order_id'] . '" target="_blank">' . htmlspecialchars($notification['order_number']) . '</a></td></tr>';
        if (isset($notification['order_status']) && $notification['order_status']) {
            $html .= '<tr><td><strong>Order Status:</strong></td><td>' . htmlspecialchars($notification['order_status']) . '</td></tr>';
        }
        if (isset($notification['order_total']) && $notification['order_total']) {
            $html .= '<tr><td><strong>Order Total:</strong></td><td>৳' . number_format($notification['order_total'], 2) . '</td></tr>';
        }
        if (isset($notification['order_date']) && $notification['order_date']) {
            $html .= '<tr><td><strong>Order Date:</strong></td><td>' . date('M d, Y H:i', strtotime($notification['order_date'])) . '</td></tr>';
        }
        $html .= '</table>';
        $html .= '</div>';
    }

    $html .= '</div>';

    // Clean output buffer and send JSON
    ob_clean();
    echo json_encode([
        'success' => true,
        'html' => $html,
        'notification' => $notification
    ]);

} catch (PDOException $e) {
    error_log('Database error in notification details: ' . $e->getMessage());
    error_log('SQL Query error details: ' . print_r($e->errorInfo, true));
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log('General error in notification details: ' . $e->getMessage());
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Error: ' . $e->getMessage()]);
}
?>
