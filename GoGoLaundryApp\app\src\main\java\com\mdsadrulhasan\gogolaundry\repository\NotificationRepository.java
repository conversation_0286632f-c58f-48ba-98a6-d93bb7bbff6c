package com.mdsadrulhasan.gogolaundry.repository;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.GoGoLaundryApp;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.model.Notification;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.utils.AppExecutors;
import com.mdsadrulhasan.gogolaundry.utils.Resource;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Repository for notification-related operations
 */
public class NotificationRepository {
    private static final String TAG = "NotificationRepository";

    private static NotificationRepository instance;

    private final ApiService apiService;
    private final AppExecutors executors;
    private final SessionManager sessionManager;

    /**
     * Private constructor
     */
    private NotificationRepository() {
        apiService = ApiClient.getApiService(GoGoLaundryApp.getInstance());
        executors = AppExecutors.getInstance();
        sessionManager = new SessionManager(GoGoLaundryApp.getInstance());
    }

    /**
     * Get repository instance
     *
     * @return Repository instance
     */
    public static synchronized NotificationRepository getInstance() {
        if (instance == null) {
            instance = new NotificationRepository();
        }
        return instance;
    }

    // Context for toast notifications
    private Context getContext() {
        return GoGoLaundryApp.getInstance().getApplicationContext();
    }

    /**
     * Get notifications for a user
     *
     * @param userId User ID
     * @param forceRefresh Whether to force a refresh from the server (ignored, always fetches from API)
     * @return LiveData with Resource containing notifications
     */
    public LiveData<Resource<List<Notification>>> getNotificationsByUserId(int userId, boolean forceRefresh) {
        MutableLiveData<Resource<List<Notification>>> result = new MutableLiveData<>();
        result.setValue(Resource.loading(null));

        // Always fetch from API since we don't use local storage anymore
        fetchNotificationsFromApi(userId, result);

        return result;
    }



    /**
     * Fetch notifications from API
     *
     * @param userId User ID
     * @param result MutableLiveData to update with result
     */
    private void fetchNotificationsFromApi(int userId, MutableLiveData<Resource<List<Notification>>> result) {
        Log.d(TAG, "Fetching notifications from API for user: " + userId);
        apiService.getNotifications(userId).enqueue(new Callback<ApiResponse<List<Notification>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<Notification>>> call, Response<ApiResponse<List<Notification>>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<List<Notification>> apiResponse = response.body();

                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        List<Notification> notifications = apiResponse.getData();
                        Log.d(TAG, "Received " + notifications.size() + " notifications from API");

                        // The API now only returns unread notifications (is_read=0)
                        // so we don't need to filter them

                        // Return the notifications directly
                        result.setValue(Resource.success(notifications));
                    } else {
                        // API returned error
                        String errorMessage = apiResponse.getMessage();
                        Log.e(TAG, "API error: " + errorMessage);
                        result.setValue(Resource.error(errorMessage, null));
                    }
                } else {
                    // HTTP error
                    String errorMessage = "Error: " + response.code();
                    Log.e(TAG, errorMessage);
                    result.setValue(Resource.error(errorMessage, null));
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<Notification>>> call, Throwable t) {
                Log.e(TAG, "Error fetching notifications: " + t.getMessage());
                result.setValue(Resource.error("Network error: " + t.getMessage(), null));

                // No fallback to local storage since we don't use it anymore
                result.setValue(Resource.error("No notifications found. Please try again when online.", null));
            }
        });
    }

    /**
     * Get unread notifications count for a user
     *
     * @param userId User ID
     * @return LiveData with unread notifications count
     */
    public LiveData<Integer> getUnreadNotificationsCount(int userId) {
        MutableLiveData<Integer> result = new MutableLiveData<>();

        // Fetch notifications from API and count them
        apiService.getNotifications(userId).enqueue(new Callback<ApiResponse<List<Notification>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<Notification>>> call, Response<ApiResponse<List<Notification>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<Notification> notifications = response.body().getData();
                    if (notifications != null) {
                        // Since the API only returns unread notifications, the count is simply the size
                        int count = notifications.size();
                        Log.d(TAG, "Unread notifications count: " + count);
                        result.setValue(count);
                    } else {
                        result.setValue(0);
                    }
                } else {
                    result.setValue(0);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<Notification>>> call, Throwable t) {
                Log.e(TAG, "Error fetching unread count: " + t.getMessage());
                result.setValue(0);
            }
        });

        return result;
    }

    /**
     * Mark a notification as read
     *
     * @param notificationId Notification ID
     * @return LiveData with updated unread count
     */
    public LiveData<Integer> markAsRead(int notificationId) {
        MutableLiveData<Integer> result = new MutableLiveData<>();

        // Get user ID from session
        User user = sessionManager.getUser();
        if (user == null) {
            Log.e(TAG, "Cannot mark notification as read: No logged in user");
            result.setValue(-1);
            return result;
        }

        int userId = user.getId();
        Log.d(TAG, "Marking notification as read - ID: " + notificationId + ", User ID: " + userId);

        // Make API call to mark notification as read
        markAsReadOnServer(notificationId, userId, result);

        return result;
    }



    /**
     * Mark notification as read on server
     *
     * @param notificationId Notification ID
     * @param userId User ID
     * @param result MutableLiveData to update with result
     */
    private void markAsReadOnServer(int notificationId, int userId, MutableLiveData<Integer> result) {
        Log.d(TAG, "API CALL: Marking notification as read on server: " + notificationId + " for user: " + userId);
        Log.d(TAG, "API URL: " + apiService.getBaseUrl() + "api/notifications/mark_read.php");

        apiService.markNotificationAsRead(notificationId, userId).enqueue(new Callback<ApiResponse<Object>>() {
            @Override
            public void onResponse(Call<ApiResponse<Object>> call, Response<ApiResponse<Object>> response) {
                Log.d(TAG, "API RESPONSE (Mark Read): Status code: " + response.code() + ", Raw response: " + response);

                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    Log.d(TAG, "API SUCCESS: Notification marked as read on server: " + notificationId + ", Message: " + response.body().getMessage());

                    // Get updated unread count
                    getUnreadNotificationsCount(userId).observeForever(count -> {
                        result.setValue(count);
                    });
                } else {
                    // Error marking as read on server
                    String errorMessage = response.isSuccessful() && response.body() != null ?
                            response.body().getMessage() : "Error: " + response.code();
                    Log.e(TAG, "Error marking notification as read on server: " + errorMessage);

                    // Return error
                    result.setValue(-1);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Object>> call, Throwable t) {
                Log.e(TAG, "Network error marking notification as read on server: " + t.getMessage());

                // Return error
                result.setValue(-1);
            }
        });
    }



    /**
     * Mark all notifications as read for a user
     *
     * @param userId User ID
     * @return LiveData with updated unread count (always 0 if successful)
     */
    public LiveData<Integer> markAllAsRead(int userId) {
        MutableLiveData<Integer> result = new MutableLiveData<>();

        // Make API call to mark all as read on server
        makeMarkAllAsReadApiCall(userId, result);

        return result;
    }

    /**
     * Make API call to mark all notifications as read on server
     *
     * @param userId User ID
     * @param result MutableLiveData to update with result
     */
    private void makeMarkAllAsReadApiCall(int userId, MutableLiveData<Integer> result) {
        apiService.markAllNotificationsAsRead(userId).enqueue(new Callback<ApiResponse<Object>>() {
            @Override
            public void onResponse(Call<ApiResponse<Object>> call, Response<ApiResponse<Object>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    Log.d(TAG, "Successfully marked all notifications as read on server for user: " + userId);

                    // All notifications are now read, so unread count is 0
                    result.setValue(0);
                } else {
                    // Error marking all as read on server
                    String errorMessage = response.isSuccessful() && response.body() != null ?
                            response.body().getMessage() : "Error: " + response.code();
                    Log.e(TAG, "Error marking all notifications as read on server: " + errorMessage);

                    // Return error
                    result.setValue(-1);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Object>> call, Throwable t) {
                Log.e(TAG, "Network error marking all notifications as read on server: " + t.getMessage());

                // Return error
                result.setValue(-1);
            }
        });
    }



    /**
     * Get preference for showing read notifications
     * Note: This is kept for backward compatibility but always returns false
     * since we now filter notifications at the server level
     *
     * @return Always returns false since read notifications are filtered at the server
     */
    public boolean getShowReadNotificationsPreference() {
        // Always return false since we now filter at the server level
        return false;
    }

    /**
     * Set preference for showing read notifications
     * Note: This is kept for backward compatibility but does nothing
     * since we now filter notifications at the server level
     *
     * @param showReadNotifications Ignored parameter
     */
    public void setShowReadNotificationsPreference(boolean showReadNotifications) {
        // Do nothing since we now filter at the server level
        Log.d(TAG, "setShowReadNotificationsPreference called but ignored - server now filters notifications");
    }

    /**
     * Get preference for deleting notifications after they're read
     * Note: This is kept for backward compatibility but always returns true
     * since we now filter notifications at the server level
     *
     * @return Always returns true since read notifications are filtered at the server
     */
    public boolean getDeleteAfterReadPreference() {
        // Always return true since we now filter at the server level
        return true;
    }

    /**
     * Set preference for deleting notifications after they're read
     * Note: This is kept for backward compatibility but does nothing
     * since we now filter notifications at the server level
     *
     * @param deleteAfterRead Ignored parameter
     */
    public void setDeleteAfterReadPreference(boolean deleteAfterRead) {
        // Do nothing since we now filter at the server level
        Log.d(TAG, "setDeleteAfterReadPreference called but ignored - server now filters notifications");
    }

    /**
     * Extract order number from notification message
     *
     * @param message Notification message
     * @return Order number or null if not found
     */
    private String extractOrderNumberFromMessage(String message) {
        if (message == null) return null;

        try {
            // Pattern to match order numbers like #ORD2505237175
            Pattern pattern = Pattern.compile("#(ORD\\d+)");
            Matcher matcher = pattern.matcher(message);

            if (matcher.find()) {
                return matcher.group(1);
            }

            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error extracting order number from message: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * Force delete all read notifications for a user
     * Note: This is kept for backward compatibility but always returns true
     * since we now filter notifications at the server level
     *
     * @param userId User ID
     * @return Always returns true since read notifications are filtered at the server
     */
    public boolean forceDeleteReadNotifications(int userId) {
        Log.d(TAG, "Force delete read notifications called but ignored - server now filters notifications");
        return true;
    }
}
