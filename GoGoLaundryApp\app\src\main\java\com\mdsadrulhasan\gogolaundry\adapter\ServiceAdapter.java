package com.mdsadrulhasan.gogolaundry.adapter;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.model.Service;

import java.util.List;

/**
 * Adapter for displaying services in a RecyclerView
 */
public class ServiceAdapter extends RecyclerView.Adapter<ServiceAdapter.ServiceViewHolder> {

    private List<Service> services;
    private final OnServiceClickListener listener;

    /**
     * Interface for handling service item clicks
     */
    public interface OnServiceClickListener {
        void onServiceClick(Service service);
        void onAddToCartClick(Service service);
    }

    /**
     * Constructor
     *
     * @param services List of services
     * @param listener Click listener
     */
    public ServiceAdapter(List<Service> services, OnServiceClickListener listener) {
        this.services = services;
        this.listener = listener;
    }

    /**
     * Update services list and refresh adapter
     *
     * @param services New list of services
     */
    public void updateServices(List<Service> services) {
        this.services = services;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ServiceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_service, parent, false);
        return new ServiceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ServiceViewHolder holder, int position) {
        Service service = services.get(position);
        holder.bind(service, listener);
    }

    @Override
    public int getItemCount() {
        return services.size();
    }

    /**
     * ViewHolder for service items
     */
    static class ServiceViewHolder extends RecyclerView.ViewHolder {

        private final ImageView serviceIcon;
        private final TextView serviceName;
        private final TextView serviceDescription;
        private final TextView servicePrice;
        private final MaterialButton btnAddToCart;

        public ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            serviceIcon = itemView.findViewById(R.id.service_icon);
            serviceName = itemView.findViewById(R.id.service_name);
            serviceDescription = itemView.findViewById(R.id.service_description);
            servicePrice = itemView.findViewById(R.id.service_price);
            btnAddToCart = itemView.findViewById(R.id.btn_add_to_cart);
        }

        /**
         * Bind service data to views
         *
         * @param service Service to display
         * @param listener Click listener
         */
        public void bind(Service service, OnServiceClickListener listener) {
            serviceName.setText(service.getName());
            serviceDescription.setText(service.getDescription());
            // Price is hidden for services - prices should only be shown at the item level

            // Load service icon
            if (service.getIconUrl() != null && !service.getIconUrl().isEmpty()) {
                String iconUrl = service.getIconUrl();
                String imageUrl;

                // Check if the URL is already absolute
                if (iconUrl.startsWith("http://") || iconUrl.startsWith("https://")) {
                    imageUrl = iconUrl;
                } else {
                    // Construct URL based on the API base URL
                    String baseUrl = ApiClient.getBaseUrl();
                    // Remove any leading slashes from iconUrl to avoid double slashes
                    if (iconUrl.startsWith("/")) {
                        iconUrl = iconUrl.substring(1);
                    }

                    // If the icon URL contains "uploads/", assume it's relative to the admin panel root
                    if (iconUrl.contains("uploads/")) {
                        // Go up one level from the API directory to the admin panel root
                        imageUrl = baseUrl + "../" + iconUrl;
                    } else {
                        // Assume it's in the uploads/services directory
                        imageUrl = baseUrl + "../uploads/services/" + iconUrl;
                    }
                }

                // Log the image URL for debugging
                Log.d("ServiceAdapter", "Loading service icon from: " + imageUrl);

                // Load the image with Glide
                Glide.with(itemView.getContext())
                        .load(imageUrl)
                        .placeholder(getServiceDefaultIcon(service))
                        .error(getServiceDefaultIcon(service))
                        .into(serviceIcon);
            } else {
                serviceIcon.setImageResource(getServiceDefaultIcon(service));
                Log.d("ServiceAdapter", "No icon URL available for service: " + service.getName());
            }

            // Set click listeners
            itemView.setOnClickListener(v -> listener.onServiceClick(service));
            btnAddToCart.setOnClickListener(v -> listener.onAddToCartClick(service));
        }

        /**
         * Get default icon based on service name
         *
         * @param service Service
         * @return Resource ID for default icon
         */
        private int getServiceDefaultIcon(Service service) {
            String serviceName = service.getName().toLowerCase();

            if (serviceName.contains("wash") || serviceName.contains("laundry")) {
                return R.drawable.ic_washing_machine;
            } else if (serviceName.contains("iron") || serviceName.contains("press")) {
                return R.drawable.ic_iron;
            } else if (serviceName.contains("dry") || serviceName.contains("clean")) {
                return R.drawable.ic_dry_cleaning;
            } else {
                return R.drawable.ic_washing_machine; // Default
            }
        }
    }
}
