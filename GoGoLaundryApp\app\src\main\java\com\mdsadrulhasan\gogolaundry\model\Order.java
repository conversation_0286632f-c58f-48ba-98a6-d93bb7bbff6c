package com.mdsadrulhasan.gogolaundry.model;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;
import com.google.gson.annotations.SerializedName;
import com.mdsadrulhasan.gogolaundry.database.converters.OrderItemListConverter;

import java.util.List;

/**
 * Model class for orders
 */
@Entity(tableName = "orders")
@TypeConverters(OrderItemListConverter.class)
public class Order {

    @PrimaryKey
    @SerializedName("id")
    private int id;

    @SerializedName("order_number")
    private String orderNumber;

    @SerializedName("tracking_number")
    private String trackingNumber;

    @SerializedName("user_id")
    private int userId;

    @SerializedName("subtotal")
    private double subtotal;

    @SerializedName("discount")
    private double discount;

    @SerializedName("delivery_fee")
    private double deliveryFee;

    @SerializedName("total")
    private double totalAmount;

    @SerializedName("status")
    private String status;

    @SerializedName("payment_status")
    private String paymentStatus;

    @SerializedName("payment_method")
    private String paymentMethod;

    @SerializedName("pickup_date")
    private String pickupDate;

    @SerializedName("pickup_time")
    private String pickupTime;

    @SerializedName("delivery_date")
    private String deliveryDate;

    @SerializedName("delivery_time")
    private String deliveryTime;

    @SerializedName("address")
    private String address;

    @SerializedName("notes")
    private String notes;

    @SerializedName("created_at")
    private String createdAt;

    @SerializedName("updated_at")
    private String updatedAt;

    @SerializedName("order_items")
    private List<OrderItem> orderItems;

    @SerializedName("customer_name")
    private String customerName;

    @SerializedName("customer_phone")
    private String customerPhone;

    @SerializedName("delivery_address")
    private String deliveryAddress;

    // Default constructor required by Room
    public Order() {
    }

    // Constructor with all fields
    public Order(int id, String orderNumber, String trackingNumber, int userId,
                double subtotal, double discount, double deliveryFee, double totalAmount,
                String status, String paymentStatus, String paymentMethod,
                String pickupDate, String pickupTime, String deliveryDate, String deliveryTime,
                String address, String notes, String createdAt, String updatedAt,
                List<OrderItem> orderItems, String customerName, String customerPhone,
                String deliveryAddress) {
        this.id = id;
        this.orderNumber = orderNumber;
        this.trackingNumber = trackingNumber;
        this.userId = userId;
        this.subtotal = subtotal;
        this.discount = discount;
        this.deliveryFee = deliveryFee;
        this.totalAmount = totalAmount;
        this.status = status;
        this.paymentStatus = paymentStatus;
        this.paymentMethod = paymentMethod;
        this.pickupDate = pickupDate;
        this.pickupTime = pickupTime;
        this.deliveryDate = deliveryDate;
        this.deliveryTime = deliveryTime;
        this.address = address;
        this.notes = notes;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.orderItems = orderItems;
        this.customerName = customerName;
        this.customerPhone = customerPhone;
        this.deliveryAddress = deliveryAddress;
    }

    // Constructor with minimal fields (for backward compatibility)
    public Order(int id, String orderNumber, String trackingNumber, int userId, double totalAmount, String status,
                String paymentStatus, String paymentMethod, String pickupDate, String pickupTime,
                String deliveryDate, String deliveryTime, String address, String notes,
                String createdAt, String updatedAt, List<OrderItem> orderItems,
                String customerName, String customerPhone, String deliveryAddress) {
        this(id, orderNumber, trackingNumber, userId, 0.0, 0.0, 0.0, totalAmount,
             status, paymentStatus, paymentMethod, pickupDate, pickupTime,
             deliveryDate, deliveryTime, address, notes, createdAt, updatedAt,
             orderItems, customerName, customerPhone, deliveryAddress);
    }

    // Getters and setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public double getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(double subtotal) {
        this.subtotal = subtotal;
    }

    public double getDiscount() {
        return discount;
    }

    public void setDiscount(double discount) {
        this.discount = discount;
    }

    public double getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(double deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPickupDate() {
        return pickupDate;
    }

    public void setPickupDate(String pickupDate) {
        this.pickupDate = pickupDate;
    }

    public String getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(String pickupTime) {
        this.pickupTime = pickupTime;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(String deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<OrderItem> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<OrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    // Helper method to get formatted total amount
    public String getFormattedTotalAmount() {
        return "৳" + totalAmount;
    }

    // Alias for getTotalAmount() for compatibility with InvoiceFragment
    public double getTotal() {
        return totalAmount;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }
}
