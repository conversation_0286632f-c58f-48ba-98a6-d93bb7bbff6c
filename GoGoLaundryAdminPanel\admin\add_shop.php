<?php
// Include authentication middleware
require_once 'auth.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        $required_fields = ['shop_name', 'owner_name', 'phone', 'address', 'latitude', 'longitude'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("$field is required");
            }
        }

        // Sanitize and validate input
        $shop_name = trim($_POST['shop_name']);
        $owner_name = trim($_POST['owner_name']);
        $phone = trim($_POST['phone']);
        $email = !empty($_POST['email']) ? trim($_POST['email']) : null;
        $address = trim($_POST['address']);
        $latitude = floatval($_POST['latitude']);
        $longitude = floatval($_POST['longitude']);
        $commission_rate = floatval($_POST['commission_rate'] ?? 15.0);
        $is_verified = intval($_POST['is_verified'] ?? 0);
        $is_active = intval($_POST['is_active'] ?? 1);

        // Validate coordinates
        if ($latitude < -90 || $latitude > 90) {
            throw new Exception("Invalid latitude. Must be between -90 and 90.");
        }
        if ($longitude < -180 || $longitude > 180) {
            throw new Exception("Invalid longitude. Must be between -180 and 180.");
        }

        // Validate commission rate
        if ($commission_rate < 0 || $commission_rate > 50) {
            throw new Exception("Commission rate must be between 0 and 50 percent.");
        }

        // Check if phone number already exists
        $checkPhoneStmt = $pdo->prepare("SELECT id FROM laundry_shops WHERE phone = ?");
        $checkPhoneStmt->execute([$phone]);
        if ($checkPhoneStmt->fetch()) {
            throw new Exception("A shop with this phone number already exists.");
        }

        // Insert new shop
        $sql = "
            INSERT INTO laundry_shops (
                name, owner_name, phone, email, address,
                latitude, longitude, commission_percentage,
                is_verified, is_active, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $shop_name, $owner_name, $phone, $email, $address,
            $latitude, $longitude, $commission_rate,
            $is_verified, $is_active
        ]);

        if ($result) {
            $shop_id = $pdo->lastInsertId();

            // Log the action
            $admin_id = $_SESSION['admin_id'];
            $log_message = "Added new shop: $shop_name (ID: $shop_id)";
            logAdminActivity($pdo, $admin_id, 'shop_add', $log_message);

            $_SESSION['success_message'] = "Shop '$shop_name' has been added successfully!";
        } else {
            throw new Exception("Failed to add shop. Please try again.");
        }

    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }
}

// Redirect back to shops page
header('Location: shops.php');
exit();

/**
 * Log admin activity
 */
function logAdminActivity($pdo, $admin_id, $action, $description) {
    try {
        $sql = "INSERT INTO admin_activity_logs (admin_id, action, description, ip_address, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$admin_id, $action, $description, $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
    } catch (Exception $e) {
        // Log error but don't fail the main operation
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}
?>
