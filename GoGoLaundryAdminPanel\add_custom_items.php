<?php
/**
 * Add Custom Items Script
 *
 * This script adds custom items to the database to match the ones in the mobile app cart
 */

// Include required files
require_once 'config/db.php';
require_once 'includes/functions.php';

// First, make sure the services table exists and has data
$stmt = $pdo->query("SHOW TABLES LIKE 'services'");
$servicesTableExists = $stmt->rowCount() > 0;

if (!$servicesTableExists) {
    echo "Services table does not exist. Please run add_sample_data.php first.<br>";
    exit;
}

// Check if there are any services
$stmt = $pdo->query("SELECT COUNT(*) FROM services");
$serviceCount = $stmt->fetchColumn();

if ($serviceCount == 0) {
    echo "No services found. Please run add_sample_data.php first.<br>";
    exit;
}

// Get service IDs
$stmt = $pdo->query("SELECT id, name FROM services");
$services = $stmt->fetchAll(PDO::FETCH_ASSOC);

$serviceMap = [];
foreach ($services as $service) {
    $serviceMap[$service['name']] = $service['id'];
}

// Default service ID (use the first service if available)
$defaultServiceId = reset($serviceMap);

// Check if items table exists
$stmt = $pdo->query("SHOW TABLES LIKE 'items'");
$itemsTableExists = $stmt->rowCount() > 0;

if (!$itemsTableExists) {
    echo "Items table does not exist. Please run add_sample_data.php first.<br>";
    exit;
}

// Define custom items to add or update
// These should match the items in the mobile app cart
$customItems = [
    [
        'id' => 1,
        'name' => 'test',
        'description' => 'Test item for mobile app',
        'price' => 22.00,
        'service_name' => 'Wash & Fold' // Use an existing service
    ],
    [
        'id' => 2,
        'name' => 'আইরন',
        'description' => 'Bengali name item for testing',
        'price' => 22.00,
        'service_name' => 'Ironing' // Use an existing service
    ]
];

// Check if each custom item exists, update if it does, add if it doesn't
foreach ($customItems as $item) {
    // Check if item with this ID exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM items WHERE id = ?");
    $stmt->execute([$item['id']]);
    $itemExists = $stmt->fetchColumn() > 0;
    
    // Get service ID
    $serviceId = isset($serviceMap[$item['service_name']]) ? $serviceMap[$item['service_name']] : $defaultServiceId;
    
    if ($itemExists) {
        // Update existing item
        $stmt = $pdo->prepare("
            UPDATE items 
            SET service_id = ?, name = ?, description = ?, price = ?, is_active = 1, in_stock = 1
            WHERE id = ?
        ");
        
        $stmt->execute([
            $serviceId,
            $item['name'],
            $item['description'],
            $item['price'],
            $item['id']
        ]);
        
        echo "Updated item ID {$item['id']}: {$item['name']} for service: {$item['service_name']}<br>";
    } else {
        // Insert new item with specific ID
        $stmt = $pdo->prepare("
            INSERT INTO items (id, service_id, name, description, price, is_active, in_stock)
            VALUES (?, ?, ?, ?, ?, 1, 1)
        ");
        
        $stmt->execute([
            $item['id'],
            $serviceId,
            $item['name'],
            $item['description'],
            $item['price']
        ]);
        
        echo "Added item ID {$item['id']}: {$item['name']} for service: {$item['service_name']}<br>";
    }
}

// Check if the items were added successfully
$stmt = $pdo->query("SELECT id, name, price FROM items WHERE id IN (1, 2)");
$items = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Custom Items in Database:</h3>";
echo "<ul>";
foreach ($items as $item) {
    echo "<li>ID: {$item['id']}, Name: {$item['name']}, Price: {$item['price']}</li>";
}
echo "</ul>";

echo "<br>Custom items added/updated successfully. <a href='admin/create_order.php'>Go to Create Order page</a>";
