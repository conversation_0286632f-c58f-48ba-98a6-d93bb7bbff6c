# Promotional Dialog Implementation

## Overview
A compact, centered promotional dialog with glassmorphism effects that appears every time the app is launched (not on internal navigation). The dialog features a close button positioned above the main dialog content, proper sizing constraints, and smooth animations.

## Features
- **App Launch Detection**: Shows every time the app is launched from outside (not on internal navigation)
- **Compact Design**: Centered popup with proper margins (not full-screen)
- **Responsive Sizing**: Maximum width of 360dp with 32dp horizontal margins
- **Transparent Background**: Semi-transparent overlay with blur effects
- **Glassmorphism Design**: Modern glass-like appearance consistent with app theme
- **Close Button**: Positioned above the dialog content for easy dismissal
- **Smooth Animations**: Scale-in entrance and scale-out exit animations
- **Navigation Integration**: "Shop Now" button navigates to ServicesFragment
- **Optimized Content**: Compact text sizes and spacing for better UX

## Files Created/Modified

### New Files:
1. **PromoDialog.java** - Main dialog fragment class
2. **dialog_promo_sale.xml** - Dialog layout with promotional content
3. **promo_dialog_background.xml** - Glassmorphism background drawable
4. **promo_close_button_background.xml** - Close button styling
5. **dialog_scale_in.xml** - Entrance animation
6. **dialog_scale_out.xml** - Exit animation

### Modified Files:
1. **MainActivity.java** - Added app launch flag passing to HomeFragment
2. **HomeFragment.java** - Added dialog showing logic with app launch detection

## Usage

### Automatic Display
The dialog automatically appears every time the app is launched from outside (not on internal navigation). It shows after the shimmer effects complete (approximately 3 seconds after fragment creation).

### Manual Display
To show the dialog manually (for testing):

```java
PromoDialog promoDialog = PromoDialog.newInstance();
promoDialog.show(getSupportFragmentManager(), "PromoDialog");
```

### Testing App Launch Behavior
The dialog will show every time you:
1. Close the app completely and reopen it
2. Launch the app from the launcher
3. Launch the app from recent apps after it was killed

The dialog will NOT show when:
1. Navigating between fragments within the app
2. Using the back button to return to HomeFragment
3. Using bottom navigation to go to HomeFragment

## Customization

### Content Modification
Edit `dialog_promo_sale.xml` to change:
- Sale text and titles
- Product images
- Button text
- Colors and styling

### Timing Adjustment
Modify the delay in `HomeFragment.java`:
```java
// Current: Shows 1 second after shimmer completes
mainHandler.postDelayed(() -> {
    // Show dialog code
}, SHIMMER_DURATION + 1000);
```

### Animation Changes
Replace animation files in `/res/anim/`:
- `dialog_scale_in.xml` - Entrance animation
- `dialog_scale_out.xml` - Exit animation

### Styling Updates
Modify drawable files:
- `promo_dialog_background.xml` - Main dialog background
- `promo_close_button_background.xml` - Close button styling

## Technical Details

### Architecture
- Uses DialogFragment for proper lifecycle management
- Implements MVVM pattern compliance
- Follows Material Design guidelines
- Consistent with app's glassmorphism theme

### Performance
- Lightweight implementation
- Proper memory management
- Animation optimization
- Fragment lifecycle awareness

### Compatibility
- Works with existing navigation system
- Compatible with MainActivity structure
- Supports all screen sizes
- Follows Android best practices

## Testing

1. **App Launch Test**: Close app completely and reopen to see dialog
2. **Navigation Test**: Click "Shop Now" to verify navigation works
3. **Close Test**: Test both close button and background tap dismissal
4. **Animation Test**: Verify smooth entrance and exit animations
5. **Lifecycle Test**: Rotate device and test fragment lifecycle handling

## Troubleshooting

### Dialog Not Showing
- Check if `sessionManager.isFirstLaunch()` returns true
- Verify fragment is properly attached
- Check timing delays

### Animation Issues
- Ensure animation files are in correct directory
- Verify animation resource IDs are correct
- Check for context availability

### Navigation Problems
- Verify MainActivity instance check
- Ensure fragment container ID is correct
- Check fragment manager state
