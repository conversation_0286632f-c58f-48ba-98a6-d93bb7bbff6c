package com.mdsadrulhasan.gogolaundry.api;

import com.google.gson.annotations.SerializedName;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.model.OrderItem;
import com.mdsadrulhasan.gogolaundry.model.OrderStatusHistory;

import java.util.List;

/**
 * Response class for order tracking API
 * This class handles the nested structure of the order tracking response
 */
public class OrderTrackingResponse {
    
    @SerializedName("order")
    private Order order;
    
    @SerializedName("items")
    private List<OrderItem> items;
    
    @SerializedName("status_history")
    private List<OrderStatusHistory> statusHistory;
    
    /**
     * Get order
     * 
     * @return Order
     */
    public Order getOrder() {
        return order;
    }
    
    /**
     * Set order
     * 
     * @param order Order
     */
    public void setOrder(Order order) {
        this.order = order;
    }
    
    /**
     * Get order items
     * 
     * @return Order items
     */
    public List<OrderItem> getItems() {
        return items;
    }
    
    /**
     * Set order items
     * 
     * @param items Order items
     */
    public void setItems(List<OrderItem> items) {
        this.items = items;
    }
    
    /**
     * Get order status history
     * 
     * @return Order status history
     */
    public List<OrderStatusHistory> getStatusHistory() {
        return statusHistory;
    }
    
    /**
     * Set order status history
     * 
     * @param statusHistory Order status history
     */
    public void setStatusHistory(List<OrderStatusHistory> statusHistory) {
        this.statusHistory = statusHistory;
    }
}
