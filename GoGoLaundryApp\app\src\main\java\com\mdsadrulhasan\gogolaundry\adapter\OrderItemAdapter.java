package com.mdsadrulhasan.gogolaundry.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.OrderItem;

import java.util.List;

/**
 * Adapter for order items
 */
public class OrderItemAdapter extends RecyclerView.Adapter<OrderItemAdapter.OrderItemViewHolder> {

    private final Context context;
    private List<OrderItem> items;

    /**
     * Constructor
     *
     * @param context Context
     * @param items Order items
     */
    public OrderItemAdapter(Context context, List<OrderItem> items) {
        this.context = context;
        this.items = items;
    }

    /**
     * Update items
     *
     * @param items New items
     */
    public void updateItems(List<OrderItem> items) {
        this.items = items;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public OrderItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_order_item, parent, false);
        return new OrderItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull OrderItemViewHolder holder, int position) {
        OrderItem item = items.get(position);
        holder.bind(item);
    }

    @Override
    public int getItemCount() {
        return items != null ? items.size() : 0;
    }

    /**
     * ViewHolder for order items
     */
    static class OrderItemViewHolder extends RecyclerView.ViewHolder {

        private final ImageView itemImage;
        private final TextView itemName;
        private final TextView serviceName;
        private final TextView itemQuantity;
        private final TextView itemPrice;
        private final TextView itemSubtotal;

        public OrderItemViewHolder(@NonNull View itemView) {
            super(itemView);
            itemImage = itemView.findViewById(R.id.item_image);
            itemName = itemView.findViewById(R.id.item_name);
            serviceName = itemView.findViewById(R.id.service_name);
            itemQuantity = itemView.findViewById(R.id.item_quantity);
            itemPrice = itemView.findViewById(R.id.item_price);
            itemSubtotal = itemView.findViewById(R.id.item_subtotal);
        }

        /**
         * Bind order item data
         *
         * @param item Order item
         */
        public void bind(OrderItem item) {
            // Set item name
            itemName.setText(item.getServiceName());

            // Set service name if available
            if (item.getServiceBnName() != null && !item.getServiceBnName().isEmpty()) {
                serviceName.setText(item.getServiceBnName());
                serviceName.setVisibility(View.VISIBLE);
            } else {
                serviceName.setVisibility(View.GONE);
            }

            // Set quantity
            itemQuantity.setText(itemView.getContext().getString(R.string.quantity_format,
                    String.valueOf(item.getQuantity()),
                    item.getUnit() != null ? item.getUnit() : ""));

            // Set price
            itemPrice.setText(itemView.getContext().getString(R.string.price_format,
                    String.valueOf(item.getPrice())));

            // Set subtotal
            itemSubtotal.setText(itemView.getContext().getString(R.string.subtotal_format,
                    String.valueOf(item.getSubtotal())));

            // Load image if available
            if (item.getImageUrl() != null && !item.getImageUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(item.getImageUrl())
                        .apply(new RequestOptions()
                                .placeholder(R.drawable.placeholder_image)
                                .error(R.drawable.placeholder_image))
                        .into(itemImage);
                itemImage.setVisibility(View.VISIBLE);
            } else {
                itemImage.setVisibility(View.GONE);
            }
        }
    }
}
