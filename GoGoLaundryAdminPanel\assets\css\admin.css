/**
 * Admin Dashboard CSS
 */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

/* Sidebar Styles */
.sidebar {
    min-width: 250px;
    max-width: 250px;
    background: #343a40;
    color: #fff;
    transition: all 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    overflow-y: auto; /* Enable scrolling for long menus */
}

.sidebar.active {
    margin-left: -250px;
}

.sidebar .sidebar-header {
    padding: 20px;
    background: #212529;
    text-align: center;
}

.sidebar .sidebar-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.sidebar .sidebar-subtitle {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 5px;
}

.sidebar ul.components {
    padding: 20px 0;
    flex-grow: 1;
}

.sidebar ul li {
    position: relative;
}

.sidebar ul li a {
    padding: 10px 20px;
    font-size: 1rem;
    display: block;
    color: #adb5bd;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar ul li a:hover {
    color: #fff;
    background: #495057;
}

.sidebar ul li.active > a {
    color: #fff;
    background: #0d6efd;
}

.sidebar ul li.submenu-active > a {
    color: #fff;
    background: #495057;
}

.sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar ul ul a {
    padding-left: 40px;
    font-size: 0.9rem;
}

/* Mobile-specific sidebar styles */
@media (max-width: 768px) {
    .sidebar ul li a {
        padding: 15px 20px;
    }

    .sidebar ul li a i {
        margin-right: 15px;
        width: 20px;
    }

    .sidebar ul ul a {
        padding-left: 50px;
    }
}

.sidebar-footer {
    padding: 15px;
    background: #212529;
    text-align: center;
}

/* Content Styles */
.content {
    width: 100%;
    min-height: 100vh;
    transition: all 0.3s ease-in-out;
    position: relative;
}

.content.active {
    margin-left: -250px;
}

/* Navbar button styles */
#sidebarCollapse {
    background: transparent;
    border: none;
    padding: 10px;
    transition: all 0.3s;
}

#sidebarCollapse:hover {
    background: rgba(255, 255, 255, 0.1);
}

#sidebarCollapse:focus {
    outline: none;
    box-shadow: none;
}

/* Card Styles */
.card {
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 20px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Dashboard Stats */
.stats-card {
    border-left: 4px solid;
    transition: transform 0.3s;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card .stats-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 1.5rem;
    font-weight: 600;
}

.stats-card .stats-text {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Table Styles */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 10px;
    color: #6c757d;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: #0d6efd;
    color: #fff !important;
    border: none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #0b5ed7;
    color: #fff !important;
    border: none;
}

/* Form Styles */
.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.form-label {
    font-weight: 500;
}

.form-text {
    font-size: 0.8rem;
}

/* Button Styles */
.btn {
    border-radius: 5px;
    padding: 8px 16px;
}

.btn-icon {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Alert Styles */
.alert {
    border-radius: 5px;
}

/* Dropdown Menu Styles */
.dropdown-menu {
    position: absolute !important;
    z-index: 1000 !important;
}

.dropdown-menu.show {
    display: block !important;
}

.btn-group {
    position: relative;
}

/* Chart Styles */
.chart-area {
    position: relative;
    height: 300px;
    margin: 0 auto;
}

.chart-pie {
    position: relative;
    margin: 0 auto;
}

.chart-pie canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Dashboard Card Enhancements */
.hover-shadow {
    transition: all 0.3s ease;
}

.hover-shadow:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.rounded-3 {
    border-radius: 0.5rem !important;
}

.bg-gradient-light {
    background: linear-gradient(to right, #f8f9fa, #e9ecef);
}

/* Icon Circle */
.icon-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    font-size: 1.5rem;
}

/* Chart Legend Styles */
#orderStatusLegend {
    margin-top: 15px;
}

#orderStatusLegend .legend-item {
    display: inline-flex;
    align-items: center;
    margin: 0 8px 8px 0;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: rgba(0,0,0,0.02);
    transition: all 0.2s ease;
}

#orderStatusLegend .legend-item:hover {
    background-color: rgba(0,0,0,0.05);
    transform: translateY(-2px);
}

#orderStatusLegend .legend-item i {
    margin-right: 5px;
}

/* Tooltip Styles */
.chartjs-tooltip {
    opacity: 1;
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding: 10px;
    font-size: 12px;
    pointer-events: none;
    max-width: 200px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    /* Sidebar adjustments for mobile */
    .sidebar {
        margin-left: -250px;
        position: fixed;
        z-index: 1050;
        height: 100%;
        box-shadow: none;
        top: 0;
        left: 0;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }

    .sidebar.active {
        margin-left: 0;
        box-shadow: 3px 0 10px rgba(0, 0, 0, 0.2);
    }

    /* Improve touch targets in sidebar */
    .sidebar ul li a {
        padding: 15px 20px;
        font-size: 16px; /* Larger font for better touch */
    }

    .sidebar ul ul a {
        padding-left: 40px;
        font-size: 14px;
    }

    /* Content adjustments */
    .content {
        width: 100%;
        margin-left: 0;
        padding-bottom: 60px; /* Extra padding for mobile scrolling */
    }

    /* Remove content shifting */
    .content.active {
        margin-left: 0;
    }

    /* Navbar adjustments */
    .navbar {
        padding: 10px 5px;
    }

    #sidebarCollapse {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        margin-right: 10px;
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Overlay for sidebar */
    .sidebar-overlay {
        display: none;
        position: fixed;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.4);
        z-index: 1040;
        opacity: 0;
        transition: all 0.3s ease-in-out;
        top: 0;
        left: 0;
    }

    .sidebar-overlay.active {
        display: block;
        opacity: 1;
    }

    /* Dropdown menu adjustments */
    .dropdown-menu {
        position: absolute !important;
        float: none;
        min-width: 180px;
    }

    /* Table adjustments */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Card and chart adjustments */
    .card {
        margin-bottom: 15px;
    }

    .chart-area, .chart-pie {
        height: 250px;
    }

    /* Form adjustments */
    .form-control, .form-select, .btn {
        height: 45px; /* Larger touch targets */
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Improve spacing */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
}
