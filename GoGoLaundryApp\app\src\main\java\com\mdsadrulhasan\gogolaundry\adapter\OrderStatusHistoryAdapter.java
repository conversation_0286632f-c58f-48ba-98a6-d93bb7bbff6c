package com.mdsadrulhasan.gogolaundry.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.OrderStatusHistory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for order status history
 */
public class OrderStatusHistoryAdapter extends RecyclerView.Adapter<OrderStatusHistoryAdapter.StatusHistoryViewHolder> {

    private final Context context;
    private List<OrderStatusHistory> items;

    /**
     * Constructor
     *
     * @param context Context
     * @param items Status history items
     */
    public OrderStatusHistoryAdapter(Context context, List<OrderStatusHistory> items) {
        this.context = context;
        this.items = items;
    }

    /**
     * Update items
     *
     * @param items New items
     */
    public void updateItems(List<OrderStatusHistory> items) {
        this.items = items;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public StatusHistoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_status_history, parent, false);
        return new StatusHistoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StatusHistoryViewHolder holder, int position) {
        OrderStatusHistory item = items.get(position);
        holder.bind(item);
    }

    @Override
    public int getItemCount() {
        return items != null ? items.size() : 0;
    }

    /**
     * ViewHolder for status history items
     */
    static class StatusHistoryViewHolder extends RecyclerView.ViewHolder {

        private final TextView statusText;
        private final TextView dateText;
        private final TextView notesText;
        private final TextView updatedByText;

        public StatusHistoryViewHolder(@NonNull View itemView) {
            super(itemView);
            statusText = itemView.findViewById(R.id.status_text);
            dateText = itemView.findViewById(R.id.date_text);
            notesText = itemView.findViewById(R.id.notes_text);
            updatedByText = itemView.findViewById(R.id.updated_by_text);
        }

        /**
         * Bind status history data
         *
         * @param item Status history item
         */
        public void bind(OrderStatusHistory item) {
            // Set status
            statusText.setText(formatStatus(item.getStatus()));

            // Set date
            dateText.setText(formatDate(item.getCreatedAt()));

            // Set notes if available
            if (item.getNotes() != null && !item.getNotes().isEmpty()) {
                notesText.setText(item.getNotes());
                notesText.setVisibility(View.VISIBLE);
            } else {
                notesText.setVisibility(View.GONE);
            }

            // Set updated by if available
            String updatedBy = getUpdatedByText(item);
            if (updatedBy != null && !updatedBy.isEmpty()) {
                updatedByText.setText(updatedBy);
                updatedByText.setVisibility(View.VISIBLE);
            } else {
                updatedByText.setVisibility(View.GONE);
            }

            // Set status color
            setStatusColor(item.getStatus());
        }

        /**
         * Format status for display
         *
         * @param status Status
         * @return Formatted status
         */
        private String formatStatus(String status) {
            if (status == null || status.isEmpty()) {
                return "Unknown";
            }

            // Replace underscores with spaces and capitalize each word
            String[] words = status.split("_");
            StringBuilder formattedStatus = new StringBuilder();

            for (String word : words) {
                if (word.length() > 0) {
                    formattedStatus.append(Character.toUpperCase(word.charAt(0)))
                            .append(word.substring(1).toLowerCase())
                            .append(" ");
                }
            }

            return formattedStatus.toString().trim();
        }

        /**
         * Format date for display
         *
         * @param dateString Date string
         * @return Formatted date
         */
        private String formatDate(String dateString) {
            if (dateString == null || dateString.isEmpty()) {
                return "";
            }

            try {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                SimpleDateFormat outputFormat = new SimpleDateFormat("MMM dd, yyyy 'at' hh:mm a", Locale.getDefault());
                Date date = inputFormat.parse(dateString);
                return date != null ? outputFormat.format(date) : dateString;
            } catch (ParseException e) {
                return dateString;
            }
        }

        /**
         * Get updated by text
         *
         * @param item Status history item
         * @return Updated by text
         */
        private String getUpdatedByText(OrderStatusHistory item) {
            String updatedByType = item.getUpdatedByType();

            if (updatedByType == null || updatedByType.isEmpty()) {
                return "Updated by System";
            }

            // Get updated by ID (could be null for system updates)
            int updatedById = item.getUpdatedBy();
            String updatedByInfo = updatedById > 0 ? " #" + updatedById : "";

            switch (updatedByType.toLowerCase()) {
                case "admin":
                    return "Updated by Admin" + updatedByInfo;
                case "user":
                    return "Updated by Customer" + updatedByInfo;
                case "delivery_personnel":
                    return "Updated by Delivery Person" + updatedByInfo;
                case "system":
                default:
                    return "Updated by System";
            }
        }

        /**
         * Set status color based on status
         *
         * @param status Status
         */
        private void setStatusColor(String status) {
            int colorResId;

            switch (status.toLowerCase()) {
                case "placed":
                case "confirmed":
                    colorResId = R.color.info;
                    break;
                case "pickup_scheduled":
                case "picked_up":
                case "processing":
                    colorResId = R.color.warning;
                    break;
                case "ready_for_delivery":
                case "out_for_delivery":
                    colorResId = R.color.primary;
                    break;
                case "delivered":
                    colorResId = R.color.success;
                    break;
                case "cancelled":
                    colorResId = R.color.error;
                    break;
                default:
                    colorResId = R.color.text_secondary;
                    break;
            }

            statusText.setTextColor(itemView.getContext().getResources().getColor(colorResId));
        }
    }
}
