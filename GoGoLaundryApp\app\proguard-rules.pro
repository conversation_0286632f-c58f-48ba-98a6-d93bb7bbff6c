# ===============================================================================
# GOGOLAUNDRY PROGUARD CONFIGURATION - MAXIMUM SECURITY & OBFUSCATION
# ===============================================================================
# This configuration provides comprehensive protection against decompilation
# and reverse engineering attempts while maintaining app functionality.
# ===============================================================================

# ===============================================================================
# BASIC OBFUSCATION SETTINGS
# ===============================================================================

# Enable aggressive obfuscation
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontpreverify
-verbose

# Optimization settings for maximum obfuscation
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*,!code/allocation/variable
-optimizationpasses 5
-allowaccessmodification
-repackageclasses ''
-flattenpackagehierarchy ''

# Remove debugging information
-renamesourcefileattribute ""
-keepattributes !SourceFile,!LineNumberTable

# ===============================================================================
# STRING OBFUSCATION & ANTI-REVERSE ENGINEERING
# ===============================================================================

# Obfuscate all string literals (makes API endpoints, keys harder to find)
-adaptclassstrings
-adaptresourcefilenames
-adaptresourcefilecontents

# Remove all logging and debug information
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove System.out.println calls
-assumenosideeffects class java.lang.System {
    public static void out.println(...);
    public static void err.println(...);
}

# ===============================================================================
# ANDROID FRAMEWORK PRESERVATION
# ===============================================================================

# Keep Android framework classes
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends androidx.fragment.app.Fragment
-keep public class * extends androidx.fragment.app.DialogFragment

# Keep View constructors
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Keep onClick methods
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# ===============================================================================
# ANDROIDX & MATERIAL DESIGN PRESERVATION
# ===============================================================================

# AndroidX and Material Design Components
-keep class androidx.** { *; }
-keep interface androidx.** { *; }
-dontwarn androidx.**

-keep class com.google.android.material.** { *; }
-dontwarn com.google.android.material.**

# ===============================================================================
# SERIALIZATION & PARCELABLE PRESERVATION
# ===============================================================================

# Keep Serializable classes
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Keep Parcelable classes
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# ===============================================================================
# GSON & JSON PRESERVATION
# ===============================================================================

# Gson specific classes
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep generic signature of TypeToken
-keepattributes Signature
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# Application classes that will be serialized/deserialized over Gson
-keep class com.mdsadrulhasan.gogolaundry.model.** { *; }
-keep class com.mdsadrulhasan.gogolaundry.data.model.** { *; }

# ===============================================================================
# RETROFIT & NETWORKING PRESERVATION
# ===============================================================================

# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }

# ===============================================================================
# ROOM DATABASE PRESERVATION
# ===============================================================================

# Room Database
-keep class androidx.room.** { *; }
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *
-dontwarn androidx.room.paging.**

# Keep database entities and DAOs
-keep class com.mdsadrulhasan.gogolaundry.database.** { *; }

# ===============================================================================
# FIREBASE & GOOGLE SERVICES PRESERVATION
# ===============================================================================

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Firebase Messaging
-keep class com.google.firebase.messaging.** { *; }
-keep class com.google.firebase.iid.** { *; }

# ===============================================================================
# THIRD-PARTY LIBRARIES PRESERVATION
# ===============================================================================

# Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

# SweetAlert
-keep class cn.pedant.SweetAlert.** { *; }
-dontwarn cn.pedant.SweetAlert.**

# Shimmer
-keep class com.facebook.shimmer.** { *; }

# PhotoView
-keep class com.github.chrisbanes.photoview.** { *; }

# ===============================================================================
# APPLICATION-SPECIFIC PRESERVATION
# ===============================================================================

# Keep main application class
-keep class com.mdsadrulhasan.gogolaundry.GoGoLaundryApp { *; }

# Keep all activities, fragments, and services (but obfuscate their internals)
-keep public class com.mdsadrulhasan.gogolaundry.** extends android.app.Activity
-keep public class com.mdsadrulhasan.gogolaundry.** extends androidx.fragment.app.Fragment
-keep public class com.mdsadrulhasan.gogolaundry.** extends android.app.Service

# Keep API interfaces (but obfuscate method names where possible)
-keep interface com.mdsadrulhasan.gogolaundry.api.** { *; }

# Keep ViewModels and LiveData
-keep class com.mdsadrulhasan.gogolaundry.viewmodel.** { *; }

# ===============================================================================
# ADVANCED ANTI-REVERSE ENGINEERING
# ===============================================================================

# Obfuscate package names aggressively
-repackageclasses 'o'
-allowaccessmodification

# Remove unused code aggressively
-dontwarn **
-ignorewarnings

# Additional string obfuscation
-adaptclassstrings
-adaptresourcefilenames **.xml,**.png,**.jpg
-adaptresourcefilecontents **.xml

# ===============================================================================
# REFLECTION PRESERVATION (MINIMAL)
# ===============================================================================

# Only keep essential reflection-based classes
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses

# ===============================================================================
# ENHANCED ANTI-DECOMPILATION MEASURES
# ===============================================================================

# Advanced obfuscation techniques
-overloadaggressively
-useuniqueclassmembernames
-dontusemixedcaseclassnames
-keeppackagenames doNotKeepAThing

# Control flow obfuscation (makes code harder to understand)
-optimizations !method/inlining/*,!class/merging/*,!code/allocation/variable

# Remove all possible debugging information
-keepattributes !SourceFile,!LineNumberTable,!LocalVariableTable,!LocalVariableTypeTable,!SourceDir,!Synthetic,!Deprecated,!*Annotation*,!Signature,!Exceptions

# String encryption (additional layer)
-adaptclassstrings **
-adaptresourcefilenames **.properties,**.xml,**.png,**.jpg,**.jpeg,**.gif,**.webp
-adaptresourcefilecontents **.properties,**.xml

# Remove stack traces (makes debugging harder for attackers)
-assumenosideeffects class java.lang.Throwable {
    public void printStackTrace();
    public void printStackTrace(java.io.PrintStream);
    public void printStackTrace(java.io.PrintWriter);
}

# Remove reflection helpers
-assumenosideeffects class java.lang.Class {
    public java.lang.String getName();
    public java.lang.String getSimpleName();
    public java.lang.String getCanonicalName();
}

# ===============================================================================
# NATIVE CODE PROTECTION
# ===============================================================================

# If you have native libraries, protect them too
-keepclasseswithmembernames class * {
    native <methods>;
}

# ===============================================================================
# RESOURCE OBFUSCATION
# ===============================================================================

# Obfuscate resource names (makes it harder to understand app structure)
-adaptresourcefilenames **.xml,**.png,**.jpg,**.jpeg,**.gif,**.webp,**.json
-adaptresourcefilecontents **.xml,**.json

# ===============================================================================
# FINAL SECURITY MEASURES
# ===============================================================================

# Remove all metadata that could help reverse engineering
-keepattributes !SourceFile,!LineNumberTable,!LocalVariableTable,!LocalVariableTypeTable

# Maximum aggressive optimization
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*,!method/inlining/*
-optimizationpasses 7

# Additional security: Remove unused classes more aggressively
-dontshrink
-dontoptimize

# Revert the above for release builds - we want optimization
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
-optimizationpasses 7

# ===============================================================================
# ANTI-TAMPERING MEASURES
# ===============================================================================

# These rules make it harder to modify and repackage your app
-printmapping mapping.txt
-printseeds seeds.txt
-printusage usage.txt

# ===============================================================================
# ADDITIONAL SECURITY ENHANCEMENTS
# ===============================================================================

# Protect against dynamic analysis and runtime manipulation
-assumenosideeffects class java.lang.reflect.Method {
    public java.lang.Object invoke(...);
}

-assumenosideeffects class java.lang.reflect.Field {
    public java.lang.Object get(...);
    public void set(...);
}

# Remove BuildConfig debug information
-assumenosideeffects class **.BuildConfig {
    public static final boolean DEBUG;
    public static final String BUILD_TYPE;
    public static final String FLAVOR;
}

# Protect API keys and sensitive strings (add your specific classes here)
-keep class com.mdsadrulhasan.gogolaundry.utils.Constants {
    !public static final java.lang.String API_*;
    !public static final java.lang.String KEY_*;
    !public static final java.lang.String SECRET_*;
}

# Advanced control flow obfuscation
-optimizations !method/marking/private,!method/marking/static,!method/marking/final
-optimizations !method/removal/parameter,!method/propagation/parameter
-optimizations !field/removal/writeonly,!field/marking/private
-optimizations !class/marking/final,!class/unboxing/enum

# Remove parameter names (makes reverse engineering harder)
-keepattributes !MethodParameters

# Protect against hooking frameworks (Xposed, Frida)
-assumenosideeffects class java.lang.System {
    public static java.lang.String getProperty(...);
    public static java.util.Properties getProperties();
}

# Remove file system access traces
-assumenosideeffects class java.io.File {
    public boolean exists();
    public boolean isDirectory();
    public boolean isFile();
    public java.lang.String[] list();
}

# Protect against package inspection
-assumenosideeffects class android.content.pm.PackageManager {
    public android.content.pm.PackageInfo getPackageInfo(...);
    public java.util.List getInstalledPackages(...);
    public java.util.List getInstalledApplications(...);
}

# Additional string encryption for sensitive data
-adaptclassstrings com.mdsadrulhasan.gogolaundry.api.**
-adaptclassstrings com.mdsadrulhasan.gogolaundry.utils.**
-adaptclassstrings com.mdsadrulhasan.gogolaundry.config.**

# Protect against memory dumps
-assumenosideeffects class java.lang.Runtime {
    public void gc();
    public long freeMemory();
    public long totalMemory();
    public long maxMemory();
}

# Remove development and testing code
-assumenosideeffects class * {
    public void test*(...);
    public void debug*(...);
    public void log*(...);
}

# Protect WebView if used for payments
-keep class android.webkit.WebView { *; }
-keep class android.webkit.WebViewClient { *; }
-keep class android.webkit.WebChromeClient { *; }
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}

# Protect payment-related classes (adjust package names as needed)
-keep class com.mdsadrulhasan.gogolaundry.payment.** { *; }
-keep class com.mdsadrulhasan.gogolaundry.billing.** { *; }

# Additional protection for sensitive activities
-keep class com.mdsadrulhasan.gogolaundry.**.*Activity {
    !private <fields>;
    !private <methods>;
}

# Protect against dynamic loading attacks
-assumenosideeffects class java.lang.ClassLoader {
    public java.lang.Class loadClass(...);
    public java.net.URL getResource(...);
    public java.io.InputStream getResourceAsStream(...);
}

# Remove timezone and locale information that could be used for fingerprinting
-assumenosideeffects class java.util.TimeZone {
    public static java.util.TimeZone getDefault();
    public java.lang.String getID();
}

-assumenosideeffects class java.util.Locale {
    public static java.util.Locale getDefault();
    public java.lang.String getCountry();
    public java.lang.String getLanguage();
}

# ===============================================================================
# FINAL VERIFICATION RULES
# ===============================================================================

# Ensure critical security classes are properly obfuscated
-printconfiguration configuration.txt
-printmapping mapping.txt
-printseeds seeds.txt
-printusage usage.txt

# Verify obfuscation effectiveness
-whyareyoukeeping class com.mdsadrulhasan.gogolaundry.**

# ===============================================================================
# END OF ENHANCED CONFIGURATION
# ===============================================================================