/**
 * Login Page JavaScript
 * 
 * This file handles the login page functionality
 */

$(document).ready(function() {
    // Password Login Form Submission
    $('#passwordLoginForm').on('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const phone = $('#phone').val().trim();
        const password = $('#password').val();
        
        // Validate phone number
        if (!validatePhone(phone)) {
            showError('#passwordLoginError', 'Please enter a valid phone number');
            return;
        }
        
        // Validate password
        if (password.length < 1) {
            showError('#passwordLoginError', 'Please enter your password');
            return;
        }
        
        // Disable button and show loading state
        const loginBtn = $('#passwordLoginBtn');
        loginBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Logging in...');
        
        // Hide previous messages
        hideMessages();
        
        // Send login request
        $.ajax({
            url: 'api/login.php',
            type: 'POST',
            dataType: 'json',
            data: {
                phone: phone,
                password: password
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showSuccess('#passwordLoginSuccess', response.message);
                    
                    // Redirect to dashboard
                    setTimeout(function() {
                        window.location.href = 'dashboard.php';
                    }, 1000);
                } else {
                    // Show error message
                    showError('#passwordLoginError', response.message);
                    loginBtn.prop('disabled', false).text('Login');
                }
            },
            error: function(xhr, status, error) {
                // Parse error response if possible
                try {
                    const response = JSON.parse(xhr.responseText);
                    showError('#passwordLoginError', response.message || 'An error occurred. Please try again.');
                } catch (e) {
                    showError('#passwordLoginError', 'An error occurred. Please try again.');
                }
                
                loginBtn.prop('disabled', false).text('Login');
            }
        });
    });
    
    // Send OTP Button Click
    $('#sendOtpBtn').on('click', function() {
        const phone = $('#otpPhone').val().trim();
        
        // Validate phone number
        if (!validatePhone(phone)) {
            showError('#otpLoginError', 'Please enter a valid phone number');
            return;
        }
        
        // Disable button and show loading state
        const sendOtpBtn = $(this);
        sendOtpBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...');
        
        // Hide previous messages
        hideMessages();
        
        // Send OTP request
        $.ajax({
            url: 'api/send_otp.php',
            type: 'POST',
            dataType: 'json',
            data: {
                phone: phone,
                purpose: 'login'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showSuccess('#otpLoginSuccess', response.message);
                    
                    // Show OTP input section
                    $('.otp-section').removeClass('d-none');
                    
                    // Start OTP timer
                    startOtpTimer('#otpTimer', response.data.expires_in || 600, function() {
                        // Timer expired callback
                        sendOtpBtn.prop('disabled', false).text('Resend OTP');
                    });
                    
                    // Update button text
                    sendOtpBtn.prop('disabled', true).text('OTP Sent');
                } else {
                    // Show error message
                    showError('#otpLoginError', response.message);
                    sendOtpBtn.prop('disabled', false).text('Send OTP');
                }
            },
            error: function(xhr, status, error) {
                // Parse error response if possible
                try {
                    const response = JSON.parse(xhr.responseText);
                    showError('#otpLoginError', response.message || 'An error occurred. Please try again.');
                } catch (e) {
                    showError('#otpLoginError', 'An error occurred. Please try again.');
                }
                
                sendOtpBtn.prop('disabled', false).text('Send OTP');
            }
        });
    });
    
    // OTP Login Form Submission
    $('#otpLoginForm').on('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const phone = $('#otpPhone').val().trim();
        const otp = $('#otp').val().trim();
        
        // Validate phone number
        if (!validatePhone(phone)) {
            showError('#otpLoginError', 'Please enter a valid phone number');
            return;
        }
        
        // Validate OTP
        if (otp.length !== 6 || !/^\d+$/.test(otp)) {
            showError('#otpLoginError', 'Please enter a valid 6-digit OTP');
            return;
        }
        
        // Disable button and show loading state
        const verifyBtn = $('#verifyOtpBtn');
        verifyBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verifying...');
        
        // Hide previous messages
        hideMessages();
        
        // Send verify OTP request
        $.ajax({
            url: 'api/verify_otp.php',
            type: 'POST',
            dataType: 'json',
            data: {
                phone: phone,
                otp: otp,
                purpose: 'login'
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    showSuccess('#otpLoginSuccess', response.message);
                    
                    // Redirect to dashboard
                    setTimeout(function() {
                        window.location.href = 'dashboard.php';
                    }, 1000);
                } else {
                    // Show error message
                    showError('#otpLoginError', response.message);
                    verifyBtn.prop('disabled', false).text('Verify & Login');
                }
            },
            error: function(xhr, status, error) {
                // Parse error response if possible
                try {
                    const response = JSON.parse(xhr.responseText);
                    showError('#otpLoginError', response.message || 'An error occurred. Please try again.');
                } catch (e) {
                    showError('#otpLoginError', 'An error occurred. Please try again.');
                }
                
                verifyBtn.prop('disabled', false).text('Verify & Login');
            }
        });
    });
    
    // Helper Functions
    
    // Validate phone number
    function validatePhone(phone) {
        // Bangladesh phone number format: 01XXXXXXXXX
        const phoneRegex = /^(?:\+?880|0)1[3-9]\d{8}$/;
        return phoneRegex.test(phone);
    }
    
    // Show error message
    function showError(selector, message) {
        $(selector).removeClass('d-none').text(message);
    }
    
    // Show success message
    function showSuccess(selector, message) {
        $(selector).removeClass('d-none').text(message);
    }
    
    // Hide all messages
    function hideMessages() {
        $('#passwordLoginError, #passwordLoginSuccess, #otpLoginError, #otpLoginSuccess').addClass('d-none');
    }
    
    // Start OTP timer
    function startOtpTimer(selector, duration, callback) {
        let timer = duration;
        const timerElement = $(selector);
        
        const interval = setInterval(function() {
            const minutes = Math.floor(timer / 60);
            const seconds = timer % 60;
            
            timerElement.text(minutes + ':' + (seconds < 10 ? '0' : '') + seconds);
            
            if (--timer < 0) {
                clearInterval(interval);
                if (typeof callback === 'function') {
                    callback();
                }
            }
        }, 1000);
    }
});
