<?php
/**
 * Edit Admin
 *
 * This page allows super admins to edit admin users
 */

// Include authentication middleware
require_once 'auth.php';

// Check if user is super admin
if ($adminData['role'] !== 'super_admin') {
    $_SESSION['error_message'] = 'You do not have permission to access this page.';
    header('Location: index.php');
    exit;
}

// Include required files
require_once '../includes/AdminManager.php';

// Initialize admin manager
$adminManager = new AdminManager($pdo);

// Get admin ID from query string
$adminId = isset($_GET['id']) && is_numeric($_GET['id']) ? (int)$_GET['id'] : 0;

// Get admin data
$admin = $adminManager->getAdminById($adminId);

// If admin not found, redirect to admins page
if (!$admin) {
    $_SESSION['error_message'] = 'Admin not found.';
    header('Location: admins.php');
    exit;
}

// Handle form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Get form data
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $fullName = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
        $role = isset($_POST['role']) ? trim($_POST['role']) : 'admin';
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        $password = isset($_POST['password']) ? trim($_POST['password']) : '';
        $confirmPassword = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
        
        // Validate form data
        if (empty($email)) {
            $error = 'Email is required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Invalid email format.';
        } elseif (empty($fullName)) {
            $error = 'Full name is required.';
        } elseif (!in_array($role, ['admin', 'super_admin'])) {
            $error = 'Invalid role selected.';
        } elseif (!empty($password) && strlen($password) < 8) {
            $error = 'Password must be at least 8 characters long.';
        } elseif (!empty($password) && $password !== $confirmPassword) {
            $error = 'Passwords do not match.';
        } else {
            // Check if email already exists (excluding current admin)
            if ($email !== $admin['email'] && $adminManager->adminExistsByEmail($email)) {
                $error = 'Email already exists. Please use a different email address.';
            } else {
                // Update admin
                $result = $adminManager->updateAdmin(
                    $adminId,
                    $email,
                    $fullName,
                    $role,
                    $isActive,
                    $password // Will only update password if not empty
                );
                
                if ($result) {
                    // Log action
                    $adminManager->logAdminAction(
                        $adminData['id'],
                        'admin_update',
                        'Updated admin ID: ' . $adminId . ' (' . $admin['username'] . ')',
                        getClientIp()
                    );
                    
                    $success = 'Admin user updated successfully.';
                    
                    // Refresh admin data
                    $admin = $adminManager->getAdminById($adminId);
                } else {
                    $error = 'Failed to update admin user. Please try again.';
                }
            }
        }
    }
}

// Page title and breadcrumbs
$pageTitle = 'Edit Admin: ' . $admin['username'];
$breadcrumbs = [
    'Administrators' => 'admins.php',
    'Edit Admin' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Edit Admin: <?php echo htmlspecialchars($admin['username']); ?></h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="admins.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Admins
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i> <?php echo $success; ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header bg-light">
        <h5 class="mb-0">Admin User Information</h5>
    </div>
    <div class="card-body">
        <form action="edit_admin.php?id=<?php echo $adminId; ?>" method="post">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($admin['username']); ?>" disabled>
                    <div class="form-text">Username cannot be changed.</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($admin['full_name']); ?>" required>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                    <select class="form-select" id="role" name="role" required <?php echo $adminId === $adminData['id'] ? 'disabled' : ''; ?>>
                        <option value="admin" <?php echo $admin['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                        <option value="super_admin" <?php echo $admin['role'] === 'super_admin' ? 'selected' : ''; ?>>Super Admin</option>
                    </select>
                    <?php if ($adminId === $adminData['id']): ?>
                        <div class="form-text text-warning">You cannot change your own role.</div>
                        <input type="hidden" name="role" value="<?php echo $admin['role']; ?>">
                    <?php else: ?>
                        <div class="form-text">Super admins can manage other admin users.</div>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="form-check form-switch mt-4">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $admin['is_active'] ? 'checked' : ''; ?> <?php echo $adminId === $adminData['id'] ? 'disabled' : ''; ?>>
                        <label class="form-check-label" for="is_active">Active Account</label>
                    </div>
                    <?php if ($adminId === $adminData['id']): ?>
                        <div class="form-text text-warning">You cannot deactivate your own account.</div>
                        <input type="hidden" name="is_active" value="1">
                    <?php else: ?>
                        <div class="form-text">Inactive accounts cannot log in.</div>
                    <?php endif; ?>
                </div>
            </div>
            
            <hr class="my-4">
            
            <h5 class="mb-3">Change Password (Optional)</h5>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="password" class="form-label">New Password</label>
                    <input type="password" class="form-control" id="password" name="password">
                    <div class="form-text">Leave blank to keep current password. New password must be at least 8 characters long.</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                </div>
            </div>
            
            <div class="text-end mt-4">
                <a href="admins.php" class="btn btn-secondary me-2">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i> Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password validation
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    
    confirmPasswordInput.addEventListener('input', function() {
        if (passwordInput.value !== confirmPasswordInput.value) {
            confirmPasswordInput.setCustomValidity('Passwords do not match');
        } else {
            confirmPasswordInput.setCustomValidity('');
        }
    });
    
    passwordInput.addEventListener('input', function() {
        if (passwordInput.value.length > 0 && passwordInput.value.length < 8) {
            passwordInput.setCustomValidity('Password must be at least 8 characters long');
        } else {
            passwordInput.setCustomValidity('');
            
            if (confirmPasswordInput.value) {
                if (passwordInput.value !== confirmPasswordInput.value) {
                    confirmPasswordInput.setCustomValidity('Passwords do not match');
                } else {
                    confirmPasswordInput.setCustomValidity('');
                }
            }
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
