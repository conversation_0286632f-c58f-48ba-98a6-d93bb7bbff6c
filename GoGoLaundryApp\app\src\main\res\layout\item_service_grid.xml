<?xml version="1.0" encoding="utf-8"?>
<!-- Modern Material Design Service Grid Item -->
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/card_stroke_light"
    app:strokeWidth="0.5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Service Icon Container -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/icon_container"
            android:layout_width="56dp"
            android:layout_height="56dp"
            app:cardBackgroundColor="@color/primary_light"
            app:cardCornerRadius="28dp"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/service_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:contentDescription="@string/service_icon"
                android:scaleType="centerInside"
                app:tint="@color/primary"
                tools:src="@drawable/ic_washing_machine" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Service Name -->
        <TextView
            android:id="@+id/service_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:ellipsize="end"
            android:gravity="center"
            android:lineSpacingExtra="2dp"
            android:maxLines="2"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/icon_container"
            tools:text="Dry Cleaning" />

        <!-- Service Price (Optional) -->
        <TextView
            android:id="@+id/service_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
            android:textColor="@color/text_secondary"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/service_name"
            tools:text="From ৳50"
            tools:visibility="visible" />

        <!-- Service Description (Optional) -->
        <TextView
            android:id="@+id/service_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
            android:textColor="@color/text_tertiary"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/service_price"
            tools:text="Professional cleaning"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>