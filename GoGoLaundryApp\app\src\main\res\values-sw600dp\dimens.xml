<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Margin and padding for tablets -->
    <dimen name="margin_extra_small">6dp</dimen>
    <dimen name="margin_small">12dp</dimen>
    <dimen name="margin_medium">24dp</dimen>
    <dimen name="margin_large">32dp</dimen>
    <dimen name="margin_extra_large">48dp</dimen>
    <dimen name="margin_huge">64dp</dimen>

    <!-- Text sizes for tablets -->
    <dimen name="text_size_micro">14sp</dimen>
    <dimen name="text_size_small">16sp</dimen>
    <dimen name="text_size_medium">18sp</dimen>
    <dimen name="text_size_large">20sp</dimen>
    <dimen name="text_size_extra_large">24sp</dimen>
    <dimen name="text_size_huge">28sp</dimen>
    <dimen name="text_size_title">26sp</dimen>
    <dimen name="text_size_headline">32sp</dimen>

    <!-- Component specific for tablets -->
    <dimen name="button_height">64dp</dimen>
    <dimen name="button_corner_radius">10dp</dimen>
    <dimen name="card_corner_radius">10dp</dimen>
    <dimen name="avatar_size">56dp</dimen>

    <!-- Elevation for tablets -->
    <dimen name="elevation_card_small">1.5dp</dimen>

    <!-- Orders grid specific -->
    <integer name="orders_grid_columns">3</integer>

    <!-- Service card responsive dimensions for tablets -->
    <dimen name="service_card_min_height">200dp</dimen>
    <dimen name="service_card_max_height">240dp</dimen>
    <dimen name="service_icon_container_size">90dp</dimen>
    <dimen name="service_icon_size">54dp</dimen>
    <dimen name="service_card_corner_radius">18dp</dimen>
    <dimen name="service_card_elevation">6dp</dimen>
    <dimen name="grid_spacing">12dp</dimen>

    <!-- Compact service card dimensions for tablets -->
    <dimen name="compact_service_image_height">140dp</dimen>
    <dimen name="compact_service_card_padding">16dp</dimen>
    <dimen name="compact_service_image_margin">20dp</dimen>
</resources>
