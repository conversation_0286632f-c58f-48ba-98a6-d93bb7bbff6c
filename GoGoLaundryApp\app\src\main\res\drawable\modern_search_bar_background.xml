<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow effect for depth -->
    <item android:top="2dp" android:left="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/search_shadow" />
            <corners android:radius="28dp" />
        </shape>
    </item>
    
    <!-- Main glass background with gradient -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/search_background_primary"
                android:endColor="@color/search_background_secondary"
                android:angle="135" />
            <corners android:radius="26dp" />
        </shape>
    </item>
    
    <!-- Primary border for glass effect -->
    <item android:bottom="2dp" android:right="2dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="1.5dp" 
                android:color="@color/search_border_primary" />
            <corners android:radius="26dp" />
        </shape>
    </item>
    
    <!-- Inner highlight for premium look -->
    <item android:top="1dp" android:left="1dp" android:right="3dp" android:bottom="3dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.8dp" 
                android:color="@color/search_highlight" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    
    <!-- Subtle inner glow -->
    <item android:top="2dp" android:left="2dp" android:right="4dp" android:bottom="4dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/search_background_secondary" />
            <corners android:radius="24dp" />
        </shape>
    </item>
    
</layer-list>
