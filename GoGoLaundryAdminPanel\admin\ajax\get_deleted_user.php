<?php
/**
 * Get Deleted User AJAX Handler
 *
 * This file handles AJAX requests to get deleted user details
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/UserManager.php';
require_once '../../includes/AdminManager.php';

// Set content type
header('Content-Type: application/json');

// Check if admin is logged in
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Initialize managers
$userManager = new UserManager($pdo);
$adminManager = new AdminManager($pdo);

// Verify CSRF token
if (!isset($_GET['csrf_token']) || !$adminManager->verifyCsrfToken($_GET['csrf_token'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request'
    ]);
    exit;
}

// Get user ID
$userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

if ($userId <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid user ID'
    ]);
    exit;
}

// Get deleted user
$user = $userManager->getDeletedUserById($userId);

if (!$user) {
    echo json_encode([
        'success' => false,
        'message' => 'User not found'
    ]);
    exit;
}

// Log action
$adminManager->logAdminAction(
    $_SESSION['admin_id'],
    'view_deleted_user',
    'Viewed deleted user details for user ID: ' . $userId,
    getClientIp()
);

// Return user data
echo json_encode([
    'success' => true,
    'message' => 'User details retrieved successfully',
    'data' => $user
]);
