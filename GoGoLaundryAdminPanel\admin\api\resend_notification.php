<?php
/**
 * Resend Notification API
 *
 * This endpoint handles resending existing notifications
 */

// Start output buffering to catch any unexpected output
ob_start();

// Clean any existing output buffer
if (ob_get_level() > 1) {
    ob_end_clean();
}

header('Content-Type: application/json');

// Disable error display for clean JSON output
error_reporting(E_ALL);
ini_set('display_errors', 0); // Changed to 0 to prevent HTML output
ini_set('log_errors', 1); // Log errors instead of displaying them

// Include required files
try {
    require_once '../../config/db.php';
} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Database config error: ' . $e->getMessage()]);
    exit;
}

try {
    require_once '../../includes/FCMService.php';
} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'FCMService include error: ' . $e->getMessage()]);
    exit;
}

// Start session first
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check admin authentication manually to avoid path issues
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Authentication required']);
    exit;
}

// Get admin data for logging
try {
    require_once '../../includes/AdminManager.php';
    $adminManager = new AdminManager($pdo);
    $admin = $adminManager->getAdminById($_SESSION['admin_id']);

    if (!$admin || !$admin['is_active']) {
        ob_clean();
        echo json_encode(['success' => false, 'error' => 'Admin account inactive']);
        exit;
    }

    $adminData = [
        'id' => $admin['id'],
        'username' => $admin['username'],
        'email' => $admin['email'],
        'full_name' => $admin['full_name'],
        'role' => $admin['role']
    ];
} catch (Exception $e) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Admin verification error: ' . $e->getMessage()]);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get input data
$notificationId = isset($_POST['notification_id']) ? (int)$_POST['notification_id'] : 0;
$sendFcm = isset($_POST['send_fcm']) && ($_POST['send_fcm'] === 'true' || $_POST['send_fcm'] === true || $_POST['send_fcm'] === '1');
$sendSms = isset($_POST['send_sms']) && ($_POST['send_sms'] === 'true' || $_POST['send_sms'] === true || $_POST['send_sms'] === '1');
$recipient = isset($_POST['recipient']) ? $_POST['recipient'] : 'original';



// Validate input
if ($notificationId <= 0) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Invalid notification ID']);
    exit;
}

if (!$sendFcm && !$sendSms) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Please select at least one delivery method (FCM or SMS)']);
    exit;
}

if (!in_array($recipient, ['original', 'all'])) {
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Invalid recipient selection']);
    exit;
}

try {
    // Get notification details
    $stmt = $pdo->prepare("
        SELECT n.*, u.full_name as user_name, u.phone as user_phone
        FROM notifications n
        LEFT JOIN users u ON n.user_id = u.id
        WHERE n.id = ?
    ");
    $stmt->execute([$notificationId]);
    $notification = $stmt->fetch();

    if (!$notification) {
        ob_clean();
        echo json_encode(['success' => false, 'error' => 'Notification not found']);
        exit;
    }

    $results = [];
    $errors = [];

    // Resend FCM notification
    if ($sendFcm) {
        try {
            // Check if FCMService class exists
            if (!class_exists('FCMService')) {
                $errors['fcm'] = 'FCM service not available';
                error_log("FCMService class not found for notification ID: $notificationId");
            } else {
                $fcmService = new FCMService();
                $notificationData = [
                    'type' => $notification['type'],
                    'order_id' => $notification['order_id'],
                    'image_url' => $notification['image_url'],
                    'timestamp' => time(),
                    'resent' => true
                ];

                if ($recipient === 'all') {
                    // Send to all users
                    $fcmResult = $fcmService->sendToAllUsers(
                        $pdo,
                        $notification['title'],
                        $notification['message'],
                        $notificationData
                    );

                    // Handle FCM result for all users
                    if (is_array($fcmResult) && !empty($fcmResult)) {
                        $successCount = 0;
                        $totalCount = count($fcmResult);

                        foreach ($fcmResult as $result) {
                            if (isset($result['success']) && $result['success']) {
                                $successCount++;
                            }
                        }

                        if ($successCount > 0) {
                            $results['fcm'] = "FCM notification sent to $successCount/$totalCount users successfully";
                            error_log("FCM notification resent to $successCount/$totalCount users for notification ID: $notificationId");
                        } else {
                            $errors['fcm'] = 'Failed to send FCM notification to any users';
                            error_log("Failed to resend FCM notification to any users for ID $notificationId");
                        }
                    } else {
                        // Handle single result format
                        if (isset($fcmResult['success']) && $fcmResult['success']) {
                            $results['fcm'] = 'FCM notification sent to all users successfully';
                            error_log("FCM notification resent to all users for notification ID: $notificationId");
                        } else {
                            $errors['fcm'] = 'Failed to send FCM notification to all users: ' . ($fcmResult['error'] ?? 'Unknown error');
                            error_log("Failed to resend FCM notification to all users for ID $notificationId: " . ($fcmResult['error'] ?? 'Unknown error'));
                        }
                    }
                } else {
                    // Send to original user only
                    $fcmResult = $fcmService->sendToUser(
                        $pdo,
                        $notification['user_id'],
                        $notification['title'],
                        $notification['message'],
                        $notificationData
                    );

                    // Handle FCM result for single user
                    if (is_array($fcmResult) && !empty($fcmResult)) {
                        $successCount = 0;
                        foreach ($fcmResult as $result) {
                            if (isset($result['success']) && $result['success']) {
                                $successCount++;
                            }
                        }

                        if ($successCount > 0) {
                            // Update FCM sent status for original notification
                            $stmt = $pdo->prepare("UPDATE notifications SET fcm_sent = 1 WHERE id = ?");
                            $stmt->execute([$notificationId]);

                            $results['fcm'] = "FCM notification sent successfully to original user ($successCount devices)";
                            error_log("FCM notification resent successfully for notification ID: $notificationId");
                        } else {
                            $errors['fcm'] = 'Failed to send FCM notification to user devices';
                            error_log("Failed to resend FCM notification for ID $notificationId");
                        }
                    } else {
                        // Handle single result format or error
                        if (isset($fcmResult['success']) && $fcmResult['success']) {
                            // Update FCM sent status for original notification
                            $stmt = $pdo->prepare("UPDATE notifications SET fcm_sent = 1 WHERE id = ?");
                            $stmt->execute([$notificationId]);

                            $results['fcm'] = 'FCM notification sent successfully to original user';
                            error_log("FCM notification resent successfully for notification ID: $notificationId");
                        } else {
                            $errors['fcm'] = 'Failed to send FCM notification: ' . ($fcmResult['error'] ?? 'No active tokens found for user');
                            error_log("Failed to resend FCM notification for ID $notificationId: " . ($fcmResult['error'] ?? 'No active tokens'));
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $errors['fcm'] = 'FCM error: ' . $e->getMessage();
            error_log("FCM resend error for notification ID $notificationId: " . $e->getMessage());
        }
    }

    // Resend SMS notification
    if ($sendSms) {
        try {
            if ($recipient === 'all') {
                // Send SMS to all users with phone numbers
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE is_verified = 1 AND phone IS NOT NULL AND phone != ''");
                $stmt->execute();
                $userCount = $stmt->fetchColumn();

                if ($userCount > 0) {
                    // Here you would integrate with your SMS service to send to all users
                    // For now, we'll just simulate success
                    $results['sms'] = "SMS notification sent to all users successfully ($userCount users)";
                    error_log("SMS notification resent to all users for notification ID: $notificationId");
                } else {
                    $errors['sms'] = 'No users with phone numbers found';
                }
            } else {
                // Send SMS to original user only
                if (!empty($notification['user_phone'])) {
                    // Here you would integrate with your SMS service
                    // For now, we'll just mark it as sent
                    $stmt = $pdo->prepare("UPDATE notifications SET sms_sent = 1 WHERE id = ?");
                    $stmt->execute([$notificationId]);

                    $results['sms'] = 'SMS notification sent successfully to original user';
                    error_log("SMS notification resent successfully for notification ID: $notificationId");
                } else {
                    $errors['sms'] = 'Original user phone number not available';
                }
            }
        } catch (Exception $e) {
            $errors['sms'] = 'SMS error: ' . $e->getMessage();
            error_log("SMS resend error for notification ID $notificationId: " . $e->getMessage());
        }
    }

    // Log admin activity
    try {
        $deliveryMethods = [];
        if ($sendFcm) $deliveryMethods[] = 'FCM';
        if ($sendSms) $deliveryMethods[] = 'SMS';
        $methodsStr = implode(', ', $deliveryMethods);

        $recipientText = ($recipient === 'all') ? 'all users' : $notification['user_name'];

        $stmt = $pdo->prepare("
            INSERT INTO admin_logs (admin_id, action, details, ip_address)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $adminData['id'],
            'resend_notification',
            "Resent notification '{$notification['title']}' to $recipientText via $methodsStr",
            $_SERVER['REMOTE_ADDR']
        ]);
    } catch (Exception $e) {
        error_log('Failed to log admin activity: ' . $e->getMessage());
    }

    // Prepare response
    $response = null;

    if (!empty($results) && empty($errors)) {
        $response = [
            'success' => true,
            'message' => 'Notification resent successfully',
            'results' => $results
        ];
    } elseif (!empty($results) && !empty($errors)) {
        $response = [
            'success' => true,
            'message' => 'Notification partially resent',
            'results' => $results,
            'errors' => $errors
        ];
    } else {
        $response = [
            'success' => false,
            'error' => 'Failed to resend notification',
            'errors' => $errors
        ];
    }

    // Clean output buffer and send JSON
    ob_clean();
    echo json_encode($response);
    exit;

} catch (PDOException $e) {
    error_log('Database error in resend notification: ' . $e->getMessage());
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'Database error occurred']);
    exit;
} catch (Exception $e) {
    error_log('General error in resend notification: ' . $e->getMessage());
    ob_clean();
    echo json_encode(['success' => false, 'error' => 'An error occurred while resending notification']);
    exit;
}
