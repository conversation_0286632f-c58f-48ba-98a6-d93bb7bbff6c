package com.mdsadrulhasan.gogolaundry.repository;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.GoGoLaundryApp;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.UserResponse;
import com.mdsadrulhasan.gogolaundry.database.dao.UserDao;
import com.mdsadrulhasan.gogolaundry.database.entity.UserEntity;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.utils.AppExecutors;
import com.mdsadrulhasan.gogolaundry.utils.Resource;

import java.util.Date;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Repository for user-related operations
 */
public class UserRepository {
    private static final String TAG = "UserRepository";
    
    private static UserRepository instance;
    
    private final UserDao userDao;
    private final ApiService apiService;
    private final AppExecutors executors;
    
    private UserRepository() {
        userDao = GoGoLaundryApp.getInstance().getDatabase().userDao();
        apiService = ApiClient.getApiService(GoGoLaundryApp.getInstance());
        executors = AppExecutors.getInstance();
    }
    
    /**
     * Get repository instance
     * 
     * @return Repository instance
     */
    public static synchronized UserRepository getInstance() {
        if (instance == null) {
            instance = new UserRepository();
        }
        return instance;
    }
    
    /**
     * Login user
     * 
     * @param phone Phone number
     * @param password Password
     * @return LiveData of login result
     */
    public LiveData<Resource<User>> login(String phone, String password) {
        MutableLiveData<Resource<User>> result = new MutableLiveData<>();
        result.setValue(Resource.loading(null));
        
        apiService.login(phone, password).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    UserResponse userResponse = response.body().getData();
                    if (userResponse != null && userResponse.getUser() != null) {
                        User user = userResponse.getUser();
                        
                        // Save user to database
                        saveUserToDatabase(user);
                        
                        result.setValue(Resource.success(user));
                    } else {
                        result.setValue(Resource.error("Invalid response from server", null));
                    }
                } else {
                    String errorMessage = "Login failed";
                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    }
                    result.setValue(Resource.error(errorMessage, null));
                }
            }
            
            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                result.setValue(Resource.error("Network error: " + t.getMessage(), null));
            }
        });
        
        return result;
    }
    
    /**
     * Register user
     * 
     * @param fullName Full name
     * @param phone Phone number
     * @param email Email
     * @param password Password
     * @param otp OTP code
     * @param address Address
     * @param divisionId Division ID
     * @param districtId District ID
     * @param upazillaId Upazilla ID
     * @param divisionName Division name
     * @param districtName District name
     * @param upazillaName Upazilla name
     * @return LiveData of registration result
     */
    public LiveData<Resource<User>> register(
            String fullName, String phone, String email, String password, String otp,
            String address, Integer divisionId, Integer districtId, Integer upazillaId,
            String divisionName, String districtName, String upazillaName) {
        
        MutableLiveData<Resource<User>> result = new MutableLiveData<>();
        result.setValue(Resource.loading(null));
        
        apiService.register(
                fullName, phone, email, password, otp, address,
                divisionId, districtId, upazillaId,
                divisionName, districtName, upazillaName
        ).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    UserResponse userResponse = response.body().getData();
                    if (userResponse != null && userResponse.getUser() != null) {
                        User user = userResponse.getUser();
                        
                        // Save user to database
                        saveUserToDatabase(user);
                        
                        result.setValue(Resource.success(user));
                    } else {
                        result.setValue(Resource.error("Invalid response from server", null));
                    }
                } else {
                    String errorMessage = "Registration failed";
                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    }
                    result.setValue(Resource.error(errorMessage, null));
                }
            }
            
            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                result.setValue(Resource.error("Network error: " + t.getMessage(), null));
            }
        });
        
        return result;
    }
    
    /**
     * Get user by ID
     * 
     * @param userId User ID
     * @return LiveData of user
     */
    public LiveData<UserEntity> getUserById(int userId) {
        return userDao.getUserByIdLive(userId);
    }
    
    /**
     * Save user to database
     * 
     * @param user User to save
     */
    private void saveUserToDatabase(User user) {
        executors.diskIO().execute(() -> {
            UserEntity userEntity = new UserEntity();
            userEntity.setId(user.getId());
            userEntity.setFullName(user.getFullName());
            userEntity.setPhone(user.getPhone());
            userEntity.setEmail(user.getEmail());
            userEntity.setAddress(user.getAddress());
            userEntity.setDivisionId(user.getDivisionId());
            userEntity.setDistrictId(user.getDistrictId());
            userEntity.setUpazillaId(user.getUpazillaId());
            userEntity.setVerified(user.isVerified());
            userEntity.setProfilePictureUrl(user.getProfilePictureUrl());
            userEntity.setDivisionName(user.getDivisionName());
            userEntity.setDistrictName(user.getDistrictName());
            userEntity.setUpazillaName(user.getUpazillaName());
            
            // Set timestamps
            userEntity.setCreatedAt(new Date());
            userEntity.setUpdatedAt(new Date());
            
            userDao.insert(userEntity);
        });
    }
}
