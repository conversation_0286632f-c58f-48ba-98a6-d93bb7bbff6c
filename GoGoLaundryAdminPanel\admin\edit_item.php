<?php
/**
 * Edit Item
 *
 * This page allows administrators to edit an existing laundry item
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/ServiceManager.php';
require_once '../includes/ItemManager.php';

// Initialize managers
$serviceManager = new ServiceManager($pdo);
$itemManager = new ItemManager($pdo);

// Check if item ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'Invalid item ID.';
    header('Location: items.php');
    exit;
}

$itemId = (int)$_GET['id'];

// Get item data
$item = $itemManager->getItemById($itemId);

// Check if item exists
if (!$item) {
    $_SESSION['error_message'] = 'Item not found.';
    header('Location: items.php');
    exit;
}

// Get all services for dropdown
$services = $serviceManager->getActiveServices();

// Initialize variables
$serviceId = $item['service_id'];
$name = $item['name'];
$bnName = $item['bn_name'];
$description = $item['description'];
$bnDescription = $item['bn_description'];
$price = $item['price'];
$imageUrl = $item['image_url'];
$isActive = $item['is_active'];
$inStock = $item['in_stock'];
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_item'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error_message'] = 'Invalid security token. Please try again.';
        header('Location: edit_item.php?id=' . $itemId);
        exit;
    }

    // Get form data
    $serviceId = isset($_POST['service_id']) ? (int)$_POST['service_id'] : 0;
    $name = trim($_POST['name'] ?? '');
    $bnName = trim($_POST['bn_name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $bnDescription = trim($_POST['bn_description'] ?? '');
    $price = trim($_POST['price'] ?? '');
    $imageUrl = trim($_POST['image_url'] ?? '');
    $isActive = isset($_POST['is_active']) && $_POST['is_active'] === '1';
    $inStock = isset($_POST['in_stock']) && $_POST['in_stock'] === '1';

    // Validate form data
    if (empty($serviceId)) {
        $errors['service_id'] = 'Service is required.';
    }

    if (empty($name)) {
        $errors['name'] = 'Item name is required.';
    } elseif (strlen($name) > 100) {
        $errors['name'] = 'Item name cannot exceed 100 characters.';
    }

    if (!empty($bnName) && strlen($bnName) > 100) {
        $errors['bn_name'] = 'Bengali name cannot exceed 100 characters.';
    }

    if (strlen($description) > 500) {
        $errors['description'] = 'Description cannot exceed 500 characters.';
    }

    if (empty($price)) {
        $errors['price'] = 'Price is required.';
    } elseif (!is_numeric($price) || $price < 0) {
        $errors['price'] = 'Price must be a valid positive number.';
    }

    if (!empty($imageUrl) && strlen($imageUrl) > 255) {
        $errors['image_url'] = 'Image URL cannot exceed 255 characters.';
    }

    // If no errors, update item
    if (empty($errors)) {
        $result = $itemManager->updateItem($itemId, $serviceId, $name, $description, $price, $isActive, $inStock, $bnName, $bnDescription, $imageUrl);

        if ($result) {
            $_SESSION['success_message'] = 'Item updated successfully.';
            header('Location: items.php');
            exit;
        } else {
            $_SESSION['error_message'] = 'Failed to update item. Please try again.';
        }
    }
}

// Set page title
$pageTitle = 'Edit Item';
?>

<?php include 'includes/header.php'; ?>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="items.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Items
                        </a>
                    </div>
                </div>

                <?php include 'includes/alerts.php'; ?>

                <!-- Edit Item Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Item Information</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="edit_item.php?id=<?php echo $itemId; ?>">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            
                            <div class="mb-3">
                                <label for="service_id" class="form-label">Service <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['service_id']) ? 'is-invalid' : ''; ?>" 
                                        id="service_id" name="service_id" required>
                                    <option value="">Select Service</option>
                                    <?php foreach ($services as $service): ?>
                                        <option value="<?php echo $service['id']; ?>" <?php echo $serviceId == $service['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($service['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['service_id'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['service_id']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Item Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['name']) ? 'is-invalid' : ''; ?>" 
                                       id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                                <?php if (isset($errors['name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['name']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bn_name" class="form-label">Bengali Name</label>
                                <input type="text" class="form-control <?php echo isset($errors['bn_name']) ? 'is-invalid' : ''; ?>" 
                                       id="bn_name" name="bn_name" value="<?php echo htmlspecialchars($bnName); ?>">
                                <?php if (isset($errors['bn_name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['bn_name']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>" 
                                          id="description" name="description" rows="3"><?php echo htmlspecialchars($description); ?></textarea>
                                <?php if (isset($errors['description'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bn_description" class="form-label">Bengali Description</label>
                                <textarea class="form-control <?php echo isset($errors['bn_description']) ? 'is-invalid' : ''; ?>" 
                                          id="bn_description" name="bn_description" rows="3"><?php echo htmlspecialchars($bnDescription); ?></textarea>
                                <?php if (isset($errors['bn_description'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['bn_description']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="price" class="form-label">Price (Tk) <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control <?php echo isset($errors['price']) ? 'is-invalid' : ''; ?>" 
                                       id="price" name="price" value="<?php echo htmlspecialchars($price); ?>" required>
                                <?php if (isset($errors['price'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['price']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="image_url" class="form-label">Image URL</label>
                                <input type="text" class="form-control <?php echo isset($errors['image_url']) ? 'is-invalid' : ''; ?>" 
                                       id="image_url" name="image_url" value="<?php echo htmlspecialchars($imageUrl); ?>">
                                <?php if (isset($errors['image_url'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['image_url']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                       <?php echo $isActive ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="in_stock" name="in_stock" value="1" 
                                       <?php echo $inStock ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="in_stock">In Stock</label>
                            </div>
                            
                            <button type="submit" name="update_item" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Item
                            </button>
                        </form>
                    </div>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>
