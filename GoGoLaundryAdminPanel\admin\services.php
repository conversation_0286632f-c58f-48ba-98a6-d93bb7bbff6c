<?php
/**
 * Services Management
 *
 * This page allows administrators to manage laundry services
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/ServiceManager.php';

// Initialize service manager
$serviceManager = new ServiceManager($pdo);

// Process delete request
if (isset($_POST['delete_service']) && isset($_POST['service_id']) && isset($_POST['csrf_token'])) {
    // Verify CSRF token
    if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error_message'] = 'Invalid security token. Please try again.';
        header('Location: services.php');
        exit;
    }

    $serviceId = (int)$_POST['service_id'];

    // Check if service is in use
    if ($serviceManager->isServiceInUse($serviceId)) {
        $_SESSION['error_message'] = 'This service cannot be deleted because it is associated with existing orders.';
    } else {
        // Delete service
        if ($serviceManager->deleteService($serviceId)) {
            $_SESSION['success_message'] = 'Service deleted successfully.';
        } else {
            $_SESSION['error_message'] = 'Failed to delete service. Please try again.';
        }
    }

    header('Location: services.php');
    exit;
}

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$perPageOptions = [5, 10, 25, 50, 100];
$perPage = isset($_GET['per_page']) && in_array((int)$_GET['per_page'], $perPageOptions) ? (int)$_GET['per_page'] : 5;

// Get services with pagination and filtering
$result = $serviceManager->getAllServices($page, $perPage, $search);
$services = $result['services'];
$pagination = $result['pagination'];

// Set page title
$pageTitle = 'Manage Services';
?>

<?php include 'includes/header.php'; ?>

<style>
/* Custom pagination styling to match the design */
.pagination .page-link {
    border-radius: 8px !important;
    margin: 0 3px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 8px 12px;
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.pagination .page-link:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
}

.pagination .page-item.disabled .page-link {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
}

/* Search form styling */
.bg-white.rounded-3.shadow-sm {
    border: 1px solid #e9ecef;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #545b62;
    border-color: #545b62;
}
</style>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="add_service.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Service
                        </a>
                    </div>
                </div>

                <?php include 'includes/alerts.php'; ?>

                <!-- Search Services -->
                <div class="bg-white rounded-3 shadow-sm p-3 mb-4">
                    <form method="get" action="services.php" class="row g-3 align-items-center">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="Search by name or description"
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" name="per_page" id="per_page" onchange="this.form.submit()">
                                <?php foreach ($perPageOptions as $option): ?>
                                    <option value="<?php echo $option; ?>" <?php echo $perPage == $option ? 'selected' : ''; ?>>
                                        <?php echo $option; ?> per page
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <?php if (!empty($search) || $perPage != 5): ?>
                                <a href="services.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Services List -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Services List</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($services)): ?>
                            <div class="alert alert-info">
                                No services found. <?php echo !empty($search) ? 'Try a different search term.' : ''; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="servicesTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($services as $service): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($service['id']); ?></td>
                                                <td><?php echo htmlspecialchars($service['name']); ?></td>
                                                <td><?php echo htmlspecialchars($service['description']); ?></td>
                                                <td>
                                                    <?php if ($service['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="edit_service.php?id=<?php echo $service['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#deleteModal<?php echo $service['id']; ?>">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </td>
                                            </tr>

                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $service['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            Are you sure you want to delete the service: <strong><?php echo htmlspecialchars($service['name']); ?></strong>?
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <form method="post" action="services.php">
                                                                <input type="hidden" name="service_id" value="<?php echo $service['id']; ?>">
                                                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                                <button type="submit" name="delete_service" class="btn btn-danger">Delete</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php include 'includes/pagination.php'; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>
