package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.card.MaterialCardView;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.OrderItemAdapter;
import com.mdsadrulhasan.gogolaundry.adapter.OrderStatusHistoryAdapter;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.model.OrderItem;
import com.mdsadrulhasan.gogolaundry.model.OrderStatusHistory;
import com.mdsadrulhasan.gogolaundry.utils.DialogUtils;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.OrderTrackingViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for tracking order status and details
 */
public class OrderTrackingFragment extends Fragment {
    private static final String TAG = "OrderTrackingFragment";
    private static final String ARG_TRACKING_NUMBER = "tracking_number";
    private static final String ARG_ORDER_NUMBER = "order_number";

    private OrderTrackingViewModel viewModel;
    private String trackingNumber;
    private String orderNumber;

    // UI components
    private ProgressBar progressBar;
    private LinearLayout contentLayout;
    private TextView errorText;
    private MaterialCardView orderDetailsCard;
    private MaterialCardView orderItemsCard;
    private MaterialCardView orderStatusCard;

    // Order details views
    private TextView orderNumberText;
    private TextView orderDateText;
    private TextView orderStatusText;
    private TextView orderTotalText;
    private TextView customerNameText;
    private TextView customerPhoneText;
    private TextView paymentMethodText;
    private TextView paymentStatusText;
    private TextView deliveryAddressText;

    // Order items views
    private RecyclerView orderItemsRecyclerView;
    private OrderItemAdapter orderItemAdapter;

    // Order status history views
    private RecyclerView statusHistoryRecyclerView;
    private OrderStatusHistoryAdapter statusHistoryAdapter;

    // Status progress views
    private View statusPlacedIndicator;
    private View statusConfirmedIndicator;
    private View statusPickupScheduledIndicator;
    private View statusPickedUpIndicator;
    private View statusProcessingIndicator;
    private View statusReadyIndicator;
    private View statusOutForDeliveryIndicator;
    private View statusDeliveredIndicator;
    private View statusCancelledIndicator;

    private View statusPlacedLine;
    private View statusConfirmedLine;
    private View statusPickupScheduledLine;
    private View statusPickedUpLine;
    private View statusProcessingLine;
    private View statusReadyLine;
    private View statusOutForDeliveryLine;

    /**
     * Create a new instance of OrderTrackingFragment
     *
     * @param trackingNumber Order tracking number
     * @param orderNumber Order number (fallback if tracking number is not available)
     * @return OrderTrackingFragment instance
     */
    public static OrderTrackingFragment newInstance(String trackingNumber, String orderNumber) {
        OrderTrackingFragment fragment = new OrderTrackingFragment();
        Bundle args = new Bundle();
        args.putString(ARG_TRACKING_NUMBER, trackingNumber);
        args.putString(ARG_ORDER_NUMBER, orderNumber);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            trackingNumber = getArguments().getString(ARG_TRACKING_NUMBER);
            orderNumber = getArguments().getString(ARG_ORDER_NUMBER);
        }

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(OrderTrackingViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_order_tracking, container, false);
        initViews(view);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Set up RecyclerViews
        setupRecyclerViews();

        // Load order data
        loadOrderData();

        // Observe order data
        observeOrderData();
    }

    /**
     * Initialize views
     *
     * @param view Root view
     */
    private void initViews(View view) {
        // Main layout components
        progressBar = view.findViewById(R.id.progress_bar);
        contentLayout = view.findViewById(R.id.content_layout);
        errorText = view.findViewById(R.id.error_text);
        orderDetailsCard = view.findViewById(R.id.order_details_card);
        orderItemsCard = view.findViewById(R.id.order_items_card);
        orderStatusCard = view.findViewById(R.id.order_status_card);

        // Order details views
        orderNumberText = view.findViewById(R.id.order_number);
        orderDateText = view.findViewById(R.id.order_date);
        orderStatusText = view.findViewById(R.id.order_status);
        orderTotalText = view.findViewById(R.id.order_total);
        customerNameText = view.findViewById(R.id.customer_name);
        customerPhoneText = view.findViewById(R.id.customer_phone);
        paymentMethodText = view.findViewById(R.id.payment_method);
        paymentStatusText = view.findViewById(R.id.payment_status);
        deliveryAddressText = view.findViewById(R.id.delivery_address);

        // Order items views
        orderItemsRecyclerView = view.findViewById(R.id.order_items_recycler_view);

        // Order status history views
        statusHistoryRecyclerView = view.findViewById(R.id.status_history_recycler_view);

        // Status progress views
        statusPlacedIndicator = view.findViewById(R.id.status_placed_indicator);
        statusConfirmedIndicator = view.findViewById(R.id.status_confirmed_indicator);
        statusPickupScheduledIndicator = view.findViewById(R.id.status_pickup_scheduled_indicator);
        statusPickedUpIndicator = view.findViewById(R.id.status_picked_up_indicator);
        statusProcessingIndicator = view.findViewById(R.id.status_processing_indicator);
        statusReadyIndicator = view.findViewById(R.id.status_ready_indicator);
        statusOutForDeliveryIndicator = view.findViewById(R.id.status_out_for_delivery_indicator);
        statusDeliveredIndicator = view.findViewById(R.id.status_delivered_indicator);
        statusCancelledIndicator = view.findViewById(R.id.status_cancelled_indicator);

        statusPlacedLine = view.findViewById(R.id.status_placed_line);
        statusConfirmedLine = view.findViewById(R.id.status_confirmed_line);
        statusPickupScheduledLine = view.findViewById(R.id.status_pickup_scheduled_line);
        statusPickedUpLine = view.findViewById(R.id.status_picked_up_line);
        statusProcessingLine = view.findViewById(R.id.status_processing_line);
        statusReadyLine = view.findViewById(R.id.status_ready_line);
        statusOutForDeliveryLine = view.findViewById(R.id.status_out_for_delivery_line);
    }

    /**
     * Set up RecyclerViews
     */
    private void setupRecyclerViews() {
        // Set up order items RecyclerView
        orderItemAdapter = new OrderItemAdapter(requireContext(), new ArrayList<>());
        orderItemsRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        orderItemsRecyclerView.setAdapter(orderItemAdapter);

        // Set up status history RecyclerView
        statusHistoryAdapter = new OrderStatusHistoryAdapter(requireContext(), new ArrayList<>());
        statusHistoryRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        statusHistoryRecyclerView.setAdapter(statusHistoryAdapter);
    }

    /**
     * Load order data
     */
    private void loadOrderData() {
        showLoading();

        // Use tracking number if available, otherwise use order number
        if (trackingNumber != null && !trackingNumber.isEmpty()) {
            viewModel.trackOrderByTrackingNumber(trackingNumber);
        } else if (orderNumber != null && !orderNumber.isEmpty()) {
            viewModel.trackOrderByOrderNumber(orderNumber);
        } else {
            showError("No tracking number or order number provided");
        }
    }

    /**
     * Observe order data
     */
    private void observeOrderData() {
        viewModel.getOrderData().observe(getViewLifecycleOwner(), resource -> {
            if (resource.isLoading()) {
                showLoading();
            } else if (resource.isSuccess()) {
                Order order = resource.getData();
                if (order != null) {
                    showContent();
                    updateOrderDetails(order);
                    updateOrderItems(order.getOrderItems());
                    updateStatusHistory(viewModel.getStatusHistory());
                    updateStatusProgress(order.getStatus());
                } else {
                    showError("Order data not available");
                }
            } else if (resource.isError()) {
                showError(resource.getMessage());
            }
        });
    }

    /**
     * Show loading state
     */
    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        contentLayout.setVisibility(View.GONE);
        errorText.setVisibility(View.GONE);
    }

    /**
     * Show content
     */
    private void showContent() {
        progressBar.setVisibility(View.GONE);
        contentLayout.setVisibility(View.VISIBLE);
        errorText.setVisibility(View.GONE);
    }

    /**
     * Show error
     *
     * @param message Error message
     */
    private void showError(String message) {
        progressBar.setVisibility(View.GONE);
        contentLayout.setVisibility(View.GONE);
        errorText.setVisibility(View.VISIBLE);
        errorText.setText(message);

        // Show error toast
        ToastUtils.showErrorToast(requireContext(), message);
    }

    /**
     * Update order details
     *
     * @param order Order data
     */
    private void updateOrderDetails(Order order) {
        // Set order details
        orderNumberText.setText(getString(R.string.order_id, order.getOrderNumber()));
        orderDateText.setText(getString(R.string.order_date, order.getCreatedAt()));
        orderStatusText.setText(getString(R.string.order_status, formatOrderStatus(order.getStatus())));
        orderTotalText.setText(getString(R.string.order_total, String.valueOf(order.getTotalAmount())));

        // Set customer details if available
        if (order.getCustomerName() != null && !order.getCustomerName().isEmpty()) {
            customerNameText.setText(order.getCustomerName());
            customerNameText.setVisibility(View.VISIBLE);
        } else {
            customerNameText.setVisibility(View.GONE);
        }

        if (order.getCustomerPhone() != null && !order.getCustomerPhone().isEmpty()) {
            customerPhoneText.setText(order.getCustomerPhone());
            customerPhoneText.setVisibility(View.VISIBLE);
        } else {
            customerPhoneText.setVisibility(View.GONE);
        }

        // Set payment details
        paymentMethodText.setText(getString(R.string.payment_method, formatPaymentMethod(order.getPaymentMethod())));
        paymentStatusText.setText(getString(R.string.payment_status, formatPaymentStatus(order.getPaymentStatus())));

        // Set delivery address if available
        if (order.getDeliveryAddress() != null && !order.getDeliveryAddress().isEmpty()) {
            deliveryAddressText.setText(order.getDeliveryAddress());
            deliveryAddressText.setVisibility(View.VISIBLE);
        } else {
            deliveryAddressText.setVisibility(View.GONE);
        }

        // Set status color
        setStatusColor(orderStatusText, order.getStatus());
    }

    /**
     * Update order items
     *
     * @param orderItems Order items
     */
    private void updateOrderItems(List<OrderItem> orderItems) {
        if (orderItems != null && !orderItems.isEmpty()) {
            orderItemAdapter.updateItems(orderItems);
            orderItemsCard.setVisibility(View.VISIBLE);
        } else {
            orderItemsCard.setVisibility(View.GONE);
        }
    }

    /**
     * Update status history
     *
     * @param statusHistory Status history
     */
    private void updateStatusHistory(List<OrderStatusHistory> statusHistory) {
        if (statusHistory != null && !statusHistory.isEmpty()) {
            statusHistoryAdapter.updateItems(statusHistory);
            orderStatusCard.setVisibility(View.VISIBLE);
        } else {
            orderStatusCard.setVisibility(View.GONE);
        }
    }

    /**
     * Update status progress indicators
     *
     * @param status Current order status
     */
    private void updateStatusProgress(String status) {
        // Reset all indicators to inactive state
        resetStatusIndicators();

        // Set active indicators based on current status
        switch (status.toLowerCase()) {
            case "placed":
                setStatusActive(statusPlacedIndicator, null);
                break;

            case "confirmed":
                setStatusActive(statusPlacedIndicator, statusPlacedLine);
                setStatusActive(statusConfirmedIndicator, null);
                break;

            case "pickup_scheduled":
                setStatusActive(statusPlacedIndicator, statusPlacedLine);
                setStatusActive(statusConfirmedIndicator, statusConfirmedLine);
                setStatusActive(statusPickupScheduledIndicator, null);
                break;

            case "picked_up":
                setStatusActive(statusPlacedIndicator, statusPlacedLine);
                setStatusActive(statusConfirmedIndicator, statusConfirmedLine);
                setStatusActive(statusPickupScheduledIndicator, statusPickupScheduledLine);
                setStatusActive(statusPickedUpIndicator, null);
                break;

            case "processing":
                setStatusActive(statusPlacedIndicator, statusPlacedLine);
                setStatusActive(statusConfirmedIndicator, statusConfirmedLine);
                setStatusActive(statusPickupScheduledIndicator, statusPickupScheduledLine);
                setStatusActive(statusPickedUpIndicator, statusPickedUpLine);
                setStatusActive(statusProcessingIndicator, null);
                break;

            case "ready_for_delivery":
                setStatusActive(statusPlacedIndicator, statusPlacedLine);
                setStatusActive(statusConfirmedIndicator, statusConfirmedLine);
                setStatusActive(statusPickupScheduledIndicator, statusPickupScheduledLine);
                setStatusActive(statusPickedUpIndicator, statusPickedUpLine);
                setStatusActive(statusProcessingIndicator, statusProcessingLine);
                setStatusActive(statusReadyIndicator, null);
                break;

            case "out_for_delivery":
                setStatusActive(statusPlacedIndicator, statusPlacedLine);
                setStatusActive(statusConfirmedIndicator, statusConfirmedLine);
                setStatusActive(statusPickupScheduledIndicator, statusPickupScheduledLine);
                setStatusActive(statusPickedUpIndicator, statusPickedUpLine);
                setStatusActive(statusProcessingIndicator, statusProcessingLine);
                setStatusActive(statusReadyIndicator, statusReadyLine);
                setStatusActive(statusOutForDeliveryIndicator, null);
                break;

            case "delivered":
                setStatusActive(statusPlacedIndicator, statusPlacedLine);
                setStatusActive(statusConfirmedIndicator, statusConfirmedLine);
                setStatusActive(statusPickupScheduledIndicator, statusPickupScheduledLine);
                setStatusActive(statusPickedUpIndicator, statusPickedUpLine);
                setStatusActive(statusProcessingIndicator, statusProcessingLine);
                setStatusActive(statusReadyIndicator, statusReadyLine);
                setStatusActive(statusOutForDeliveryIndicator, statusOutForDeliveryLine);
                setStatusActive(statusDeliveredIndicator, null);
                break;

            case "cancelled":
                // Show cancelled indicator
                statusCancelledIndicator.setVisibility(View.VISIBLE);
                break;
        }
    }

    /**
     * Reset all status indicators to inactive state
     */
    private void resetStatusIndicators() {
        // Set all indicators to inactive color
        int inactiveColor = getResources().getColor(R.color.text_primary);

        statusPlacedIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusConfirmedIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusPickupScheduledIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusPickedUpIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusProcessingIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusReadyIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusOutForDeliveryIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusDeliveredIndicator.setBackgroundResource(R.drawable.status_indicator_inactive);
        statusCancelledIndicator.setVisibility(View.GONE);

        // Set all lines to inactive color
        statusPlacedLine.setBackgroundColor(inactiveColor);
        statusConfirmedLine.setBackgroundColor(inactiveColor);
        statusPickupScheduledLine.setBackgroundColor(inactiveColor);
        statusPickedUpLine.setBackgroundColor(inactiveColor);
        statusProcessingLine.setBackgroundColor(inactiveColor);
        statusReadyLine.setBackgroundColor(inactiveColor);
        statusOutForDeliveryLine.setBackgroundColor(inactiveColor);
    }

    /**
     * Set status indicator to active state
     *
     * @param indicator Status indicator view
     * @param line Line view (can be null)
     */
    private void setStatusActive(View indicator, View line) {
        // Set indicator to active
        indicator.setBackgroundResource(R.drawable.status_indicator_active);

        // Set line to active if provided
        if (line != null) {
            line.setBackgroundColor(getResources().getColor(R.color.primary));
        }
    }

    /**
     * Format order status for display
     *
     * @param status Order status
     * @return Formatted status
     */
    private String formatOrderStatus(String status) {
        if (status == null || status.isEmpty()) {
            return "Unknown";
        }

        // Replace underscores with spaces and capitalize each word
        String[] words = status.split("_");
        StringBuilder formattedStatus = new StringBuilder();

        for (String word : words) {
            if (word.length() > 0) {
                formattedStatus.append(Character.toUpperCase(word.charAt(0)))
                        .append(word.substring(1).toLowerCase())
                        .append(" ");
            }
        }

        return formattedStatus.toString().trim();
    }

    /**
     * Format payment method for display
     *
     * @param paymentMethod Payment method
     * @return Formatted payment method
     */
    private String formatPaymentMethod(String paymentMethod) {
        if (paymentMethod == null || paymentMethod.isEmpty()) {
            Log.w("OrderTrackingFragment", "Payment method is null or empty");
            return "Unknown";
        }

        // Log the original payment method for debugging
        Log.d("OrderTrackingFragment", "Formatting payment method: " + paymentMethod);

        switch (paymentMethod.toLowerCase().trim()) {
            case "cash":
            case "cash on delivery":
                return "Cash on Delivery";
            case "card":
            case "credit/debit card":
                return "Credit/Debit Card";
            case "online":
                return "Online Payment";
            case "bkash":
                return "bKash";
            case "nagad":
                return "Nagad";
            case "rocket":
                return "Rocket";
            case "mobile_banking":
                return "Mobile Banking";
            default:
                // If the API already converted it to a display name, return as-is
                // This handles cases where the API returns "Cash on Delivery" directly
                Log.d("OrderTrackingFragment", "Using payment method as-is: " + paymentMethod);
                return paymentMethod;
        }
    }

    /**
     * Format payment status for display
     *
     * @param paymentStatus Payment status
     * @return Formatted payment status
     */
    private String formatPaymentStatus(String paymentStatus) {
        if (paymentStatus == null || paymentStatus.isEmpty()) {
            return "Unknown";
        }

        switch (paymentStatus.toLowerCase()) {
            case "pending":
                return "Pending";
            case "paid":
                return "Paid";
            case "failed":
                return "Failed";
            case "refunded":
                return "Refunded";
            default:
                return paymentStatus;
        }
    }

    /**
     * Set status text color based on order status
     *
     * @param statusTextView Status TextView
     * @param status Order status
     */
    private void setStatusColor(TextView statusTextView, String status) {
        int colorResId;

        switch (status.toLowerCase()) {
            case "placed":
            case "confirmed":
                colorResId = R.color.info;
                break;
            case "pickup_scheduled":
            case "picked_up":
            case "processing":
                colorResId = R.color.warning;
                break;
            case "ready_for_delivery":
            case "out_for_delivery":
                colorResId = R.color.primary;
                break;
            case "delivered":
                colorResId = R.color.success;
                break;
            case "cancelled":
                colorResId = R.color.error;
                break;
            default:
                colorResId = R.color.text_secondary;
                break;
        }

        statusTextView.setTextColor(getResources().getColor(colorResId));
    }
}
