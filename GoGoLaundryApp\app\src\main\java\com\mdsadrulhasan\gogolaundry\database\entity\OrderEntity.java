package com.mdsadrulhasan.gogolaundry.database.entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;

import java.util.Date;
import java.util.List;

/**
 * Order entity for Room database
 */
@Entity(tableName = "orders")
public class OrderEntity {

    @PrimaryKey
    private int id;

    @ColumnInfo(name = "order_number")
    private String orderNumber;

    @ColumnInfo(name = "tracking_number")
    private String trackingNumber;

    @ColumnInfo(name = "user_id")
    private int userId;

    @ColumnInfo(name = "delivery_personnel_id")
    private Integer deliveryPersonnelId;

    @ColumnInfo(name = "promo_code_id")
    private Integer promoCodeId;

    private double subtotal;
    private double discount;

    @ColumnInfo(name = "delivery_fee")
    private double deliveryFee;

    private double total;

    @ColumnInfo(name = "payment_method")
    private String paymentMethod;

    @ColumnInfo(name = "payment_status")
    private String paymentStatus;

    private String status;

    @ColumnInfo(name = "pickup_address")
    private String pickupAddress;

    @ColumnInfo(name = "pickup_division_id")
    private Integer pickupDivisionId;

    @ColumnInfo(name = "pickup_district_id")
    private Integer pickupDistrictId;

    @ColumnInfo(name = "pickup_upazilla_id")
    private Integer pickupUpazillaId;

    @ColumnInfo(name = "pickup_date")
    private Date pickupDate;

    @ColumnInfo(name = "pickup_time_slot")
    private String pickupTimeSlot;

    @ColumnInfo(name = "delivery_address")
    private String deliveryAddress;

    @ColumnInfo(name = "delivery_division_id")
    private Integer deliveryDivisionId;

    @ColumnInfo(name = "delivery_district_id")
    private Integer deliveryDistrictId;

    @ColumnInfo(name = "delivery_upazilla_id")
    private Integer deliveryUpazillaId;

    @ColumnInfo(name = "delivery_date")
    private Date deliveryDate;

    @ColumnInfo(name = "delivery_time_slot")
    private String deliveryTimeSlot;

    private String notes;

    @ColumnInfo(name = "created_at")
    private Date createdAt;

    @ColumnInfo(name = "updated_at")
    private Date updatedAt;

    // Additional fields for UI display
    private String customerName;
    private String customerPhone;
    private String deliveryPersonName;
    private String deliveryPersonPhone;
    private String promoCode;

    // Transient field for order items (not stored in database)
    private transient List<OrderItemEntity> orderItems;

    // Getters and setters

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public Integer getDeliveryPersonnelId() {
        return deliveryPersonnelId;
    }

    public void setDeliveryPersonnelId(Integer deliveryPersonnelId) {
        this.deliveryPersonnelId = deliveryPersonnelId;
    }

    public Integer getPromoCodeId() {
        return promoCodeId;
    }

    public void setPromoCodeId(Integer promoCodeId) {
        this.promoCodeId = promoCodeId;
    }

    public double getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(double subtotal) {
        this.subtotal = subtotal;
    }

    public double getDiscount() {
        return discount;
    }

    public void setDiscount(double discount) {
        this.discount = discount;
    }

    public double getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(double deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public double getTotal() {
        return total;
    }

    public void setTotal(double total) {
        this.total = total;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    // Remaining getters and setters

    public String getPickupAddress() {
        return pickupAddress;
    }

    public void setPickupAddress(String pickupAddress) {
        this.pickupAddress = pickupAddress;
    }

    public Integer getPickupDivisionId() {
        return pickupDivisionId;
    }

    public void setPickupDivisionId(Integer pickupDivisionId) {
        this.pickupDivisionId = pickupDivisionId;
    }

    public Integer getPickupDistrictId() {
        return pickupDistrictId;
    }

    public void setPickupDistrictId(Integer pickupDistrictId) {
        this.pickupDistrictId = pickupDistrictId;
    }

    public Integer getPickupUpazillaId() {
        return pickupUpazillaId;
    }

    public void setPickupUpazillaId(Integer pickupUpazillaId) {
        this.pickupUpazillaId = pickupUpazillaId;
    }

    public Date getPickupDate() {
        return pickupDate;
    }

    public void setPickupDate(Date pickupDate) {
        this.pickupDate = pickupDate;
    }

    public String getPickupTimeSlot() {
        return pickupTimeSlot;
    }

    public void setPickupTimeSlot(String pickupTimeSlot) {
        this.pickupTimeSlot = pickupTimeSlot;
    }

    public String getDeliveryAddress() {
        return deliveryAddress;
    }

    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }

    public Integer getDeliveryDivisionId() {
        return deliveryDivisionId;
    }

    public void setDeliveryDivisionId(Integer deliveryDivisionId) {
        this.deliveryDivisionId = deliveryDivisionId;
    }

    public Integer getDeliveryDistrictId() {
        return deliveryDistrictId;
    }

    public void setDeliveryDistrictId(Integer deliveryDistrictId) {
        this.deliveryDistrictId = deliveryDistrictId;
    }

    public Integer getDeliveryUpazillaId() {
        return deliveryUpazillaId;
    }

    public void setDeliveryUpazillaId(Integer deliveryUpazillaId) {
        this.deliveryUpazillaId = deliveryUpazillaId;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getDeliveryTimeSlot() {
        return deliveryTimeSlot;
    }

    public void setDeliveryTimeSlot(String deliveryTimeSlot) {
        this.deliveryTimeSlot = deliveryTimeSlot;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getDeliveryPersonName() {
        return deliveryPersonName;
    }

    public void setDeliveryPersonName(String deliveryPersonName) {
        this.deliveryPersonName = deliveryPersonName;
    }

    public String getDeliveryPersonPhone() {
        return deliveryPersonPhone;
    }

    public void setDeliveryPersonPhone(String deliveryPersonPhone) {
        this.deliveryPersonPhone = deliveryPersonPhone;
    }

    public String getPromoCode() {
        return promoCode;
    }

    public void setPromoCode(String promoCode) {
        this.promoCode = promoCode;
    }

    /**
     * Get order items
     *
     * @return List of order items
     */
    @Ignore
    public List<OrderItemEntity> getOrderItems() {
        return orderItems;
    }

    /**
     * Set order items
     *
     * @param orderItems List of order items
     */
    public void setOrderItems(List<OrderItemEntity> orderItems) {
        this.orderItems = orderItems;
    }
}
