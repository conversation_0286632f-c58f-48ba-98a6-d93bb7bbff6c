<?php
/**
 * Verify OTP API Endpoint
 *
 * This endpoint handles OTP verification
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OtpManager.php';
require_once '../includes/UserManager.php';
require_once '../includes/SettingsManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Validate input
if (empty($inputData['phone'])) {
    jsonResponse(false, 'Phone number is required', [], 400);
}

if (empty($inputData['otp'])) {
    jsonResponse(false, 'OTP is required', [], 400);
}

$phone = sanitize($inputData['phone']);
$otp = sanitize($inputData['otp']);
$purpose = !empty($inputData['purpose']) ? sanitize($inputData['purpose']) : 'registration';

// Validate phone number
if (!validatePhone($phone)) {
    jsonResponse(false, 'Invalid phone number format', [], 400);
}

// Validate purpose
$validPurposes = ['registration', 'login', 'reset_password'];
if (!in_array($purpose, $validPurposes)) {
    jsonResponse(false, 'Invalid purpose', [], 400);
}

// Initialize managers
$otpManager = new OtpManager($pdo);
$userManager = new UserManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Check if OTP verification is enabled from database
$otpEnabled = $settingsManager->isOtpEnabled();

// Default to valid OTP
$isValid = true;

// Only verify OTP if OTP verification is enabled
if ($otpEnabled) {
    // Verify OTP
    $isValid = $otpManager->verifyOtp($phone, $otp, $purpose);

    if (!$isValid) {
        jsonResponse(false, 'Invalid or expired OTP', [], 400);
    }
}

// Handle different purposes
switch ($purpose) {
    case 'registration':
        // Mark user as verified if they exist
        if ($userManager->userExistsByPhone($phone)) {
            $userManager->verifyUser($phone);
            jsonResponse(true, 'Phone number verified successfully');
        } else {
            // For registration, we just verify the OTP
            jsonResponse(true, 'OTP verified successfully');
        }
        break;

    case 'login':
        // For login, we verify the OTP and return user data
        $user = $userManager->getUserByPhoneWithLocationNames($phone);

        if (!$user) {
            jsonResponse(false, 'User not found', [], 404);
        }

        if (!$user['is_verified']) {
            $userManager->verifyUser($phone);
        }

        // Remove sensitive data
        unset($user['password']);

        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['phone'] = $user['phone'];
        $_SESSION['is_logged_in'] = true;

        // Log session information for debugging
        error_log('Login successful via OTP for user: ' . $user['id'] . ' with phone: ' . $user['phone']);
        error_log('Session ID after OTP login: ' . session_id());
        error_log('Session data after OTP login: ' . json_encode($_SESSION));

        // Convert numeric values to appropriate types for Android
        $user['is_verified'] = (bool)$user['is_verified'];
        if (isset($user['division_id'])) $user['division_id'] = (int)$user['division_id'];
        if (isset($user['district_id'])) $user['district_id'] = (int)$user['district_id'];
        if (isset($user['upazilla_id'])) $user['upazilla_id'] = (int)$user['upazilla_id'];

        // The app is expecting a simple success message without user data
        // This is because it's using ApiResponse<Object> instead of ApiResponse<UserResponse>
        // We'll send a simple success message with a special field that will trigger auto-login
        jsonResponse(true, 'Login successful', [
            'auto_login' => true,
            'user_id' => (int)$user['id'], // Ensure this is an integer
            'phone' => $user['phone'],
            'full_name' => $user['full_name']
        ]);
        break;

    case 'reset_password':
        // For password reset, we just verify the OTP
        jsonResponse(true, 'OTP verified successfully. You can now reset your password.');
        break;

    default:
        jsonResponse(false, 'Invalid purpose', [], 400);
}
