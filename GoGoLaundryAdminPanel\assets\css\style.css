/**
 * Custom CSS Styles
 */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 30px;
}

.card-header {
    padding: 15px 20px;
}

.card-body {
    padding: 25px;
}

.form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
}

.btn {
    padding: 10px 20px;
    border-radius: 5px;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.alert {
    border-radius: 5px;
    padding: 12px 15px;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border-radius: 5px 5px 0 0;
    padding: 10px 15px;
}

.nav-tabs .nav-link.active {
    font-weight: 500;
}

/* Registration Steps */
.registration-step {
    margin-bottom: 20px;
}

/* OTP Timer */
#otpTimer, #regOtpTimer {
    font-weight: bold;
}

/* Dashboard */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-top: 15px;
    }
    
    .card-body {
        padding: 20px;
    }
}
