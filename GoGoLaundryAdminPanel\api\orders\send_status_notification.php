<?php
/**
 * Send Order Status Update Notification API
 *
 * This endpoint sends FCM notifications when order status is updated
 *
 * Required parameters:
 * - order_id: Order ID
 * - status: New order status
 * - user_id: User ID (for authentication)
 *
 * Optional parameters:
 * - notes: Additional notes about the status update
 * - updated_by: ID of the person who updated the status
 * - updated_by_type: Type of updater (admin, shop_owner, delivery_personnel)
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 * - data: Notification details
 */

// Suppress PHP warnings and notices to prevent HTML output in JSON response
error_reporting(E_ERROR | E_PARSE);
ini_set('display_errors', 0);

// Set proper headers for JSON response
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/FCMService.php';
require_once '../../includes/NotificationManager.php';
require_once '../../includes/OrderManager.php';
require_once '../../includes/UserManager.php';

// Set content type
header('Content-Type: application/json');

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Only POST method is allowed';
    echo json_encode($response);
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    // If JSON input is empty, try POST data
    if (empty($input)) {
        $input = $_POST;
    }

    // Validate required parameters
    $requiredFields = ['order_id', 'status', 'user_id'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            $response['message'] = "Missing required field: $field";
            echo json_encode($response);
            exit();
        }
    }

    $orderId = intval($input['order_id']);
    $newStatus = trim($input['status']);
    $userId = intval($input['user_id']);
    $notes = isset($input['notes']) ? trim($input['notes']) : '';
    $updatedBy = isset($input['updated_by']) ? intval($input['updated_by']) : null;
    $updatedByType = isset($input['updated_by_type']) ? trim($input['updated_by_type']) : 'admin';

    // Validate order status
    $validStatuses = [
        'placed', 'confirmed', 'pickup_scheduled', 'picked_up', 
        'processing', 'ready_for_delivery', 'out_for_delivery', 
        'delivered', 'cancelled'
    ];
    
    if (!in_array($newStatus, $validStatuses)) {
        $response['message'] = 'Invalid order status. Valid statuses: ' . implode(', ', $validStatuses);
        echo json_encode($response);
        exit();
    }

    // Initialize managers
    $orderManager = new OrderManager($pdo);
    $userManager = new UserManager($pdo);
    $fcmService = new FCMService();
    $notificationManager = new NotificationManager($pdo);

    // Get order details
    $order = $orderManager->getOrderById($orderId);
    if (!$order) {
        $response['message'] = 'Order not found';
        echo json_encode($response);
        exit();
    }

    // Verify that the user owns this order or is an admin
    if ($order['user_id'] != $userId) {
        // Check if the user is an admin
        $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE id = ?");
        $stmt->execute([$userId]);
        if ($stmt->rowCount() === 0) {
            $response['message'] = 'Unauthorized: You can only update your own orders';
            echo json_encode($response);
            exit();
        }
    }

    // Get user details
    $user = $userManager->getUserById($order['user_id']);
    if (!$user) {
        $response['message'] = 'User not found';
        echo json_encode($response);
        exit();
    }

    // Create status-specific notification messages
    $statusMessages = [
        'placed' => 'Your order has been placed successfully',
        'confirmed' => 'Your order has been confirmed and is being prepared',
        'pickup_scheduled' => 'Pickup has been scheduled for your order',
        'picked_up' => 'Your order has been picked up and is on its way to our facility',
        'processing' => 'Your order is currently being processed',
        'ready_for_delivery' => 'Your order is ready for delivery',
        'out_for_delivery' => 'Your order is out for delivery and will arrive soon',
        'delivered' => 'Your order has been delivered successfully',
        'cancelled' => 'Your order has been cancelled'
    ];

    $notificationTitle = "Order Status Updated";
    $notificationMessage = $statusMessages[$newStatus] ?? "Your order status has been updated to: $newStatus";
    
    if (!empty($notes)) {
        $notificationMessage .= ". Note: $notes";
    }

    // Prepare notification data
    $notificationData = [
        'type' => 'order_status',
        'order_id' => $orderId,
        'order_number' => $order['order_number'],
        'tracking_number' => $order['tracking_number'],
        'status' => $newStatus,
        'previous_status' => $order['status'],
        'notes' => $notes,
        'updated_by' => $updatedBy,
        'updated_by_type' => $updatedByType,
        'timestamp' => time()
    ];

    // Create notification record
    $notificationId = $notificationManager->createNotification(
        $order['user_id'], 
        $orderId, 
        $notificationTitle, 
        $notificationMessage, 
        'order_status', 
        true, 
        false
    );

    if (!$notificationId) {
        $response['message'] = 'Failed to create notification record';
        echo json_encode($response);
        exit();
    }

    // Send FCM notification to the user
    $fcmResult = $fcmService->sendToUser(
        $pdo, 
        $order['user_id'], 
        $notificationTitle, 
        $notificationMessage, 
        $notificationData
    );

    // Update FCM sent status based on result
    if ($fcmResult['success']) {
        $stmt = $pdo->prepare("UPDATE notifications SET fcm_sent = 1 WHERE id = ?");
        $stmt->execute([$notificationId]);
        
        error_log("FCM notification sent successfully for order status update: Order #{$order['order_number']}, Status: $newStatus");
    } else {
        $stmt = $pdo->prepare("UPDATE notifications SET fcm_sent = 0 WHERE id = ?");
        $stmt->execute([$notificationId]);
        
        error_log("Failed to send FCM notification for order status update: Order #{$order['order_number']}, Error: " . ($fcmResult['error'] ?? 'Unknown error'));
    }

    // Prepare response
    $response['success'] = true;
    $response['message'] = 'Order status notification sent successfully';
    $response['data'] = [
        'notification_id' => $notificationId,
        'order_id' => $orderId,
        'order_number' => $order['order_number'],
        'status' => $newStatus,
        'user_id' => $order['user_id'],
        'fcm_result' => $fcmResult,
        'sent_at' => date('Y-m-d H:i:s')
    ];

} catch (Exception $e) {
    error_log('Error in send_status_notification.php: ' . $e->getMessage());
    $response['message'] = 'An error occurred while sending the notification';
    $response['data'] = ['error' => $e->getMessage()];
}

// Return response
echo json_encode($response);
?>
