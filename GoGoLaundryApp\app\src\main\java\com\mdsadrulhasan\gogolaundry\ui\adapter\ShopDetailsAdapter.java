package com.mdsadrulhasan.gogolaundry.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced RecyclerView adapter for shop details with services and items
 * Replaces ViewPager2 for better performance as requested
 */
public class ShopDetailsAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_SERVICE_HEADER = 0;
    private static final int TYPE_SERVICE_ITEM = 1;
    private static final int TYPE_ITEMS_HEADER = 2;
    private static final int TYPE_SHOP_ITEM = 3;
    private static final int TYPE_EMPTY_STATE = 4;

    private final Context context;
    private final List<Object> items;
    private final OnItemClickListener listener;

    public interface OnItemClickListener {
        void onServiceClick(ShopServiceEntity service);
        void onItemClick(ShopItemEntity item);
        void onAddToCartClick(ShopItemEntity item);
    }

    public ShopDetailsAdapter(Context context, OnItemClickListener listener) {
        this.context = context;
        this.listener = listener;
        this.items = new ArrayList<>();
    }

    @Override
    public int getItemViewType(int position) {
        Object item = items.get(position);
        if (item instanceof String) {
            String header = (String) item;
            if (header.equals("SERVICES")) {
                return TYPE_SERVICE_HEADER;
            } else if (header.equals("ITEMS")) {
                return TYPE_ITEMS_HEADER;
            } else {
                return TYPE_EMPTY_STATE;
            }
        } else if (item instanceof ShopServiceEntity) {
            return TYPE_SERVICE_ITEM;
        } else if (item instanceof ShopItemEntity) {
            return TYPE_SHOP_ITEM;
        }
        return TYPE_EMPTY_STATE;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(context);
        
        switch (viewType) {
            case TYPE_SERVICE_HEADER:
            case TYPE_ITEMS_HEADER:
                View headerView = inflater.inflate(R.layout.item_shop_details_header, parent, false);
                return new HeaderViewHolder(headerView);
                
            case TYPE_SERVICE_ITEM:
                View serviceView = inflater.inflate(R.layout.item_shop_service_enhanced, parent, false);
                return new ServiceViewHolder(serviceView);
                
            case TYPE_SHOP_ITEM:
                View itemView = inflater.inflate(R.layout.item_shop_item_enhanced, parent, false);
                return new ItemViewHolder(itemView);
                
            default:
                View emptyView = inflater.inflate(R.layout.item_empty_state, parent, false);
                return new EmptyViewHolder(emptyView);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        Object item = items.get(position);
        
        switch (holder.getItemViewType()) {
            case TYPE_SERVICE_HEADER:
            case TYPE_ITEMS_HEADER:
                HeaderViewHolder headerHolder = (HeaderViewHolder) holder;
                String headerText = (String) item;
                headerHolder.bind(headerText);
                break;
                
            case TYPE_SERVICE_ITEM:
                ServiceViewHolder serviceHolder = (ServiceViewHolder) holder;
                ShopServiceEntity service = (ShopServiceEntity) item;
                serviceHolder.bind(service, listener);
                break;
                
            case TYPE_SHOP_ITEM:
                ItemViewHolder itemHolder = (ItemViewHolder) holder;
                ShopItemEntity shopItem = (ShopItemEntity) item;
                itemHolder.bind(shopItem, listener);
                break;
                
            case TYPE_EMPTY_STATE:
                EmptyViewHolder emptyHolder = (EmptyViewHolder) holder;
                String emptyText = (String) item;
                emptyHolder.bind(emptyText);
                break;
        }
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public void updateData(List<ShopServiceEntity> services, List<ShopItemEntity> shopItems) {
        items.clear();
        
        // Add services section
        if (services != null && !services.isEmpty()) {
            items.add("SERVICES");
            items.addAll(services);
        } else {
            items.add("SERVICES");
            items.add("No services available");
        }
        
        // Add items section
        if (shopItems != null && !shopItems.isEmpty()) {
            items.add("ITEMS");
            items.addAll(shopItems);
        } else {
            items.add("ITEMS");
            items.add("No items available");
        }
        
        notifyDataSetChanged();
    }

    // ViewHolder classes
    static class HeaderViewHolder extends RecyclerView.ViewHolder {
        private final TextView headerText;
        private final ImageView headerIcon;

        HeaderViewHolder(@NonNull View itemView) {
            super(itemView);
            headerText = itemView.findViewById(R.id.headerText);
            headerIcon = itemView.findViewById(R.id.headerIcon);
        }

        void bind(String header) {
            headerText.setText(header);
            if (header.equals("SERVICES")) {
                headerIcon.setImageResource(R.drawable.ic_services);
            } else if (header.equals("ITEMS")) {
                headerIcon.setImageResource(R.drawable.ic_items);
            }
        }
    }

    static class ServiceViewHolder extends RecyclerView.ViewHolder {
        private final MaterialCardView cardView;
        private final TextView serviceName;
        private final TextView serviceDescription;
        private final TextView servicePrice;
        private final ImageView serviceIcon;

        ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.serviceCardView);
            serviceName = itemView.findViewById(R.id.serviceName);
            serviceDescription = itemView.findViewById(R.id.serviceDescription);
            servicePrice = itemView.findViewById(R.id.servicePrice);
            serviceIcon = itemView.findViewById(R.id.serviceIcon);
        }

        void bind(ShopServiceEntity service, OnItemClickListener listener) {
            serviceName.setText(service.getServiceName());
            serviceDescription.setText(service.getDescription());
            
            if (service.getBasePrice() > 0) {
                servicePrice.setText(String.format("From ৳%.2f", service.getBasePrice()));
                servicePrice.setVisibility(View.VISIBLE);
            } else {
                servicePrice.setVisibility(View.GONE);
            }

            // Set service icon based on service type
            setServiceIcon(service.getServiceName());

            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onServiceClick(service);
                }
            });
        }

        private void setServiceIcon(String serviceName) {
            if (serviceName.toLowerCase().contains("wash")) {
                serviceIcon.setImageResource(R.drawable.ic_washing_machine);
            } else if (serviceName.toLowerCase().contains("dry")) {
                serviceIcon.setImageResource(R.drawable.ic_dry_cleaning);
            } else if (serviceName.toLowerCase().contains("iron")) {
                serviceIcon.setImageResource(R.drawable.ic_iron);
            } else {
                serviceIcon.setImageResource(R.drawable.ic_laundry_item);
            }
        }
    }

    static class ItemViewHolder extends RecyclerView.ViewHolder {
        private final MaterialCardView cardView;
        private final TextView itemName;
        private final TextView itemDescription;
        private final TextView itemPrice;
        private final TextView estimatedTime;
        private final MaterialButton addToCartButton;
        private final ImageView itemIcon;

        ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.itemCardView);
            itemName = itemView.findViewById(R.id.itemName);
            itemDescription = itemView.findViewById(R.id.itemDescription);
            itemPrice = itemView.findViewById(R.id.itemPrice);
            estimatedTime = itemView.findViewById(R.id.estimatedTime);
            addToCartButton = itemView.findViewById(R.id.addToCartButton);
            itemIcon = itemView.findViewById(R.id.itemIcon);
        }

        void bind(ShopItemEntity item, OnItemClickListener listener) {
            itemName.setText(item.getItemName());
            itemDescription.setText(item.getDescription());
            itemPrice.setText(String.format("৳%.2f", item.getPrice()));
            
            if (item.getEstimatedHours() > 0) {
                estimatedTime.setText(String.format("%d hours", item.getEstimatedHours()));
                estimatedTime.setVisibility(View.VISIBLE);
            } else {
                estimatedTime.setVisibility(View.GONE);
            }

            // Set item icon
            itemIcon.setImageResource(R.drawable.ic_laundry_item);

            cardView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onItemClick(item);
                }
            });

            addToCartButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onAddToCartClick(item);
                }
            });
        }
    }

    static class EmptyViewHolder extends RecyclerView.ViewHolder {
        private final TextView emptyText;
        private final ImageView emptyIcon;

        EmptyViewHolder(@NonNull View itemView) {
            super(itemView);
            emptyText = itemView.findViewById(R.id.emptyText);
            emptyIcon = itemView.findViewById(R.id.emptyIcon);
        }

        void bind(String text) {
            emptyText.setText(text);
            emptyIcon.setImageResource(R.drawable.ic_empty_box);
        }
    }
}
