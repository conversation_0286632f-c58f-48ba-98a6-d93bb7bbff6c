            </div> <!-- End Main Content Container -->
        </div> <!-- End Page Content -->
    </div> <!-- End Wrapper -->

    <!-- DataTables JS (jQuery and Bootstrap JS already loaded in header) -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

    <!-- Custom JS -->
    <script>
        $(document).ready(function() {
            console.log("Footer script loaded");

            // Create overlay div if it doesn't exist
            if ($('.sidebar-overlay').length === 0) {
                $('body').append('<div class="sidebar-overlay"></div>');
                console.log("Sidebar overlay created");
            }

            // Enhanced sidebar toggle with both click and touch events
            $('#sidebarCollapse').off().on('click touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log("Sidebar toggle clicked");

                $('.sidebar').toggleClass('active');
                $('.sidebar-overlay').toggleClass('active');

                // Prevent body scrolling when sidebar is open
                if ($('.sidebar').hasClass('active')) {
                    $('body').css('overflow', 'hidden');
                    console.log("Sidebar opened");
                } else {
                    $('body').css('overflow', 'auto');
                    console.log("Sidebar closed");
                }

                return false;
            });

            // Close sidebar when clicking or touching overlay
            $('.sidebar-overlay').off().on('click touchstart', function(e) {
                e.preventDefault();
                console.log("Overlay clicked");

                closeSidebar();
                return false;
            });

            // Mobile close button
            $('#closeSidebarMobile').off().on('click touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log("Mobile close button clicked");

                closeSidebar();
                return false;
            });

            // Function to close sidebar
            function closeSidebar() {
                $('.sidebar').removeClass('active');
                $('.sidebar-overlay').removeClass('active');
                $('body').css('overflow', 'auto');
                console.log("Sidebar closed via function");
            }

            // Make dropdown toggles work better on mobile
            $('.sidebar .dropdown-toggle').off().on('click touchstart', function(e) {
                // Only prevent default on mobile
                if (window.innerWidth <= 768) {
                    e.preventDefault();
                    e.stopPropagation();

                    const target = $(this).attr('href');
                    console.log("Sidebar dropdown toggle clicked: " + target);

                    // Toggle the collapse
                    $(target).collapse('toggle');

                    return false;
                }
            });

            // Add active class to parent when submenu is active
            $('.sidebar .collapse').on('show.bs.collapse', function() {
                $(this).parent('li').addClass('submenu-active');
            }).on('hide.bs.collapse', function() {
                $(this).parent('li').removeClass('submenu-active');
            });

            // Handle navbar dropdown
            $('.navbar .dropdown-toggle').off().on('click touchstart', function(e) {
                console.log("Navbar dropdown toggle clicked");
            });

            // Initialize DataTables
            if ($.fn.DataTable) {
                try {
                    $('.datatable').DataTable({
                        responsive: true,
                        language: {
                            search: "_INPUT_",
                            searchPlaceholder: "Search...",
                            lengthMenu: "Show _MENU_ entries",
                            info: "Showing _START_ to _END_ of _TOTAL_ entries",
                            infoEmpty: "Showing 0 to 0 of 0 entries",
                            infoFiltered: "(filtered from _MAX_ total entries)"
                        }
                    });
                    console.log("DataTables initialized");
                } catch (e) {
                    console.error("Error initializing DataTables:", e);
                }
            } else {
                console.warn("DataTables not available");
            }

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Confirm delete actions
            $('.confirm-delete').on('click', function(e) {
                if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                    e.preventDefault();
                }
            });
        });
    </script>

    <!-- FCM Initialization -->
    <script src="../assets/js/fcm-init.js"></script>

    <!-- Real-time Notifications -->
    <script src="../assets/js/realtime-notifications.js"></script>

    <?php if (isset($pageScripts)): ?>
        <?php foreach ($pageScripts as $script): ?>
            <script src="<?php echo $script; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>
