-- Add payment fields to orders table
ALTER TABLE `orders` 
ADD COLUMN `transaction_id` VARCHAR(100) NULL AFTER `payment_status`,
ADD COLUMN `payment_provider` VARCHAR(50) NULL AFTER `transaction_id`;

-- Update payment_method enum to include more options
ALTER TABLE `orders` 
MODIFY COLUMN `payment_method` ENUM('cash', 'card', 'bKash', 'Nagad', 'Rocket', 'mobile_banking') NOT NULL DEFAULT 'cash';

-- Create index on transaction_id for faster lookups
CREATE INDEX `idx_orders_transaction_id` ON `orders` (`transaction_id`);
