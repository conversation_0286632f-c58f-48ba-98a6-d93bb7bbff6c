# 🎯 Dynamic Promotional Dialog System

## 📋 Overview

The GoGoLaundry app now features a **dynamic promotional dialog system** that allows admins to create, manage, and control promotional dialogs from the admin panel. The dialog will only show when there's active promotional content available.

## ✨ Features

### 🔧 Admin Panel Features
- ✅ **Create promotional dialogs** with custom content
- ✅ **Upload images** or use image URLs
- ✅ **Set custom colors** for background, text, and buttons
- ✅ **Schedule dialogs** with start and end dates
- ✅ **Toggle active/inactive** status
- ✅ **Edit and delete** existing dialogs
- ✅ **Promo codes** support

### 📱 Mobile App Features
- ✅ **Dynamic content loading** from API
- ✅ **Automatic dialog display** on app launch (only if active dialog exists)
- ✅ **Custom styling** based on admin settings
- ✅ **Image loading** with Glide
- ✅ **Analytics tracking** (view and click events)

## 🚀 Setup Instructions

### 1. Database Setup
```bash
# Navigate to admin directory
cd GoGoLaundryApp/admin

# Run the setup script to create the database table
php setup_promo_dialogs.php
```

### 2. Admin Panel Access
1. Navigate to: `your-domain.com/GoGoLaundryApp/admin/promotional_dialogs.php`
2. Login with admin credentials
3. Create your first promotional dialog

### 3. Android App
The app will automatically:
- Check for active promotional dialogs on launch
- Display the dialog if one is available
- Hide the dialog if no active promotions exist

## 📊 Admin Panel Usage

### Creating a Promotional Dialog

1. **Click "Create New Promo"** button
2. **Fill in the form:**
   - **Title**: Main heading (e.g., "HOT PICKS LOW PRICES")
   - **Subtitle**: Secondary text (e.g., "Best Deals on Best Prices")
   - **Discount Text**: Offer details (e.g., "UP TO 60% OFF")
   - **Promo Code**: Optional code (e.g., "SAVE60")
   - **Button Text**: Action button text (e.g., "Shop Now")
   - **Image**: Upload file or provide URL
   - **Colors**: Customize background, text, and button colors
   - **Schedule**: Set start and end dates (optional)
   - **Status**: Active/Inactive toggle

3. **Click "Create Promo Dialog"**

### Managing Dialogs

- **✏️ Edit**: Click edit button to modify existing dialogs
- **⏸️ Pause/▶️ Activate**: Toggle dialog status
- **🗑️ Delete**: Remove dialogs permanently
- **📊 View**: See all dialogs in a table format

## 🔄 How It Works

### Dialog Display Logic
```
App Launch → API Call → Check Active Dialog → Show/Hide Dialog
```

1. **App launches** and MainActivity calls the API
2. **API checks** for active promotional dialogs
3. **If active dialog exists**: Show with dynamic content
4. **If no active dialog**: No dialog is displayed
5. **Analytics**: Track views and interactions

### API Endpoints

#### Get Active Dialog
```
GET /api/promotional_dialog.php
```

**Response:**
```json
{
  "success": true,
  "has_dialog": true,
  "dialog": {
    "id": 1,
    "title": "HOT PICKS\nLOW PRICES",
    "subtitle": "Best Deals on Best Prices",
    "discount_text": "UP TO\n60%\nOFF",
    "promo_code": "SAVE60",
    "button_text": "Shop Now",
    "image_url": "https://your-domain.com/uploads/promo/image.jpg",
    "background_color": "#6c757d",
    "text_color": "#ffffff",
    "button_color": "#ffd700",
    "is_active": true
  }
}
```

#### Log Interaction
```
POST /api/promotional_dialog.php
```

**Request:**
```json
{
  "action": "view",
  "dialog_id": 1,
  "user_id": 123
}
```

## 🎨 Customization

### Colors
- **Background Color**: Dialog background
- **Text Color**: All text elements
- **Button Color**: Action button background

### Images
- **Upload**: JPG, PNG, GIF (max 5MB)
- **URL**: Direct image links
- **Fallback**: Default placeholder if image fails

### Scheduling
- **Start Date**: When dialog becomes active
- **End Date**: When dialog expires
- **No Dates**: Always active (if status is active)

## 📱 Mobile App Integration

### PromoDialog Class
- Supports both static and dynamic content
- Automatically populates views with API data
- Handles image loading with Glide
- Applies custom colors and styling

### MainActivity Integration
- Checks API on app launch
- Only shows dialog if active content exists
- Logs analytics for tracking

## 🔧 Troubleshooting

### Dialog Not Showing
1. ✅ Check if dialog is marked as "Active" in admin panel
2. ✅ Verify start/end dates are correct
3. ✅ Check API endpoint is accessible
4. ✅ Review Android logs for API errors

### Image Not Loading
1. ✅ Verify image URL is accessible
2. ✅ Check image format (JPG/PNG/GIF only)
3. ✅ Ensure image size is under 5MB
4. ✅ Check network permissions in app

### Admin Panel Issues
1. ✅ Verify database table exists
2. ✅ Check file upload permissions
3. ✅ Ensure admin authentication is working
4. ✅ Review PHP error logs

## 📈 Analytics

The system tracks:
- **Dialog Views**: When users see the dialog
- **Dialog Clicks**: When users click the action button
- **User Data**: Associated with user IDs when available

## 🔮 Future Enhancements

- 📊 **Advanced Analytics Dashboard**
- 🎯 **User Targeting** (by location, preferences)
- 📅 **Recurring Campaigns**
- 🔔 **Push Notification Integration**
- 🌐 **Multi-language Support**

---

**🎉 Your promotional dialog system is now ready to boost user engagement and drive sales!**
