<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Shadow for depth -->
    <item android:left="2dp" android:top="2dp">
        <shape android:shape="rectangle">
            <solid android:color="#15000000" />
            <corners android:radius="14dp" />
        </shape>
    </item>

    <!-- Main clean background with high visibility -->
    <item android:right="2dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="#E6FFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>

    <!-- Border for definition -->
    <item android:right="2dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <stroke
                android:width="1.5dp"
                android:color="#FFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>

</layer-list>
