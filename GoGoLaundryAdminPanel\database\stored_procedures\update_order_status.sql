DELIMITER //

CREATE PROCEDURE update_order_status(
    IN p_order_id INT,
    IN p_status VARCHAR(50),
    IN p_notes TEXT,
    IN p_updated_by INT,
    IN p_updated_by_type VARCHAR(20),
    IN p_delivery_personnel_id INT
)
BEGIN
    DECLARE v_current_status VARCHAR(50);
    DECLARE v_user_id INT;
    DECLARE v_order_number VARCHAR(50);
    
    -- Get current status and user ID
    SELECT status, user_id, order_number INTO v_current_status, v_user_id, v_order_number
    FROM orders
    WHERE id = p_order_id;
    
    -- Update order status
    UPDATE orders
    SET status = p_status,
        delivery_personnel_id = p_delivery_personnel_id,
        updated_at = NOW()
    WHERE id = p_order_id;
    
    -- Add status history
    INSERT INTO order_status_history (
        order_id,
        status,
        notes,
        updated_by,
        updated_by_type
    ) VALUES (
        p_order_id,
        p_status,
        p_notes,
        p_updated_by,
        p_updated_by_type
    );
    
    -- If status is 'delivered', update payment status to 'paid' for cash orders
    IF p_status = 'delivered' THEN
        UPDATE orders
        SET payment_status = 'paid'
        WHERE id = p_order_id AND payment_method = 'cash' AND payment_status = 'pending';
    END IF;
    
    -- Create notification for user
    INSERT INTO notifications (
        user_id,
        order_id,
        title,
        message,
        type,
        fcm_sent,
        sms_sent
    ) VALUES (
        v_user_id,
        p_order_id,
        'Order Status Update',
        CONCAT('Your order #', v_order_number, ' has been updated to: ', 
               CASE 
                   WHEN p_status = 'placed' THEN 'Placed'
                   WHEN p_status = 'confirmed' THEN 'Confirmed'
                   WHEN p_status = 'pickup_scheduled' THEN 'Pickup Scheduled'
                   WHEN p_status = 'picked_up' THEN 'Picked Up'
                   WHEN p_status = 'processing' THEN 'Processing'
                   WHEN p_status = 'ready_for_delivery' THEN 'Ready for Delivery'
                   WHEN p_status = 'out_for_delivery' THEN 'Out for Delivery'
                   WHEN p_status = 'delivered' THEN 'Delivered'
                   WHEN p_status = 'cancelled' THEN 'Cancelled'
                   ELSE p_status
               END),
        'order_status',
        0,
        0
    );
    
    -- If delivery personnel is assigned, create notification for them
    IF p_delivery_personnel_id IS NOT NULL THEN
        INSERT INTO notifications (
            user_id,
            order_id,
            title,
            message,
            type,
            fcm_sent,
            sms_sent
        ) VALUES (
            p_delivery_personnel_id,
            p_order_id,
            'New Order Assignment',
            CONCAT('You have been assigned to order #', v_order_number),
            'order_assignment',
            0,
            0
        );
    END IF;
END //

DELIMITER ;
