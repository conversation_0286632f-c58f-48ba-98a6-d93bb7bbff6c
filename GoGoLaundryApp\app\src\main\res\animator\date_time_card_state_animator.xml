<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:valueTo="0.95"
                android:duration="150"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:valueTo="0.95"
                android:duration="150"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="alpha"
                android:valueTo="0.8"
                android:duration="150"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
    <!-- Default state -->
    <item>
        <set>
            <objectAnimator
                android:propertyName="scaleX"
                android:valueTo="1.0"
                android:duration="200"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="scaleY"
                android:valueTo="1.0"
                android:duration="200"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
            <objectAnimator
                android:propertyName="alpha"
                android:valueTo="1.0"
                android:duration="200"
                android:interpolator="@android:interpolator/fast_out_slow_in" />
        </set>
    </item>
    
</selector>
