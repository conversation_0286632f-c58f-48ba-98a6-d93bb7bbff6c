<?php
/**
 * Verify Account Status API Endpoint
 *
 * This endpoint checks if a user account exists, is verified, and has not been deleted
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/UserManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Validate input
if (empty($inputData['user_id'])) {
    jsonResponse(false, 'User ID is required', [], 400);
}

if (empty($inputData['phone'])) {
    jsonResponse(false, 'Phone number is required', [], 400);
}

$userId = (int)$inputData['user_id'];
$phone = sanitize($inputData['phone']);

// Format phone number
$phone = formatPhone($phone);

// Initialize user manager
$userManager = new UserManager($pdo);

// Check if user exists
$user = $userManager->getUserById($userId);

// Prepare response data
$responseData = [
    'exists' => false,
    'is_verified' => false,
    'is_deleted' => false
];

// Check if user exists
if (!$user) {
    // Check if user is in deleted_users table
    $stmt = $pdo->prepare("
        SELECT * FROM deleted_users
        WHERE user_id = ? OR phone = ?
        ORDER BY deleted_at DESC
        LIMIT 1
    ");
    $stmt->execute([$userId, $phone]);
    $deletedUser = $stmt->fetch();

    if ($deletedUser) {
        $responseData['exists'] = true;
        $responseData['is_deleted'] = true;
    }

    // Return response
    jsonResponse(true, 'Account status verified', $responseData);
}

// User exists
$responseData['exists'] = true;

// Check if phone matches
if ($user['phone'] !== $phone) {
    // Phone doesn't match, return error
    jsonResponse(false, 'Phone number does not match user ID', [], 400);
}

// Check if user is verified
$responseData['is_verified'] = (bool)$user['is_verified'];

// Return response
jsonResponse(true, 'Account status verified', $responseData);
