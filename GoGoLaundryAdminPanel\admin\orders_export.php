<?php
/**
 * Orders Export to Excel
 *
 * This page exports orders data to Excel format
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/OrderManager.php';

// Initialize order manager
$orderManager = new OrderManager($pdo);

// Get filter parameters from query string (same as orders.php)
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : '';
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : '';

// Build filters array
$filters = [];
if (!empty($search)) {
    $filters['search'] = $search;
}
if (!empty($status)) {
    $filters['status'] = $status;
}
if (!empty($startDate)) {
    $filters['start_date'] = $startDate;
}
if (!empty($endDate)) {
    $filters['end_date'] = $endDate;
}

// Get all orders matching the filters (no pagination for export)
$result = $orderManager->getOrdersByFilter($filters, 1, 10000); // Get up to 10,000 orders
$orders = $result['orders'];

// Helper functions
function formatStatus($status) {
    return ucwords(str_replace('_', ' ', $status));
}

function sanitizeForCSV($value) {
    // Remove any potential CSV injection characters
    $value = str_replace(['"', "'", '=', '+', '-', '@'], '', $value);
    return $value;
}

// Set headers for Excel download
$filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');

// Create output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8 (helps with Excel compatibility)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// CSV Headers
$headers = [
    'Order Number',
    'Tracking Number',
    'Customer Name',
    'Customer Phone',
    'Order Date',
    'Pickup Date',
    'Pickup Time',
    'Status',
    'Payment Method',
    'Subtotal (BDT)',
    'Discount (BDT)',
    'Delivery Fee (BDT)',
    'Total (BDT)',
    'Pickup Address',
    'Delivery Address',
    'Delivery Personnel',
    'Notes',
    'Created At',
    'Updated At'
];

// Write headers to CSV
fputcsv($output, $headers);

// Write data rows
foreach ($orders as $order) {
    $row = [
        sanitizeForCSV($order['order_number']),
        sanitizeForCSV($order['tracking_number']),
        sanitizeForCSV($order['customer_name']),
        sanitizeForCSV($order['customer_phone']),
        date('Y-m-d', strtotime($order['created_at'])),
        $order['pickup_date'] ? date('Y-m-d', strtotime($order['pickup_date'])) : '',
        sanitizeForCSV($order['pickup_time_slot']),
        formatStatus($order['status']),
        ucfirst(sanitizeForCSV($order['payment_method'])),
        number_format($order['subtotal'], 2, '.', ''),
        number_format($order['discount'], 2, '.', ''),
        number_format($order['delivery_fee'], 2, '.', ''),
        number_format($order['total'], 2, '.', ''),
        sanitizeForCSV($order['pickup_address']),
        sanitizeForCSV($order['delivery_address']),
        sanitizeForCSV($order['delivery_person_name'] ?? 'Not Assigned'),
        sanitizeForCSV($order['notes']),
        date('Y-m-d H:i:s', strtotime($order['created_at'])),
        $order['updated_at'] ? date('Y-m-d H:i:s', strtotime($order['updated_at'])) : ''
    ];
    
    fputcsv($output, $row);
}

// Log the export action
$adminManager->logAdminAction(
    $adminData['id'],
    'orders_export',
    'Exported ' . count($orders) . ' orders to Excel',
    getClientIp()
);

// Close output stream
fclose($output);
exit;
?>
