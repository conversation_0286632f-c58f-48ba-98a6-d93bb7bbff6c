<?php
/**
 * Add Sample Locations Script
 *
 * This script adds sample divisions, districts, and upazillas to the database
 */

// Include required files
require_once 'config/db.php';
require_once 'includes/functions.php';

// Check if divisions table exists and has data
$stmt = $pdo->query("SHOW TABLES LIKE 'divisions'");
$divisionsTableExists = $stmt->rowCount() > 0;

if (!$divisionsTableExists) {
    echo "Divisions table does not exist. Creating it...<br>";
    
    // Create divisions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `divisions` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(50) NOT NULL,
          `bn_name` varchar(50) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    
    echo "Divisions table created.<br>";
}

// Check if districts table exists
$stmt = $pdo->query("SHOW TABLES LIKE 'districts'");
$districtsTableExists = $stmt->rowCount() > 0;

if (!$districtsTableExists) {
    echo "Districts table does not exist. Creating it...<br>";
    
    // Create districts table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `districts` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `division_id` int(11) NOT NULL,
          `name` varchar(50) NOT NULL,
          `bn_name` varchar(50) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `division_id` (`division_id`),
          CONSTRAINT `districts_ibfk_1` FOREIGN KEY (`division_id`) REFERENCES `divisions` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    
    echo "Districts table created.<br>";
}

// Check if upazillas table exists
$stmt = $pdo->query("SHOW TABLES LIKE 'upazillas'");
$upazillasTableExists = $stmt->rowCount() > 0;

if (!$upazillasTableExists) {
    echo "Upazillas table does not exist. Creating it...<br>";
    
    // Create upazillas table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `upazillas` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `district_id` int(11) NOT NULL,
          `name` varchar(50) NOT NULL,
          `bn_name` varchar(50) DEFAULT NULL,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `district_id` (`district_id`),
          CONSTRAINT `upazillas_ibfk_1` FOREIGN KEY (`district_id`) REFERENCES `districts` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    
    echo "Upazillas table created.<br>";
}

// Check if there are any divisions
$stmt = $pdo->query("SELECT COUNT(*) FROM divisions");
$divisionCount = $stmt->fetchColumn();

if ($divisionCount == 0) {
    echo "No divisions found. Adding sample divisions...<br>";
    
    // Add sample divisions
    $divisions = [
        ['name' => 'Dhaka'],
        ['name' => 'Chittagong'],
        ['name' => 'Rajshahi'],
        ['name' => 'Khulna'],
        ['name' => 'Barisal'],
        ['name' => 'Sylhet'],
        ['name' => 'Rangpur'],
        ['name' => 'Mymensingh']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO divisions (name) VALUES (?)");
    
    foreach ($divisions as $division) {
        $stmt->execute([$division['name']]);
        echo "Added division: {$division['name']}<br>";
    }
}

// Check if there are any districts
$stmt = $pdo->query("SELECT COUNT(*) FROM districts");
$districtCount = $stmt->fetchColumn();

if ($districtCount == 0) {
    echo "No districts found. Adding sample districts...<br>";
    
    // Get division IDs
    $stmt = $pdo->query("SELECT id, name FROM divisions");
    $divisions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $divisionMap = [];
    foreach ($divisions as $division) {
        $divisionMap[$division['name']] = $division['id'];
    }
    
    // Add sample districts
    $districts = [
        ['division_name' => 'Dhaka', 'name' => 'Dhaka'],
        ['division_name' => 'Dhaka', 'name' => 'Gazipur'],
        ['division_name' => 'Dhaka', 'name' => 'Narayanganj'],
        ['division_name' => 'Chittagong', 'name' => 'Chittagong'],
        ['division_name' => 'Chittagong', 'name' => 'Cox\'s Bazar'],
        ['division_name' => 'Rajshahi', 'name' => 'Rajshahi'],
        ['division_name' => 'Khulna', 'name' => 'Khulna'],
        ['division_name' => 'Barisal', 'name' => 'Barisal'],
        ['division_name' => 'Sylhet', 'name' => 'Sylhet'],
        ['division_name' => 'Rangpur', 'name' => 'Rangpur'],
        ['division_name' => 'Mymensingh', 'name' => 'Mymensingh']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO districts (division_id, name) VALUES (?, ?)");
    
    foreach ($districts as $district) {
        if (isset($divisionMap[$district['division_name']])) {
            $divisionId = $divisionMap[$district['division_name']];
            
            $stmt->execute([$divisionId, $district['name']]);
            echo "Added district: {$district['name']} for division: {$district['division_name']}<br>";
        } else {
            echo "Warning: Division '{$district['division_name']}' not found for district '{$district['name']}'<br>";
        }
    }
}

// Check if there are any upazillas
$stmt = $pdo->query("SELECT COUNT(*) FROM upazillas");
$upazillaCount = $stmt->fetchColumn();

if ($upazillaCount == 0) {
    echo "No upazillas found. Adding sample upazillas...<br>";
    
    // Get district IDs
    $stmt = $pdo->query("SELECT id, name FROM districts");
    $districts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $districtMap = [];
    foreach ($districts as $district) {
        $districtMap[$district['name']] = $district['id'];
    }
    
    // Add sample upazillas
    $upazillas = [
        ['district_name' => 'Dhaka', 'name' => 'Mirpur'],
        ['district_name' => 'Dhaka', 'name' => 'Gulshan'],
        ['district_name' => 'Dhaka', 'name' => 'Dhanmondi'],
        ['district_name' => 'Dhaka', 'name' => 'Mohammadpur'],
        ['district_name' => 'Gazipur', 'name' => 'Gazipur Sadar'],
        ['district_name' => 'Gazipur', 'name' => 'Tongi'],
        ['district_name' => 'Narayanganj', 'name' => 'Narayanganj Sadar'],
        ['district_name' => 'Chittagong', 'name' => 'Chittagong Sadar'],
        ['district_name' => 'Cox\'s Bazar', 'name' => 'Cox\'s Bazar Sadar'],
        ['district_name' => 'Rajshahi', 'name' => 'Rajshahi Sadar'],
        ['district_name' => 'Khulna', 'name' => 'Khulna Sadar'],
        ['district_name' => 'Barisal', 'name' => 'Barisal Sadar'],
        ['district_name' => 'Sylhet', 'name' => 'Sylhet Sadar'],
        ['district_name' => 'Rangpur', 'name' => 'Rangpur Sadar'],
        ['district_name' => 'Mymensingh', 'name' => 'Mymensingh Sadar']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO upazillas (district_id, name) VALUES (?, ?)");
    
    foreach ($upazillas as $upazilla) {
        if (isset($districtMap[$upazilla['district_name']])) {
            $districtId = $districtMap[$upazilla['district_name']];
            
            $stmt->execute([$districtId, $upazilla['name']]);
            echo "Added upazilla: {$upazilla['name']} for district: {$upazilla['district_name']}<br>";
        } else {
            echo "Warning: District '{$upazilla['district_name']}' not found for upazilla '{$upazilla['name']}'<br>";
        }
    }
}

echo "<br>Sample location data check complete. <a href='add_test_order.php'>Go to Add Test Order page</a>";
