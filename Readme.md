# 🧼 GoGo Laundry – আপনার আধুনিক লন্ড্রি সলিউশন! 🧺📱

**A Complete Smart Laundry App Solution – Built for Modern Living**

> একটি ইন্টেলিজেন্ট, ফিচার-প্যাকড এবং ইউজার-ফ্রেন্ডলি লন্ড্রি সার্ভিস অ্যাপ, যা তৈরি করেছি Android (Java), PHP (REST API), MySQL, ও Firebase Cloud Messaging (FCM) ব্যবহার করে।

## Table of Contents
- [Overview](#overview)
- [Features](#features)
- [Project Plan & Roadmap](#project-plan--roadmap)
- [System Architecture](#system-architecture)
- [Order Tracking System](#order-tracking-system)
- [Tech Stack](#tech-stack)
- [Installation & Setup](#installation--setup)
- [Deployment](#deployment)
- [Target Users](#target-users)

## Overview

GoGo Laundry is a comprehensive laundry service application designed to streamline the process of ordering laundry services. The system consists of an Android mobile application for customers and a web-based admin panel for service providers.

এই অ্যাপটি ব্যবহারকারীদের লন্ড্রি সার্ভিস অর্ডারিং প্রসেসকে ডিজিটাল ও সহজ করেছে – শুধুমাত্র কিছু ক্লিকে!

## Features

### 🔥 User App Features
#### 🔐 Smart, Simple and Secure User Experience:

- ✅ ইমেইল ও পাসওয়ার্ড দিয়ে দ্রুত Sign Up / Login
- ✅ ক্যাটাগরি অনুযায়ী পরিষেবা নির্বাচন – যেমনঃ Washing, Ironing, Dry Cleaning
- ✅ অর্ডার প্লেস করার সময়: নাম, মোবাইল, ঠিকানা এবং ডেলিভারি টাইম নির্বাচন
- ✅ ডায়নামিক আইটেম সিলেকশন ও অটো মূল্য গণনা
- ✅ Promo Code ব্যবহারের মাধ্যমে ইনস্ট্যান্ট ডিসকাউন্ট
- ✅ অর্ডার হিস্টোরি ও লাইভ ট্র্যাকিং স্ট্যাটাস
- ✅ FCM ভিত্তিক রিয়েলটাইম নোটিফিকেশন – যেমন অর্ডার রিসিভড, প্রসেসিং, রেডি, ডেলিভার্ড

### 🛠️ Admin Panel Features (Web Dashboard):

- 💻 Fully Responsive Order Management Dashboard
- 🗂️ ক্যাটাগরি, আইটেম, প্রাইস এবং স্টক কন্ট্রোল, ছবিসহ CRUD অপারেশন
- 🎟️ Promo Code Management – ডিসকাউন্ট, এক্সপায়ার ডেট ও অ্যাকটিভেশন নিয়ন্ত্রণ
- 📢 Push Notification Panel – নির্দিষ্ট ইউজার/সকল ইউজারের জন্য কাস্টম বার্তা পাঠানো
- 📊 অর্ডার বিশ্লেষণ ও স্ট্যাটিসটিকস (Upcoming Feature)

### 🎨 Premium UI Design (User-Centered Design):

- ✨ Minimalist & Clean Interface – distraction-free ফ্লো
- 🎯 Material Design 3.0 Guidelines অনুসরণ করে ডিজাইনকৃত
- 🖼️ সার্ভিস ছবিসহ রিচ লিস্ট ভিউ, সুন্দর এনিমেশন ও ট্রানজিশন
- 📱 100% Responsive Layout – মোবাইলের যেকোনো স্ক্রিন সাইজে কাজ করে
- 🌙 Light & Dark Mode (Coming Soon)

## Project Plan & Roadmap

### Development Timeline

| Phase | Timeline | Status |
|-------|----------|--------|
| Phase 1: Core App Development | Q1 2023 | ✅ Completed |
| Phase 2: Admin Panel Enhancement | Q2 2023 | ✅ Completed |
| Phase 3: Advanced Tracking System | Q3 2023 | 🔄 In Progress |
| Phase 4: Analytics & Reporting | Q4 2023 | ⏳ Planned |
| Phase 5: Multi-vendor Support | Q1 2024 | 📅 Scheduled |

### Milestones

1. **MVP Launch** ✅
   - Basic order placement
   - Simple tracking
   - Admin management

2. **Enhanced User Experience** ✅
   - Improved UI/UX
   - Promo code system
   - Push notifications

3. **Advanced Tracking System** 🔄
   - Real-time order status updates
   - Delivery personnel tracking
   - Estimated time calculations

4. **Business Intelligence** ⏳
   - Customer analytics
   - Order pattern analysis
   - Revenue reporting

5. **Expansion Features** 📅
   - Multiple service providers
   - Geographical expansion
   - API for third-party integration

## System Architecture

### High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Android App    │◄────┤  PHP REST API   │◄────┤  Admin Panel    │
│  (Java)         │     │  (Slim)         │     │  (PHP/Bootstrap)│
│                 │     │                 │     │                 │
└────────┬────────┘     └────────┬────────┘     └─────────────────┘
         │                       │
         │                       │
         │               ┌───────▼───────┐      ┌─────────────────┐
         │               │               │      │                 │
         └──────────────►│  MySQL DB     │◄─────┤  FCM Server     │
                         │               │      │  (Notifications)│
                         └───────────────┘      │                 │
                                                └─────────────────┘
```

### Database Schema Overview

The database consists of the following main tables:

- **users**: Customer information and authentication
- **orders**: Order details and status
- **services**: Available laundry services
- **items**: Individual laundry items with pricing
- **order_items**: Many-to-many relationship between orders and items
- **promo_codes**: Discount codes and their rules
- **notifications**: Message history and delivery status

## Order Tracking System

### Order Status Workflow

1. **Order Placed** → Initial state when customer submits an order
2. **Order Confirmed** → Admin has reviewed and confirmed the order
3. **Pickup Scheduled** → Delivery personnel assigned for pickup
4. **Picked Up** → Items collected from customer
5. **Processing** → Items being cleaned at facility
6. **Ready for Delivery** → Cleaning complete, ready for return
7. **Out for Delivery** → Items en route to customer
8. **Delivered** → Order complete, items returned to customer
9. **Cancelled** → Order cancelled (can occur at various stages)

### Tracking Implementation

The tracking system uses a combination of:

- **Database Status Updates**: Central source of truth for order status
- **Push Notifications**: Real-time alerts to customers via FCM
- **SMS Notifications**: Optional text messages for critical status changes
- **In-App Tracking**: Visual representation of order progress
- **Admin Dashboard**: Comprehensive view of all orders and their statuses

### API Endpoints for Tracking

```
GET /api/orders/{id}/status         # Get current status
GET /api/orders/{id}/history        # Get status history
POST /api/orders/{id}/status/update # Update status (admin only)
GET /api/tracking/{tracking_number} # Public tracking endpoint
```

## Tech Stack

| Component | Technology Used | Description |
|-----------|----------------|-------------|
| Android App | Java (XML UI, Retrofit, SQLite) | Native Android application with local database for offline capability |
| Backend API | PHP (RESTful, Slim Framework) | Lightweight API framework with RESTful endpoints |
| Database | MySQL | Relational database for data persistence |
| Notifications | Firebase Cloud Messaging (FCM) | Real-time push notifications to mobile devices |
| Admin Dashboard | PHP + Bootstrap (HTML/CSS/JS) | Responsive web interface for service management |
| SMS Gateway | BulkSMSBD API | Integration for SMS notifications |
| Location Services | Google Maps API | For address selection and delivery tracking |

## Installation & Setup

### Prerequisites

- Android Studio 4.0+ (for Android app)
- PHP 7.4+ with Composer
- MySQL 5.7+
- Firebase account for FCM
- XAMPP/WAMP/LAMP stack for local development

### Android App Setup

1. Clone the repository
2. Open the `GoGoLaundryApp` folder in Android Studio
3. Update the `ApiClient.java` with your server URL
4. Configure Firebase and add `google-services.json`
5. Build and run the application

### Admin Panel Setup

1. Set up a web server with PHP and MySQL
2. Import the database schema from `database/schema.sql`
3. Configure `config/config.php` and `config/db.php`
4. Install dependencies: `composer install`
5. Access the admin panel at `http://your-server/GoGoLaundry/GoGoLaundryAdminPanel/admin/`

## Deployment

### Server Requirements

- Web server (Apache/Nginx)
- PHP 7.4+ with extensions: mysqli, json, curl, gd
- MySQL 5.7+
- SSL certificate for secure communication
- Minimum 2GB RAM, 1 CPU core
- 20GB storage (more for image uploads)

### Scaling Considerations

- Implement caching for API responses
- Set up database replication for read-heavy operations
- Use a CDN for static assets and images
- Consider containerization with Docker for easier deployment
- Implement load balancing for high-traffic scenarios

## Target Users

### 👥 টার্গেট ইউজার বেস:

- ব্যস্ত অফিস কর্মী বা প্রফেশনাল
- ব্যাচেলর / মেস / হোস্টেল ব্যবহারকারী
- ছোট হোটেল, রেন্টাল রুম বা গেস্ট হাউস
- পরিবার যারা চায় নির্ভরযোগ্য লন্ড্রি সার্ভিস

### Market Analysis

The application targets urban areas with:
- High density of working professionals
- Limited time for household chores
- Increasing smartphone penetration
- Growing comfort with digital services

---

© 2023 GoGo Laundry. All rights reserved.
