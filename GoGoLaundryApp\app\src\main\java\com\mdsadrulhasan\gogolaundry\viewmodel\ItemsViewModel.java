package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.repository.ItemRepository;
import com.mdsadrulhasan.gogolaundry.utils.Resource;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * ViewModel for items screen
 */
public class ItemsViewModel extends AndroidViewModel {

    private static final String TAG = "ItemsViewModel";

    private final ItemRepository itemRepository;
    private final MutableLiveData<Boolean> forceRefresh = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> forceRefreshPopular = new MutableLiveData<>(false);
    private final MutableLiveData<Integer> serviceId = new MutableLiveData<>();
    private LiveData<Resource<List<ItemEntity>>> itemsData;
    private LiveData<Resource<List<ItemEntity>>> popularItemsData;

    /**
     * Constructor
     *
     * @param application Application context
     */
    public ItemsViewModel(@NonNull Application application) {
        super(application);
        itemRepository = ItemRepository.getInstance();

        // Initialize items data with combined trigger for both forceRefresh and serviceId changes
        MediatorLiveData<Boolean> combinedTrigger = new MediatorLiveData<>();
        combinedTrigger.addSource(forceRefresh, value -> combinedTrigger.setValue(value));
        combinedTrigger.addSource(serviceId, value -> combinedTrigger.setValue(forceRefresh.getValue()));

        itemsData = Transformations.switchMap(combinedTrigger, refresh -> {
            Integer currentServiceId = serviceId.getValue();
            if (currentServiceId != null && currentServiceId > 0) {
                return itemRepository.getItemsByServiceId(refresh != null ? refresh : false, currentServiceId);
            } else {
                return itemRepository.getAllItems(refresh != null ? refresh : false);
            }
        });

        // Initialize popular items data
        popularItemsData = Transformations.switchMap(forceRefreshPopular, refresh ->
            itemRepository.getPopularItems(refresh));
    }

    /**
     * Get items LiveData
     *
     * @return LiveData of items resource
     */
    public LiveData<Resource<List<ItemEntity>>> getItems() {
        return itemsData;
    }

    /**
     * Get popular items LiveData
     *
     * @return LiveData of popular items resource
     */
    public LiveData<Resource<List<ItemEntity>>> getPopularItems() {
        return popularItemsData;
    }

    /**
     * Refresh items data
     */
    public void refreshItems() {
        forceRefresh.setValue(true);
    }

    /**
     * Refresh popular items data
     */
    public void refreshPopularItems() {
        forceRefreshPopular.setValue(true);
    }

    /**
     * Set service ID filter
     *
     * @param id Service ID
     */
    public void setServiceId(int id) {
        Log.d(TAG, "Setting service ID: " + id);
        if (serviceId.getValue() == null || serviceId.getValue() != id) {
            serviceId.setValue(id);
            // Force refresh to ensure data is loaded for the new service ID
            forceRefresh.setValue(true);
        }
    }

    /**
     * Convert ItemEntity list to Item model list for UI
     *
     * @param itemEntities List of item entities
     * @return List of item models
     */
    public List<Item> convertToItemModels(List<ItemEntity> itemEntities) {
        List<Item> items = new ArrayList<>();

        for (ItemEntity entity : itemEntities) {
            Item item = new Item();
            item.setId(entity.getId());
            item.setServiceId(entity.getServiceId());
            item.setName(entity.getName());
            item.setBnName(entity.getBnName());
            item.setDescription(entity.getDescription());
            item.setBnDescription(entity.getBnDescription());
            item.setPrice(entity.getPrice());
            item.setImageUrl(entity.getImageUrl());
            item.setActive(entity.isActive());
            item.setInStock(entity.isInStock());
            item.setServiceName(entity.getServiceName());

            // Set dates if available
            if (entity.getCreatedAt() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
                item.setCreatedAt(dateFormat.format(entity.getCreatedAt()));
            }

            if (entity.getUpdatedAt() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
                item.setUpdatedAt(dateFormat.format(entity.getUpdatedAt()));
            }

            items.add(item);
        }

        return items;
    }
}
