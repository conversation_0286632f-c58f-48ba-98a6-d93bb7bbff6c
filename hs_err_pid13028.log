#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=13028, tid=6332
#
# JRE version:  (21.0.7+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\6b307627437613cc426e8327b4ca03b6\redhat.java\ss_ws --pipe=\\.\pipe\lsp-9024008fafb0003bda423a78b5ecb6f8-sock

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Thu May 22 03:28:49 2025 Bangladesh Standard Time elapsed time: 0.049522 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002871ab1fbe0):  JavaThread "Unknown thread" [_thread_in_vm, id=6332, stack(0x000000f5b6400000,0x000000f5b6500000) (1024K)]

Stack: [0x000000f5b6400000,0x000000f5b6500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xbfba7]
V  [jvm.dll+0x70b72d]
V  [jvm.dll+0x70bdec]
V  [jvm.dll+0x6dcc68]
V  [jvm.dll+0x871dbc]
V  [jvm.dll+0x3bc47c]
V  [jvm.dll+0x85a848]
V  [jvm.dll+0x45080e]
V  [jvm.dll+0x452451]
C  [jli.dll+0x5278]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002872edfc430, length=1, elements={
0x000002871ab1fbe0
}

Java Threads: ( => current thread )
=>0x000002871ab1fbe0 JavaThread "Unknown thread"             [_thread_in_vm, id=6332, stack(0x000000f5b6400000,0x000000f5b6500000) (1024K)]
Total: 1

Other Threads:
  0x000002871ab90890 WatcherThread "VM Periodic Task Thread"        [id=14256, stack(0x000000f5b6600000,0x000000f5b6700000) (1024K)]
  0x000002871ab3eee0 WorkerThread "GC Thread#0"                     [id=14620, stack(0x000000f5b6500000,0x000000f5b6600000) (1024K)]
Total: 2

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002872f000000-0x000002872fba0000-0x000002872fba0000), size 12189696, SharedBaseAddress: 0x000002872f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000028730000000-0x0000028770000000, reserved size: 1073741824
Narrow klass base: 0x000002872f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 512K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 2% used [0x00000000eab00000,0x00000000eab80020,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 0K, committed 0K, reserved 1048576K
  class space    used 0K, committed 0K, reserved 1048576K

Card table byte_map: [0x000002871a4d0000,0x000002871a6e0000] _byte_map_base: 0x0000028719ed0000

Marking Bits: (ParMarkBitMap*) 0x00007ff82f4e31f0
 Begin Bits: [0x000002872cd20000, 0x000002872dd20000)
 End Bits:   [0x000002872dd20000, 0x000002872ed20000)

Polling page: 0x0000028718a00000

Metaspace:

Usage:
  Non-class:      0 bytes used.
      Class:      0 bytes used.
       Both:      0 bytes used.

Virtual space:
  Non-class space:        0 bytes reserved,       0 bytes (  ?%) committed,  0 nodes.
      Class space:        1.00 GB reserved,       0 bytes (  0%) committed,  1 nodes.
             Both:        1.00 GB reserved,       0 bytes (  0%) committed. 

Chunk freelists:
   Non-Class:  0 bytes
       Class:  16.00 MB
        Both:  16.00 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 0.
num_arena_deaths: 0.
num_vsnodes_births: 1.
num_vsnodes_deaths: 0.
num_space_committed: 0.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 1.
num_chunk_merges: 0.
num_chunk_splits: 1.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x00000287257f0000, 0x0000028725a60000, 0x000002872cd20000]
CodeHeap 'profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x000002871dd20000, 0x000002871df90000, 0x0000028725250000]
CodeHeap 'non-nmethods': size=5760Kb used=194Kb max_used=342Kb free=5565Kb
 bounds [0x0000028725250000, 0x00000287254c0000, 0x00000287257f0000]
 total_blobs=70 nmethods=0 adapters=48
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.012 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (1 events):
Event: 0.027 Thread 0x000002871ab1fbe0 Thread added: 0x000002871ab1fbe0


Dynamic libraries:
0x00007ff7b73b0000 - 0x00007ff7b73be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8cebf0000 - 0x00007ff8cede8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8cd350000 - 0x00007ff8cd412000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8cc4c0000 - 0x00007ff8cc7b6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8cc850000 - 0x00007ff8cc950000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8ab3e0000 - 0x00007ff8ab3f8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff8cdf20000 - 0x00007ff8ce0bd000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8ccbe0000 - 0x00007ff8ccc02000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8cd320000 - 0x00007ff8cd34b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8ccac0000 - 0x00007ff8ccbd9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8cc950000 - 0x00007ff8cc9ed000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8a9610000 - 0x00007ff8a962e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8bcf80000 - 0x00007ff8bd21a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ff8cce60000 - 0x00007ff8ccefe000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8cd6c0000 - 0x00007ff8cd6ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c8280000 - 0x00007ff8c828c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff89b290000 - 0x00007ff89b31d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ff82e830000 - 0x00007ff82f5c0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff8cd6f0000 - 0x00007ff8cd7a1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8ce9c0000 - 0x00007ff8cea5f000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8cd1e0000 - 0x00007ff8cd303000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8cc3e0000 - 0x00007ff8cc407000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8cd550000 - 0x00007ff8cd5bb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8cc0d0000 - 0x00007ff8cc11b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8c0fc0000 - 0x00007ff8c0fe7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8c39b0000 - 0x00007ff8c39ba000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8cc0b0000 - 0x00007ff8cc0c2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8caaf0000 - 0x00007ff8cab02000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c39c0000 - 0x00007ff8c39ca000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff8ca880000 - 0x00007ff8caa81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8baf20000 - 0x00007ff8baf54000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8cc7c0000 - 0x00007ff8cc842000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8b5360000 - 0x00007ff8b536f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8ab9b0000 - 0x00007ff8ab9cf000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\6b307627437613cc426e8327b4ca03b6\redhat.java\ss_ws --pipe=\\.\pipe\lsp-9024008fafb0003bda423a78b5ecb6f8-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 7:00 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4495M free)
TotalPageFile size 22476M (AvailPageFile size 32M)
current process WorkingSet (physical memory assigned to process): 24M, peak: 24M
current process commit charge ("private bytes"): 180M, peak: 181M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
