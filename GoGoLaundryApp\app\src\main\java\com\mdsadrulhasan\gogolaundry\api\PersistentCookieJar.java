package com.mdsadrulhasan.gogolaundry.api;

import android.content.Context;
import android.util.Log;

import com.mdsadrulhasan.gogolaundry.utils.SessionManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import okhttp3.Cookie;
import okhttp3.CookieJar;
import okhttp3.HttpUrl;

/**
 * A persistent cookie jar that stores cookies in SharedPreferences
 */
public class PersistentCookieJar implements CookieJar {
    private static final String TAG = "PersistentCookieJar";
    private final SessionManager sessionManager;

    public PersistentCookieJar(Context context) {
        this.sessionManager = new SessionManager(context.getApplicationContext());
    }

    @Override
    public void saveFromResponse(HttpUrl url, List<Cookie> cookies) {
        if (cookies.isEmpty()) {
            Log.d(TAG, "No cookies to save from response");
            return;
        }

        Log.d(TAG, "Saving " + cookies.size() + " cookies from response for URL: " + url);
        
        // Find the PHPSESSID cookie
        for (Cookie cookie : cookies) {
            Log.d(TAG, "Cookie: " + cookie.name() + "=" + cookie.value() + " for domain: " + cookie.domain());
            
            if (cookie.name().equals("PHPSESSID")) {
                // Save the PHPSESSID cookie
                String cookieString = "PHPSESSID=" + cookie.value();
                sessionManager.saveCookies(cookieString);
                Log.d(TAG, "Saved PHPSESSID cookie: " + cookieString);
                break;
            }
        }
    }

    @Override
    public List<Cookie> loadForRequest(HttpUrl url) {
        String cookies = sessionManager.getCookies();
        if (cookies == null || cookies.isEmpty()) {
            Log.d(TAG, "No cookies available for request to: " + url);
            return Collections.emptyList();
        }

        Log.d(TAG, "Loading cookies for request to: " + url);
        Log.d(TAG, "Cookies: " + cookies);
        
        // Parse cookies string
        List<Cookie> cookieList = new ArrayList<>();
        String[] cookieParts = cookies.split(";");
        
        for (String cookiePart : cookieParts) {
            cookiePart = cookiePart.trim();
            if (cookiePart.startsWith("PHPSESSID=")) {
                String value = cookiePart.substring("PHPSESSID=".length());
                
                // Create a new cookie
                Cookie.Builder builder = new Cookie.Builder()
                        .name("PHPSESSID")
                        .value(value)
                        .path("/")
                        .domain(url.host());
                
                cookieList.add(builder.build());
                Log.d(TAG, "Added PHPSESSID cookie: " + value + " for domain: " + url.host());
            }
        }
        
        return cookieList;
    }
    
    /**
     * Clear all cookies
     */
    public void clear() {
        Log.d(TAG, "Clearing all cookies");
        sessionManager.clearCookies();
    }
}
