<?php
/**
 * Get Upazillas API Endpoint
 *
 * This endpoint returns upazillas for a given district
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get district ID from query parameters
$districtId = isset($_GET['district_id']) ? (int)$_GET['district_id'] : 0;

// Validate district ID
if ($districtId <= 0) {
    jsonResponse(false, 'Invalid district ID', [], 400);
}

// Get upazillas for the given district
$stmt = $pdo->prepare("SELECT * FROM upazillas WHERE district_id = ? ORDER BY name");
$stmt->execute([$districtId]);
$upazillas = $stmt->fetchAll();

// Return success response
jsonResponse(true, 'Upazillas retrieved successfully', $upazillas);
