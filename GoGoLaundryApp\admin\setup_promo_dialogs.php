<?php
require_once 'includes/config.php';

// Read and execute the SQL file
$sqlFile = __DIR__ . '/sql/promotional_dialogs.sql';

if (!file_exists($sqlFile)) {
    die("SQL file not found: $sqlFile");
}

$sql = file_get_contents($sqlFile);

if ($sql === false) {
    die("Failed to read SQL file");
}

// Split SQL into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

$success = true;
$errors = [];

foreach ($statements as $statement) {
    if (empty($statement)) continue;
    
    try {
        if ($conn->query($statement) === FALSE) {
            $errors[] = "Error executing statement: " . $conn->error;
            $success = false;
        }
    } catch (Exception $e) {
        $errors[] = "Exception: " . $e->getMessage();
        $success = false;
    }
}

if ($success) {
    echo "✅ Promotional dialogs table created successfully!\n";
    echo "✅ Sample promotional dialog inserted!\n";
    echo "\nYou can now:\n";
    echo "1. Access the admin panel at: promotional_dialogs.php\n";
    echo "2. Create, edit, and manage promotional dialogs\n";
    echo "3. The Android app will automatically fetch active dialogs\n";
} else {
    echo "❌ Errors occurred:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
}

$conn->close();
?>
