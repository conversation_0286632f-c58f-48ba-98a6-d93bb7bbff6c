<?php
/**
 * FCM Send Notification API
 *
 * This endpoint sends FCM notifications to users
 *
 * Required parameters:
 * - title: Notification title
 * - message: Notification message
 * - type: Notification type (order_status, promo, system, custom)
 *
 * Optional parameters:
 * - user_id: Send to specific user (if not provided, sends to all users)
 * - order_id: Associated order ID
 * - data: Additional data to send with notification
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 * - data: Notification details and send results
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Include required files
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/FCMService.php';
require_once '../../includes/NotificationManager.php';

// Initialize response
$response = [
    'success' => false,
    'message' => 'Unknown error occurred',
    'data' => null
];

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    // If JSON input is empty, try POST data
    if (empty($input)) {
        $input = $_POST;
    }

    // Validate required parameters
    $requiredFields = ['title', 'message', 'type'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            $response['message'] = "Missing required field: $field";
            echo json_encode($response);
            exit();
        }
    }

    $title = trim($input['title']);
    $message = trim($input['message']);
    $type = trim($input['type']);
    $userId = isset($input['user_id']) ? intval($input['user_id']) : null;
    $orderId = isset($input['order_id']) && !empty(trim($input['order_id'])) ? intval($input['order_id']) : null;
    $additionalData = isset($input['data']) ? $input['data'] : [];

    // Validate notification type
    $allowedTypes = ['order_status', 'promo', 'system', 'custom'];
    if (!in_array($type, $allowedTypes)) {
        $response['message'] = 'Invalid notification type. Allowed: ' . implode(', ', $allowedTypes);
        echo json_encode($response);
        exit();
    }

    // Initialize services
    $fcmService = new FCMService();
    $notificationManager = new NotificationManager($pdo);

    // Prepare notification data
    $notificationData = array_merge([
        'type' => $type,
        'order_id' => $orderId,
        'timestamp' => time()
    ], $additionalData);

    $fcmResults = [];
    $notificationIds = [];

    if ($userId) {
        // Send to specific user

        // Validate user exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND is_verified = 1");
        $stmt->execute([$userId]);

        if ($stmt->rowCount() === 0) {
            $response['message'] = 'User not found or not verified';
            echo json_encode($response);
            exit();
        }

        // Create notification record
        $notificationId = $notificationManager->createNotification(
            $userId, $orderId, $title, $message, $type, true, false
        );

        if ($notificationId) {
            $notificationIds[] = $notificationId;

            // Send FCM notification
            $fcmResult = $fcmService->sendToUser($pdo, $userId, $title, $message, $notificationData);
            $fcmResults[] = $fcmResult;

            // Update FCM sent status
            if ($fcmResult['success']) {
                $stmt = $pdo->prepare("UPDATE notifications SET fcm_sent = 1 WHERE id = ?");
                $stmt->execute([$notificationId]);
            }
        }

    } else {
        // Send to all users

        // Get all verified users
        $stmt = $pdo->prepare("SELECT id FROM users WHERE is_verified = 1");
        $stmt->execute();
        $userIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($userIds)) {
            $response['message'] = 'No verified users found';
            echo json_encode($response);
            exit();
        }

        // Create notification records for all users
        foreach ($userIds as $uid) {
            $notificationId = $notificationManager->createNotification(
                $uid, $orderId, $title, $message, $type, true, false
            );

            if ($notificationId) {
                $notificationIds[] = $notificationId;
            }
        }

        // Send FCM notification to all users
        $fcmResult = $fcmService->sendToAllUsers($pdo, $title, $message, $notificationData);
        $fcmResults[] = $fcmResult;

        // Update FCM sent status for successful sends
        if (!empty($notificationIds)) {
            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
            $stmt = $pdo->prepare("UPDATE notifications SET fcm_sent = 1 WHERE id IN ($placeholders)");
            $stmt->execute($notificationIds);
        }
    }

    // Prepare response
    $response['success'] = true;
    $response['message'] = 'Notification sent successfully';
    $response['data'] = [
        'notification_ids' => $notificationIds,
        'fcm_results' => $fcmResults,
        'total_notifications' => count($notificationIds),
        'sent_at' => date('Y-m-d H:i:s')
    ];

    // Log the notification
    $targetDescription = $userId ? "user $userId" : "all users";
    error_log("FCM Notification sent to $targetDescription: $title");

} catch (Exception $e) {
    $response['message'] = 'Server error: ' . $e->getMessage();
    error_log('FCM Send Notification Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
?>
