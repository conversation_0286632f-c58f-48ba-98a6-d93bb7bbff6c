# JavaScript Libraries

This directory should contain the following JavaScript libraries:

## Required Libraries

1. **Chart.js** - For creating charts and graphs
   - Download from: https://cdn.jsdelivr.net/npm/chart.js
   - Save as: `chart.min.js`

2. **DataTables** - For enhanced table functionality
   - Already included via CDN in the header.php file

## Installation Instructions

### Chart.js

1. Download Chart.js from https://cdn.jsdelivr.net/npm/chart.js
2. Save it as `chart.min.js` in this directory

Alternatively, you can continue using the CDN version that's already included in the header.php file:

```html
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
```

## Custom JavaScript Files

- **sales-report-charts.js** - Contains chart initialization for the sales report page
- Other custom JavaScript files for specific functionality
