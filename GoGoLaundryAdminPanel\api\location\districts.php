<?php
/**
 * Get Districts API Endpoint
 *
 * This endpoint returns districts for a given division
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get division ID from query parameters
$divisionId = isset($_GET['division_id']) ? (int)$_GET['division_id'] : 0;

// Validate division ID
if ($divisionId <= 0) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'Invalid division ID']);
    exit;
}

// Get districts for the given division
$stmt = $pdo->prepare("SELECT * FROM districts WHERE division_id = ? ORDER BY name");
$stmt->execute([$divisionId]);
$districts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Return success response
header('Content-Type: application/json');
echo json_encode($districts);
