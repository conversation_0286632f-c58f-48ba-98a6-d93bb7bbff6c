package com.mdsadrulhasan.gogolaundry.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;

import java.util.List;

/**
 * Data Access Object for Service entity
 */
@Dao
public interface ServiceDao {

    /**
     * Insert a service
     *
     * @param service Service to insert
     * @return ID of inserted service
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(ServiceEntity service);

    /**
     * Insert multiple services
     *
     * @param services Services to insert
     * @return IDs of inserted services
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertAll(List<ServiceEntity> services);

    /**
     * Update a service
     *
     * @param service Service to update
     */
    @Update
    void update(ServiceEntity service);

    /**
     * Get service by ID
     *
     * @param id Service ID
     * @return Service with the given ID
     */
    @Query("SELECT * FROM services WHERE id = :id")
    ServiceEntity getServiceById(int id);

    /**
     * Get service by ID as LiveData
     *
     * @param id Service ID
     * @return LiveData of service with the given ID
     */
    @Query("SELECT * FROM services WHERE id = :id")
    LiveData<ServiceEntity> getServiceByIdLive(int id);

    /**
     * Get all services
     *
     * @return List of all services
     */
    @Query("SELECT * FROM services ORDER BY sort_order ASC")
    List<ServiceEntity> getAllServices();

    /**
     * Get all active services
     *
     * @return List of all active services
     */
    @Query("SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC")
    List<ServiceEntity> getAllActiveServices();

    /**
     * Get all services as LiveData
     *
     * @return LiveData of all services
     */
    @Query("SELECT * FROM services ORDER BY sort_order ASC")
    LiveData<List<ServiceEntity>> getAllServicesLive();

    /**
     * Get all active services as LiveData
     *
     * @return LiveData of all active services
     */
    @Query("SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC")
    LiveData<List<ServiceEntity>> getAllActiveServicesLive();

    /**
     * Delete all services
     */
    @Query("DELETE FROM services")
    void deleteAll();
}
