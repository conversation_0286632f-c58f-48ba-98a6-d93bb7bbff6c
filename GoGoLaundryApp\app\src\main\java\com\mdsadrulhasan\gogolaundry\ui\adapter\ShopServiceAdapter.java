package com.mdsadrulhasan.gogolaundry.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * Adapter for displaying shop services in RecyclerView
 */
public class ShopServiceAdapter extends RecyclerView.Adapter<ShopServiceAdapter.ServiceViewHolder> {

    private List<ShopServiceEntity> services;
    private final OnServiceClickListener listener;

    /**
     * Interface for handling service item clicks
     */
    public interface OnServiceClickListener {
        void onServiceClick(ShopServiceEntity service);
    }

    /**
     * Constructor
     *
     * @param services List of services
     * @param listener Click listener
     */
    public ShopServiceAdapter(List<ShopServiceEntity> services, OnServiceClickListener listener) {
        this.services = services != null ? services : new ArrayList<>();
        this.listener = listener;
    }

    @NonNull
    @Override
    public ServiceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_shop_service, parent, false);
        return new ServiceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ServiceViewHolder holder, int position) {
        ShopServiceEntity service = services.get(position);
        holder.bind(service, listener);
    }

    @Override
    public int getItemCount() {
        return services.size();
    }

    /**
     * Update services list
     *
     * @param newServices New list of services
     */
    public void updateServices(List<ShopServiceEntity> newServices) {
        this.services = newServices != null ? newServices : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * ViewHolder for service items
     */
    static class ServiceViewHolder extends RecyclerView.ViewHolder {
        private final ImageView serviceIcon;
        private final TextView serviceName;
        private final TextView serviceDescription;
        private final TextView estimatedTime;

        public ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            serviceIcon = itemView.findViewById(R.id.serviceImageView);
            serviceName = itemView.findViewById(R.id.serviceNameTextView);
            serviceDescription = itemView.findViewById(R.id.serviceDescriptionTextView);
            estimatedTime = itemView.findViewById(R.id.estimatedTimeTextView);
        }

        public void bind(ShopServiceEntity service, OnServiceClickListener listener) {
            // Set service name (prefer Bengali name if available)
            String displayName = service.getServiceBnName() != null && !service.getServiceBnName().isEmpty()
                    ? service.getServiceBnName()
                    : service.getServiceName();
            serviceName.setText(displayName);

            // Set estimated time
            estimatedTime.setText(itemView.getContext().getString(R.string.estimated_time_hours, service.getEstimatedHours()));

            // Load service icon
            if (service.getServiceImageUrl() != null && !service.getServiceImageUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(service.getServiceImageUrl())
                        .placeholder(R.drawable.placeholder_image)
                        .error(R.drawable.placeholder_image)
                        .into(serviceIcon);
            } else {
                serviceIcon.setImageResource(R.drawable.placeholder_image);
            }

            // Set click listener
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onServiceClick(service);
                }
            });
        }
    }
}
