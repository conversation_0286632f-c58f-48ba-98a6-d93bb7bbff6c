<?php
/**
 * Admin Profile Page
 *
 * This page allows admins to view and edit their profile information
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/AdminManager.php';

// Initialize admin manager
$adminManager = new AdminManager($pdo);

// Handle form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error = 'Invalid security token. Please try again.';
    } else {
        // Get form data
        $email = trim($_POST['email'] ?? '');
        $fullName = trim($_POST['full_name'] ?? '');
        
        // Validate input
        if (empty($email)) {
            $error = 'Email is required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } elseif (empty($fullName)) {
            $error = 'Full name is required.';
        } else {
            // Check if email is already taken by another admin
            $existingAdmin = $adminManager->getAdminByEmail($email);
            if ($existingAdmin && $existingAdmin['id'] != $adminData['id']) {
                $error = 'This email address is already in use by another admin.';
            } else {
                // Update admin profile
                $result = $adminManager->updateAdminProfile(
                    $adminData['id'],
                    $email,
                    $fullName
                );
                
                if ($result) {
                    // Log action
                    $adminManager->logAdminAction(
                        $adminData['id'],
                        'profile_update',
                        'Updated own profile information',
                        getClientIp()
                    );
                    
                    $success = 'Profile updated successfully.';
                    
                    // Refresh admin data
                    $adminData = $adminManager->getAdminById($adminData['id']);
                } else {
                    $error = 'Failed to update profile. Please try again.';
                }
            }
        }
    }
}

// Page title and breadcrumbs
$pageTitle = 'My Profile';
$breadcrumbs = [
    'My Profile' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">My Profile</h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="change_password.php" class="btn btn-outline-primary">
            <i class="fas fa-key me-1"></i> Change Password
        </a>
    </div>
</div>

<?php if (!empty($error)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error; ?>
    </div>
<?php endif; ?>

<?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle me-2"></i> <?php echo $success; ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i> Profile Information
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" value="<?php echo htmlspecialchars($adminData['username']); ?>" readonly>
                                <div class="form-text">Username cannot be changed.</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Role</label>
                                <input type="text" class="form-control" id="role" value="<?php echo ucfirst(str_replace('_', ' ', $adminData['role'])); ?>" readonly>
                                <div class="form-text">Role is managed by super administrators.</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($adminData['email']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="full_name" name="full_name" value="<?php echo htmlspecialchars($adminData['full_name']); ?>" required>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update Profile
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i> Account Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Account Status</label>
                    <div>
                        <?php if ($adminData['is_active']): ?>
                            <span class="badge bg-success">Active</span>
                        <?php else: ?>
                            <span class="badge bg-danger">Inactive</span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Created Date</label>
                    <div class="text-muted">
                        <?php echo date('F j, Y', strtotime($adminData['created_at'])); ?>
                    </div>
                </div>
                
                <?php if (!empty($adminData['last_login'])): ?>
                <div class="mb-3">
                    <label class="form-label">Last Login</label>
                    <div class="text-muted">
                        <?php echo date('F j, Y g:i A', strtotime($adminData['last_login'])); ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="mb-0">
                    <label class="form-label">Login Attempts</label>
                    <div class="text-muted">
                        <?php echo $adminData['login_attempts']; ?> failed attempts
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
