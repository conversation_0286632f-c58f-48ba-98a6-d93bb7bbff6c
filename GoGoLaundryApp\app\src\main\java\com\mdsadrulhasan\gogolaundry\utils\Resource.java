package com.mdsadrulhasan.gogolaundry.utils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * A generic class that holds a value with its loading status.
 * @param <T> Type of the resource data
 */
public class Resource<T> {
    
    @NonNull
    private final Status status;
    
    @Nullable
    private final T data;
    
    @Nullable
    private final String message;
    
    private Resource(@NonNull Status status, @Nullable T data, @Nullable String message) {
        this.status = status;
        this.data = data;
        this.message = message;
    }
    
    /**
     * Create a successful resource with data
     * 
     * @param data Resource data
     * @param <T> Type of the resource data
     * @return Successful resource
     */
    public static <T> Resource<T> success(@Nullable T data) {
        return new Resource<>(Status.SUCCESS, data, null);
    }
    
    /**
     * Create an error resource with message and data
     * 
     * @param msg Error message
     * @param data Resource data
     * @param <T> Type of the resource data
     * @return Error resource
     */
    public static <T> Resource<T> error(String msg, @Nullable T data) {
        return new Resource<>(Status.ERROR, data, msg);
    }
    
    /**
     * Create a loading resource with data
     * 
     * @param data Resource data
     * @param <T> Type of the resource data
     * @return Loading resource
     */
    public static <T> Resource<T> loading(@Nullable T data) {
        return new Resource<>(Status.LOADING, data, null);
    }
    
    /**
     * Get resource status
     * 
     * @return Resource status
     */
    @NonNull
    public Status getStatus() {
        return status;
    }
    
    /**
     * Get resource data
     * 
     * @return Resource data
     */
    @Nullable
    public T getData() {
        return data;
    }
    
    /**
     * Get resource message
     * 
     * @return Resource message
     */
    @Nullable
    public String getMessage() {
        return message;
    }
    
    /**
     * Check if resource is successful
     * 
     * @return True if resource is successful, false otherwise
     */
    public boolean isSuccess() {
        return status == Status.SUCCESS;
    }
    
    /**
     * Check if resource is loading
     * 
     * @return True if resource is loading, false otherwise
     */
    public boolean isLoading() {
        return status == Status.LOADING;
    }
    
    /**
     * Check if resource is error
     * 
     * @return True if resource is error, false otherwise
     */
    public boolean isError() {
        return status == Status.ERROR;
    }
    
    /**
     * Resource status enum
     */
    public enum Status {
        SUCCESS,
        ERROR,
        LOADING
    }
}
