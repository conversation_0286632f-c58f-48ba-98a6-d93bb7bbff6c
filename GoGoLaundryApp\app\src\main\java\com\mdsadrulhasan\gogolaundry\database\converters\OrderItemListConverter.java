package com.mdsadrulhasan.gogolaundry.database.converters;

import androidx.room.TypeConverter;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mdsadrulhasan.gogolaundry.model.OrderItem;

import java.lang.reflect.Type;
import java.util.List;

/**
 * Type converter for Room database to convert between List<OrderItem> and String
 */
public class OrderItemListConverter {
    
    private static final Gson gson = new Gson();
    
    /**
     * Convert List<OrderItem> to JSON string for storage in database
     * 
     * @param orderItems List of order items
     * @return JSON string representation
     */
    @TypeConverter
    public static String fromOrderItemList(List<OrderItem> orderItems) {
        if (orderItems == null) {
            return null;
        }
        return gson.toJson(orderItems);
    }
    
    /**
     * Convert JSON string to List<OrderItem> when reading from database
     * 
     * @param orderItemsString JSON string representation
     * @return List of order items
     */
    @TypeConverter
    public static List<OrderItem> toOrderItemList(String orderItemsString) {
        if (orderItemsString == null) {
            return null;
        }
        Type listType = new TypeToken<List<OrderItem>>() {}.getType();
        return gson.fromJson(orderItemsString, listType);
    }
}
