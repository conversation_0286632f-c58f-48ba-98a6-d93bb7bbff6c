# Resend Error Debugging Guide

## 🔍 **Error Analysis**

### **Error Observed:**
- Error popup showing "Error occurred while resending notification"
- IP address displayed: *************
- <PERSON><PERSON> shows "Sending..." state but fails

### **Potential Causes:**
1. **API Path Issues** - Incorrect file paths or missing files
2. **Authentication Problems** - Admin auth middleware failing
3. **Database Connection** - PDO connection issues
4. **FCMService Issues** - Class not found or method errors
5. **JavaScript/PHP Data Type Mismatch** - Boolean conversion problems

## 🔧 **Debugging Steps Implemented**

### **1. Enhanced Error Reporting**
```php
// Added to resend_notification.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Enhanced include error handling
try {
    require_once '../../config/db.php';
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Database config error: ' . $e->getMessage()]);
    exit;
}
```

### **2. JavaScript Console Logging**
```javascript
// Added debug logging
console.log('Sending resend request:', {
    notification_id: currentNotificationId,
    send_fcm: sendFcm,
    send_sms: sendSms,
    recipient: recipient
});

// Enhanced error handling
error: function(xhr, status, error) {
    console.error('AJAX Error:', xhr.responseText);
    alert('Error occurred while resending notification: ' + error);
}
```

### **3. Boolean Value Handling**
```php
// Fixed boolean conversion
$sendFcm = isset($_POST['send_fcm']) && ($_POST['send_fcm'] === 'true' || $_POST['send_fcm'] === true || $_POST['send_fcm'] === '1');
$sendSms = isset($_POST['send_sms']) && ($_POST['send_sms'] === 'true' || $_POST['send_sms'] === true || $_POST['send_sms'] === '1');
```

### **4. Test API Endpoint**
Created `test_resend.php` for isolated testing:
```php
// Simple test without FCM dependencies
echo json_encode([
    'success' => true,
    'message' => 'Test resend successful',
    'notification_id' => $notificationId,
    'notification_title' => $notification['title'],
    'recipient' => $recipient,
    'send_fcm' => $sendFcmBool,
    'send_sms' => $sendSmsBool,
    'results' => $results
]);
```

## 🧪 **Testing Instructions**

### **Step 1: Test Basic API**
1. **Click the green test button (🧪)** next to any notification
2. **Check browser console** for detailed logs
3. **Expected**: "Test successful!" alert and console data

### **Step 2: Check Browser Console**
1. **Open Developer Tools** (F12)
2. **Go to Console tab**
3. **Click resend button** (🔄)
4. **Look for error messages** in console

### **Step 3: Check Network Tab**
1. **Open Developer Tools** (F12)
2. **Go to Network tab**
3. **Click resend button** (🔄)
4. **Check the API request/response**

### **Step 4: Check Server Logs**
1. **Check PHP error logs** in your server
2. **Look for entries** related to resend_notification.php
3. **Check for database or FCM errors**

## 🔍 **Common Issues & Solutions**

### **Issue 1: File Path Problems**
```
Error: "FCMService include error" or "Database config error"
Solution: Check file paths in admin/api/resend_notification.php
```

### **Issue 2: Authentication Failure**
```
Error: "Auth error" or redirect to login
Solution: Check admin session and auth.php file
```

### **Issue 3: Database Connection**
```
Error: "Database error occurred"
Solution: Check config/db.php and database credentials
```

### **Issue 4: FCMService Class Issues**
```
Error: "FCMService class not found"
Solution: Check includes/FCMService.php exists and is properly included
```

### **Issue 5: Boolean Conversion**
```
Error: "Please select at least one delivery method"
Solution: Check JavaScript boolean values being sent correctly
```

## 📊 **Debugging Checklist**

### ✅ **Files to Check:**
- [ ] `admin/api/resend_notification.php` - Main API file
- [ ] `admin/api/test_resend.php` - Test API file
- [ ] `config/db.php` - Database configuration
- [ ] `includes/FCMService.php` - FCM service class
- [ ] `admin/auth.php` - Authentication middleware

### ✅ **Browser Console Checks:**
- [ ] JavaScript errors in console
- [ ] AJAX request data being sent
- [ ] Server response content
- [ ] Network request status codes

### ✅ **Server Log Checks:**
- [ ] PHP error logs
- [ ] Database connection errors
- [ ] FCM service errors
- [ ] Authentication errors

## 🎯 **Expected Test Results**

### **Test Button (🧪) Success:**
```json
{
    "success": true,
    "message": "Test resend successful",
    "notification_id": 123,
    "notification_title": "Testing notification",
    "recipient": "original",
    "send_fcm": true,
    "send_sms": false,
    "results": {
        "fcm": "FCM would be sent (test mode)"
    }
}
```

### **Console Output:**
```
Testing resend for notification ID: 123
Test response: {success: true, message: "Test resend successful", ...}
```

### **Resend Button (🔄) Success:**
```json
{
    "success": true,
    "message": "Notification resent successfully",
    "results": {
        "fcm": "FCM notification sent successfully to original user"
    }
}
```

## 🚀 **Next Steps**

### **If Test Button Works:**
- The basic API structure is fine
- Issue is likely with FCMService or complex logic
- Focus on FCM integration debugging

### **If Test Button Fails:**
- Basic API structure has issues
- Check file paths and authentication
- Fix fundamental problems first

### **If Console Shows Errors:**
- Follow the specific error messages
- Check server logs for detailed errors
- Fix issues step by step

## 📝 **Debugging Commands**

### **Check File Permissions:**
```bash
ls -la admin/api/
ls -la includes/
ls -la config/
```

### **Test API Directly:**
```bash
curl -X POST http://*************/GoGoLaundry/GoGoLaundryAdminPanel/admin/api/test_resend.php \
  -d "notification_id=1&send_fcm=true&send_sms=false&recipient=original"
```

### **Check PHP Logs:**
```bash
tail -f /var/log/php_errors.log
# or
tail -f /xampp/logs/php_error_log
```

## 🎯 **Resolution Strategy**

1. **Start with test button** - Isolate basic functionality
2. **Check browser console** - Get detailed error information
3. **Verify file paths** - Ensure all includes work correctly
4. **Test authentication** - Make sure admin session is valid
5. **Debug FCM service** - Check if FCMService class loads properly
6. **Fix step by step** - Address each error systematically

**The debugging tools are now in place - use them to identify the exact issue! 🔍**
