<?php
/**
 * Revenue API
 *
 * This file handles the API endpoint for revenue data
 */

// Include required files
require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/OrderManager.php';

// Set headers
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle different request methods
if ($method !== 'GET') {
    // Method not allowed
    header("HTTP/1.1 405 Method Not Allowed");
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Initialize OrderManager
$orderManager = new OrderManager($pdo);

// Get period parameter (default: week)
$period = isset($_GET['period']) ? $_GET['period'] : 'week';

// Log request for debugging
error_log("Revenue API called with period: $period");

try {
    // Get data based on period
    switch ($period) {
        case 'week':
            $startDate = date('Y-m-d', strtotime('-6 days'));
            $endDate = date('Y-m-d');
            $data = getRevenueData($orderManager, $startDate, $endDate, 'daily');
            break;

        case 'month':
            $startDate = date('Y-m-d', strtotime('-29 days'));
            $endDate = date('Y-m-d');
            $data = getRevenueData($orderManager, $startDate, $endDate, 'daily');
            break;

        case 'year':
            $year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
            $data = getRevenueData($orderManager, null, null, 'monthly', $year);
            break;

        default:
            // Invalid period
            error_log("Invalid period provided: $period");
            echo json_encode(['success' => false, 'message' => 'Invalid period']);
            exit;
    }

    // Log response for debugging
    error_log("Revenue API response: " . json_encode($data));

    // Return response
    echo json_encode(['success' => true, 'data' => $data]);
    exit;
} catch (Exception $e) {
    // Log error
    error_log("Error in revenue API: " . $e->getMessage());

    // Return error response
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing your request']);
    exit;
}

/**
 * Get revenue data
 *
 * @param OrderManager $orderManager OrderManager instance
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param string $type Type of data (daily or monthly)
 * @param int $year Year (for monthly data)
 * @return array Revenue data
 */
function getRevenueData($orderManager, $startDate, $endDate, $type = 'daily', $year = null) {
    $labels = [];
    $values = [];

    try {
        if ($type === 'daily') {
            // Log for debugging
            error_log("Getting daily revenue data for $startDate to $endDate");

            // Get daily revenue
            $revenueData = $orderManager->getDailyRevenue($startDate, $endDate);

            // Log the revenue data for debugging
            error_log("Daily revenue data: " . json_encode($revenueData));

            // Create a map of dates to revenue
            $revenueMap = [];
            foreach ($revenueData as $data) {
                $revenueMap[$data['date']] = (float)$data['revenue'];
            }

            // Generate all dates in the range
            $currentDate = new DateTime($startDate);
            $lastDate = new DateTime($endDate);
            $interval = new DateInterval('P1D');

            while ($currentDate <= $lastDate) {
                $dateStr = $currentDate->format('Y-m-d');
                $labels[] = $currentDate->format('M d');
                $values[] = isset($revenueMap[$dateStr]) ? (float)$revenueMap[$dateStr] : 0;
                $currentDate->add($interval);
            }
        } else {
            // Log for debugging
            error_log("Getting monthly revenue data for year $year");

            // Get monthly revenue
            $revenueData = $orderManager->getMonthlyRevenue($year);

            // Log the revenue data for debugging
            error_log("Monthly revenue data: " . json_encode($revenueData));

            // Create a map of months to revenue
            $revenueMap = [];
            foreach ($revenueData as $data) {
                $revenueMap[$data['month']] = (float)$data['revenue'];
            }

            // Generate all months
            $monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            for ($month = 1; $month <= 12; $month++) {
                $labels[] = $monthNames[$month - 1];
                $values[] = isset($revenueMap[$month]) ? (float)$revenueMap[$month] : 0;
            }
        }

        // If no data was found, add a message
        if (empty($values) || array_sum($values) == 0) {
            error_log("No revenue data found for the specified period");
        }

        return [
            'labels' => $labels,
            'values' => $values
        ];
    } catch (Exception $e) {
        error_log("Error in getRevenueData: " . $e->getMessage());
        return [
            'labels' => [],
            'values' => []
        ];
    }
}
