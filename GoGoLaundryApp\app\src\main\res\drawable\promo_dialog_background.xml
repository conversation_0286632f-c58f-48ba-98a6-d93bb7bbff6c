<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">
    
    <!-- Glassmorphism background with gradient -->
    <gradient
        android:angle="135"
        android:startColor="#40FFFFFF"
        android:centerColor="#30FFFFFF"
        android:endColor="#20FFFFFF"
        android:type="linear" />
    
    <!-- Rounded corners -->
    <corners android:radius="24dp" />
    
    <!-- Subtle border -->
    <stroke
        android:width="1dp"
        android:color="#30FFFFFF" />
        
</shape>
