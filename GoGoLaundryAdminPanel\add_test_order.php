<?php
/**
 * Add Test Order Script
 *
 * This script adds a test order to the database
 */

// Include required files
require_once 'config/db.php';
require_once 'includes/functions.php';
require_once 'includes/OrderManager.php';
require_once 'includes/UserManager.php';
require_once 'includes/ServiceManager.php';
require_once 'includes/ItemManager.php';
require_once 'includes/LocationManager.php';

// Initialize managers
$orderManager = new OrderManager($pdo);
$userManager = new UserManager($pdo);
$serviceManager = new ServiceManager($pdo);
$itemManager = new ItemManager($pdo);
$locationManager = new LocationManager($pdo);

// Get a user
$result = $userManager->getAllUsers(1, 1);
$user = $result['users'][0] ?? null;

if (!$user) {
    echo "No users found. Please create a user first.<br>";
    exit;
}

// Get services and items
$servicesResult = $serviceManager->getAllServices(1, 10);
$services = $servicesResult['services'];

if (empty($services)) {
    echo "No services found. Please create services first.<br>";
    exit;
}

$itemsResult = $itemManager->getAllItems(1, 10);
$items = $itemsResult['items'];

if (empty($items)) {
    echo "No items found. Please create items first.<br>";
    exit;
}

// Get divisions, districts, and upazillas
$divisions = $locationManager->getAllDivisions();
if (empty($divisions)) {
    echo "No divisions found. Please add location data first.<br>";
    exit;
}

$districts = $locationManager->getDistrictsByDivision($divisions[0]['id']);
if (empty($districts)) {
    echo "No districts found. Please add location data first.<br>";
    exit;
}

// Check if the method exists
if (method_exists($locationManager, 'getUpazillasByDistrict')) {
    $upazillas = $locationManager->getUpazillasByDistrict($districts[0]['id']);
} else {
    // Fallback to direct query
    $stmt = $pdo->prepare("SELECT * FROM upazillas WHERE district_id = ? LIMIT 10");
    $stmt->execute([$districts[0]['id']]);
    $upazillas = $stmt->fetchAll();
}
if (empty($upazillas)) {
    echo "No upazillas found. Please add location data first.<br>";
    exit;
}

// Generate order number and tracking number
$orderNumber = 'ORD' . date('YmdHis');
$trackingNumber = 'TRK' . date('YmdHis');

try {
    // Start transaction
    $pdo->beginTransaction();

    // Insert order
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            order_number,
            tracking_number,
            user_id,
            subtotal,
            delivery_fee,
            total,
            payment_method,
            status,
            pickup_address,
            pickup_division_id,
            pickup_district_id,
            pickup_upazilla_id,
            pickup_date,
            pickup_time_slot,
            delivery_address,
            delivery_division_id,
            delivery_district_id,
            delivery_upazilla_id,
            notes
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");

    $subtotal = 150.00;
    $deliveryFee = 50.00;
    $total = $subtotal + $deliveryFee;

    $stmt->execute([
        $orderNumber,
        $trackingNumber,
        $user['id'],
        $subtotal,
        $deliveryFee,
        $total,
        'cash',
        'placed',
        '123 Pickup Street',
        $divisions[0]['id'],
        $districts[0]['id'],
        $upazillas[0]['id'],
        date('Y-m-d', strtotime('+1 day')),
        '10:00 AM - 12:00 PM',
        '456 Delivery Avenue',
        $divisions[0]['id'],
        $districts[0]['id'],
        $upazillas[0]['id'],
        'Test order created via script'
    ]);

    $orderId = $pdo->lastInsertId();

    // Add order items
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id,
            item_id,
            quantity,
            price,
            subtotal
        ) VALUES (
            ?, ?, ?, ?, ?
        )
    ");

    // Add first item
    $item1 = $items[0];
    $quantity1 = 2;
    $price1 = $item1['price'];
    $subtotal1 = $price1 * $quantity1;

    $stmt->execute([
        $orderId,
        $item1['id'],
        $quantity1,
        $price1,
        $subtotal1
    ]);

    // Add second item if available
    if (isset($items[1])) {
        $item2 = $items[1];
        $quantity2 = 1;
        $price2 = $item2['price'];
        $subtotal2 = $price2 * $quantity2;

        $stmt->execute([
            $orderId,
            $item2['id'],
            $quantity2,
            $price2,
            $subtotal2
        ]);
    }

    // Commit transaction
    $pdo->commit();

    echo "Test order created successfully!<br>";
    echo "Order ID: $orderId<br>";
    echo "Order Number: $orderNumber<br>";
    echo "Tracking Number: $trackingNumber<br>";
    echo "<a href='admin/orders.php'>Go to Orders page</a>";
} catch (PDOException $e) {
    // Rollback transaction
    $pdo->rollBack();

    echo "Error creating test order: " . $e->getMessage() . "<br>";
}
?>
