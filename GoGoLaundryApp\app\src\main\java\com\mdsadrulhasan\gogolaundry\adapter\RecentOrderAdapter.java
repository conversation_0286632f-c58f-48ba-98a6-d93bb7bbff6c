package com.mdsadrulhasan.gogolaundry.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Adapter for displaying recent orders in horizontal RecyclerView
 */
public class RecentOrderAdapter extends RecyclerView.Adapter<RecentOrderAdapter.RecentOrderViewHolder> {

    private Context context;
    private List<OrderEntity> orders;
    private OnOrderClickListener listener;

    /**
     * Interface for handling order click events
     */
    public interface OnOrderClickListener {
        void onOrderClick(OrderEntity order);
        void onTrackOrderClick(OrderEntity order);
        void onReceiptClick(OrderEntity order);
        void onReorderClick(OrderEntity order);
    }

    /**
     * Constructor
     */
    public RecentOrderAdapter(Context context, List<OrderEntity> orders, OnOrderClickListener listener) {
        this.context = context;
        this.orders = orders != null ? orders : new ArrayList<>();
        this.listener = listener;
    }

    /**
     * Update orders list
     */
    public void updateOrders(List<OrderEntity> newOrders) {
        this.orders = newOrders != null ? newOrders : new ArrayList<>();
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecentOrderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recent_order, parent, false);
        return new RecentOrderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecentOrderViewHolder holder, int position) {
        OrderEntity order = orders.get(position);
        holder.bind(order);
    }

    @Override
    public int getItemCount() {
        return orders.size();
    }

    /**
     * ViewHolder for recent order items
     */
    public class RecentOrderViewHolder extends RecyclerView.ViewHolder {

        private TextView orderNumber;
        private TextView orderDate;
        private TextView orderStatus;
        private TextView orderItems;
        private TextView orderTotal;
        private MaterialButton trackButton;
        private MaterialButton receiptButton;
        private ImageView statusIcon;
        private View statusIndicator;

        public RecentOrderViewHolder(@NonNull View itemView) {
            super(itemView);

            orderNumber = itemView.findViewById(R.id.order_number);
            orderDate = itemView.findViewById(R.id.order_date);
            orderStatus = itemView.findViewById(R.id.order_status);
            orderItems = itemView.findViewById(R.id.order_items);
            orderTotal = itemView.findViewById(R.id.order_total);
            trackButton = itemView.findViewById(R.id.btn_track_order);
            receiptButton = itemView.findViewById(R.id.btn_receipt);
            statusIcon = itemView.findViewById(R.id.status_icon);
            statusIndicator = itemView.findViewById(R.id.status_indicator);

            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onOrderClick(orders.get(getAdapterPosition()));
                }
            });

            trackButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onTrackOrderClick(orders.get(getAdapterPosition()));
                }
            });

            receiptButton.setOnClickListener(v -> {
                if (listener != null) {
                    OrderEntity order = orders.get(getAdapterPosition());
                    if (isDelivered(order.getStatus())) {
                        listener.onReorderClick(order);
                    } else {
                        listener.onReceiptClick(order);
                    }
                }
            });
        }

        public void bind(OrderEntity order) {
            // Set order number
            orderNumber.setText("Order #" + order.getOrderNumber());

            // Set order date
            if (order.getCreatedAt() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
                orderDate.setText(dateFormat.format(order.getCreatedAt()));
            } else {
                orderDate.setText("Date not available");
            }

            // Set order status with appropriate styling
            String status = order.getStatus();
            orderStatus.setText(getFormattedStatus(status));
            setStatusStyling(status);

            // Set order items preview (placeholder for now)
            // In a real implementation, you would load order items from the API
            orderItems.setText("Items • " + getServiceName(order));

            // Set order total
            orderTotal.setText(String.format("৳ %.2f", order.getTotal()));

            // Update track button based on status
            updateTrackButton(status);

            // Update receipt/reorder button based on status
            updateReceiptButton(status);
        }

        /**
         * Format status text for display
         */
        private String getFormattedStatus(String status) {
            if (status == null) return "Unknown";

            switch (status.toLowerCase()) {
                case "pending":
                    return "Pending";
                case "confirmed":
                    return "Confirmed";
                case "picked_up":
                    return "Picked Up";
                case "in_progress":
                    return "In Progress";
                case "ready":
                    return "Ready";
                case "out_for_delivery":
                    return "Out for Delivery";
                case "delivered":
                    return "Delivered";
                case "cancelled":
                    return "Cancelled";
                default:
                    return status.substring(0, 1).toUpperCase() + status.substring(1);
            }
        }

        /**
         * Set status styling based on order status
         */
        private void setStatusStyling(String status) {
            if (status == null) return;

            int colorRes;
            int iconRes;

            switch (status.toLowerCase()) {
                case "delivered":
                    colorRes = R.color.success;
                    iconRes = R.drawable.ic_check;
                    break;
                case "cancelled":
                    colorRes = R.color.error;
                    iconRes = R.drawable.ic_close;
                    break;
                case "pending":
                case "confirmed":
                    colorRes = R.color.warning;
                    iconRes = R.drawable.ic_time;
                    break;
                case "picked_up":
                case "in_progress":
                case "ready":
                case "out_for_delivery":
                    colorRes = R.color.info;
                    iconRes = R.drawable.ic_location;
                    break;
                default:
                    colorRes = R.color.text_secondary;
                    iconRes = R.drawable.ic_info;
                    break;
            }

            // Set status text color
            orderStatus.setTextColor(ContextCompat.getColor(context, colorRes));

            // Set status icon
            statusIcon.setImageResource(iconRes);
            statusIcon.setColorFilter(ContextCompat.getColor(context, colorRes));

            // Set status indicator color
            statusIndicator.setBackgroundTintList(ContextCompat.getColorStateList(context, colorRes));
        }

        /**
         * Update track button based on order status
         */
        private void updateTrackButton(String status) {
            if (status == null) return;

            switch (status.toLowerCase()) {
                case "delivered":
                    trackButton.setText("View");
                    trackButton.setIcon(ContextCompat.getDrawable(context, R.drawable.ic_details));
                    break;
                case "cancelled":
                    trackButton.setText("Details");
                    trackButton.setIcon(ContextCompat.getDrawable(context, R.drawable.ic_info));
                    break;
                default:
                    trackButton.setText("Track");
                    trackButton.setIcon(ContextCompat.getDrawable(context, R.drawable.ic_location));
                    break;
            }
        }

        /**
         * Get service name placeholder
         */
        private String getServiceName(OrderEntity order) {
            // This is a placeholder. In a real implementation, you would
            // load the actual service name from the order items
            return "Wash & Fold";
        }

        /**
         * Update receipt/reorder button based on order status
         */
        private void updateReceiptButton(String status) {
            if (status == null) return;

            if (isDelivered(status)) {
                // Show reorder button for delivered orders
                receiptButton.setText("Reorder");
                receiptButton.setIcon(ContextCompat.getDrawable(context, R.drawable.reorder));
                receiptButton.setVisibility(View.VISIBLE);
            } else {
                // Show receipt button for non-delivered orders (only if order is confirmed or later)
                if (canShowReceipt(status)) {
                    receiptButton.setText("Receipt");
                    receiptButton.setIcon(ContextCompat.getDrawable(context, R.drawable.ic_receipt));
                    receiptButton.setVisibility(View.VISIBLE);
                } else {
                    receiptButton.setVisibility(View.GONE);
                }
            }
        }

        /**
         * Check if order can show receipt
         */
        private boolean canShowReceipt(String status) {
            return !status.equalsIgnoreCase("placed") &&
                   !status.equalsIgnoreCase("cancelled");
        }
    }

    /**
     * Check if order is delivered
     */
    private boolean isDelivered(String status) {
        return status != null && status.equalsIgnoreCase("delivered");
    }
}
