#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 331350016 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3600), pid=10344, tid=11512
#
# JRE version: Java(TM) SE Runtime Environment (21.0.2+13) (build 21.0.2+13-LTS-58)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-bin\cetblhg4pflnnks72fxwobvgv\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Sat May 24 14:23:51 2025 Bangladesh Standard Time elapsed time: 22.064433 seconds (0d 0h 0m 22s)

---------------  T H R E A D  ---------------

Current thread (0x0000016d7f22cb10):  VMThread "VM Thread"          [id=11512, stack(0x0000004701400000,0x0000004701500000) (1024K)]

Stack: [0x0000004701400000,0x0000004701500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0x6c74d5]
V  [jvm.dll+0x6bbeca]
V  [jvm.dll+0x355bca]
V  [jvm.dll+0x35d816]
V  [jvm.dll+0x3ae67e]
V  [jvm.dll+0x3ae928]
V  [jvm.dll+0x3295dc]
V  [jvm.dll+0x329667]
V  [jvm.dll+0x36df1c]
V  [jvm.dll+0x36c71d]
V  [jvm.dll+0x328d51]
V  [jvm.dll+0x36be2a]
V  [jvm.dll+0x85ef18]
V  [jvm.dll+0x860014]
V  [jvm.dll+0x860550]
V  [jvm.dll+0x8607f3]
V  [jvm.dll+0x7ff568]
V  [jvm.dll+0x6c953d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x000000470aafed40): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000016d56c386f0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000016d5cda4670, length=148, elements={
0x0000016d73818920, 0x0000016d7f2ca200, 0x0000016d7f2caf70, 0x0000016d7f2cce50,
0x0000016d7f2ceb40, 0x0000016d7f2cf900, 0x0000016d7f2d2cc0, 0x0000016d7f2e7b60,
0x0000016d7f30a7d0, 0x0000016d7f2d1fa0, 0x0000016d7f2d3350, 0x0000016d7f2d0560,
0x0000016d7f2d0bf0, 0x0000016d7f2d1280, 0x0000016d570ed420, 0x0000016d570e9280,
0x0000016d570ecd90, 0x0000016d570ec070, 0x0000016d570eb9e0, 0x0000016d570edab0,
0x0000016d570e9910, 0x0000016d570e9fa0, 0x0000016d570ea630, 0x0000016d570eacc0,
0x0000016d570ec700, 0x0000016d570ee140, 0x0000016d570ee7d0, 0x0000016d570eee60,
0x0000016d570e8560, 0x0000016d570efb80, 0x0000016d570e8bf0, 0x0000016d58d7b990,
0x0000016d58d7ee10, 0x0000016d58d7c6b0, 0x0000016d58d7d3d0, 0x0000016d58d7e0f0,
0x0000016d58d7cd40, 0x0000016d58d7e780, 0x0000016d58d80850, 0x0000016d58d7f4a0,
0x0000016d58d801c0, 0x0000016d58d80ee0, 0x0000016d58d7b300, 0x0000016d58d79f50,
0x0000016d58d7a5e0, 0x0000016d58d7ac70, 0x0000016d586e5720, 0x0000016d586e3ce0,
0x0000016d586e4a00, 0x0000016d586e5db0, 0x0000016d586e5090, 0x0000016d586e2fc0,
0x0000016d586e6440, 0x0000016d586e6ad0, 0x0000016d586e3650, 0x0000016d586e7160,
0x0000016d586e01d0, 0x0000016d586e1c10, 0x0000016d586e77f0, 0x0000016d586e0860,
0x0000016d586e0ef0, 0x0000016d5d0a2930, 0x0000016d5d0a77f0, 0x0000016d5d0a8510,
0x0000016d5d0a6ad0, 0x0000016d5d0a2fc0, 0x0000016d5d0a5090, 0x0000016d5d0a6440,
0x0000016d5d0a3ce0, 0x0000016d5d0a5720, 0x0000016d5d0a4370, 0x0000016d5d0a8ba0,
0x0000016d5d0a4a00, 0x0000016d5d0a98c0, 0x0000016d5d0a5db0, 0x0000016d5d0a7e80,
0x0000016d5a834680, 0x0000016d5a8325b0, 0x0000016d5a837b00, 0x0000016d5a835a30,
0x0000016d5a838190, 0x0000016d5a831200, 0x0000016d5a838820, 0x0000016d5a8360c0,
0x0000016d5a831890, 0x0000016d5a831f20, 0x0000016d5a832c40, 0x0000016d5a837470,
0x0000016d5a836750, 0x0000016d5a834d10, 0x0000016d5a833960, 0x0000016d5a833ff0,
0x0000016d5a836de0, 0x0000016d5a8353a0, 0x0000016d590fd540, 0x0000016d59103120,
0x0000016d59103e40, 0x0000016d591016e0, 0x0000016d591037b0, 0x0000016d59102a90,
0x0000016d590ff610, 0x0000016d59101d70, 0x0000016d590fe8f0, 0x0000016d590fe260,
0x0000016d590ffca0, 0x0000016d590fdbd0, 0x0000016d59100330, 0x0000016d59102400,
0x0000016d590fef80, 0x0000016d591009c0, 0x0000016d59368c50, 0x0000016d593692e0,
0x0000016d5936c0d0, 0x0000016d593678a0, 0x0000016d59369970, 0x0000016d5936db10,
0x0000016d5936a000, 0x0000016d5936a690, 0x0000016d593685c0, 0x0000016d5936d480,
0x0000016d59367f30, 0x0000016d5936ad20, 0x0000016d5936b3b0, 0x0000016d5936ba40,
0x0000016d5936c760, 0x0000016d5936cdf0, 0x0000016d57a55860, 0x0000016d57a523e0,
0x0000016d57a56580, 0x0000016d57a52a70, 0x0000016d57a51030, 0x0000016d57a53100,
0x0000016d57a53790, 0x0000016d57a516c0, 0x0000016d57a551d0, 0x0000016d57a55ef0,
0x0000016d5c7ce9f0, 0x0000016d56c39aa0, 0x0000016d56c38060, 0x0000016d56c32b10,
0x0000016d56c32480, 0x0000016d56c37340, 0x0000016d56c386f0, 0x0000016d56c33ec0,
0x0000016d56c3a130, 0x0000016d56c38d80, 0x0000016d56c39410, 0x0000016d59191ea0
}

Java Threads: ( => current thread )
  0x0000016d73818920 JavaThread "main"                              [_thread_blocked, id=9512, stack(0x0000004700d00000,0x0000004700e00000) (1024K)]
  0x0000016d7f2ca200 JavaThread "Reference Handler"          daemon [_thread_blocked, id=6096, stack(0x0000004701500000,0x0000004701600000) (1024K)]
  0x0000016d7f2caf70 JavaThread "Finalizer"                  daemon [_thread_blocked, id=3032, stack(0x0000004701600000,0x0000004701700000) (1024K)]
  0x0000016d7f2cce50 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16336, stack(0x0000004701700000,0x0000004701800000) (1024K)]
  0x0000016d7f2ceb40 JavaThread "Attach Listener"            daemon [_thread_blocked, id=9048, stack(0x0000004701800000,0x0000004701900000) (1024K)]
  0x0000016d7f2cf900 JavaThread "Service Thread"             daemon [_thread_blocked, id=16512, stack(0x0000004701900000,0x0000004701a00000) (1024K)]
  0x0000016d7f2d2cc0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=17948, stack(0x0000004701a00000,0x0000004701b00000) (1024K)]
  0x0000016d7f2e7b60 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=16560, stack(0x0000004701b00000,0x0000004701c00000) (1024K)]
  0x0000016d7f30a7d0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=10492, stack(0x0000004701c00000,0x0000004701d00000) (1024K)]
  0x0000016d7f2d1fa0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=5404, stack(0x0000004701d00000,0x0000004701e00000) (1024K)]
  0x0000016d7f2d3350 JavaThread "Notification Thread"        daemon [_thread_blocked, id=17640, stack(0x0000004701e00000,0x0000004701f00000) (1024K)]
  0x0000016d7f2d0560 JavaThread "Daemon health stats"               [_thread_blocked, id=10608, stack(0x0000004702700000,0x0000004702800000) (1024K)]
  0x0000016d7f2d0bf0 JavaThread "Incoming local TCP Connector on port 51508"        [_thread_in_native, id=8104, stack(0x0000004702800000,0x0000004702900000) (1024K)]
  0x0000016d7f2d1280 JavaThread "Daemon periodic checks"            [_thread_blocked, id=14216, stack(0x0000004702900000,0x0000004702a00000) (1024K)]
  0x0000016d570ed420 JavaThread "Daemon"                            [_thread_blocked, id=10100, stack(0x0000004702a00000,0x0000004702b00000) (1024K)]
  0x0000016d570e9280 JavaThread "Handler for socket connection from /127.0.0.1:51508 to /127.0.0.1:51509"        [_thread_in_native, id=18396, stack(0x0000004702b00000,0x0000004702c00000) (1024K)]
  0x0000016d570ecd90 JavaThread "Cancel handler"                    [_thread_blocked, id=18196, stack(0x0000004702c00000,0x0000004702d00000) (1024K)]
  0x0000016d570ec070 JavaThread "Daemon worker"                     [_thread_blocked, id=17684, stack(0x0000004702d00000,0x0000004702e00000) (1024K)]
  0x0000016d570eb9e0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:51508 to /127.0.0.1:51509"        [_thread_blocked, id=10516, stack(0x0000004702e00000,0x0000004702f00000) (1024K)]
  0x0000016d570edab0 JavaThread "Stdin handler"                     [_thread_blocked, id=17584, stack(0x0000004702f00000,0x0000004703000000) (1024K)]
  0x0000016d570e9910 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=9496, stack(0x0000004703000000,0x0000004703100000) (1024K)]
  0x0000016d570e9fa0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=14856, stack(0x0000004703100000,0x0000004703200000) (1024K)]
  0x0000016d570ea630 JavaThread "File lock request listener"        [_thread_in_native, id=16160, stack(0x0000004703200000,0x0000004703300000) (1024K)]
  0x0000016d570eacc0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)"        [_thread_blocked, id=17056, stack(0x0000004703300000,0x0000004703400000) (1024K)]
  0x0000016d570ec700 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\8.12\fileHashes)"        [_thread_blocked, id=1488, stack(0x0000004703600000,0x0000004703700000) (1024K)]
  0x0000016d570ee140 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\buildOutputCleanup)"        [_thread_blocked, id=15944, stack(0x0000004703700000,0x0000004703800000) (1024K)]
  0x0000016d570ee7d0 JavaThread "File watcher server"        daemon [_thread_blocked, id=14732, stack(0x0000004703800000,0x0000004703900000) (1024K)]
  0x0000016d570eee60 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=17832, stack(0x0000004703900000,0x0000004703a00000) (1024K)]
  0x0000016d570e8560 JavaThread "jar transforms"                    [_thread_blocked, id=15176, stack(0x0000004703a00000,0x0000004703b00000) (1024K)]
  0x0000016d570efb80 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\8.12\checksums)"        [_thread_blocked, id=10224, stack(0x0000004703b00000,0x0000004703c00000) (1024K)]
  0x0000016d570e8bf0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)"        [_thread_blocked, id=6276, stack(0x0000004703c00000,0x0000004703d00000) (1024K)]
  0x0000016d58d7b990 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)"        [_thread_blocked, id=18184, stack(0x0000004703d00000,0x0000004703e00000) (1024K)]
  0x0000016d58d7ee10 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)"        [_thread_blocked, id=15192, stack(0x0000004703e00000,0x0000004703f00000) (1024K)]
  0x0000016d58d7c6b0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=1720, stack(0x0000004703f00000,0x0000004704000000) (1024K)]
  0x0000016d58d7d3d0 JavaThread "Problems report writer"            [_thread_blocked, id=10584, stack(0x0000004704000000,0x0000004704100000) (1024K)]
  0x0000016d58d7e0f0 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=11048, stack(0x0000004704100000,0x0000004704200000) (1024K)]
  0x0000016d58d7cd40 JavaThread "Unconstrained build operations"        [_thread_blocked, id=15436, stack(0x0000004704200000,0x0000004704300000) (1024K)]
  0x0000016d58d7e780 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=9720, stack(0x0000004704300000,0x0000004704400000) (1024K)]
  0x0000016d58d80850 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=5980, stack(0x0000004704400000,0x0000004704500000) (1024K)]
  0x0000016d58d7f4a0 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=2652, stack(0x0000004704500000,0x0000004704600000) (1024K)]
  0x0000016d58d801c0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=1868, stack(0x0000004704600000,0x0000004704700000) (1024K)]
  0x0000016d58d80ee0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=10076, stack(0x0000004704700000,0x0000004704800000) (1024K)]
  0x0000016d58d7b300 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=14212, stack(0x0000004704800000,0x0000004704900000) (1024K)]
  0x0000016d58d79f50 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=15516, stack(0x0000004704900000,0x0000004704a00000) (1024K)]
  0x0000016d58d7a5e0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=16112, stack(0x0000004704a00000,0x0000004704b00000) (1024K)]
  0x0000016d58d7ac70 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=14244, stack(0x0000004704b00000,0x0000004704c00000) (1024K)]
  0x0000016d586e5720 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=11968, stack(0x0000004704c00000,0x0000004704d00000) (1024K)]
  0x0000016d586e3ce0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=12196, stack(0x0000004704d00000,0x0000004704e00000) (1024K)]
  0x0000016d586e4a00 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=14332, stack(0x0000004704e00000,0x0000004704f00000) (1024K)]
  0x0000016d586e5db0 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=15052, stack(0x0000004704f00000,0x0000004705000000) (1024K)]
  0x0000016d586e5090 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=9768, stack(0x0000004705000000,0x0000004705100000) (1024K)]
  0x0000016d586e2fc0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=13020, stack(0x0000004705100000,0x0000004705200000) (1024K)]
  0x0000016d586e6440 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=8788, stack(0x0000004705200000,0x0000004705300000) (1024K)]
  0x0000016d586e6ad0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=7684, stack(0x0000004705300000,0x0000004705400000) (1024K)]
  0x0000016d586e3650 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=17968, stack(0x0000004705400000,0x0000004705500000) (1024K)]
  0x0000016d586e7160 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=15268, stack(0x0000004705500000,0x0000004705600000) (1024K)]
  0x0000016d586e01d0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=14348, stack(0x0000004705600000,0x0000004705700000) (1024K)]
  0x0000016d586e1c10 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=16716, stack(0x0000004705700000,0x0000004705800000) (1024K)]
  0x0000016d586e77f0 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=16420, stack(0x0000004705800000,0x0000004705900000) (1024K)]
  0x0000016d586e0860 JavaThread "Memory manager"                    [_thread_blocked, id=16712, stack(0x0000004705900000,0x0000004705a00000) (1024K)]
  0x0000016d586e0ef0 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=17536, stack(0x0000004705a00000,0x0000004705b00000) (1024K)]
  0x0000016d5d0a2930 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=5628, stack(0x0000004705b00000,0x0000004705c00000) (1024K)]
  0x0000016d5d0a77f0 JavaThread "build event listener"              [_thread_blocked, id=10368, stack(0x0000004705c00000,0x0000004705d00000) (1024K)]
  0x0000016d5d0a8510 JavaThread "included builds"                   [_thread_blocked, id=15316, stack(0x0000004705d00000,0x0000004705e00000) (1024K)]
  0x0000016d5d0a6ad0 JavaThread "Execution worker"                  [_thread_blocked, id=8964, stack(0x0000004705e00000,0x0000004705f00000) (1024K)]
  0x0000016d5d0a2fc0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=17612, stack(0x0000004705f00000,0x0000004706000000) (1024K)]
  0x0000016d5d0a5090 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=7604, stack(0x0000004706000000,0x0000004706100000) (1024K)]
  0x0000016d5d0a6440 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=816, stack(0x0000004706100000,0x0000004706200000) (1024K)]
  0x0000016d5d0a3ce0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=17304, stack(0x0000004706200000,0x0000004706300000) (1024K)]
  0x0000016d5d0a5720 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=18120, stack(0x0000004706300000,0x0000004706400000) (1024K)]
  0x0000016d5d0a4370 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=15560, stack(0x0000004706400000,0x0000004706500000) (1024K)]
  0x0000016d5d0a8ba0 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\GoGoLaundry\GoGoLaundryApp\.gradle\8.12\executionHistory)"        [_thread_blocked, id=12132, stack(0x0000004706500000,0x0000004706600000) (1024K)]
  0x0000016d5d0a4a00 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=5504, stack(0x0000004706600000,0x0000004706700000) (1024K)]
  0x0000016d5d0a98c0 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=6852, stack(0x0000004706700000,0x0000004706800000) (1024K)]
  0x0000016d5d0a5db0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=1644, stack(0x0000004706800000,0x0000004706900000) (1024K)]
  0x0000016d5d0a7e80 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=17444, stack(0x0000004706900000,0x0000004706a00000) (1024K)]
  0x0000016d5a834680 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=11136, stack(0x0000004706a00000,0x0000004706b00000) (1024K)]
  0x0000016d5a8325b0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=14504, stack(0x0000004706b00000,0x0000004706c00000) (1024K)]
  0x0000016d5a837b00 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=3188, stack(0x0000004706c00000,0x0000004706d00000) (1024K)]
  0x0000016d5a835a30 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=13520, stack(0x0000004706d00000,0x0000004706e00000) (1024K)]
  0x0000016d5a838190 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=2872, stack(0x0000004706e00000,0x0000004706f00000) (1024K)]
  0x0000016d5a831200 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=16604, stack(0x0000004706f00000,0x0000004707000000) (1024K)]
  0x0000016d5a838820 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=9500, stack(0x0000004707000000,0x0000004707100000) (1024K)]
  0x0000016d5a8360c0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=5888, stack(0x0000004707100000,0x0000004707200000) (1024K)]
  0x0000016d5a831890 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=7460, stack(0x0000004707200000,0x0000004707300000) (1024K)]
  0x0000016d5a831f20 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=416, stack(0x0000004707300000,0x0000004707400000) (1024K)]
  0x0000016d5a832c40 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=4340, stack(0x0000004707400000,0x0000004707500000) (1024K)]
  0x0000016d5a837470 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=17540, stack(0x0000004707500000,0x0000004707600000) (1024K)]
  0x0000016d5a836750 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=11016, stack(0x0000004707600000,0x0000004707700000) (1024K)]
  0x0000016d5a834d10 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=17720, stack(0x0000004707700000,0x0000004707800000) (1024K)]
  0x0000016d5a833960 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=8880, stack(0x0000004707800000,0x0000004707900000) (1024K)]
  0x0000016d5a833ff0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=2536, stack(0x0000004707900000,0x0000004707a00000) (1024K)]
  0x0000016d5a836de0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=10856, stack(0x0000004707a00000,0x0000004707b00000) (1024K)]
  0x0000016d5a8353a0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=13440, stack(0x0000004707b00000,0x0000004707c00000) (1024K)]
  0x0000016d590fd540 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=4860, stack(0x0000004707c00000,0x0000004707d00000) (1024K)]
  0x0000016d59103120 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=6564, stack(0x0000004707d00000,0x0000004707e00000) (1024K)]
  0x0000016d59103e40 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=16276, stack(0x0000004707e00000,0x0000004707f00000) (1024K)]
  0x0000016d591016e0 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=5388, stack(0x0000004707f00000,0x0000004708000000) (1024K)]
  0x0000016d591037b0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=1292, stack(0x0000004708000000,0x0000004708100000) (1024K)]
  0x0000016d59102a90 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=7748, stack(0x0000004708100000,0x0000004708200000) (1024K)]
  0x0000016d590ff610 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=17616, stack(0x0000004708200000,0x0000004708300000) (1024K)]
  0x0000016d59101d70 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=9064, stack(0x0000004708300000,0x0000004708400000) (1024K)]
  0x0000016d590fe8f0 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=7724, stack(0x0000004708400000,0x0000004708500000) (1024K)]
  0x0000016d590fe260 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=18392, stack(0x0000004708500000,0x0000004708600000) (1024K)]
  0x0000016d590ffca0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=18044, stack(0x0000004708600000,0x0000004708700000) (1024K)]
  0x0000016d590fdbd0 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=15652, stack(0x0000004708700000,0x0000004708800000) (1024K)]
  0x0000016d59100330 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=14788, stack(0x0000004708800000,0x0000004708900000) (1024K)]
  0x0000016d59102400 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=14600, stack(0x0000004708900000,0x0000004708a00000) (1024K)]
  0x0000016d590fef80 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=4436, stack(0x0000004708a00000,0x0000004708b00000) (1024K)]
  0x0000016d591009c0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=9932, stack(0x0000004708b00000,0x0000004708c00000) (1024K)]
  0x0000016d59368c50 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=2392, stack(0x0000004703400000,0x0000004703500000) (1024K)]
  0x0000016d593692e0 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=17844, stack(0x0000004708c00000,0x0000004708d00000) (1024K)]
  0x0000016d5936c0d0 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=14924, stack(0x0000004708d00000,0x0000004708e00000) (1024K)]
  0x0000016d593678a0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=7084, stack(0x0000004708e00000,0x0000004708f00000) (1024K)]
  0x0000016d59369970 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=3016, stack(0x0000004708f00000,0x0000004709000000) (1024K)]
  0x0000016d5936db10 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=13808, stack(0x0000004709000000,0x0000004709100000) (1024K)]
  0x0000016d5936a000 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=12720, stack(0x0000004709100000,0x0000004709200000) (1024K)]
  0x0000016d5936a690 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=5260, stack(0x0000004709200000,0x0000004709300000) (1024K)]
  0x0000016d593685c0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=7368, stack(0x0000004709300000,0x0000004709400000) (1024K)]
  0x0000016d5936d480 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=1684, stack(0x0000004709400000,0x0000004709500000) (1024K)]
  0x0000016d59367f30 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=5248, stack(0x0000004709500000,0x0000004709600000) (1024K)]
  0x0000016d5936ad20 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=14588, stack(0x0000004709600000,0x0000004709700000) (1024K)]
  0x0000016d5936b3b0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=14808, stack(0x0000004709700000,0x0000004709800000) (1024K)]
  0x0000016d5936ba40 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=14168, stack(0x0000004709800000,0x0000004709900000) (1024K)]
  0x0000016d5936c760 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=14572, stack(0x0000004709900000,0x0000004709a00000) (1024K)]
  0x0000016d5936cdf0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=14720, stack(0x0000004709a00000,0x0000004709b00000) (1024K)]
  0x0000016d57a55860 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=18208, stack(0x0000004709b00000,0x0000004709c00000) (1024K)]
  0x0000016d57a523e0 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=16256, stack(0x0000004709c00000,0x0000004709d00000) (1024K)]
  0x0000016d57a56580 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=9984, stack(0x0000004709d00000,0x0000004709e00000) (1024K)]
  0x0000016d57a52a70 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=17772, stack(0x0000004709e00000,0x0000004709f00000) (1024K)]
  0x0000016d57a51030 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=10524, stack(0x0000004709f00000,0x000000470a000000) (1024K)]
  0x0000016d57a53100 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=15024, stack(0x000000470a000000,0x000000470a100000) (1024K)]
  0x0000016d57a53790 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=7076, stack(0x000000470a200000,0x000000470a300000) (1024K)]
  0x0000016d57a516c0 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=17904, stack(0x000000470a300000,0x000000470a400000) (1024K)]
  0x0000016d57a551d0 JavaThread "stderr"                            [_thread_in_native, id=2724, stack(0x000000470a400000,0x000000470a500000) (1024K)]
  0x0000016d57a55ef0 JavaThread "stdout"                            [_thread_in_native, id=7856, stack(0x000000470a500000,0x000000470a600000) (1024K)]
  0x0000016d5c7ce9f0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=16984, stack(0x0000004701f00000,0x0000004702000000) (1024K)]
  0x0000016d56c39aa0 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=15284, stack(0x000000470a600000,0x000000470a700000) (1024K)]
  0x0000016d56c38060 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=8944, stack(0x000000470a700000,0x000000470a800000) (1024K)]
  0x0000016d56c32b10 JavaThread "ForkJoinPool-1-worker-1"    daemon [_thread_blocked, id=1588, stack(0x000000470a100000,0x000000470a200000) (1024K)]
  0x0000016d56c32480 JavaThread "ForkJoinPool-1-worker-3"    daemon [_thread_blocked, id=2148, stack(0x000000470a800000,0x000000470a900000) (1024K)]
  0x0000016d56c37340 JavaThread "ForkJoinPool-1-worker-2"    daemon [_thread_blocked, id=16536, stack(0x000000470a900000,0x000000470aa00000) (1024K)]
  0x0000016d56c386f0 JavaThread "ForkJoinPool-1-worker-4"    daemon [_thread_blocked, id=10748, stack(0x000000470aa00000,0x000000470ab00000) (1024K)]
  0x0000016d56c33ec0 JavaThread "ForkJoinPool-1-worker-5"    daemon [_thread_blocked, id=5244, stack(0x000000470ab00000,0x000000470ac00000) (1024K)]
  0x0000016d56c3a130 JavaThread "ForkJoinPool-1-worker-6"    daemon [_thread_blocked, id=3728, stack(0x000000470ac00000,0x000000470ad00000) (1024K)]
  0x0000016d56c38d80 JavaThread "ForkJoinPool-1-worker-7"    daemon [_thread_blocked, id=15196, stack(0x000000470ad00000,0x000000470ae00000) (1024K)]
  0x0000016d56c39410 JavaThread "ForkJoinPool-1-worker-8"    daemon [_thread_blocked, id=15712, stack(0x000000470ae00000,0x000000470af00000) (1024K)]
  0x0000016d59191ea0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=17496, stack(0x000000470af00000,0x000000470b000000) (1024K)]
Total: 148

Other Threads:
=>0x0000016d7f22cb10 VMThread "VM Thread"                           [id=11512, stack(0x0000004701400000,0x0000004701500000) (1024K)]
  0x0000016d7f218750 WatcherThread "VM Periodic Task Thread"        [id=9748, stack(0x0000004701300000,0x0000004701400000) (1024K)]
  0x0000016d7386fa30 WorkerThread "GC Thread#0"                     [id=15800, stack(0x0000004700e00000,0x0000004700f00000) (1024K)]
  0x0000016d7fcbe4f0 WorkerThread "GC Thread#1"                     [id=7688, stack(0x0000004702000000,0x0000004702100000) (1024K)]
  0x0000016d7fcbe890 WorkerThread "GC Thread#2"                     [id=6416, stack(0x0000004702100000,0x0000004702200000) (1024K)]
  0x0000016d7fcbec30 WorkerThread "GC Thread#3"                     [id=6992, stack(0x0000004702200000,0x0000004702300000) (1024K)]
  0x0000016d7fb36550 WorkerThread "GC Thread#4"                     [id=7844, stack(0x0000004702300000,0x0000004702400000) (1024K)]
  0x0000016d7fb368f0 WorkerThread "GC Thread#5"                     [id=5228, stack(0x0000004702400000,0x0000004702500000) (1024K)]
  0x0000016d7fb36c90 WorkerThread "GC Thread#6"                     [id=10160, stack(0x0000004702500000,0x0000004702600000) (1024K)]
  0x0000016d7f939b10 WorkerThread "GC Thread#7"                     [id=2680, stack(0x0000004702600000,0x0000004702700000) (1024K)]
  0x0000016d73880ac0 ConcurrentGCThread "G1 Main Marker"            [id=17876, stack(0x0000004700f00000,0x0000004701000000) (1024K)]
  0x0000016d73882c70 WorkerThread "G1 Conc#0"                       [id=7256, stack(0x0000004701000000,0x0000004701100000) (1024K)]
  0x0000016d56f85b10 WorkerThread "G1 Conc#1"                       [id=14292, stack(0x0000004703500000,0x0000004703600000) (1024K)]
  0x0000016d7f164f20 ConcurrentGCThread "G1 Refine#0"               [id=7112, stack(0x0000004701100000,0x0000004701200000) (1024K)]
  0x0000016d7f1658a0 ConcurrentGCThread "G1 Service"                [id=16360, stack(0x0000004701200000,0x0000004701300000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0    22098 17434       4       com.android.tools.r8.dex.C::b (267 bytes)
C2 CompilerThread1    22098 17050       4       com.android.tools.r8.internal.xn::a (3327 bytes)
C2 CompilerThread2    22098 17087 %     4       com.android.tools.r8.dex.C::a @ 143 (831 bytes)
Total: 3

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffa241acd68] Threads_lock - owner thread: 0x0000016d7f22cb10
[0x00007ffa241ace68] Heap_lock - owner thread: 0x0000016d56c386f0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000016d11000000-0x0000016d11c90000-0x0000016d11c90000), size 13172736, SharedBaseAddress: 0x0000016d11000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000016d12000000-0x0000016d52000000, reserved size: 1073741824
Narrow klass base: 0x0000016d11000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 480256K, used 285624K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 121362K, committed 123712K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Updating 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HS|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080800000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080e00000| Updating 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Updating 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082500000| Updating 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%|HS|  |TAMS 0x0000000082b00000| PB 0x0000000082a00000| Complete 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Updating 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HS|  |TAMS 0x0000000083100000| PB 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083200000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HS|  |TAMS 0x0000000083300000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083300000| Updating 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000| PB 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083700000| Updating 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HS|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084300000| Updating 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Updating 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Updating 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Updating 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Updating 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Updating 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Updating 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Updating 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Updating 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Updating 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Updating 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Updating 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Updating 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Updating 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Updating 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Updating 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%|HS|  |TAMS 0x0000000086700000| PB 0x0000000086600000| Complete 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Updating 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Updating 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Updating 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Updating 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%|HS|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HC|  |TAMS 0x0000000087500000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%|HC|  |TAMS 0x0000000087600000| PB 0x0000000087500000| Complete 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Updating 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%|HS|  |TAMS 0x0000000087900000| PB 0x0000000087800000| Complete 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%|HC|  |TAMS 0x0000000087a00000| PB 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%|HS|  |TAMS 0x0000000087b00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%|HS|  |TAMS 0x0000000087c00000| PB 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%|HC|  |TAMS 0x0000000087d00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%|HC|  |TAMS 0x0000000087e00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%|HS|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%|HC|  |TAMS 0x0000000088000000| PB 0x0000000087f00000| Complete 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%|HS|  |TAMS 0x0000000088100000| PB 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HS|  |TAMS 0x0000000088300000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HS|  |TAMS 0x0000000088400000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HS|  |TAMS 0x0000000088500000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HS|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Updating 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Updating 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%|HS|  |TAMS 0x0000000088c00000| PB 0x0000000088b00000| Complete 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%|HC|  |TAMS 0x0000000088d00000| PB 0x0000000088c00000| Complete 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%|HC|  |TAMS 0x0000000088e00000| PB 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%|HC|  |TAMS 0x0000000088f00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%|HC|  |TAMS 0x0000000089000000| PB 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%|HC|  |TAMS 0x0000000089100000| PB 0x0000000089000000| Complete 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%|HC|  |TAMS 0x0000000089200000| PB 0x0000000089100000| Complete 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%|HS|  |TAMS 0x0000000089300000| PB 0x0000000089200000| Complete 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%|HC|  |TAMS 0x0000000089400000| PB 0x0000000089300000| Complete 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%|HS|  |TAMS 0x0000000089500000| PB 0x0000000089400000| Complete 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%|HC|  |TAMS 0x0000000089600000| PB 0x0000000089500000| Complete 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%|HS|  |TAMS 0x0000000089700000| PB 0x0000000089600000| Complete 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%|HC|  |TAMS 0x0000000089800000| PB 0x0000000089700000| Complete 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%|HS|  |TAMS 0x0000000089b00000| PB 0x0000000089a00000| Complete 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%|HC|  |TAMS 0x0000000089c00000| PB 0x0000000089b00000| Complete 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%|HS|  |TAMS 0x0000000089e00000| PB 0x0000000089d00000| Complete 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%|HS|  |TAMS 0x000000008a000000| PB 0x0000000089f00000| Complete 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%|HS|  |TAMS 0x000000008a100000| PB 0x000000008a000000| Complete 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%|HC|  |TAMS 0x000000008a200000| PB 0x000000008a100000| Complete 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%|HS|  |TAMS 0x000000008a300000| PB 0x000000008a200000| Complete 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%|HC|  |TAMS 0x000000008a400000| PB 0x000000008a300000| Complete 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%|HS|  |TAMS 0x000000008a700000| PB 0x000000008a600000| Complete 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%|HC|  |TAMS 0x000000008a800000| PB 0x000000008a700000| Complete 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%|HC|  |TAMS 0x000000008a900000| PB 0x000000008a800000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Updating 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%|HS|  |TAMS 0x000000008b000000| PB 0x000000008af00000| Complete 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%|HC|  |TAMS 0x000000008b100000| PB 0x000000008b000000| Complete 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%|HC|  |TAMS 0x000000008b200000| PB 0x000000008b100000| Complete 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| O|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Updating 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%|HS|  |TAMS 0x000000008ca00000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| O|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| O|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| O|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| O|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| O|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| O|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| O|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| O|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| O|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| O|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| O|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| O|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| O|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| O|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| O|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| O|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| O|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| O|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| O|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| O|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| O|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| O|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| O|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| O|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| O|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| O|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| O|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Updating 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| O|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fbee0c0, 0x000000008fc00000| 92%| O|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090400000, 0x0000000090500000|  0%| F|  |TAMS 0x0000000090400000| PB 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090500000, 0x0000000090600000|  0%| F|  |TAMS 0x0000000090500000| PB 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000| PB 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000| PB 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090800000, 0x0000000090900000|  0%| F|  |TAMS 0x0000000090800000| PB 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000| PB 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000| PB 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000| PB 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000| PB 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090d00000, 0x0000000090e00000|  0%| F|  |TAMS 0x0000000090d00000| PB 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090e00000, 0x0000000090f00000|  0%| F|  |TAMS 0x0000000090e00000| PB 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000090f00000, 0x0000000091000000|  0%| F|  |TAMS 0x0000000090f00000| PB 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091b00000, 0x0000000091c00000|  0%| F|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091d00000, 0x0000000091e00000|  0%| F|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000091f00000, 0x0000000092000000|  0%| F|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092400000, 0x0000000092500000|  0%| F|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092800000, 0x0000000092900000|  0%| F|  |TAMS 0x0000000092800000| PB 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092900000, 0x0000000092a00000|  0%| F|  |TAMS 0x0000000092900000| PB 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092a00000, 0x0000000092b00000|  0%| F|  |TAMS 0x0000000092a00000| PB 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092b00000, 0x0000000092c00000|  0%| F|  |TAMS 0x0000000092b00000| PB 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092c00000, 0x0000000092d00000|  0%| F|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092e00000, 0x0000000092f00000|  0%| F|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000092f00000, 0x0000000093000000|  0%| F|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093100000, 0x0000000093200000|  0%| F|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093200000, 0x0000000093300000|  0%| F|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093300000, 0x0000000093400000|  0%| F|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093400000, 0x0000000093500000|  0%| F|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093500000, 0x0000000093600000|  0%| F|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093600000, 0x0000000093700000|  0%| F|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000093f00000, 0x0000000094000000|  0%| F|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094800000, 0x0000000094900000|  0%| F|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094900000, 0x0000000094a00000|  0%| F|  |TAMS 0x0000000094900000| PB 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000| PB 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094b00000, 0x0000000094c00000|  0%| F|  |TAMS 0x0000000094b00000| PB 0x0000000094b00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094c00000, 0x0000000094d00000|  0%| F|  |TAMS 0x0000000094c00000| PB 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000| PB 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094e00000, 0x0000000094f00000|  0%| F|  |TAMS 0x0000000094e00000| PB 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000| PB 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000| PB 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000| PB 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095200000, 0x0000000095300000|  0%| F|  |TAMS 0x0000000095200000| PB 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095300000, 0x0000000095400000|  0%| F|  |TAMS 0x0000000095300000| PB 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095400000, 0x0000000095500000|  0%| F|  |TAMS 0x0000000095400000| PB 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095500000, 0x0000000095600000|  0%| F|  |TAMS 0x0000000095500000| PB 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095600000, 0x0000000095700000|  0%| F|  |TAMS 0x0000000095600000| PB 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095700000, 0x0000000095800000|  0%| F|  |TAMS 0x0000000095700000| PB 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095800000, 0x0000000095900000|  0%| F|  |TAMS 0x0000000095800000| PB 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095a00000, 0x0000000095b00000|  0%| F|  |TAMS 0x0000000095a00000| PB 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095b00000, 0x0000000095c00000|  0%| F|  |TAMS 0x0000000095b00000| PB 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095c00000, 0x0000000095d00000|  0%| F|  |TAMS 0x0000000095c00000| PB 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095d00000, 0x0000000095e00000|  0%| F|  |TAMS 0x0000000095d00000| PB 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000| PB 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000| PB 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000| PB 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000| PB 0x0000000096100000| Untracked 
| 354|0x0000000096200000, 0x0000000096200000, 0x0000000096300000|  0%| F|  |TAMS 0x0000000096200000| PB 0x0000000096200000| Untracked 
| 355|0x0000000096300000, 0x0000000096300000, 0x0000000096400000|  0%| F|  |TAMS 0x0000000096300000| PB 0x0000000096300000| Untracked 
| 356|0x0000000096400000, 0x0000000096400000, 0x0000000096500000|  0%| F|  |TAMS 0x0000000096400000| PB 0x0000000096400000| Untracked 
| 357|0x0000000096500000, 0x0000000096500000, 0x0000000096600000|  0%| F|  |TAMS 0x0000000096500000| PB 0x0000000096500000| Untracked 
| 358|0x0000000096600000, 0x0000000096600000, 0x0000000096700000|  0%| F|  |TAMS 0x0000000096600000| PB 0x0000000096600000| Untracked 
| 359|0x0000000096700000, 0x0000000096700000, 0x0000000096800000|  0%| F|  |TAMS 0x0000000096700000| PB 0x0000000096700000| Untracked 
| 360|0x0000000096800000, 0x0000000096800000, 0x0000000096900000|  0%| F|  |TAMS 0x0000000096800000| PB 0x0000000096800000| Untracked 
| 361|0x0000000096900000, 0x0000000096900000, 0x0000000096a00000|  0%| F|  |TAMS 0x0000000096900000| PB 0x0000000096900000| Untracked 
| 362|0x0000000096a00000, 0x0000000096a00000, 0x0000000096b00000|  0%| F|  |TAMS 0x0000000096a00000| PB 0x0000000096a00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096b00000, 0x0000000096c00000|  0%| F|  |TAMS 0x0000000096b00000| PB 0x0000000096b00000| Untracked 
| 364|0x0000000096c00000, 0x0000000096c00000, 0x0000000096d00000|  0%| F|  |TAMS 0x0000000096c00000| PB 0x0000000096c00000| Untracked 
| 365|0x0000000096d00000, 0x0000000096d00000, 0x0000000096e00000|  0%| F|  |TAMS 0x0000000096d00000| PB 0x0000000096d00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096e00000, 0x0000000096f00000|  0%| F|  |TAMS 0x0000000096e00000| PB 0x0000000096e00000| Untracked 
| 367|0x0000000096f00000, 0x0000000096f00000, 0x0000000097000000|  0%| F|  |TAMS 0x0000000096f00000| PB 0x0000000096f00000| Untracked 
| 368|0x0000000097000000, 0x0000000097000000, 0x0000000097100000|  0%| F|  |TAMS 0x0000000097000000| PB 0x0000000097000000| Untracked 
| 369|0x0000000097100000, 0x0000000097100000, 0x0000000097200000|  0%| F|  |TAMS 0x0000000097100000| PB 0x0000000097100000| Untracked 
| 370|0x0000000097200000, 0x0000000097200000, 0x0000000097300000|  0%| F|  |TAMS 0x0000000097200000| PB 0x0000000097200000| Untracked 
| 371|0x0000000097300000, 0x0000000097300000, 0x0000000097400000|  0%| F|  |TAMS 0x0000000097300000| PB 0x0000000097300000| Untracked 
| 372|0x0000000097400000, 0x0000000097400000, 0x0000000097500000|  0%| F|  |TAMS 0x0000000097400000| PB 0x0000000097400000| Untracked 
| 373|0x0000000097500000, 0x0000000097500000, 0x0000000097600000|  0%| F|  |TAMS 0x0000000097500000| PB 0x0000000097500000| Untracked 
| 374|0x0000000097600000, 0x0000000097600000, 0x0000000097700000|  0%| F|  |TAMS 0x0000000097600000| PB 0x0000000097600000| Untracked 
| 375|0x0000000097700000, 0x0000000097700000, 0x0000000097800000|  0%| F|  |TAMS 0x0000000097700000| PB 0x0000000097700000| Untracked 
| 376|0x0000000097800000, 0x0000000097800000, 0x0000000097900000|  0%| F|  |TAMS 0x0000000097800000| PB 0x0000000097800000| Untracked 
| 377|0x0000000097900000, 0x0000000097900000, 0x0000000097a00000|  0%| F|  |TAMS 0x0000000097900000| PB 0x0000000097900000| Untracked 
| 378|0x0000000097a00000, 0x0000000097a00000, 0x0000000097b00000|  0%| F|  |TAMS 0x0000000097a00000| PB 0x0000000097a00000| Untracked 
| 379|0x0000000097b00000, 0x0000000097b00000, 0x0000000097c00000|  0%| F|  |TAMS 0x0000000097b00000| PB 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097c00000, 0x0000000097d00000|  0%| F|  |TAMS 0x0000000097c00000| PB 0x0000000097c00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097d00000, 0x0000000097e00000|  0%| F|  |TAMS 0x0000000097d00000| PB 0x0000000097d00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097e00000, 0x0000000097f00000|  0%| F|  |TAMS 0x0000000097e00000| PB 0x0000000097e00000| Untracked 
| 383|0x0000000097f00000, 0x0000000097f00000, 0x0000000098000000|  0%| F|  |TAMS 0x0000000097f00000| PB 0x0000000097f00000| Untracked 
| 384|0x0000000098000000, 0x0000000098000000, 0x0000000098100000|  0%| F|  |TAMS 0x0000000098000000| PB 0x0000000098000000| Untracked 
| 385|0x0000000098100000, 0x0000000098100000, 0x0000000098200000|  0%| F|  |TAMS 0x0000000098100000| PB 0x0000000098100000| Untracked 
| 386|0x0000000098200000, 0x0000000098200000, 0x0000000098300000|  0%| F|  |TAMS 0x0000000098200000| PB 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098300000, 0x0000000098400000|  0%| F|  |TAMS 0x0000000098300000| PB 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098400000, 0x0000000098500000|  0%| F|  |TAMS 0x0000000098400000| PB 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098500000, 0x0000000098600000|  0%| F|  |TAMS 0x0000000098500000| PB 0x0000000098500000| Untracked 
| 390|0x0000000098600000, 0x0000000098600000, 0x0000000098700000|  0%| F|  |TAMS 0x0000000098600000| PB 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098700000, 0x0000000098800000|  0%| F|  |TAMS 0x0000000098700000| PB 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098800000, 0x0000000098900000|  0%| F|  |TAMS 0x0000000098800000| PB 0x0000000098800000| Untracked 
| 393|0x0000000098900000, 0x0000000098900000, 0x0000000098a00000|  0%| F|  |TAMS 0x0000000098900000| PB 0x0000000098900000| Untracked 
| 394|0x0000000098a00000, 0x0000000098a00000, 0x0000000098b00000|  0%| F|  |TAMS 0x0000000098a00000| PB 0x0000000098a00000| Untracked 
| 395|0x0000000098b00000, 0x0000000098b00000, 0x0000000098c00000|  0%| F|  |TAMS 0x0000000098b00000| PB 0x0000000098b00000| Untracked 
| 396|0x0000000098c00000, 0x0000000098c00000, 0x0000000098d00000|  0%| F|  |TAMS 0x0000000098c00000| PB 0x0000000098c00000| Untracked 
| 397|0x0000000098d00000, 0x0000000098d00000, 0x0000000098e00000|  0%| F|  |TAMS 0x0000000098d00000| PB 0x0000000098d00000| Untracked 
| 398|0x0000000098e00000, 0x0000000098e00000, 0x0000000098f00000|  0%| F|  |TAMS 0x0000000098e00000| PB 0x0000000098e00000| Untracked 
| 399|0x0000000098f00000, 0x0000000098f00000, 0x0000000099000000|  0%| F|  |TAMS 0x0000000098f00000| PB 0x0000000098f00000| Untracked 
| 400|0x0000000099000000, 0x0000000099000000, 0x0000000099100000|  0%| F|  |TAMS 0x0000000099000000| PB 0x0000000099000000| Untracked 
| 401|0x0000000099100000, 0x0000000099100000, 0x0000000099200000|  0%| F|  |TAMS 0x0000000099100000| PB 0x0000000099100000| Untracked 
| 402|0x0000000099200000, 0x0000000099200000, 0x0000000099300000|  0%| F|  |TAMS 0x0000000099200000| PB 0x0000000099200000| Untracked 
| 403|0x0000000099300000, 0x0000000099300000, 0x0000000099400000|  0%| F|  |TAMS 0x0000000099300000| PB 0x0000000099300000| Untracked 
| 404|0x0000000099400000, 0x0000000099400000, 0x0000000099500000|  0%| F|  |TAMS 0x0000000099400000| PB 0x0000000099400000| Untracked 
| 405|0x0000000099500000, 0x0000000099500000, 0x0000000099600000|  0%| F|  |TAMS 0x0000000099500000| PB 0x0000000099500000| Untracked 
| 406|0x0000000099600000, 0x0000000099600000, 0x0000000099700000|  0%| F|  |TAMS 0x0000000099600000| PB 0x0000000099600000| Untracked 
| 407|0x0000000099700000, 0x0000000099700000, 0x0000000099800000|  0%| F|  |TAMS 0x0000000099700000| PB 0x0000000099700000| Untracked 
| 408|0x0000000099800000, 0x0000000099800000, 0x0000000099900000|  0%| F|  |TAMS 0x0000000099800000| PB 0x0000000099800000| Untracked 
| 409|0x0000000099900000, 0x0000000099900000, 0x0000000099a00000|  0%| F|  |TAMS 0x0000000099900000| PB 0x0000000099900000| Untracked 
| 410|0x0000000099a00000, 0x0000000099a00000, 0x0000000099b00000|  0%| F|  |TAMS 0x0000000099a00000| PB 0x0000000099a00000| Untracked 
| 411|0x0000000099b00000, 0x0000000099b00000, 0x0000000099c00000|  0%| F|  |TAMS 0x0000000099b00000| PB 0x0000000099b00000| Untracked 
| 412|0x0000000099c00000, 0x0000000099c00000, 0x0000000099d00000|  0%| F|  |TAMS 0x0000000099c00000| PB 0x0000000099c00000| Untracked 
| 413|0x0000000099d00000, 0x0000000099d00000, 0x0000000099e00000|  0%| F|  |TAMS 0x0000000099d00000| PB 0x0000000099d00000| Untracked 
| 414|0x0000000099e00000, 0x0000000099e00000, 0x0000000099f00000|  0%| F|  |TAMS 0x0000000099e00000| PB 0x0000000099e00000| Untracked 
| 415|0x0000000099f00000, 0x0000000099f00000, 0x000000009a000000|  0%| F|  |TAMS 0x0000000099f00000| PB 0x0000000099f00000| Untracked 
| 416|0x000000009a000000, 0x000000009a000000, 0x000000009a100000|  0%| F|  |TAMS 0x000000009a000000| PB 0x000000009a000000| Untracked 
| 417|0x000000009a100000, 0x000000009a100000, 0x000000009a200000|  0%| F|  |TAMS 0x000000009a100000| PB 0x000000009a100000| Untracked 
| 418|0x000000009a200000, 0x000000009a200000, 0x000000009a300000|  0%| F|  |TAMS 0x000000009a200000| PB 0x000000009a200000| Untracked 
| 419|0x000000009a300000, 0x000000009a300000, 0x000000009a400000|  0%| F|  |TAMS 0x000000009a300000| PB 0x000000009a300000| Untracked 
| 420|0x000000009a400000, 0x000000009a400000, 0x000000009a500000|  0%| F|  |TAMS 0x000000009a400000| PB 0x000000009a400000| Untracked 
| 421|0x000000009a500000, 0x000000009a500000, 0x000000009a600000|  0%| F|  |TAMS 0x000000009a500000| PB 0x000000009a500000| Untracked 
| 422|0x000000009a600000, 0x000000009a600000, 0x000000009a700000|  0%| F|  |TAMS 0x000000009a600000| PB 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a700000, 0x000000009a800000|  0%| F|  |TAMS 0x000000009a700000| PB 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a800000, 0x000000009a900000|  0%| F|  |TAMS 0x000000009a800000| PB 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000| PB 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000| PB 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ab00000, 0x000000009ac00000|  0%| F|  |TAMS 0x000000009ab00000| PB 0x000000009ab00000| Untracked 
| 428|0x000000009ac00000, 0x000000009ac00000, 0x000000009ad00000|  0%| F|  |TAMS 0x000000009ac00000| PB 0x000000009ac00000| Untracked 
| 429|0x000000009ad00000, 0x000000009ad00000, 0x000000009ae00000|  0%| F|  |TAMS 0x000000009ad00000| PB 0x000000009ad00000| Untracked 
| 430|0x000000009ae00000, 0x000000009ae00000, 0x000000009af00000|  0%| F|  |TAMS 0x000000009ae00000| PB 0x000000009ae00000| Untracked 
| 431|0x000000009af00000, 0x000000009af00000, 0x000000009b000000|  0%| F|  |TAMS 0x000000009af00000| PB 0x000000009af00000| Untracked 
| 432|0x000000009b000000, 0x000000009b100000, 0x000000009b100000|100%| S|CS|TAMS 0x000000009b000000| PB 0x000000009b000000| Complete 
| 433|0x000000009b100000, 0x000000009b200000, 0x000000009b200000|100%| S|CS|TAMS 0x000000009b100000| PB 0x000000009b100000| Complete 
| 434|0x000000009b200000, 0x000000009b300000, 0x000000009b300000|100%| S|CS|TAMS 0x000000009b200000| PB 0x000000009b200000| Complete 
| 435|0x000000009b300000, 0x000000009b300000, 0x000000009b400000|  0%| F|  |TAMS 0x000000009b300000| PB 0x000000009b300000| Untracked 
| 436|0x000000009b400000, 0x000000009b400000, 0x000000009b500000|  0%| F|  |TAMS 0x000000009b400000| PB 0x000000009b400000| Untracked 
| 437|0x000000009b500000, 0x000000009b500000, 0x000000009b600000|  0%| F|  |TAMS 0x000000009b500000| PB 0x000000009b500000| Untracked 
| 438|0x000000009b600000, 0x000000009b600000, 0x000000009b700000|  0%| F|  |TAMS 0x000000009b600000| PB 0x000000009b600000| Untracked 
| 439|0x000000009b700000, 0x000000009b700000, 0x000000009b800000|  0%| F|  |TAMS 0x000000009b700000| PB 0x000000009b700000| Untracked 
| 440|0x000000009b800000, 0x000000009b800000, 0x000000009b900000|  0%| F|  |TAMS 0x000000009b800000| PB 0x000000009b800000| Untracked 
|2020|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| O|  |TAMS 0x00000000fe4f6780| PB 0x00000000fe400000| Updating 
|2021|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| O|  |TAMS 0x00000000fe600000| PB 0x00000000fe500000| Updating 
|2022|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| O|  |TAMS 0x00000000fe700000| PB 0x00000000fe600000| Updating 
|2023|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| O|  |TAMS 0x00000000fe800000| PB 0x00000000fe700000| Updating 
|2024|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| O|  |TAMS 0x00000000fe900000| PB 0x00000000fe800000| Updating 
|2025|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| O|  |TAMS 0x00000000fea00000| PB 0x00000000fe900000| Untracked 
|2026|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| O|  |TAMS 0x00000000feb00000| PB 0x00000000fea00000| Updating 
|2027|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| O|  |TAMS 0x00000000fec00000| PB 0x00000000feb00000| Untracked 
|2028|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| O|  |TAMS 0x00000000fed00000| PB 0x00000000fec00000| Untracked 
|2029|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| O|  |TAMS 0x00000000fee00000| PB 0x00000000fed00000| Untracked 
|2030|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| O|  |TAMS 0x00000000fef00000| PB 0x00000000fee00000| Untracked 
|2031|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| O|  |TAMS 0x00000000ff000000| PB 0x00000000fef00000| Updating 
|2032|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| O|  |TAMS 0x00000000ff100000| PB 0x00000000ff000000| Updating 
|2033|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| O|  |TAMS 0x00000000ff200000| PB 0x00000000ff100000| Untracked 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| O|  |TAMS 0x00000000ff300000| PB 0x00000000ff200000| Untracked 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff300000| Untracked 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff400000| Untracked 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff500000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff600000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff700000| Updating 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff800000| Untracked 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffb00000, 0x00000000ffc00000|  0%| F|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffc00000, 0x00000000ffd00000|  0%| F|  |TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffd00000| Updating 
|2046|0x00000000ffe00000, 0x00000000ffe00000, 0x00000000fff00000|  0%| F|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x00000000fff00000, 0x0000000100000000|  0%| F|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x0000016d7c3b0000,0x0000016d7c7b0000] _byte_map_base: 0x0000016d7bfb0000

Marking Bits: (CMBitMap*) 0x0000016d73870030
 Bits: [0x0000016d7c7b0000, 0x0000016d7e7b0000)

Polling page: 0x0000016d71700000

Metaspace:

Usage:
  Non-class:    102.14 MB used.
      Class:     16.37 MB used.
       Both:    118.52 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     103.31 MB ( 81%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      17.50 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     120.81 MB ( 10%) committed. 

Chunk freelists:
   Non-Class:  7.97 MB
       Class:  14.33 MB
        Both:  22.30 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 201.38 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 4124.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1932.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 9557.
num_chunk_merges: 12.
num_chunk_splits: 6340.
num_chunks_enlarged: 4150.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=8729Kb max_used=9238Kb free=111270Kb
 bounds [0x0000016d07ad0000, 0x0000016d083e0000, 0x0000016d0f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=27316Kb max_used=27946Kb free=92683Kb
 bounds [0x0000016d00000000, 0x0000016d01b50000, 0x0000016d07530000]
CodeHeap 'non-nmethods': size=5760Kb used=3008Kb max_used=3037Kb free=2751Kb
 bounds [0x0000016d07530000, 0x0000016d07830000, 0x0000016d07ad0000]
 total_blobs=15527 nmethods=14486 adapters=942
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 22.022 Thread 0x0000016d7f2e7b60 nmethod 17409 0x0000016d07c63f10 code [0x0000016d07c640a0, 0x0000016d07c643e0]
Event: 22.022 Thread 0x0000016d7f2e7b60 17428       4       com.android.tools.r8.internal.o30::put (259 bytes)
Event: 22.029 Thread 0x0000016d7f30a7d0 17429       3       com.android.tools.r8.internal.Xo::<init> (24 bytes)
Event: 22.029 Thread 0x0000016d7f30a7d0 nmethod 17429 0x0000016d0001c090 code [0x0000016d0001c260, 0x0000016d0001c4e8]
Event: 22.031 Thread 0x0000016d7f30a7d0 17430       3       com.android.tools.r8.internal.Vo::hashCode (13 bytes)
Event: 22.031 Thread 0x0000016d7f30a7d0 nmethod 17430 0x0000016d00291590 code [0x0000016d00291740, 0x0000016d00291950]
Event: 22.039 Thread 0x0000016d7f30a7d0 17431       3       com.android.tools.r8.graph.t3$$Lambda/0x0000016d130835f8::accept (20 bytes)
Event: 22.039 Thread 0x0000016d7f30a7d0 nmethod 17431 0x0000016d00393390 code [0x0000016d00393580, 0x0000016d00393ad0]
Event: 22.039 Thread 0x0000016d7f30a7d0 17432       3       com.android.tools.r8.graph.t3$$Lambda/0x0000016d13083b60::test (8 bytes)
Event: 22.040 Thread 0x0000016d7f30a7d0 nmethod 17432 0x0000016d009f0290 code [0x0000016d009f0460, 0x0000016d009f08b8]
Event: 22.043 Thread 0x0000016d7f2e7b60 nmethod 17428 0x0000016d07e54f90 code [0x0000016d07e55200, 0x0000016d07e55f58]
Event: 22.044 Thread 0x0000016d7f2e7b60 17399       4       com.android.tools.r8.internal.Zo::hashCode (21 bytes)
Event: 22.044 Thread 0x0000016d7f2e7b60 nmethod 17399 0x0000016d07c5a510 code [0x0000016d07c5a6a0, 0x0000016d07c5a7a0]
Event: 22.044 Thread 0x0000016d7f2e7b60 17400       4       com.android.tools.r8.internal.Zo::<init> (21 bytes)
Event: 22.047 Thread 0x0000016d7f2e7b60 nmethod 17400 0x0000016d07c54510 code [0x0000016d07c546c0, 0x0000016d07c54898]
Event: 22.047 Thread 0x0000016d7f2e7b60 17403       4       com.android.tools.r8.dex.C::b (36 bytes)
Event: 22.047 Thread 0x0000016d7f30a7d0 17433       2       com.android.tools.r8.internal.ms::hashCode (35 bytes)
Event: 22.047 Thread 0x0000016d7f30a7d0 nmethod 17433 0x0000016d000dc290 code [0x0000016d000dc440, 0x0000016d000dc5b0]
Event: 22.048 Thread 0x0000016d7f2e7b60 nmethod 17403 0x0000016d07eeae10 code [0x0000016d07eeafc0, 0x0000016d07eeb130]
Event: 22.048 Thread 0x0000016d7f2e7b60 17434       4       com.android.tools.r8.dex.C::b (267 bytes)

GC Heap History (20 events):
Event: 16.778 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 196608K, used 118264K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 103012K, committed 105280K, reserved 1179648K
  class space    used 14093K, committed 15232K, reserved 1048576K
}
Event: 17.255 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 225280K, used 174584K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 65 young (66560K), 9 survivors (9216K)
 Metaspace       used 103557K, committed 105728K, reserved 1179648K
  class space    used 14166K, committed 15232K, reserved 1048576K
}
Event: 17.264 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 225280K, used 121285K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 103557K, committed 105728K, reserved 1179648K
  class space    used 14166K, committed 15232K, reserved 1048576K
}
Event: 18.157 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 225280K, used 202181K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 83 young (84992K), 5 survivors (5120K)
 Metaspace       used 105829K, committed 108032K, reserved 1179648K
  class space    used 14488K, committed 15552K, reserved 1048576K
}
Event: 18.167 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 225280K, used 125203K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 105829K, committed 108032K, reserved 1179648K
  class space    used 14488K, committed 15552K, reserved 1048576K
}
Event: 19.169 GC heap before
{Heap before GC invocations=39 (full 0):
 garbage-first heap   total 225280K, used 203027K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 8 survivors (8192K)
 Metaspace       used 115151K, committed 117440K, reserved 1179648K
  class space    used 15843K, committed 16960K, reserved 1048576K
}
Event: 19.176 GC heap after
{Heap after GC invocations=40 (full 0):
 garbage-first heap   total 225280K, used 127603K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 115151K, committed 117440K, reserved 1179648K
  class space    used 15843K, committed 16960K, reserved 1048576K
}
Event: 20.498 GC heap before
{Heap before GC invocations=41 (full 0):
 garbage-first heap   total 226304K, used 223859K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 75 young (76800K), 8 survivors (8192K)
 Metaspace       used 120141K, committed 122432K, reserved 1179648K
  class space    used 16584K, committed 17728K, reserved 1048576K
}
Event: 20.505 GC heap after
{Heap after GC invocations=42 (full 0):
 garbage-first heap   total 239616K, used 149044K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 120141K, committed 122432K, reserved 1179648K
  class space    used 16584K, committed 17728K, reserved 1048576K
}
Event: 20.811 GC heap before
{Heap before GC invocations=42 (full 0):
 garbage-first heap   total 239616K, used 238132K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 48 young (49152K), 10 survivors (10240K)
 Metaspace       used 120636K, committed 122944K, reserved 1179648K
  class space    used 16655K, committed 17792K, reserved 1048576K
}
Event: 20.824 GC heap after
{Heap after GC invocations=43 (full 0):
 garbage-first heap   total 268288K, used 188416K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 120636K, committed 122944K, reserved 1179648K
  class space    used 16655K, committed 17792K, reserved 1048576K
}
Event: 20.968 GC heap before
{Heap before GC invocations=43 (full 0):
 garbage-first heap   total 268288K, used 212992K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 35 young (35840K), 10 survivors (10240K)
 Metaspace       used 120637K, committed 122944K, reserved 1179648K
  class space    used 16655K, committed 17792K, reserved 1048576K
}
Event: 20.982 GC heap after
{Heap after GC invocations=44 (full 0):
 garbage-first heap   total 322560K, used 198321K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 120637K, committed 122944K, reserved 1179648K
  class space    used 16655K, committed 17792K, reserved 1048576K
}
Event: 21.502 GC heap before
{Heap before GC invocations=44 (full 0):
 garbage-first heap   total 322560K, used 282289K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 8 survivors (8192K)
 Metaspace       used 121329K, committed 123648K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K
}
Event: 21.535 GC heap after
{Heap after GC invocations=45 (full 0):
 garbage-first heap   total 323584K, used 236505K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 121329K, committed 123648K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K
}
Event: 21.712 GC heap before
{Heap before GC invocations=45 (full 0):
 garbage-first heap   total 323584K, used 283609K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 57 young (58368K), 11 survivors (11264K)
 Metaspace       used 121357K, committed 123712K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K
}
Event: 21.741 GC heap after
{Heap after GC invocations=46 (full 0):
 garbage-first heap   total 323584K, used 261120K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 121357K, committed 123712K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K
}
Event: 21.924 GC heap before
{Heap before GC invocations=46 (full 0):
 garbage-first heap   total 323584K, used 289792K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 37 young (37888K), 8 survivors (8192K)
 Metaspace       used 121360K, committed 123712K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K
}
Event: 21.940 GC heap after
{Heap after GC invocations=47 (full 0):
 garbage-first heap   total 323584K, used 276992K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 121360K, committed 123712K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K
}
Event: 22.050 GC heap before
{Heap before GC invocations=47 (full 0):
 garbage-first heap   total 480256K, used 293376K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 23 young (23552K), 5 survivors (5120K)
 Metaspace       used 121362K, committed 123712K, reserved 1179648K
  class space    used 16766K, committed 17920K, reserved 1048576K
}

Dll operation events (16 events):
Event: 0.008 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.041 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.080 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.083 Loaded shared library C:\Program Files\Java\jdk-21\bin\instrument.dll
Event: 0.087 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.089 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.092 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.423 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.583 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll
Event: 0.707 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 0.711 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 1.656 Loaded shared library C:\Program Files\Java\jdk-21\bin\management.dll
Event: 1.658 Loaded shared library C:\Program Files\Java\jdk-21\bin\management_ext.dll
Event: 1.817 Loaded shared library C:\Program Files\Java\jdk-21\bin\extnet.dll
Event: 1.926 Loaded shared library C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
Event: 2.446 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform15007280802409166571dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 21.988 Thread 0x0000016d56c38d80 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000016d083c4f70 relative=0x0000000000000d70
Event: 21.988 Thread 0x0000016d56c38d80 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000016d083c4f70 method=com.android.tools.r8.dex.C.h()Lcom/android/tools/r8/graph/R2; @ 47 c2
Event: 21.988 Thread 0x0000016d56c38d80 DEOPT PACKING pc=0x0000016d083c4f70 sp=0x000000470adfeb10
Event: 21.988 Thread 0x0000016d56c38d80 DEOPT UNPACKING pc=0x0000016d075846a2 sp=0x000000470adfeac0 mode 2
Event: 22.045 Thread 0x0000016d56c3a130 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000016d07e55c94 relative=0x0000000000000a94
Event: 22.045 Thread 0x0000016d56c3a130 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000016d07e55c94 method=com.android.tools.r8.internal.o30.put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 92 c2
Event: 22.045 Thread 0x0000016d56c3a130 DEOPT PACKING pc=0x0000016d07e55c94 sp=0x000000470acfe7d0
Event: 22.045 Thread 0x0000016d56c3a130 DEOPT UNPACKING pc=0x0000016d075846a2 sp=0x000000470acfe788 mode 2
Event: 22.045 Thread 0x0000016d56c3a130 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000016d07e55c94 relative=0x0000000000000a94
Event: 22.045 Thread 0x0000016d56c3a130 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000016d07e55c94 method=com.android.tools.r8.internal.o30.put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 92 c2
Event: 22.045 Thread 0x0000016d56c3a130 DEOPT PACKING pc=0x0000016d07e55c94 sp=0x000000470acfe7d0
Event: 22.045 Thread 0x0000016d56c3a130 DEOPT UNPACKING pc=0x0000016d075846a2 sp=0x000000470acfe788 mode 2
Event: 22.050 Thread 0x0000016d56c3a130 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000016d07e55c94 relative=0x0000000000000a94
Event: 22.050 Thread 0x0000016d56c3a130 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000016d07e55c94 method=com.android.tools.r8.internal.o30.put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 92 c2
Event: 22.050 Thread 0x0000016d56c3a130 DEOPT PACKING pc=0x0000016d07e55c94 sp=0x000000470acfe7d0
Event: 22.050 Thread 0x0000016d56c3a130 DEOPT UNPACKING pc=0x0000016d075846a2 sp=0x000000470acfe788 mode 2
Event: 22.050 Thread 0x0000016d56c3a130 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000016d07e55c94 relative=0x0000000000000a94
Event: 22.050 Thread 0x0000016d56c3a130 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000016d07e55c94 method=com.android.tools.r8.internal.o30.put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 92 c2
Event: 22.050 Thread 0x0000016d56c3a130 DEOPT PACKING pc=0x0000016d07e55c94 sp=0x000000470acfe7d0
Event: 22.050 Thread 0x0000016d56c3a130 DEOPT UNPACKING pc=0x0000016d075846a2 sp=0x000000470acfe788 mode 2

Classes loaded (20 events):
Event: 20.225 Loading class java/nio/ByteBufferAsShortBufferL
Event: 20.225 Loading class java/nio/ByteBufferAsShortBufferL done
Event: 20.244 Loading class java/util/zip/Adler32
Event: 20.244 Loading class java/util/zip/Adler32 done
Event: 20.246 Loading class java/nio/file/Files$2
Event: 20.247 Loading class java/nio/file/Files$2 done
Event: 20.329 Loading class java/nio/file/FileTreeIterator
Event: 20.329 Loading class java/nio/file/FileTreeIterator done
Event: 20.630 Loading class java/nio/BufferUnderflowException
Event: 20.630 Loading class java/nio/BufferUnderflowException done
Event: 21.328 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable
Event: 21.328 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable done
Event: 21.336 Loading class java/util/AbstractList$RandomAccessSubList
Event: 21.336 Loading class java/util/AbstractList$SubList
Event: 21.336 Loading class java/util/AbstractList$SubList done
Event: 21.336 Loading class java/util/AbstractList$RandomAccessSubList done
Event: 21.336 Loading class java/util/AbstractList$SubList$1
Event: 21.337 Loading class java/util/AbstractList$SubList$1 done
Event: 21.360 Loading class java/nio/HeapShortBuffer
Event: 21.360 Loading class java/nio/HeapShortBuffer done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 19.028 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x00000000896cf808}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000896cf808) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.106 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x0000000088f82580}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000088f82580) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.193 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fffc5dd0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000fffc5dd0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.258 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x000000009083b370}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000009083b370) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.361 Thread 0x0000016d57a551d0 Implicit null exception at 0x0000016d08322194 to 0x0000016d083236b4
Event: 19.361 Thread 0x0000016d57a551d0 Implicit null exception at 0x0000016d07bf2b6e to 0x0000016d07bf3248
Event: 19.364 Thread 0x0000016d59368c50 Exception <a 'sun/nio/fs/WindowsException'{0x000000009058ef88}> (0x000000009058ef88) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 19.688 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c05f4b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x000000008c05f4b8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.806 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x000000008bb58228}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x000000008bb58228) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 19.813 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x000000008bb89088}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x000000008bb89088) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 20.092 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x000000008adbe3c8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008adbe3c8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 20.094 Thread 0x0000016d586e5090 Exception <a 'java/lang/NoSuchMethodError'{0x000000008ade2120}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008ade2120) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 20.180 Thread 0x0000016d586e5090 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008a667610}: Found class java.lang.Object, but interface was expected> (0x000000008a667610) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 20.248 Thread 0x0000016d586e5090 Exception <a 'sun/nio/fs/WindowsException'{0x000000008a3fd238}> (0x000000008a3fd238) 
thrown [s\open\src\hotspot\share\prims\jni.cpp, line 520]
Event: 21.329 Thread 0x0000016d56c37340 Implicit null exception at 0x0000016d08325bad to 0x0000016d083264fc
Event: 21.333 Thread 0x0000016d56c33ec0 Implicit null exception at 0x0000016d07ea9b0b to 0x0000016d07ea9bcc
Event: 21.337 Thread 0x0000016d56c32b10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008edea730}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008edea730) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 21.337 Thread 0x0000016d56c33ec0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008ec03c58}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008ec03c58) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 21.337 Thread 0x0000016d56c38d80 Exception <a 'java/lang/NoSuchMethodError'{0x000000008edb3ab0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008edb3ab0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 21.363 Thread 0x0000016d56c39410 Implicit null exception at 0x0000016d07b4eb87 to 0x0000016d07b4ec4c

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 20.181 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 20.497 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 20.505 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 20.811 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 20.824 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 20.968 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 20.983 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 21.362 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 21.362 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 21.502 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 21.548 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 21.593 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 21.597 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 21.711 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 21.750 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 21.918 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 21.948 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 21.994 Executing VM operation: G1PauseRemark
Event: 22.017 Executing VM operation: G1PauseRemark done
Event: 22.050 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Events (20 events):
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07ef0590
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07ed5210
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07ea6d90
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07e23f10
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07eb3d10
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07e5b610
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07e54f90
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07d56910
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07e17a10
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07dffe10
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07d30e10
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07d41e90
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07cbe010
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07d3ce90
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07c63f10
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07cc7490
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07c5a510
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07c54510
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07b6a390
Event: 22.010 Thread 0x0000016d7f22cb10 flushing nmethod 0x0000016d07b77610


Dynamic libraries:
0x00007ff795900000 - 0x00007ff795910000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ffa9c8d0000 - 0x00007ffa9cac8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa9a920000 - 0x00007ffa9a9e2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa9a120000 - 0x00007ffa9a416000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa9a420000 - 0x00007ffa9a520000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa85870000 - 0x00007ffa8588b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ffa88020000 - 0x00007ffa88039000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ffa9baf0000 - 0x00007ffa9bba1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa9b2f0000 - 0x00007ffa9b38e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa9c7c0000 - 0x00007ffa9c85f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa9c070000 - 0x00007ffa9c193000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa9a750000 - 0x00007ffa9a777000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffa9bec0000 - 0x00007ffa9c05d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa9a680000 - 0x00007ffa9a6a2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa890c0000 - 0x00007ffa8935a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ffa9c860000 - 0x00007ffa9c88b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa9a780000 - 0x00007ffa9a899000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa9a6b0000 - 0x00007ffa9a74d000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa91d10000 - 0x00007ffa91d1a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa9a8f0000 - 0x00007ffa9a91f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa89af0000 - 0x00007ffa89afc000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ffa69300000 - 0x00007ffa6938e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ffa23570000 - 0x00007ffa24287000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ffa9bbc0000 - 0x00007ffa9bc2b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa99dc0000 - 0x00007ffa99e0b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa8ec00000 - 0x00007ffa8ec27000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa99da0000 - 0x00007ffa99db2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa987d0000 - 0x00007ffa987e2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa884d0000 - 0x00007ffa884da000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ffa97db0000 - 0x00007ffa97fb1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa88460000 - 0x00007ffa88494000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa9a090000 - 0x00007ffa9a112000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa70ea0000 - 0x00007ffa70eaf000 	C:\Program Files\Java\jdk-21\bin\instrument.dll
0x00007ffa78650000 - 0x00007ffa7866f000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ffa9aac0000 - 0x00007ffa9b22e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa97fc0000 - 0x00007ffa98763000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa9c1a0000 - 0x00007ffa9c4f3000 	C:\WINDOWS\System32\combase.dll
0x00007ffa998c0000 - 0x00007ffa998eb000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffa9a9f0000 - 0x00007ffa9aabd000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa9c710000 - 0x00007ffa9c7bd000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa9c6a0000 - 0x00007ffa9c6fb000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffa99e90000 - 0x00007ffa99eb5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa67360000 - 0x00007ffa67437000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007ffa72ef0000 - 0x00007ffa72f08000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ffa85220000 - 0x00007ffa85230000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ffa96140000 - 0x00007ffa9624a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa99620000 - 0x00007ffa9968a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa76a50000 - 0x00007ffa76a66000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ffa78640000 - 0x00007ffa78650000 	C:\Program Files\Java\jdk-21\bin\verify.dll
0x00007ffa68a40000 - 0x00007ffa68a67000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x0000000063650000 - 0x00000000636c3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffa76a40000 - 0x00007ffa76a4a000 	C:\Program Files\Java\jdk-21\bin\management.dll
0x00007ffa74c10000 - 0x00007ffa74c1b000 	C:\Program Files\Java\jdk-21\bin\management_ext.dll
0x00007ffa9bbb0000 - 0x00007ffa9bbb8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa99810000 - 0x00007ffa99828000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa98f40000 - 0x00007ffa98f78000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa99e10000 - 0x00007ffa99e3e000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa99830000 - 0x00007ffa9983c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa99300000 - 0x00007ffa9933b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa9c700000 - 0x00007ffa9c708000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa70ec0000 - 0x00007ffa70ec9000 	C:\Program Files\Java\jdk-21\bin\extnet.dll
0x00007ffa70ba0000 - 0x00007ffa70bae000 	C:\Program Files\Java\jdk-21\bin\sunmscapi.dll
0x00007ffa9a520000 - 0x00007ffa9a67d000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa99930000 - 0x00007ffa99957000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa998f0000 - 0x00007ffa9992b000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa78c40000 - 0x00007ffa78c47000 	C:\WINDOWS\system32\wshunix.dll
0x0000000063550000 - 0x00000000635c3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform15007280802409166571dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform15007280802409166571dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-bin\cetblhg4pflnnks72fxwobvgv\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-bin\cetblhg4pflnnks72fxwobvgv\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Python313\Scripts;C:\Program Files\Python313;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
USERNAME=ntc
SHELL=C:\Program Files\Git\usr\bin\bash.exe
DISPLAY=needs-to-be-defined
LANG=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 7:10 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4116M free)
TotalPageFile size 22476M (AvailPageFile size 245M)
current process WorkingSet (physical memory assigned to process): 710M, peak: 712M
current process commit charge ("private bytes"): 906M, peak: 1222M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
