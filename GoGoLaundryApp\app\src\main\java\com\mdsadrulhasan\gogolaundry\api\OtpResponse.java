package com.mdsadrulhasan.gogolaundry.api;

import com.google.gson.annotations.SerializedName;

/**
 * OTP response model
 */
public class OtpResponse {
    @SerializedName("expires_in")
    private int expiresIn;
    
    private String phone;
    
    @SerializedName("remaining_time")
    private Integer remainingTime;

    public int getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(int expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getRemainingTime() {
        return remainingTime;
    }

    public void setRemainingTime(Integer remainingTime) {
        this.remainingTime = remainingTime;
    }
}
