<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Profile Header Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/profile_header_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:gravity="center_horizontal">

                <!-- Profile Image Container -->
                <FrameLayout
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginBottom="16dp">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:id="@+id/profile_image"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/ic_person"
                        app:shapeAppearanceOverlay="@style/CircleImageView"
                        app:strokeColor="@color/primary"
                        app:strokeWidth="3dp" />

                    <!-- Edit Button -->
                    <com.google.android.material.floatingactionbutton.FloatingActionButton
                        android:id="@+id/edit_profile_image"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom|end"
                        android:layout_marginEnd="4dp"
                        android:layout_marginBottom="4dp"
                        android:src="@drawable/ic_edit"
                        app:fabSize="mini"
                        app:tint="@android:color/white"
                        app:backgroundTint="@color/primary" />

                </FrameLayout>

                <!-- User Name -->
                <TextView
                    android:id="@+id/user_name_display"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="John Doe"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <!-- User Phone -->
                <TextView
                    android:id="@+id/user_phone_display"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+880 1234567890"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/text_secondary"
                    android:drawableStart="@drawable/ic_phone"
                    android:drawablePadding="8dp"
                    android:drawableTint="@color/text_secondary" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Menu Items Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- Edit Profile -->
                <LinearLayout
                    android:id="@+id/menu_edit_profile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_edit"
                        app:tint="@color/primary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Edit Profile"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginStart="56dp" />

                <!-- My Orders -->
                <LinearLayout
                    android:id="@+id/menu_my_orders"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_receipt"
                        app:tint="@color/primary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="My Orders"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginStart="56dp" />

                <!-- Payment Methods -->
                <LinearLayout
                    android:id="@+id/menu_payment_methods"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_payment"
                        app:tint="@color/primary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Payment Methods"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginStart="56dp" />

                <!-- Addresses -->
                <LinearLayout
                    android:id="@+id/menu_addresses"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_location"
                        app:tint="@color/primary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="My Addresses"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginStart="56dp" />

                <!-- Settings -->
                <LinearLayout
                    android:id="@+id/menu_settings"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_settings"
                        app:tint="@color/primary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Settings"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginStart="56dp" />

                <!-- Help & Support -->
                <LinearLayout
                    android:id="@+id/menu_help_support"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_help"
                        app:tint="@color/primary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Help &amp; Support"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/divider"
                    android:layout_marginStart="56dp" />

                <!-- About -->
                <LinearLayout
                    android:id="@+id/menu_about"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_info"
                        app:tint="@color/primary"
                        android:layout_marginEnd="16dp" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="About"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                        android:textColor="@color/text_primary" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/text_secondary" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Logout Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:id="@+id/menu_logout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical"
                android:background="?attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_logout"
                    app:tint="@color/error"
                    android:layout_marginEnd="16dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Logout"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textColor="@color/error"
                    android:textStyle="bold" />

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="@color/error" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
