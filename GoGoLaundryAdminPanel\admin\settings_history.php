<?php
/**
 * Settings History Page
 *
 * This page displays the history of settings changes
 */

// Include authentication middleware
require_once 'auth.php';

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$perPage = 20;
$offset = ($page - 1) * $perPage;

// Build query
$params = [];
$whereClause = '';

if (!empty($search)) {
    $whereClause = " WHERE setting_key LIKE ? OR old_value LIKE ? OR new_value LIKE ?";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

// Count total records
$countSql = "SELECT COUNT(*) as count FROM settings_history" . $whereClause;
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalCount = $countStmt->fetch()['count'];

// Get records with pagination
$sql = "
    SELECT sh.*, au.username
    FROM settings_history sh
    JOIN admin_users au ON sh.admin_id = au.id
    $whereClause
    ORDER BY sh.created_at DESC
    LIMIT ?, ?
";

// Add pagination parameters
$params[] = $offset;
$params[] = $perPage;

// Prepare and execute query
$stmt = $pdo->prepare($sql);

// Bind parameters with correct types
for ($i = 0; $i < count($params); $i++) {
    if ($i >= count($params) - 2) {
        $stmt->bindValue($i + 1, $params[$i], PDO::PARAM_INT);
    } else {
        $stmt->bindValue($i + 1, $params[$i]);
    }
}

$stmt->execute();
$history = $stmt->fetchAll();

// Calculate pagination info
$totalPages = ceil($totalCount / $perPage);

// Page title and breadcrumbs
$pageTitle = 'Settings History';
$breadcrumbs = [
    'Settings' => 'settings.php',
    'History' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Settings Change History</h1>
            <a href="settings.php" class="btn btn-primary">
                <i class="fas fa-cogs me-2"></i> Manage Settings
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">Settings Changes Log</h5>
            </div>
            <div class="col-md-6">
                <form action="settings_history.php" method="get" class="d-flex">
                    <input type="text" class="form-control" name="search" placeholder="Search settings..." value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-primary ms-2">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if (!empty($search)): ?>
                        <a href="settings_history.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Setting</th>
                        <th>Old Value</th>
                        <th>New Value</th>
                        <th>Changed By</th>
                        <th>Date & Time</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($history) > 0): ?>
                        <?php foreach ($history as $item): ?>
                            <tr>
                                <td>
                                    <span class="fw-medium"><?php echo htmlspecialchars($item['setting_key']); ?></span>
                                </td>
                                <td>
                                    <?php if ($item['old_value'] === null): ?>
                                        <span class="text-muted fst-italic">Not set</span>
                                    <?php else: ?>
                                        <code><?php echo htmlspecialchars($item['old_value']); ?></code>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <code><?php echo htmlspecialchars($item['new_value']); ?></code>
                                </td>
                                <td><?php echo htmlspecialchars($item['username']); ?></td>
                                <td><?php echo (new DateTime($item['created_at']))->format('M d, Y H:i:s'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-2"></i> No settings changes found
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                        </li>
                    <?php endif; ?>
                    
                    <?php
                        $startPage = max(1, $page - 2);
                        $endPage = min($totalPages, $page + 2);
                        
                        if ($startPage > 1) {
                            echo '<li class="page-item"><a class="page-link" href="?page=1&search=' . urlencode($search) . '">1</a></li>';
                            if ($startPage > 2) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                        }
                        
                        for ($i = $startPage; $i <= $endPage; $i++) {
                            if ($i == $page) {
                                echo '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
                            } else {
                                echo '<li class="page-item"><a class="page-link" href="?page=' . $i . '&search=' . urlencode($search) . '">' . $i . '</a></li>';
                            }
                        }
                        
                        if ($endPage < $totalPages) {
                            if ($endPage < $totalPages - 1) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                            echo '<li class="page-item"><a class="page-link" href="?page=' . $totalPages . '&search=' . urlencode($search) . '">' . $totalPages . '</a></li>';
                        }
                    ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
