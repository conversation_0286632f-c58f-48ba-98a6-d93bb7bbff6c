# Shop Location Data Guide - GoGoLaundry Admin Panel

## Overview
This guide explains different methods to obtain latitude and longitude coordinates for laundry shops in your GoGoLaundry admin panel.

## Method 1: Enhanced Admin Panel with Google Maps (Recommended)

### Setup Steps:
1. **Get Google Maps API Key:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable "Maps JavaScript API" and "Places API"
   - Create credentials (API Key)
   - Restrict the API key to your domain for security

2. **Update the API Key:**
   - Open `shops.php`
   - Replace `YOUR_GOOGLE_MAPS_API_KEY` with your actual API key
   - Line 475: `<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_ACTUAL_API_KEY&libraries=places"></script>`

### Features:
- ✅ Interactive map in the "Add Shop" modal
- ✅ Click to set location
- ✅ Drag marker to adjust position
- ✅ Search for locations by name/address
- ✅ Auto-complete suggestions
- ✅ Automatic address filling
- ✅ Current location detection

## Method 2: Standalone Location Picker (Free Alternative)

### Usage:
1. Open `location_picker.html` in your browser
2. Search for location or click on map
3. Copy the coordinates
4. Paste them in the shop form

### Features:
- ✅ Uses OpenStreetMap (completely free)
- ✅ Search functionality
- ✅ Quick location buttons for major BD cities
- ✅ Current location detection
- ✅ Copy coordinates to clipboard
- ✅ Drag marker to adjust

## Method 3: Manual Methods

### 3.1 Google Maps Website:
1. Go to [Google Maps](https://maps.google.com)
2. Search for the shop location
3. Right-click on the exact location
4. Select "What's here?"
5. Copy the coordinates from the popup

### 3.2 GPS Mobile Apps:
- **GPS Coordinates** (Android/iOS)
- **GPS Status & Toolbox** (Android)
- **Coordinates** (iOS)

### 3.3 Online Tools:
- [LatLong.net](https://www.latlong.net/)
- [GPS Coordinates](https://gps-coordinates.org/)
- [Maps.ie](https://www.maps.ie/coordinates.html)

## Method 4: Bulk Import from CSV

### For Multiple Shops:
1. Create a CSV file with shop data including coordinates
2. Use geocoding services to convert addresses to coordinates:
   - **Google Geocoding API**
   - **OpenCage Geocoding API**
   - **Nominatim (OpenStreetMap)**

### Sample CSV Format:
```csv
shop_name,owner_name,phone,address,latitude,longitude
"Clean & Fresh Laundry","John Doe","01712345678","Dhanmondi, Dhaka",23.7461,90.3742
"Express Wash","Jane Smith","01812345678","Chittagong",22.3569,91.7832
```

## Method 5: Mobile App Integration

### For Field Data Collection:
1. Create a simple mobile app for data collectors
2. Use device GPS to capture coordinates
3. Submit data directly to your database

## Important Notes:

### Coordinate Format:
- **Latitude**: -90 to +90 (North/South)
- **Longitude**: -180 to +180 (East/West)
- **Bangladesh Range**: 
  - Latitude: ~20.5 to 26.6
  - Longitude: ~88.0 to 92.7

### Accuracy Tips:
- Use at least 6 decimal places for meter-level accuracy
- Verify coordinates by checking them on a map
- For Bangladesh, ensure coordinates fall within the country bounds

### Security Considerations:
- Restrict Google Maps API key to your domain
- Monitor API usage to avoid unexpected charges
- Consider rate limiting for geocoding requests

## Troubleshooting:

### Common Issues:
1. **Map not loading**: Check API key and internet connection
2. **Wrong location**: Verify coordinate format and bounds
3. **Search not working**: Check if Places API is enabled
4. **Coordinates not updating**: Ensure JavaScript is enabled

### Testing Coordinates:
- Dhaka Center: 23.8103, 90.4125
- Chittagong: 22.3569, 91.7832
- Sylhet: 24.8949, 91.8687

## Cost Considerations:

### Google Maps Pricing (as of 2024):
- Maps JavaScript API: $7 per 1,000 loads
- Places API: $17 per 1,000 requests
- Geocoding API: $5 per 1,000 requests
- **Free tier**: $200 credit monthly

### Free Alternatives:
- OpenStreetMap with Leaflet (unlimited)
- Nominatim geocoding (free, rate-limited)
- Manual coordinate collection

## Recommended Workflow:

1. **For new shops**: Use the enhanced admin panel with Google Maps
2. **For bulk import**: Use geocoding APIs or manual collection
3. **For field work**: Use the standalone location picker
4. **For verification**: Cross-check coordinates on multiple platforms

## Support:
If you need help implementing any of these methods, please refer to the documentation or contact your development team.
