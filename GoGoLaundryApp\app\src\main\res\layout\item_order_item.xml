<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/margin_small"
    android:paddingBottom="@dimen/margin_small">

    <ImageView
        android:id="@+id/item_image"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        android:contentDescription="@string/item_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:src="@drawable/placeholder_image" />

    <TextView
        android:id="@+id/item_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/text_size_small"
        android:textStyle="bold"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintStart_toEndOf="@id/item_image"
        app:layout_constraintEnd_toStartOf="@id/item_subtotal"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="T-Shirt" />

    <TextView
        android:id="@+id/service_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/text_size_micro"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintStart_toEndOf="@id/item_image"
        app:layout_constraintEnd_toStartOf="@id/item_subtotal"
        app:layout_constraintTop_toBottomOf="@id/item_name"
        tools:text="Dry Cleaning" />

    <TextView
        android:id="@+id/item_quantity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_medium"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/text_size_micro"
        app:layout_constraintStart_toEndOf="@id/item_image"
        app:layout_constraintEnd_toStartOf="@id/item_price"
        app:layout_constraintTop_toBottomOf="@id/service_name"
        tools:text="Qty: 2" />

    <TextView
        android:id="@+id/item_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_secondary"
        android:textSize="@dimen/text_size_micro"
        app:layout_constraintEnd_toStartOf="@id/item_subtotal"
        app:layout_constraintTop_toBottomOf="@id/service_name"
        tools:text="₹22.00/item" />

    <TextView
        android:id="@+id/item_subtotal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/text_size_small"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="₹44.00" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/margin_small"
        android:background="@color/divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/item_quantity" />

</androidx.constraintlayout.widget.ConstraintLayout>
