<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="4dp"
    android:layout_marginTop="6dp"
    android:layout_marginEnd="4dp"
    android:layout_marginBottom="6dp"
    android:background="@drawable/glass_item_background"
    android:elevation="6dp"
    android:padding="16dp">

    <!-- Item Image Container -->
    <FrameLayout
        android:id="@+id/cart_item_image_container"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:background="@drawable/glass_section_background"
        android:elevation="4dp"
        android:padding="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/cart_item_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/rounded_corner_background"
            android:contentDescription="@string/item_image"
            android:scaleType="centerCrop"
            tools:src="@drawable/placeholder_image" />

    </FrameLayout>

    <!-- Item Details Section -->
    <LinearLayout
        android:id="@+id/item_details_section"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        android:orientation="vertical"
        android:padding="13dp"
        android:background="@drawable/glass_section_background"
        app:layout_constraintEnd_toStartOf="@+id/cart_remove_button"
        app:layout_constraintStart_toEndOf="@+id/cart_item_image_container"
        app:layout_constraintTop_toTopOf="@+id/cart_item_image_container">

        <!-- Item Name -->
        <TextView
            android:id="@+id/cart_item_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="Premium Cotton Shirt"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            tools:text="Premium Cotton Shirt" />

        <!-- Service Name -->
        <TextView
            android:id="@+id/cart_service_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:gravity="center"
            android:background="@drawable/glass_section_background"
            android:maxLines="1"
            android:text="Wash &amp; Iron Service"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="@color/home_accent_blue"
            android:textStyle="bold"
            tools:text="Wash &amp; Iron Service" />

        <!-- Price per piece -->
        <TextView
            android:id="@+id/cart_item_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:text="৳50.00/piece"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="@color/text_secondary"
            tools:text="৳50.00/piece" />

    </LinearLayout>

    <!-- Remove Button -->
    <FrameLayout
        android:id="@+id/cart_remove_button"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:background="@drawable/glass_section_background"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_gravity="center"
            android:contentDescription="@string/remove_item"
            android:src="@drawable/ic_delete"
            app:tint="@color/error" />

    </FrameLayout>

    <!-- Quantity and Subtotal Section -->
    <LinearLayout
        android:id="@+id/quantity_section"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/cart_item_image_container"
        app:layout_constraintTop_toBottomOf="@+id/cart_item_image_container">

        <!-- Quantity Controls -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/glass_section_background"
            android:elevation="2dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="4dp">

            <FrameLayout
                android:id="@+id/cart_decrease_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/glass_button_background"
                android:elevation="2dp">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/decrease_quantity"
                    android:src="@drawable/ic_remove"
                    app:tint="@color/home_accent_blue" />

            </FrameLayout>

            <TextView
                android:id="@+id/cart_item_quantity"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:gravity="center"
                android:text="1"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                android:textColor="@color/text_primary"
                android:textStyle="bold" />

            <FrameLayout
                android:id="@+id/cart_increase_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="@drawable/glass_button_background"
                android:elevation="2dp">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/increase_quantity"
                    android:src="@drawable/ic_add"
                    app:tint="@color/home_accent_blue" />

            </FrameLayout>

        </LinearLayout>

        <!-- Spacer -->
        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <!-- Subtotal -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/glass_section_background"
            android:elevation="2dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="12dp"
            android:paddingTop="8dp"
            android:paddingEnd="12dp"
            android:paddingBottom="8dp">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="6dp"
                android:src="@drawable/ic_receipt"
                app:tint="@color/home_accent_green" />

            <TextView
                android:id="@+id/cart_item_subtotal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="৳100.00"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                android:textColor="@color/home_accent_green"
                android:textStyle="bold"
                tools:text="৳100.00" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
