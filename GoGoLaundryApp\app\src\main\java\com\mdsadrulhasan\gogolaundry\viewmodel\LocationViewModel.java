package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.database.BangladeshAdminDAO;
import com.mdsadrulhasan.gogolaundry.model.District;
import com.mdsadrulhasan.gogolaundry.model.Division;
import com.mdsadrulhasan.gogolaundry.model.Upazilla;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * ViewModel for handling administrative location data (Division, District, Upazilla)
 */
public class LocationViewModel extends AndroidViewModel {

    private static final String TAG = "LocationViewModel";

    private final BangladeshAdminDAO adminDAO;
    private final ExecutorService executor;

    // LiveData for location data
    private final MutableLiveData<List<Division>> divisions = new MutableLiveData<>();
    private final MutableLiveData<List<District>> districts = new MutableLiveData<>();
    private final MutableLiveData<List<Upazilla>> upazillas = new MutableLiveData<>();

    // Loading states
    private final MutableLiveData<Boolean> isLoadingDivisions = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isLoadingDistricts = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isLoadingUpazillas = new MutableLiveData<>(false);

    // Error states
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    public LocationViewModel(@NonNull Application application) {
        super(application);
        adminDAO = new BangladeshAdminDAO(application);
        executor = Executors.newFixedThreadPool(3);
        
        // Open database connection
        adminDAO.open();
    }

    /**
     * Get divisions LiveData
     *
     * @return LiveData of divisions list
     */
    public LiveData<List<Division>> getDivisions() {
        return divisions;
    }

    /**
     * Get districts LiveData
     *
     * @return LiveData of districts list
     */
    public LiveData<List<District>> getDistricts() {
        return districts;
    }

    /**
     * Get upazillas LiveData
     *
     * @return LiveData of upazillas list
     */
    public LiveData<List<Upazilla>> getUpazillas() {
        return upazillas;
    }

    /**
     * Get loading state for divisions
     *
     * @return LiveData of loading state
     */
    public LiveData<Boolean> getIsLoadingDivisions() {
        return isLoadingDivisions;
    }

    /**
     * Get loading state for districts
     *
     * @return LiveData of loading state
     */
    public LiveData<Boolean> getIsLoadingDistricts() {
        return isLoadingDistricts;
    }

    /**
     * Get loading state for upazillas
     *
     * @return LiveData of loading state
     */
    public LiveData<Boolean> getIsLoadingUpazillas() {
        return isLoadingUpazillas;
    }

    /**
     * Get error message LiveData
     *
     * @return LiveData of error message
     */
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    /**
     * Load all divisions
     */
    public void loadDivisions() {
        isLoadingDivisions.setValue(true);
        errorMessage.setValue(null);

        executor.execute(() -> {
            try {
                List<Division> divisionList = adminDAO.getAllDivisions();
                Log.d(TAG, "Loaded " + divisionList.size() + " divisions");
                
                // Post results on main thread
                divisions.postValue(divisionList);
                isLoadingDivisions.postValue(false);
                
            } catch (Exception e) {
                Log.e(TAG, "Error loading divisions", e);
                errorMessage.postValue("Failed to load divisions: " + e.getMessage());
                isLoadingDivisions.postValue(false);
                divisions.postValue(new ArrayList<>());
            }
        });
    }

    /**
     * Load districts by division ID
     *
     * @param divisionId Division ID to filter districts
     */
    public void loadDistricts(int divisionId) {
        isLoadingDistricts.setValue(true);
        errorMessage.setValue(null);

        executor.execute(() -> {
            try {
                List<District> districtList = adminDAO.getDistrictsByDivision(divisionId);
                Log.d(TAG, "Loaded " + districtList.size() + " districts for division " + divisionId);
                
                // Post results on main thread
                districts.postValue(districtList);
                isLoadingDistricts.postValue(false);
                
                // Clear upazillas when districts change
                upazillas.postValue(new ArrayList<>());
                
            } catch (Exception e) {
                Log.e(TAG, "Error loading districts for division " + divisionId, e);
                errorMessage.postValue("Failed to load districts: " + e.getMessage());
                isLoadingDistricts.postValue(false);
                districts.postValue(new ArrayList<>());
            }
        });
    }

    /**
     * Load upazillas by district ID
     *
     * @param districtId District ID to filter upazillas
     */
    public void loadUpazillas(int districtId) {
        isLoadingUpazillas.setValue(true);
        errorMessage.setValue(null);

        executor.execute(() -> {
            try {
                List<Upazilla> upazillaList = adminDAO.getUpazillasByDistrict(districtId);
                Log.d(TAG, "Loaded " + upazillaList.size() + " upazillas for district " + districtId);
                
                // Post results on main thread
                upazillas.postValue(upazillaList);
                isLoadingUpazillas.postValue(false);
                
            } catch (Exception e) {
                Log.e(TAG, "Error loading upazillas for district " + districtId, e);
                errorMessage.postValue("Failed to load upazillas: " + e.getMessage());
                isLoadingUpazillas.postValue(false);
                upazillas.postValue(new ArrayList<>());
            }
        });
    }

    /**
     * Load all districts (without filtering by division)
     */
    public void loadAllDistricts() {
        isLoadingDistricts.setValue(true);
        errorMessage.setValue(null);

        executor.execute(() -> {
            try {
                List<District> districtList = adminDAO.getAllDistricts();
                Log.d(TAG, "Loaded " + districtList.size() + " districts");
                
                // Post results on main thread
                districts.postValue(districtList);
                isLoadingDistricts.postValue(false);
                
            } catch (Exception e) {
                Log.e(TAG, "Error loading all districts", e);
                errorMessage.postValue("Failed to load districts: " + e.getMessage());
                isLoadingDistricts.postValue(false);
                districts.postValue(new ArrayList<>());
            }
        });
    }

    /**
     * Load all upazillas (without filtering by district)
     */
    public void loadAllUpazillas() {
        isLoadingUpazillas.setValue(true);
        errorMessage.setValue(null);

        executor.execute(() -> {
            try {
                List<Upazilla> upazillaList = adminDAO.getAllUpazillas();
                Log.d(TAG, "Loaded " + upazillaList.size() + " upazillas");
                
                // Post results on main thread
                upazillas.postValue(upazillaList);
                isLoadingUpazillas.postValue(false);
                
            } catch (Exception e) {
                Log.e(TAG, "Error loading all upazillas", e);
                errorMessage.postValue("Failed to load upazillas: " + e.getMessage());
                isLoadingUpazillas.postValue(false);
                upazillas.postValue(new ArrayList<>());
            }
        });
    }

    /**
     * Get division by ID
     *
     * @param divisionId Division ID
     * @return Division object or null if not found
     */
    public Division getDivisionById(int divisionId) {
        try {
            return adminDAO.getDivisionById(divisionId);
        } catch (Exception e) {
            Log.e(TAG, "Error getting division by ID: " + divisionId, e);
            return null;
        }
    }

    /**
     * Get district by ID
     *
     * @param districtId District ID
     * @return District object or null if not found
     */
    public District getDistrictById(int districtId) {
        try {
            return adminDAO.getDistrictById(districtId);
        } catch (Exception e) {
            Log.e(TAG, "Error getting district by ID: " + districtId, e);
            return null;
        }
    }

    /**
     * Get upazilla by ID
     *
     * @param upazillaId Upazilla ID
     * @return Upazilla object or null if not found
     */
    public Upazilla getUpazillaById(int upazillaId) {
        try {
            return adminDAO.getUpazillaById(upazillaId);
        } catch (Exception e) {
            Log.e(TAG, "Error getting upazilla by ID: " + upazillaId, e);
            return null;
        }
    }

    /**
     * Clear all location data
     */
    public void clearAllData() {
        divisions.setValue(new ArrayList<>());
        districts.setValue(new ArrayList<>());
        upazillas.setValue(new ArrayList<>());
        errorMessage.setValue(null);
    }

    /**
     * Clear districts and upazillas
     */
    public void clearDistrictsAndUpazillas() {
        districts.setValue(new ArrayList<>());
        upazillas.setValue(new ArrayList<>());
    }

    /**
     * Clear upazillas only
     */
    public void clearUpazillas() {
        upazillas.setValue(new ArrayList<>());
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        // Close database connection and shutdown executor
        if (adminDAO != null) {
            adminDAO.close();
        }
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        Log.d(TAG, "LocationViewModel cleared");
    }
}
