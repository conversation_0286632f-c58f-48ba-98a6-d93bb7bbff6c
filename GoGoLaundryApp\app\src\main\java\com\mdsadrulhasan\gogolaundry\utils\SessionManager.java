package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import com.google.gson.Gson;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.AppConfig;
import com.mdsadrulhasan.gogolaundry.model.User;

import retrofit2.Call;

/**
 * Session manager for handling user session and app configuration
 */
public class SessionManager {
    private static final String PREF_NAME = "OtpManagementPref";
    private static final String KEY_IS_LOGGED_IN = "isLoggedIn";
    private static final String KEY_USER = "user";
    private static final String KEY_CONFIG = "config";
    private static final String KEY_STAY_LOGGED_IN = "stayLoggedIn";
    private static final String KEY_SAVED_PHONE = "savedPhone";
    private static final String KEY_SAVED_PASSWORD = "savedPassword";
    private static final String KEY_COOKIES = "cookies";
    private static final String KEY_FCM_TOKEN = "fcmToken";
    private static final String KEY_DEVICE_ID = "deviceId";
    private static final String KEY_NOTIFICATION_PERMISSION_ASKED = "notificationPermissionAsked";

    private SharedPreferences pref;
    private SharedPreferences.Editor editor;
    private Context context;
    private Gson gson;

    /**
     * Constructor
     *
     * @param context Application context
     */
    public SessionManager(Context context) {
        this.context = context;
        pref = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = pref.edit();
        gson = new Gson();
    }

    /**
     * Set login status
     *
     * @param isLoggedIn Login status
     */
    public void setLoggedIn(boolean isLoggedIn) {
        editor.putBoolean(KEY_IS_LOGGED_IN, isLoggedIn);
        editor.apply();
    }

    /**
     * Check if user is logged in
     *
     * @return True if logged in, false otherwise
     */
    public boolean isLoggedIn() {
        return pref.getBoolean(KEY_IS_LOGGED_IN, false);
    }

    /**
     * Save user data
     *
     * @param user User data
     */
    public void saveUser(User user) {
        String userJson = gson.toJson(user);
        editor.putString(KEY_USER, userJson);
        editor.apply();
    }

    /**
     * Get user data
     *
     * @return User data or null if not found
     */
    public User getUser() {
        String userJson = pref.getString(KEY_USER, null);
        if (userJson != null) {
            return gson.fromJson(userJson, User.class);
        }
        return null;
    }

    /**
     * Save app configuration
     *
     * @param config App configuration
     */
    public void saveConfig(AppConfig config) {
        String configJson = gson.toJson(config);
        editor.putString(KEY_CONFIG, configJson);
        editor.apply();
    }

    /**
     * Get app configuration
     *
     * @return App configuration or default config if not found
     */
    public AppConfig getConfig() {
        String configJson = pref.getString(KEY_CONFIG, null);
        if (configJson != null) {
            return gson.fromJson(configJson, AppConfig.class);
        }
        // Return default config if not found
        return getDefaultConfig();
    }

    /**
     * Get default app configuration
     *
     * @return Default app configuration
     */
    private AppConfig getDefaultConfig() {
        AppConfig config = new AppConfig();
        config.setAppName("OTP Management");
        config.setAppVersion("1.0.0");
        config.setOtpEnabled(false); // Default to OTP disabled for safety
        config.setOtpLength(6);
        config.setOtpExpiry(600); // 10 minutes
        config.setOtpMaxAttempts(3);
        config.setMinPasswordLength(6); // Updated to match admin setting of 6 characters
        config.setServerTime(null);
        return config;
    }

    /**
     * Check if OTP verification is enabled
     *
     * @return True if enabled, false otherwise
     */
    public boolean isOtpEnabled() {
        AppConfig config = getConfig();
        // Config should never be null now since we return a default config
        // but we'll keep the null check for safety
        return config != null && config.isOtpEnabled();
    }

    /**
     * Set stay logged in preference
     *
     * Note: This method now always saves credentials regardless of the stayLoggedIn parameter
     * for backward compatibility with existing code.
     *
     * @param stayLoggedIn Ignored (kept for backward compatibility)
     * @param phone User's phone number
     * @param password User's password
     */
    public void setStayLoggedIn(boolean stayLoggedIn, String phone, String password) {
        // Always set to true for backward compatibility
        editor.putBoolean(KEY_STAY_LOGGED_IN, true);

        // Always save credentials
        editor.putString(KEY_SAVED_PHONE, phone);
        editor.putString(KEY_SAVED_PASSWORD, password);

        editor.apply();
    }

    /**
     * Check if stay logged in is enabled
     *
     * Note: This method now always returns true for backward compatibility
     *
     * @return Always returns true
     */
    public boolean isStayLoggedIn() {
        return true;
    }

    /**
     * Get saved phone number
     *
     * @return Saved phone number or null if not found
     */
    public String getSavedPhone() {
        return pref.getString(KEY_SAVED_PHONE, null);
    }

    /**
     * Get saved password
     *
     * @return Saved password or null if not found
     */
    public String getSavedPassword() {
        return pref.getString(KEY_SAVED_PASSWORD, null);
    }

    /**
     * Clear session data
     */
    public void logout() {
        editor.remove(KEY_IS_LOGGED_IN);
        editor.remove(KEY_USER);
        editor.remove(KEY_COOKIES);

        // Always clear saved credentials
        editor.remove(KEY_SAVED_PHONE);
        editor.remove(KEY_SAVED_PASSWORD);
        editor.remove(KEY_STAY_LOGGED_IN);

        editor.apply();

        // Reset API client to clear any cached instances
        ApiClient.resetApiClient();
    }

    /**
     * Clear all data
     */
    public void clearAll() {
        editor.clear();
        editor.apply();
    }

    /**
     * Clear cached configuration
     * This forces the app to use the default configuration or fetch a new one from the server
     */
    public void clearConfig() {
        editor.remove(KEY_CONFIG);
        editor.apply();
    }

    /**
     * Fetch configuration from server
     * This method fetches the configuration from the server and saves it to SharedPreferences
     *
     * @param callback Callback to be called when the configuration is fetched
     */
    public void fetchConfigFromServer(final Callback callback) {
        // Get API service
        ApiService apiService = ApiClient.getApiService(context);

        // Get current app version
        String appVersion = getDefaultConfig().getAppVersion();

        // Fetch configuration from server
        apiService.getConfig(appVersion).enqueue(new retrofit2.Callback<ApiResponse<AppConfig>>() {
            @Override
            public void onResponse(Call<ApiResponse<AppConfig>> call, retrofit2.Response<ApiResponse<AppConfig>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    try {
                        ApiResponse<AppConfig> apiResponse = response.body();

                        if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                            AppConfig config = apiResponse.getData();

                            // Fix the otp_enabled value if it's a string
                            if (config.getOtpEnabled() instanceof String) {
                                String otpEnabledStr = (String) config.getOtpEnabled();
                                Log.d("SessionManager", "Fixing invalid otp_enabled value: " + otpEnabledStr);

                                // Check if the string is "1" or "true"
                                boolean otpEnabled = otpEnabledStr.equals("1") ||
                                                    otpEnabledStr.equalsIgnoreCase("true") ||
                                                    otpEnabledStr.equalsIgnoreCase("yes");

                                // Set the correct value
                                config.setOtpEnabled(otpEnabled);
                                Log.d("SessionManager", "Fixed otp_enabled value: " + otpEnabled);
                            }

                            // Save configuration to SharedPreferences
                            saveConfig(config);

                            // Call callback with success
                            if (callback != null) {
                                callback.onSuccess();
                            }
                        } else {
                            // Call callback with error
                            if (callback != null) {
                                String errorMessage = apiResponse != null ? apiResponse.getMessage() : "Invalid response";
                                callback.onError("Failed to fetch configuration: " + errorMessage);
                            }
                        }
                    } catch (Exception e) {
                        Log.e("SessionManager", "Error parsing config response", e);
                        if (callback != null) {
                            callback.onError("Failed to parse configuration: " + e.getMessage());
                        }
                    }
                } else {
                    // Call callback with error
                    if (callback != null) {
                        callback.onError("Failed to fetch configuration: " + response.message());
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<AppConfig>> call, Throwable t) {
                // Call callback with error
                if (callback != null) {
                    callback.onError("Failed to fetch configuration: " + t.getMessage());
                }
            }
        });
    }

    /**
     * Callback interface for fetchConfigFromServer
     */
    public interface Callback {
        void onSuccess();
        void onError(String errorMessage);
    }

    /**
     * Save cookies for session management
     *
     * @param cookies Cookies string
     */
    public void saveCookies(String cookies) {
        editor.putString(KEY_COOKIES, cookies);
        editor.apply();
    }

    /**
     * Get saved cookies
     *
     * @return Cookies string or null if not found
     */
    public String getCookies() {
        return pref.getString(KEY_COOKIES, null);
    }

    /**
     * Clear cookies
     */
    public void clearCookies() {
        editor.remove(KEY_COOKIES);
        editor.apply();
    }

    /**
     * Save FCM token
     *
     * @param token FCM token
     */
    public void saveFCMToken(String token) {
        editor.putString(KEY_FCM_TOKEN, token);
        editor.apply();
    }

    /**
     * Get FCM token
     *
     * @return FCM token or null if not found
     */
    public String getFCMToken() {
        return pref.getString(KEY_FCM_TOKEN, null);
    }

    /**
     * Clear FCM token
     */
    public void clearFCMToken() {
        editor.remove(KEY_FCM_TOKEN);
        editor.apply();
    }

    /**
     * Get device ID (generates one if not exists)
     *
     * @return Device ID
     */
    public String getDeviceId() {
        String deviceId = pref.getString(KEY_DEVICE_ID, null);
        if (deviceId == null) {
            // Generate a unique device ID
            deviceId = "android_" + System.currentTimeMillis() + "_" +
                      android.provider.Settings.Secure.getString(
                          context.getContentResolver(),
                          android.provider.Settings.Secure.ANDROID_ID
                      );
            editor.putString(KEY_DEVICE_ID, deviceId);
            editor.apply();
        }
        return deviceId;
    }

    /**
     * Get user ID from saved user data
     *
     * @return User ID or 0 if not found
     */
    public int getUserId() {
        User user = getUser();
        return user != null ? user.getId() : 0;
    }

    /**
     * Check if notification permission has been asked before
     *
     * @return true if permission has been asked before
     */
    public boolean hasAskedForNotificationPermission() {
        return pref.getBoolean(KEY_NOTIFICATION_PERMISSION_ASKED, false);
    }

    /**
     * Set notification permission asked flag
     *
     * @param asked true if permission has been asked
     */
    public void setHasAskedForNotificationPermission(boolean asked) {
        editor.putBoolean(KEY_NOTIFICATION_PERMISSION_ASKED, asked);
        editor.apply();
    }

    /**
     * Check if this is the first app launch
     *
     * @return true if this is the first launch
     */


    /**
     * Set first launch flag to false
     */

}
