<?php
/**
 * Add New Item
 *
 * This page allows administrators to add a new laundry item
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/ServiceManager.php';
require_once '../includes/ItemManager.php';

// Initialize managers
$serviceManager = new ServiceManager($pdo);
$itemManager = new ItemManager($pdo);

// Get all services for dropdown
$services = $serviceManager->getActiveServices();

// Initialize variables
$serviceId = '';
$name = '';
$bnName = '';
$description = '';
$bnDescription = '';
$price = '';
$imageUrl = '';
$isActive = true;
$inStock = true;
$errors = [];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_item'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error_message'] = 'Invalid security token. Please try again.';
        header('Location: add_item.php');
        exit;
    }

    // Get form data
    $serviceId = isset($_POST['service_id']) ? (int)$_POST['service_id'] : 0;
    $name = trim($_POST['name'] ?? '');
    $bnName = trim($_POST['bn_name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $bnDescription = trim($_POST['bn_description'] ?? '');
    $price = trim($_POST['price'] ?? '');
    $imageUrl = trim($_POST['image_url'] ?? '');
    $isActive = isset($_POST['is_active']) && $_POST['is_active'] === '1';
    $inStock = isset($_POST['in_stock']) && $_POST['in_stock'] === '1';

    // Validate form data
    if (empty($serviceId)) {
        $errors['service_id'] = 'Service is required.';
    }

    if (empty($name)) {
        $errors['name'] = 'Item name is required.';
    } elseif (strlen($name) > 100) {
        $errors['name'] = 'Item name cannot exceed 100 characters.';
    }

    if (!empty($bnName) && strlen($bnName) > 100) {
        $errors['bn_name'] = 'Bengali name cannot exceed 100 characters.';
    }

    if (strlen($description) > 500) {
        $errors['description'] = 'Description cannot exceed 500 characters.';
    }

    if (empty($price)) {
        $errors['price'] = 'Price is required.';
    } elseif (!is_numeric($price) || $price < 0) {
        $errors['price'] = 'Price must be a valid positive number.';
    }

    if (!empty($imageUrl) && strlen($imageUrl) > 255) {
        $errors['image_url'] = 'Image URL cannot exceed 255 characters.';
    }

    // If no errors, add item
    if (empty($errors)) {
        $result = $itemManager->addItem($serviceId, $name, $description, $price, $isActive, $inStock, $bnName, $bnDescription, $imageUrl);

        if ($result) {
            $_SESSION['success_message'] = 'Item added successfully.';
            header('Location: items.php');
            exit;
        } else {
            $_SESSION['error_message'] = 'Failed to add item. Please try again.';
        }
    }
}

// Set page title
$pageTitle = 'Add New Item';
?>

<?php include 'includes/header.php'; ?>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0 text-gray-800"><?php echo $pageTitle; ?></h1>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="items.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Items
                        </a>
                    </div>
                </div>

                <?php include 'includes/alerts.php'; ?>

                <!-- Add Item Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Item Information</h6>
                    </div>
                    <div class="card-body">
                        <form method="post" action="add_item.php">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            
                            <div class="mb-3">
                                <label for="service_id" class="form-label">Service <span class="text-danger">*</span></label>
                                <select class="form-select <?php echo isset($errors['service_id']) ? 'is-invalid' : ''; ?>" 
                                        id="service_id" name="service_id" required>
                                    <option value="">Select Service</option>
                                    <?php foreach ($services as $service): ?>
                                        <option value="<?php echo $service['id']; ?>" <?php echo $serviceId == $service['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($service['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['service_id'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['service_id']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Item Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo isset($errors['name']) ? 'is-invalid' : ''; ?>" 
                                       id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                                <?php if (isset($errors['name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['name']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bn_name" class="form-label">Bengali Name</label>
                                <input type="text" class="form-control <?php echo isset($errors['bn_name']) ? 'is-invalid' : ''; ?>" 
                                       id="bn_name" name="bn_name" value="<?php echo htmlspecialchars($bnName); ?>">
                                <?php if (isset($errors['bn_name'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['bn_name']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control <?php echo isset($errors['description']) ? 'is-invalid' : ''; ?>" 
                                          id="description" name="description" rows="3"><?php echo htmlspecialchars($description); ?></textarea>
                                <?php if (isset($errors['description'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['description']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bn_description" class="form-label">Bengali Description</label>
                                <textarea class="form-control <?php echo isset($errors['bn_description']) ? 'is-invalid' : ''; ?>" 
                                          id="bn_description" name="bn_description" rows="3"><?php echo htmlspecialchars($bnDescription); ?></textarea>
                                <?php if (isset($errors['bn_description'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['bn_description']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="price" class="form-label">Price (Tk) <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control <?php echo isset($errors['price']) ? 'is-invalid' : ''; ?>" 
                                       id="price" name="price" value="<?php echo htmlspecialchars($price); ?>" required>
                                <?php if (isset($errors['price'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['price']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="image_url" class="form-label">Image URL</label>
                                <input type="text" class="form-control <?php echo isset($errors['image_url']) ? 'is-invalid' : ''; ?>" 
                                       id="image_url" name="image_url" value="<?php echo htmlspecialchars($imageUrl); ?>">
                                <?php if (isset($errors['image_url'])): ?>
                                    <div class="invalid-feedback"><?php echo $errors['image_url']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" 
                                       <?php echo $isActive ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="in_stock" name="in_stock" value="1" 
                                       <?php echo $inStock ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="in_stock">In Stock</label>
                            </div>
                            
                            <button type="submit" name="add_item" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Item
                            </button>
                        </form>
                    </div>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>
