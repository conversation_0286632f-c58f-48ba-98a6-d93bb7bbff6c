package com.mdsadrulhasan.gogolaundry.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Transaction;
import androidx.room.Update;

import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity;

import java.util.List;

/**
 * Data Access Object for Order entity
 */
@Dao
public interface OrderDao {

    /**
     * Insert an order
     *
     * @param order Order to insert
     * @return ID of inserted order
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(OrderEntity order);

    /**
     * Insert an order item
     *
     * @param orderItem Order item to insert
     * @return ID of inserted order item
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertOrderItem(OrderItemEntity orderItem);

    /**
     * Insert multiple order items
     *
     * @param orderItems Order items to insert
     * @return IDs of inserted order items
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertOrderItems(List<OrderItemEntity> orderItems);

    /**
     * Update an order
     *
     * @param order Order to update
     */
    @Update
    void update(OrderEntity order);

    /**
     * Update an order item
     *
     * @param orderItem Order item to update
     */
    @Update
    void updateOrderItem(OrderItemEntity orderItem);

    /**
     * Get order by ID
     *
     * @param id Order ID
     * @return Order with the given ID
     */
    @Query("SELECT * FROM orders WHERE id = :id")
    OrderEntity getOrderById(int id);

    /**
     * Get order by ID as LiveData
     *
     * @param id Order ID
     * @return LiveData of order with the given ID
     */
    @Query("SELECT * FROM orders WHERE id = :id")
    LiveData<OrderEntity> getOrderByIdLive(int id);

    /**
     * Get order by order number
     *
     * @param orderNumber Order number
     * @return Order with the given order number
     */
    @Query("SELECT * FROM orders WHERE order_number = :orderNumber")
    OrderEntity getOrderByOrderNumber(String orderNumber);

    /**
     * Get order by tracking number
     *
     * @param trackingNumber Tracking number
     * @return Order with the given tracking number
     */
    @Query("SELECT * FROM orders WHERE tracking_number = :trackingNumber")
    OrderEntity getOrderByTrackingNumber(String trackingNumber);

    /**
     * Get order items by order ID
     *
     * @param orderId Order ID
     * @return List of order items for the given order ID
     */
    @Query("SELECT * FROM order_items WHERE order_id = :orderId")
    List<OrderItemEntity> getOrderItemsByOrderId(int orderId);

    /**
     * Get order items by order ID as LiveData
     *
     * @param orderId Order ID
     * @return LiveData of order items for the given order ID
     */
    @Query("SELECT * FROM order_items WHERE order_id = :orderId")
    LiveData<List<OrderItemEntity>> getOrderItemsByOrderIdLive(int orderId);

    /**
     * Get all orders for a user
     *
     * @param userId User ID
     * @return List of orders for the given user ID
     */
    @Query("SELECT * FROM orders WHERE user_id = :userId ORDER BY created_at DESC")
    List<OrderEntity> getOrdersByUserId(int userId);

    /**
     * Get all orders for a user as LiveData
     *
     * @param userId User ID
     * @return LiveData of orders for the given user ID
     */
    @Query("SELECT * FROM orders WHERE user_id = :userId ORDER BY created_at DESC")
    LiveData<List<OrderEntity>> getOrdersByUserIdLive(int userId);

    /**
     * Get all orders
     *
     * @return List of all orders
     */
    @Query("SELECT * FROM orders ORDER BY created_at DESC")
    List<OrderEntity> getAllOrders();

    /**
     * Delete order by ID
     *
     * @param id Order ID
     */
    @Query("DELETE FROM orders WHERE id = :id")
    void deleteOrder(int id);

    /**
     * Delete order item by ID
     *
     * @param id Order item ID
     */
    @Query("DELETE FROM order_items WHERE id = :id")
    void deleteOrderItem(int id);

    /**
     * Delete all order items for an order
     *
     * @param orderId Order ID
     */
    @Query("DELETE FROM order_items WHERE order_id = :orderId")
    void deleteOrderItemsByOrderId(int orderId);

    /**
     * Delete all orders
     */
    @Query("DELETE FROM orders")
    void deleteAllOrders();

    /**
     * Delete all order items
     */
    @Query("DELETE FROM order_items")
    void deleteAllOrderItems();

    /**
     * Delete all orders and order items
     */
    @Transaction
    default void deleteAll() {
        deleteAllOrderItems();
        deleteAllOrders();
    }
}
