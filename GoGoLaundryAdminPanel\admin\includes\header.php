<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title><?php echo $pageTitle ?? 'Admin Dashboard'; ?> - <?php echo APP_NAME; ?></title>

    <!-- jQuery (load before Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/admin.css">

    <!-- Mobile-specific styles -->
    <style>
        @media (max-width: 768px) {
            /* Hamburger button */
            #sidebarCollapse {
                padding: 12px;
                margin-right: 10px;
                z-index: 1060;
                cursor: pointer;
                -webkit-tap-highlight-color: transparent; /* Remove tap highlight on iOS */
            }

            /* Dropdown positioning */
            .dropdown-menu {
                position: absolute !important;
                transform: none !important;
            }

            /* Navbar spacing */
            .navbar .container-fluid {
                padding-left: 5px;
                padding-right: 5px;
            }

            /* Improve touch targets */
            .sidebar ul li a {
                padding: 15px 20px;
                -webkit-tap-highlight-color: rgba(0,0,0,0.1); /* Subtle tap highlight */
            }

            /* Make sure dropdowns are visible on mobile */
            .dropdown-menu.show {
                display: block !important;
            }

            /* Prevent text selection on touch */
            .sidebar, .navbar, .btn, .nav-link {
                -webkit-user-select: none;
                user-select: none;
            }

            /* Improve scrolling */
            .content {
                -webkit-overflow-scrolling: touch;
            }

            /* Close button styling */
            #closeSidebarMobile {
                opacity: 0.8;
                cursor: pointer;
                z-index: 1060;
            }

            /* Improve form elements on mobile */
            input, select, textarea, button {
                font-size: 16px !important; /* Prevent zoom on iOS */
            }

            /* Improve table scrolling */
            .table-responsive {
                -webkit-overflow-scrolling: touch;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <?php include 'sidebar.php'; ?>

        <!-- Page Content -->
        <div class="content">
            <!-- Top Navbar -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-primary">
                        <i class="fas fa-bars"></i>
                    </button>

                    <div class="d-flex align-items-center">
                        <div class="dropdown">
                            <a class="nav-link dropdown-toggle text-white" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-1"></i> <?php echo $adminData['full_name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> Profile</a></li>
                                <li><a class="dropdown-item" href="change_password.php"><i class="fas fa-key me-2"></i> Change Password</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i> Logout</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="bg-light py-2 px-3 mb-3">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                    <?php if (isset($breadcrumbs)): ?>
                        <?php
                        // Check if breadcrumbs is an array of arrays with 'text' and 'link' keys
                        $isArrayOfArrays = isset($breadcrumbs[0]) && is_array($breadcrumbs[0]) && isset($breadcrumbs[0]['text']);

                        if ($isArrayOfArrays):
                            // Handle array of arrays format
                            foreach ($breadcrumbs as $crumb):
                                $label = $crumb['text'];
                                $url = $crumb['link'];
                        ?>
                            <?php if ($url): ?>
                                <li class="breadcrumb-item"><a href="<?php echo $url; ?>"><?php echo $label; ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $label; ?></li>
                            <?php endif; ?>
                        <?php
                            endforeach;
                        else:
                            // Handle associative array format
                            foreach ($breadcrumbs as $label => $url):
                        ?>
                            <?php if ($url): ?>
                                <li class="breadcrumb-item"><a href="<?php echo $url; ?>"><?php echo $label; ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active" aria-current="page"><?php echo $label; ?></li>
                            <?php endif; ?>
                        <?php
                            endforeach;
                        endif;
                        ?>
                    <?php endif; ?>
                </ol>
            </nav>

            <!-- Main Content Container -->
            <div class="container-fluid pb-4">
                <!-- Success Alert -->
                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $_SESSION['success_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <!-- Error Alert -->
                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo $_SESSION['error_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>

                <!-- Warning Alert -->
                <?php if (isset($_SESSION['warning_message'])): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $_SESSION['warning_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['warning_message']); ?>
                <?php endif; ?>

                <!-- Info Alert -->
                <?php if (isset($_SESSION['info_message'])): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php echo $_SESSION['info_message']; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['info_message']); ?>
                <?php endif; ?>
