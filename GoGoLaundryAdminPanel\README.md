# GoGoLaundry Admin Panel

A comprehensive admin panel for the GoGoLaundry application with tracking system integration, dashboard widgets, order management, delivery personnel management, reporting features, responsive design, role-based authentication, and notification management.

## Features

- **Dashboard with Analytics Widgets**: Real-time statistics, charts, and key performance indicators
- **Order Management**: Complete order lifecycle management with status tracking
- **Delivery Personnel Management**: Assign and track delivery personnel
- **Reporting and Analytics**: Sales reports, service analytics, and customer insights
- **Notification Management**: Send and manage notifications to users
- **Role-Based Authentication**: Secure access control for different admin roles

## Installation

1. Clone the repository to your local machine or server
2. Set up the database using the SQL files in the `database` directory
3. Configure the database connection in `config/db.php`
4. Access the admin panel through your web browser

## Database Setup

1. Create a MySQL database named `gogolaundry`
2. Import the database schema from `database/schema.sql`
3. Import the stored procedures from `database/stored_procedures/`
4. (Optional) Import sample data from `database/sample_data.sql`

## Stored Procedures

The application uses stored procedures for complex operations. To install them:

```sql
mysql -u username -p gogolaundry < database/stored_procedures/update_order_status.sql
```

## Configuration

Edit the `config/config.php` file to set up your application:

```php
// Application settings
define('APP_NAME', 'GoGoLaundry');
define('APP_URL', 'http://your-domain.com/GoGoLaundry');
define('APP_VERSION', '1.0.0');

// Environment setting (development, production)
define('ENVIRONMENT', 'development');
```

## Directory Structure

```
GoGoLaundryAdminPanel/
├── admin/              # Admin panel pages
│   ├── includes/       # Admin panel components
│   └── ...
├── api/                # API endpoints
│   └── orders/         # Order-related API endpoints
├── assets/             # Static assets
│   ├── css/            # CSS files
│   ├── js/             # JavaScript files
│   └── img/            # Images
├── config/             # Configuration files
├── database/           # Database files
│   └── stored_procedures/ # Stored procedures
├── includes/           # Shared components
└── uploads/            # Uploaded files
```

## Admin Panel Pages

- **Dashboard**: `admin/index.php`
- **Orders Management**: `admin/orders.php`
- **Order Details**: `admin/order_details.php`
- **Delivery Personnel**: `admin/delivery_personnel.php`
- **Sales Report**: `admin/sales_report.php`
- **Notifications**: `admin/notifications.php`

## JavaScript Libraries

The admin panel uses the following JavaScript libraries:

- **Bootstrap 5**: For responsive design
- **Chart.js**: For charts and graphs
- **DataTables**: For enhanced table functionality
- **jQuery**: For DOM manipulation

## CSS Customization

The admin panel uses a custom CSS file located at `assets/css/admin.css`. You can modify this file to customize the appearance of the admin panel.

## API Endpoints

The admin panel includes API endpoints for various operations:

- **Order Details**: `api/orders/details.php`

## Troubleshooting

### Common Issues

1. **Database Connection Error**: Make sure your database credentials in `config/db.php` are correct
2. **Missing JavaScript Libraries**: Ensure all required JavaScript libraries are included
3. **Permission Issues**: Make sure the `uploads` directory is writable by the web server

### Error Logs

Check the PHP error logs for detailed error messages:

```
tail -f /var/log/apache2/error.log
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

Developed by [Your Name] for GoGoLaundry.

## Contact

For support or inquiries, please contact [<EMAIL>].
