<?php
/**
 * Test FCM Token Registration API
 * 
 * This script tests the FCM token registration endpoint to debug issues
 */

// Test data - replace with actual values
$testData = [
    'user_id' => 1, // Replace with a valid user ID from your database
    'token' => 'test_token_' . time(),
    'device_id' => 'test_device_' . time(),
    'device_type' => 'android'
];

echo "<h2>Testing FCM Token Registration API</h2>\n";
echo "<p>Test data: " . json_encode($testData) . "</p>\n";

// Test the API endpoint
$url = 'http://*************/GoGoLaundry/GoGoLaundryAdminPanel/api/fcm/register_token.php';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($testData))
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>Response:</h3>\n";
echo "<p>HTTP Code: $httpCode</p>\n";

if ($error) {
    echo "<p style='color: red;'>cURL Error: $error</p>\n";
} else {
    echo "<p>Raw Response:</p>\n";
    echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
    
    // Try to decode JSON
    $jsonResponse = json_decode($response, true);
    if ($jsonResponse !== null) {
        echo "<p>Parsed JSON:</p>\n";
        echo "<pre>" . json_encode($jsonResponse, JSON_PRETTY_PRINT) . "</pre>\n";
    } else {
        echo "<p style='color: red;'>Response is not valid JSON</p>\n";
    }
}

// Test database connection
echo "<h3>Database Connection Test:</h3>\n";
try {
    require_once 'config/db.php';
    echo "<p style='color: green;'>✅ Database connection successful</p>\n";
    
    // Check if fcm_tokens table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'fcm_tokens'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ fcm_tokens table exists</p>\n";
        
        // Show table structure
        $stmt = $pdo->query("DESCRIBE fcm_tokens");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<p>Table structure:</p>\n";
        echo "<pre>" . json_encode($columns, JSON_PRETTY_PRINT) . "</pre>\n";
    } else {
        echo "<p style='color: red;'>❌ fcm_tokens table does not exist</p>\n";
    }
    
    // Check if users table exists and has data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_verified = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Verified users count: " . $result['count'] . "</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>\n";
}

// Test FCMService class
echo "<h3>FCMService Class Test:</h3>\n";
try {
    require_once 'includes/FCMService.php';
    $fcmService = new FCMService();
    echo "<p style='color: green;'>✅ FCMService class loaded successfully</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ FCMService error: " . $e->getMessage() . "</p>\n";
}

echo "<h3>PHP Error Log (last 20 lines):</h3>\n";
$errorLog = error_get_last();
if ($errorLog) {
    echo "<pre>" . json_encode($errorLog, JSON_PRETTY_PRINT) . "</pre>\n";
} else {
    echo "<p>No recent PHP errors</p>\n";
}
?>
