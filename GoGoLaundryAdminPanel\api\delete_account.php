<?php
/**
 * Delete Account API Endpoint
 *
 * This endpoint handles soft-deleting a user account
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/UserManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Validate input
if (empty($inputData['confirmation'])) {
    jsonResponse(false, 'Confirmation is required', [], 400);
}

// Get input values
$confirmation = sanitize($inputData['confirmation']);

// Check if confirmation is valid
if ($confirmation !== 'DELETE') {
    jsonResponse(false, 'Invalid confirmation. Please type DELETE to confirm.', [], 400);
}

// Check for user ID in input data or session
$userId = null;

// First try to get user ID from input data
if (!empty($inputData['user_id'])) {
    $userId = (int)$inputData['user_id'];
    error_log('Using user_id from input data: ' . $userId);
}
// If not found in input data, try to get from session
else if (isset($_SESSION['user_id']) && isset($_SESSION['is_logged_in']) && $_SESSION['is_logged_in']) {
    $userId = $_SESSION['user_id'];
    error_log('Using user_id from session: ' . $userId);
}
// If still not found, return error
else {
    error_log('User not authenticated - no user_id in input data or session');
    jsonResponse(false, 'User not authenticated. Please provide user_id or login again.', [], 401);
}

// Initialize user manager
$userManager = new UserManager($pdo);

// Get user data
$user = $userManager->getUserById($userId);
if (!$user) {
    jsonResponse(false, 'User not found', [], 404);
}

// Soft delete the user account
$result = $userManager->softDeleteUser($userId);

if (!$result) {
    jsonResponse(false, 'Failed to delete account. Please try again later.', [], 500);
}

// Clear session
session_unset();
session_destroy();

// Log success
error_log('Account deleted successfully for user ID: ' . $userId);

// Return success response
jsonResponse(true, 'Your account has been deactivated. You can reactivate it within 30 days by contacting support.');
