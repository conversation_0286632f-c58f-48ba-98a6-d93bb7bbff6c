package com.mdsadrulhasan.gogolaundry.api.response;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * Response model for shop details API that includes nested services and items
 */
public class ShopDetailsResponse {

    @SerializedName("id")
    private int id;

    @SerializedName("name")
    private String name;

    @SerializedName("bn_name")
    private String bnName;

    @SerializedName("description")
    private String description;

    @SerializedName("bn_description")
    private String bnDescription;

    @SerializedName("owner_name")
    private String ownerName;

    @SerializedName("phone")
    private String phone;

    @SerializedName("email")
    private String email;

    @SerializedName("address")
    private String address;

    @SerializedName("division_id")
    private Integer divisionId;

    @SerializedName("district_id")
    private Integer districtId;

    @SerializedName("upazilla_id")
    private Integer upazillaId;

    @SerializedName("division_name")
    private String divisionName;

    @SerializedName("district_name")
    private String districtName;

    @SerializedName("upazilla_name")
    private String upazillaName;

    @SerializedName("latitude")
    private double latitude;

    @SerializedName("longitude")
    private double longitude;

    @SerializedName("operating_hours")
    private Object operatingHours; // Can be JSON object or string

    @SerializedName("rating")
    private double rating;

    @SerializedName("total_reviews")
    private int totalReviews;

    @SerializedName("commission_percentage")
    private double commissionPercentage;

    @SerializedName("is_active")
    private boolean isActive;

    @SerializedName("is_verified")
    private boolean isVerified;

    @SerializedName("profile_image_url")
    private String profileImageUrl;

    @SerializedName("cover_image_url")
    private String coverImageUrl;

    @SerializedName("created_at")
    private String createdAt;

    @SerializedName("updated_at")
    private String updatedAt;

    @SerializedName("is_open")
    private boolean isOpen;

    @SerializedName("status")
    private String status;

    @SerializedName("services")
    private List<ServiceResponse> services;

    @SerializedName("items")
    private List<ItemResponse> items;

    @SerializedName("delivery_zones")
    private List<DeliveryZoneResponse> deliveryZones;

    // Getters and setters for main class
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBnName() {
        return bnName;
    }

    public void setBnName(String bnName) {
        this.bnName = bnName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBnDescription() {
        return bnDescription;
    }

    public void setBnDescription(String bnDescription) {
        this.bnDescription = bnDescription;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getDivisionId() {
        return divisionId;
    }

    public void setDivisionId(Integer divisionId) {
        this.divisionId = divisionId;
    }

    public Integer getDistrictId() {
        return districtId;
    }

    public void setDistrictId(Integer districtId) {
        this.districtId = districtId;
    }

    public Integer getUpazillaId() {
        return upazillaId;
    }

    public void setUpazillaId(Integer upazillaId) {
        this.upazillaId = upazillaId;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getDistrictName() {
        return districtName;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getUpazillaName() {
        return upazillaName;
    }

    public void setUpazillaName(String upazillaName) {
        this.upazillaName = upazillaName;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public Object getOperatingHours() {
        return operatingHours;
    }

    public void setOperatingHours(Object operatingHours) {
        this.operatingHours = operatingHours;
    }

    public double getRating() {
        return rating;
    }

    public void setRating(double rating) {
        this.rating = rating;
    }

    public int getTotalReviews() {
        return totalReviews;
    }

    public void setTotalReviews(int totalReviews) {
        this.totalReviews = totalReviews;
    }

    public double getCommissionPercentage() {
        return commissionPercentage;
    }

    public void setCommissionPercentage(double commissionPercentage) {
        this.commissionPercentage = commissionPercentage;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isVerified() {
        return isVerified;
    }

    public void setVerified(boolean verified) {
        isVerified = verified;
    }

    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    public void setProfileImageUrl(String profileImageUrl) {
        this.profileImageUrl = profileImageUrl;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<ServiceResponse> getServices() {
        return services;
    }

    public void setServices(List<ServiceResponse> services) {
        this.services = services;
    }

    public List<ItemResponse> getItems() {
        return items;
    }

    public void setItems(List<ItemResponse> items) {
        this.items = items;
    }

    public List<DeliveryZoneResponse> getDeliveryZones() {
        return deliveryZones;
    }

    public void setDeliveryZones(List<DeliveryZoneResponse> deliveryZones) {
        this.deliveryZones = deliveryZones;
    }

    // Nested classes for services and items
    public static class ServiceResponse {
        @SerializedName("id")
        private int id;

        @SerializedName("name")
        private String name;

        @SerializedName("bn_name")
        private String bnName;

        @SerializedName("description")
        private String description;

        @SerializedName("bn_description")
        private String bnDescription;

        @SerializedName("image_url")
        private String imageUrl;

        @SerializedName("estimated_hours")
        private int estimatedHours;

        @SerializedName("is_available")
        private boolean isAvailable;

        // Getters and setters
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getBnName() {
            return bnName;
        }

        public void setBnName(String bnName) {
            this.bnName = bnName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getBnDescription() {
            return bnDescription;
        }

        public void setBnDescription(String bnDescription) {
            this.bnDescription = bnDescription;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public int getEstimatedHours() {
            return estimatedHours;
        }

        public void setEstimatedHours(int estimatedHours) {
            this.estimatedHours = estimatedHours;
        }

        public boolean isAvailable() {
            return isAvailable;
        }

        public void setAvailable(boolean available) {
            isAvailable = available;
        }
    }

    public static class ItemResponse {
        @SerializedName("id")
        private int id;

        @SerializedName("name")
        private String name;

        @SerializedName("bn_name")
        private String bnName;

        @SerializedName("description")
        private String description;

        @SerializedName("bn_description")
        private String bnDescription;

        @SerializedName("image_url")
        private String imageUrl;

        @SerializedName("default_price")
        private double defaultPrice;

        @SerializedName("custom_price")
        private Double customPrice;

        @SerializedName("effective_price")
        private double effectivePrice;

        @SerializedName("estimated_hours")
        private int estimatedHours;

        @SerializedName("is_available")
        private boolean isAvailable;

        @SerializedName("service")
        private ServiceInfo service;

        public static class ServiceInfo {
            @SerializedName("id")
            private int id;

            @SerializedName("name")
            private String name;

            @SerializedName("bn_name")
            private String bnName;

            // Getters and setters
            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getBnName() {
                return bnName;
            }

            public void setBnName(String bnName) {
                this.bnName = bnName;
            }
        }

        // Getters and setters for ItemResponse
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getBnName() {
            return bnName;
        }

        public void setBnName(String bnName) {
            this.bnName = bnName;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getBnDescription() {
            return bnDescription;
        }

        public void setBnDescription(String bnDescription) {
            this.bnDescription = bnDescription;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public double getDefaultPrice() {
            return defaultPrice;
        }

        public void setDefaultPrice(double defaultPrice) {
            this.defaultPrice = defaultPrice;
        }

        public Double getCustomPrice() {
            return customPrice;
        }

        public void setCustomPrice(Double customPrice) {
            this.customPrice = customPrice;
        }

        public double getEffectivePrice() {
            return effectivePrice;
        }

        public void setEffectivePrice(double effectivePrice) {
            this.effectivePrice = effectivePrice;
        }

        public int getEstimatedHours() {
            return estimatedHours;
        }

        public void setEstimatedHours(int estimatedHours) {
            this.estimatedHours = estimatedHours;
        }

        public boolean isAvailable() {
            return isAvailable;
        }

        public void setAvailable(boolean available) {
            isAvailable = available;
        }

        public ServiceInfo getService() {
            return service;
        }

        public void setService(ServiceInfo service) {
            this.service = service;
        }
    }

    public static class DeliveryZoneResponse {
        @SerializedName("id")
        private int id;

        @SerializedName("division_id")
        private Integer divisionId;

        @SerializedName("district_id")
        private Integer districtId;

        @SerializedName("upazilla_id")
        private Integer upazillaId;

        @SerializedName("delivery_fee")
        private double deliveryFee;

        @SerializedName("min_order_amount")
        private double minOrderAmount;

        @SerializedName("estimated_delivery_hours")
        private int estimatedDeliveryHours;

        @SerializedName("is_active")
        private boolean isActive;

        // Getters and setters
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public Integer getDivisionId() {
            return divisionId;
        }

        public void setDivisionId(Integer divisionId) {
            this.divisionId = divisionId;
        }

        public Integer getDistrictId() {
            return districtId;
        }

        public void setDistrictId(Integer districtId) {
            this.districtId = districtId;
        }

        public Integer getUpazillaId() {
            return upazillaId;
        }

        public void setUpazillaId(Integer upazillaId) {
            this.upazillaId = upazillaId;
        }

        public double getDeliveryFee() {
            return deliveryFee;
        }

        public void setDeliveryFee(double deliveryFee) {
            this.deliveryFee = deliveryFee;
        }

        public double getMinOrderAmount() {
            return minOrderAmount;
        }

        public void setMinOrderAmount(double minOrderAmount) {
            this.minOrderAmount = minOrderAmount;
        }

        public int getEstimatedDeliveryHours() {
            return estimatedDeliveryHours;
        }

        public void setEstimatedDeliveryHours(int estimatedDeliveryHours) {
            this.estimatedDeliveryHours = estimatedDeliveryHours;
        }

        public boolean isActive() {
            return isActive;
        }

        public void setActive(boolean active) {
            isActive = active;
        }
    }
}