<?php
/**
 * Add Current Date Orders Script
 *
 * This script adds test orders with the current date
 */

// Include required files
require_once 'config/db.php';
require_once 'includes/functions.php';

// Get a user ID
$stmt = $pdo->query("SELECT id FROM users LIMIT 1");
$userId = $stmt->fetchColumn();

if (!$userId) {
    echo "No users found. Please run add_sample_data.php first.<br>";
    echo "<a href='add_sample_data.php'>Add Sample Data</a>";
    exit;
}

// Get service and item IDs
$stmt = $pdo->query("SELECT id FROM services LIMIT 1");
$serviceId = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT id FROM items LIMIT 1");
$itemId = $stmt->fetchColumn();

if (!$serviceId || !$itemId) {
    echo "No services or items found. Please run add_sample_data.php first.<br>";
    echo "<a href='add_sample_data.php'>Add Sample Data</a>";
    exit;
}

// Define current date (force the year to be the current year)
$currentDate = date('Y-m-d');

// Check if there are already orders with the current date
$stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?");
$stmt->execute([$currentDate]);
$existingOrders = $stmt->fetchColumn();

if ($existingOrders > 0) {
    echo "There are already $existingOrders orders with the current date.<br>";
    echo "Deleting existing orders with the current date...<br>";

    // Delete existing orders with the current date
    $stmt = $pdo->prepare("DELETE FROM orders WHERE DATE(created_at) = ?");
    $stmt->execute([$currentDate]);
}

// Force the date to be the current date (not in the future)
$currentDate = date('Y-m-d');

// Create orders with current date
$orders = [
    [
        'status' => 'placed',
        'total' => 1500.00
    ],
    [
        'status' => 'confirmed',
        'total' => 2500.00
    ],
    [
        'status' => 'processing',
        'total' => 3500.00
    ],
    [
        'status' => 'delivered',
        'total' => 4500.00
    ]
];

// Insert orders
$orderCount = 0;
foreach ($orders as $order) {
    // Generate order number and tracking number
    $orderNumber = 'ORD' . date('YmdHis') . rand(100, 999);
    $trackingNumber = 'TRK' . date('YmdHis') . rand(100, 999);

    // Insert order with explicit created_at timestamp
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            order_number, tracking_number, user_id, subtotal, delivery_fee, total,
            payment_method, status, pickup_address, pickup_date, pickup_time_slot,
            delivery_address, created_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");

    // Create a timestamp for today
    $timestamp = date('Y-m-d H:i:s');

    $stmt->execute([
        $orderNumber,
        $trackingNumber,
        $userId,
        $order['total'] - 100, // Subtotal
        100, // Delivery fee
        $order['total'],
        'cash',
        $order['status'],
        '123 Test Street',
        date('Y-m-d', strtotime('+1 day')),
        '10:00 AM - 12:00 PM',
        '123 Test Street',
        $timestamp
    ]);

    $orderId = $pdo->lastInsertId();

    // Insert order item
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, item_id, quantity, price, subtotal
        ) VALUES (
            ?, ?, ?, ?, ?
        )
    ");

    $itemPrice = $order['total'] - 100;
    $stmt->execute([
        $orderId,
        $itemId,
        1,
        $itemPrice,
        $itemPrice
    ]);

    $orderCount++;
}

echo "Successfully added $orderCount test orders with current date.<br>";
echo "<a href='admin/index.php'>Go to Dashboard</a>";
