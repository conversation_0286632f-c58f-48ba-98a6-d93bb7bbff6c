<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Location Picker - GoGoLaundry</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        #map { height: 400px; }
        .coordinate-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-map-marker-alt"></i> Location Picker
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="search" class="form-label">Search Location</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" 
                                       placeholder="Enter address or place name...">
                                <button class="btn btn-outline-primary" type="button" onclick="searchLocation()">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                        </div>
                        <div id="map"></div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i> 
                                Click on the map to set location. You can also drag the marker to adjust position.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Location Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="latitude" class="form-label">Latitude</label>
                            <input type="text" class="form-control" id="latitude" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="longitude" class="form-label">Longitude</label>
                            <input type="text" class="form-control" id="longitude" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" rows="3" readonly></textarea>
                        </div>
                        <button class="btn btn-success w-100" onclick="copyCoordinates()">
                            <i class="fas fa-copy"></i> Copy Coordinates
                        </button>
                        <button class="btn btn-primary w-100 mt-2" onclick="getCurrentLocation()">
                            <i class="fas fa-location-arrow"></i> Use My Location
                        </button>
                    </div>
                </div>
                
                <!-- Quick Locations -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">Quick Locations (Bangladesh)</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="goToLocation(23.8103, 90.4125, 'Dhaka')">
                                Dhaka
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="goToLocation(22.3569, 91.7832, 'Chittagong')">
                                Chittagong
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="goToLocation(24.3636, 88.6241, 'Rajshahi')">
                                Rajshahi
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="goToLocation(24.8949, 91.8687, 'Sylhet')">
                                Sylhet
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="goToLocation(25.7439, 89.2752, 'Rangpur')">
                                Rangpur
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let map;
        let marker;

        // Initialize map
        function initMap() {
            // Default location (Dhaka, Bangladesh)
            map = L.map('map').setView([23.8103, 90.4125], 13);

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Add click event to map
            map.on('click', function(e) {
                setLocation(e.latlng.lat, e.latlng.lng);
            });
        }

        function setLocation(lat, lng) {
            // Remove existing marker
            if (marker) {
                map.removeLayer(marker);
            }

            // Add new marker
            marker = L.marker([lat, lng], {
                draggable: true
            }).addTo(map);

            // Update coordinates
            document.getElementById('latitude').value = lat.toFixed(8);
            document.getElementById('longitude').value = lng.toFixed(8);

            // Add drag event to marker
            marker.on('dragend', function(e) {
                const position = e.target.getLatLng();
                document.getElementById('latitude').value = position.lat.toFixed(8);
                document.getElementById('longitude').value = position.lng.toFixed(8);
                reverseGeocode(position.lat, position.lng);
            });

            // Reverse geocode to get address
            reverseGeocode(lat, lng);
        }

        function reverseGeocode(lat, lng) {
            // Using Nominatim API for reverse geocoding
            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`)
                .then(response => response.json())
                .then(data => {
                    if (data.display_name) {
                        document.getElementById('address').value = data.display_name;
                    }
                })
                .catch(error => {
                    console.error('Reverse geocoding failed:', error);
                });
        }

        function searchLocation() {
            const query = document.getElementById('search').value;
            if (!query) return;

            // Using Nominatim API for geocoding
            fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&countrycodes=bd&limit=1`)
                .then(response => response.json())
                .then(data => {
                    if (data.length > 0) {
                        const result = data[0];
                        const lat = parseFloat(result.lat);
                        const lng = parseFloat(result.lon);
                        
                        map.setView([lat, lng], 15);
                        setLocation(lat, lng);
                        document.getElementById('address').value = result.display_name;
                    } else {
                        alert('Location not found. Please try a different search term.');
                    }
                })
                .catch(error => {
                    console.error('Geocoding failed:', error);
                    alert('Search failed. Please try again.');
                });
        }

        function goToLocation(lat, lng, name) {
            map.setView([lat, lng], 13);
            setLocation(lat, lng);
            document.getElementById('search').value = name;
        }

        function getCurrentLocation() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    map.setView([lat, lng], 15);
                    setLocation(lat, lng);
                }, function(error) {
                    alert('Unable to get your location. Please allow location access or search manually.');
                });
            } else {
                alert('Geolocation is not supported by this browser.');
            }
        }

        function copyCoordinates() {
            const lat = document.getElementById('latitude').value;
            const lng = document.getElementById('longitude').value;
            
            if (lat && lng) {
                const coordinates = `Latitude: ${lat}, Longitude: ${lng}`;
                navigator.clipboard.writeText(coordinates).then(function() {
                    alert('Coordinates copied to clipboard!');
                }, function(err) {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = coordinates;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('Coordinates copied to clipboard!');
                });
            } else {
                alert('Please select a location first.');
            }
        }

        // Initialize map when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });

        // Allow Enter key to search
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchLocation();
            }
        });
    </script>
</body>
</html>
