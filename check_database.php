<?php
/**
 * Database Check Script
 * 
 * This script checks the current database structure and items
 */

// Display all errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'GoGoLaundryAdminPanel/config/db.php';

echo "<h2>Database Check</h2>";

// Check if items table exists
$stmt = $pdo->query("SHOW TABLES LIKE 'items'");
$itemsTableExists = $stmt->rowCount() > 0;

if (!$itemsTableExists) {
    echo "Items table does not exist.<br>";
    exit;
}

// Check items with IDs 1 and 2
$stmt = $pdo->query("SELECT * FROM items WHERE id IN (1, 2)");
$items = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Items with IDs 1 and 2:</h3>";

if (count($items) > 0) {
    echo "<ul>";
    foreach ($items as $item) {
        echo "<li>ID: {$item['id']}, Name: {$item['name']}, Price: {$item['price']}</li>";
    }
    echo "</ul>";
} else {
    echo "No items found with IDs 1 and 2.<br>";
    
    // Check all items in the database
    $stmt = $pdo->query("SELECT * FROM items");
    $allItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>All Items in Database:</h3>";
    
    if (count($allItems) > 0) {
        echo "<ul>";
        foreach ($allItems as $item) {
            echo "<li>ID: {$item['id']}, Name: {$item['name']}, Price: {$item['price']}</li>";
        }
        echo "</ul>";
    } else {
        echo "No items found in the database.<br>";
    }
}

// Check order_items table structure
$stmt = $pdo->query("SHOW CREATE TABLE order_items");
$tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);

echo "<h3>Order Items Table Structure:</h3>";
echo "<pre>" . htmlspecialchars($tableInfo['Create Table']) . "</pre>";

// Check if there are any existing orders
$stmt = $pdo->query("SELECT COUNT(*) FROM orders");
$orderCount = $stmt->fetchColumn();

echo "<h3>Order Count: $orderCount</h3>";

// Check if there are any existing order items
$stmt = $pdo->query("SELECT COUNT(*) FROM order_items");
$orderItemCount = $stmt->fetchColumn();

echo "<h3>Order Item Count: $orderItemCount</h3>";

// Check services table
$stmt = $pdo->query("SELECT * FROM services");
$services = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Services:</h3>";

if (count($services) > 0) {
    echo "<ul>";
    foreach ($services as $service) {
        echo "<li>ID: {$service['id']}, Name: {$service['name']}</li>";
    }
    echo "</ul>";
} else {
    echo "No services found in the database.<br>";
}

echo "<br><a href='GoGoLaundryAdminPanel/admin/index.php'>Go to Admin Panel</a>";
?>
