<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/shimmer_popular_items"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:shimmer_auto_start="true"
    app:shimmer_base_color="@color/shimmer_placeholder"
    app:shimmer_highlight_color="@color/shimmer_highlight"
    app:shimmer_duration="1500"
    app:shimmer_direction="left_to_right">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="4dp"
        android:paddingEnd="4dp">

        <!-- First shimmer item -->
        <include layout="@layout/item_popular_item_shimmer"
            android:layout_width="160dp"
            android:layout_height="200dp"
            android:layout_marginEnd="8dp" />

        <!-- Second shimmer item -->
        <include layout="@layout/item_popular_item_shimmer"
            android:layout_width="160dp"
            android:layout_height="200dp"
            android:layout_marginEnd="8dp" />

        <!-- Third shimmer item -->
        <include layout="@layout/item_popular_item_shimmer"
            android:layout_width="160dp"
            android:layout_height="200dp" />

    </LinearLayout>

</com.facebook.shimmer.ShimmerFrameLayout>
