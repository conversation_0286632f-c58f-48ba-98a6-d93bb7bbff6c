/**
 * Real-time Notifications for GoGoLaundry Admin Panel
 * 
 * This script handles real-time notification updates and displays
 */

class RealtimeNotifications {
    constructor() {
        this.isInitialized = false;
        this.notificationContainer = null;
        this.notificationBadge = null;
        this.lastNotificationCheck = Date.now();
        this.checkInterval = 30000; // Check every 30 seconds
        this.intervalId = null;
        
        this.init();
    }
    
    /**
     * Initialize real-time notifications
     */
    init() {
        if (this.isInitialized) return;
        
        console.log('Initializing real-time notifications...');
        
        // Create notification elements if they don't exist
        this.createNotificationElements();
        
        // Start checking for new notifications
        this.startNotificationChecking();
        
        // Listen for FCM messages
        this.setupFCMListener();
        
        // Listen for visibility changes to update when tab becomes active
        this.setupVisibilityListener();
        
        this.isInitialized = true;
        console.log('Real-time notifications initialized');
    }
    
    /**
     * Create notification UI elements
     */
    createNotificationElements() {
        // Create notification container if it doesn't exist
        if (!document.getElementById('realtime-notifications')) {
            const container = document.createElement('div');
            container.id = 'realtime-notifications';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            this.notificationContainer = container;
        } else {
            this.notificationContainer = document.getElementById('realtime-notifications');
        }
        
        // Find or create notification badge
        this.notificationBadge = document.querySelector('.notification-badge');
        if (!this.notificationBadge) {
            // Try to find it in different locations
            this.notificationBadge = document.querySelector('[data-notification-badge]');
        }
    }
    
    /**
     * Start periodic notification checking
     */
    startNotificationChecking() {
        // Initial check
        this.checkForNewNotifications();
        
        // Set up interval
        this.intervalId = setInterval(() => {
            this.checkForNewNotifications();
        }, this.checkInterval);
    }
    
    /**
     * Stop notification checking
     */
    stopNotificationChecking() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }
    
    /**
     * Check for new notifications
     */
    async checkForNewNotifications() {
        try {
            const response = await fetch(`admin/api/notifications/recent.php?since=${this.lastNotificationCheck}`);
            const result = await response.json();
            
            if (result.success && result.data && result.data.length > 0) {
                // Process new notifications
                result.data.forEach(notification => {
                    this.handleNewNotification(notification);
                });
                
                // Update last check time
                this.lastNotificationCheck = Date.now();
                
                // Update notification badge
                this.updateNotificationBadge();
            }
        } catch (error) {
            console.error('Error checking for new notifications:', error);
        }
    }
    
    /**
     * Handle new notification
     */
    handleNewNotification(notification) {
        console.log('New notification received:', notification);
        
        // Show toast notification
        this.showToastNotification(notification);
        
        // Play notification sound
        this.playNotificationSound();
        
        // Update page content if relevant
        this.updatePageContent(notification);
    }
    
    /**
     * Show toast notification
     */
    showToastNotification(notification) {
        const toast = document.createElement('div');
        toast.className = 'toast show';
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="toast-header">
                <i class="fas fa-bell text-primary me-2"></i>
                <strong class="me-auto">${notification.title || 'New Notification'}</strong>
                <small class="text-muted">${this.formatTime(notification.created_at)}</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${notification.message || 'You have a new notification'}
                ${notification.order_number ? `<br><small class="text-muted">Order: ${notification.order_number}</small>` : ''}
            </div>
        `;
        
        // Add click handler
        toast.addEventListener('click', () => {
            this.handleNotificationClick(notification);
        });
        
        // Add to container
        this.notificationContainer.appendChild(toast);
        
        // Auto remove after 8 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 8000);
    }
    
    /**
     * Handle notification click
     */
    handleNotificationClick(notification) {
        if (notification.order_id) {
            // Navigate to order details
            window.location.href = `order_details.php?id=${notification.order_id}`;
        } else if (notification.type === 'user_registration') {
            // Navigate to users page
            window.location.href = 'users.php';
        } else {
            // Navigate to notifications page
            window.location.href = 'notifications.php';
        }
    }
    
    /**
     * Play notification sound
     */
    playNotificationSound() {
        try {
            const audio = new Audio('../assets/sounds/notification.mp3');
            audio.volume = 0.5;
            audio.play().catch(e => {
                console.log('Could not play notification sound:', e);
            });
        } catch (error) {
            console.log('Error playing notification sound:', error);
        }
    }
    
    /**
     * Update notification badge
     */
    async updateNotificationBadge() {
        try {
            const response = await fetch('admin/api/notifications/unread_count.php');
            const result = await response.json();
            
            if (result.success && this.notificationBadge) {
                const count = result.data || 0;
                if (count > 0) {
                    this.notificationBadge.textContent = count;
                    this.notificationBadge.style.display = 'inline';
                } else {
                    this.notificationBadge.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Error updating notification badge:', error);
        }
    }
    
    /**
     * Update page content based on notification type
     */
    updatePageContent(notification) {
        const currentPage = window.location.pathname;
        
        if (notification.type === 'new_order' && 
            (currentPage.includes('index.php') || currentPage.includes('orders.php'))) {
            // Refresh orders table or dashboard
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else if (notification.type === 'user_registration' && 
                   currentPage.includes('users.php')) {
            // Refresh users table
            setTimeout(() => {
                location.reload();
            }, 2000);
        }
    }
    
    /**
     * Setup FCM listener
     */
    setupFCMListener() {
        // Listen for FCM messages if Firebase is available
        if (typeof firebase !== 'undefined' && firebase.messaging) {
            try {
                const messaging = firebase.messaging();
                messaging.onMessage((payload) => {
                    console.log('FCM message received:', payload);
                    
                    // Convert FCM payload to notification format
                    const notification = {
                        title: payload.notification?.title || payload.data?.title,
                        message: payload.notification?.body || payload.data?.message,
                        type: payload.data?.type,
                        order_id: payload.data?.order_id,
                        order_number: payload.data?.order_number,
                        created_at: new Date().toISOString()
                    };
                    
                    this.handleNewNotification(notification);
                });
            } catch (error) {
                console.error('Error setting up FCM listener:', error);
            }
        }
    }
    
    /**
     * Setup visibility change listener
     */
    setupVisibilityListener() {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // Tab became visible, check for new notifications
                this.checkForNewNotifications();
            }
        });
    }
    
    /**
     * Format time for display
     */
    formatTime(timestamp) {
        try {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) { // Less than 1 minute
                return 'Just now';
            } else if (diff < 3600000) { // Less than 1 hour
                const minutes = Math.floor(diff / 60000);
                return `${minutes}m ago`;
            } else if (diff < 86400000) { // Less than 1 day
                const hours = Math.floor(diff / 3600000);
                return `${hours}h ago`;
            } else {
                return date.toLocaleDateString();
            }
        } catch (error) {
            return 'Recently';
        }
    }
    
    /**
     * Destroy the notification system
     */
    destroy() {
        this.stopNotificationChecking();
        this.isInitialized = false;
    }
}

// Initialize real-time notifications when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on admin pages
    if (window.location.pathname.includes('/admin/')) {
        window.realtimeNotifications = new RealtimeNotifications();
    }
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', function() {
    if (window.realtimeNotifications) {
        window.realtimeNotifications.destroy();
    }
});
