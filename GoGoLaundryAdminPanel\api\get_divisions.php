<?php
/**
 * Get Divisions API Endpoint
 *
 * This endpoint returns all divisions
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get all divisions
$stmt = $pdo->query("SELECT * FROM divisions ORDER BY name");
$divisions = $stmt->fetchAll();

// Return success response
jsonResponse(true, 'Divisions retrieved successfully', $divisions);
