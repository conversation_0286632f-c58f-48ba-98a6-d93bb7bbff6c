package com.mdsadrulhasan.gogolaundry.api.deserializer;

import android.util.Log;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.mdsadrulhasan.gogolaundry.model.Order;

import java.lang.reflect.Type;

/**
 * Custom deserializer for Order objects from API responses
 * Handles nested JSON structures where order data is inside an "order" object
 */
public class OrderDeserializer implements JsonDeserializer<Order> {

    private static final String TAG = "OrderDeserializer";

    @Override
    public Order deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        try {
            // Check if we have a JsonObject
            if (!json.isJsonObject()) {
                Log.e(TAG, "Expected JsonObject but got: " + json.getClass().getSimpleName());
                return null;
            }

            JsonObject jsonObject = json.getAsJsonObject();
            
            // Check if we have a nested "order" object
            if (jsonObject.has("order")) {
                Log.d(TAG, "Found nested 'order' object, using it for deserialization");
                jsonObject = jsonObject.getAsJsonObject("order");
            } else {
                Log.d(TAG, "No nested 'order' object found, using root object");
            }

            // Create a new Order object
            Order order = new Order();
            
            // Parse order ID
            if (jsonObject.has("id") && !jsonObject.get("id").isJsonNull()) {
                order.setId(jsonObject.get("id").getAsInt());
            }
            
            // Parse order number
            if (jsonObject.has("order_number") && !jsonObject.get("order_number").isJsonNull()) {
                order.setOrderNumber(jsonObject.get("order_number").getAsString());
            }
            
            // Parse tracking number
            if (jsonObject.has("tracking_number") && !jsonObject.get("tracking_number").isJsonNull()) {
                order.setTrackingNumber(jsonObject.get("tracking_number").getAsString());
            }
            
            // Parse user ID
            if (jsonObject.has("user_id") && !jsonObject.get("user_id").isJsonNull()) {
                order.setUserId(jsonObject.get("user_id").getAsInt());
            }
            
            // Parse total amount
            if (jsonObject.has("total") && !jsonObject.get("total").isJsonNull()) {
                order.setTotalAmount(jsonObject.get("total").getAsDouble());
            }
            
            // Parse status
            if (jsonObject.has("status") && !jsonObject.get("status").isJsonNull()) {
                order.setStatus(jsonObject.get("status").getAsString());
            }
            
            // Parse payment status
            if (jsonObject.has("payment_status") && !jsonObject.get("payment_status").isJsonNull()) {
                order.setPaymentStatus(jsonObject.get("payment_status").getAsString());
            }
            
            // Parse payment method
            if (jsonObject.has("payment_method") && !jsonObject.get("payment_method").isJsonNull()) {
                order.setPaymentMethod(jsonObject.get("payment_method").getAsString());
            }
            
            // Parse pickup date
            if (jsonObject.has("pickup_date") && !jsonObject.get("pickup_date").isJsonNull()) {
                order.setPickupDate(jsonObject.get("pickup_date").getAsString());
            }
            
            // Parse pickup time
            if (jsonObject.has("pickup_time_slot") && !jsonObject.get("pickup_time_slot").isJsonNull()) {
                order.setPickupTime(jsonObject.get("pickup_time_slot").getAsString());
            }
            
            // Parse delivery date
            if (jsonObject.has("delivery_date") && !jsonObject.get("delivery_date").isJsonNull()) {
                order.setDeliveryDate(jsonObject.get("delivery_date").getAsString());
            }
            
            // Parse delivery time
            if (jsonObject.has("delivery_time_slot") && !jsonObject.get("delivery_time_slot").isJsonNull()) {
                order.setDeliveryTime(jsonObject.get("delivery_time_slot").getAsString());
            }
            
            // Parse address
            if (jsonObject.has("pickup_address") && !jsonObject.get("pickup_address").isJsonNull()) {
                order.setAddress(jsonObject.get("pickup_address").getAsString());
            }
            
            // Parse notes
            if (jsonObject.has("notes") && !jsonObject.get("notes").isJsonNull()) {
                order.setNotes(jsonObject.get("notes").getAsString());
            }
            
            // Parse created at
            if (jsonObject.has("created_at") && !jsonObject.get("created_at").isJsonNull()) {
                order.setCreatedAt(jsonObject.get("created_at").getAsString());
            }
            
            // Parse updated at
            if (jsonObject.has("updated_at") && !jsonObject.get("updated_at").isJsonNull()) {
                order.setUpdatedAt(jsonObject.get("updated_at").getAsString());
            }
            
            // Log successful deserialization
            Log.d(TAG, "Successfully deserialized Order: " + order.getOrderNumber() + ", Status: " + order.getStatus());
            
            return order;
        } catch (Exception e) {
            Log.e(TAG, "Error deserializing Order: " + e.getMessage(), e);
            throw new JsonParseException("Error deserializing Order", e);
        }
    }
}
