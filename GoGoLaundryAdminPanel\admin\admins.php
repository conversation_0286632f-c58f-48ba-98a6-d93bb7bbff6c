<?php
/**
 * Admin Users Management
 *
 * This page allows super admins to manage admin users
 */

// Include authentication middleware
require_once 'auth.php';

// Check if user is super admin
if ($adminData['role'] !== 'super_admin') {
    $_SESSION['error_message'] = 'You do not have permission to access this page.';
    header('Location: index.php');
    exit;
}

// Include required files
require_once '../includes/AdminManager.php';

// Initialize admin manager
$adminManager = new AdminManager($pdo);

// Get page parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? (int)$_GET['page'] : 1;
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$perPage = 10;

// Get admins with pagination and filtering
$result = $adminManager->getAllAdmins($page, $perPage, $search);
$admins = $result['admins'];
$pagination = $result['pagination'];

// Handle admin status toggle (activate/deactivate)
if (isset($_POST['toggle_status']) && isset($_POST['admin_id']) && is_numeric($_POST['admin_id'])) {
    $adminId = (int)$_POST['admin_id'];
    $currentStatus = isset($_POST['current_status']) ? (int)$_POST['current_status'] : 0;
    $newStatus = $currentStatus ? 0 : 1;

    // Prevent deactivating own account
    if ($adminId === $adminData['id']) {
        $_SESSION['error_message'] = 'You cannot deactivate your own account.';
    } else {
        $result = $adminManager->updateAdminStatus($adminId, $newStatus);

        if ($result) {
            // Log action
            $adminManager->logAdminAction(
                $adminData['id'],
                'admin_status_update',
                'Updated status for admin ID: ' . $adminId . ' to ' . ($newStatus ? 'active' : 'inactive'),
                getClientIp()
            );

            $_SESSION['success_message'] = 'Admin status updated successfully.';
        } else {
            $_SESSION['error_message'] = 'Failed to update admin status.';
        }
    }

    // Redirect to maintain pagination and search
    header('Location: admins.php?page=' . $page . '&search=' . urlencode($search));
    exit;
}

// Handle admin deletion
if (isset($_POST['delete_admin']) && isset($_POST['admin_id']) && is_numeric($_POST['admin_id'])) {
    $adminId = (int)$_POST['admin_id'];

    // Prevent deleting own account
    if ($adminId === $adminData['id']) {
        $_SESSION['error_message'] = 'You cannot delete your own account.';
    } else {
        $admin = $adminManager->getAdminById($adminId);

        if (!$admin) {
            $_SESSION['error_message'] = 'Admin not found.';
        } else {
            $result = $adminManager->deleteAdmin($adminId);

            if ($result) {
                // Log action
                $adminManager->logAdminAction(
                    $adminData['id'],
                    'admin_delete',
                    'Deleted admin ID: ' . $adminId . ' (' . $admin['username'] . ')',
                    getClientIp()
                );

                $_SESSION['success_message'] = 'Admin deleted successfully.';
            } else {
                $_SESSION['error_message'] = 'Failed to delete admin.';
            }
        }
    }

    // Redirect to maintain pagination and search
    header('Location: admins.php?page=' . $page . '&search=' . urlencode($search));
    exit;
}

// Page title and breadcrumbs
$pageTitle = 'Manage Administrators';
$breadcrumbs = [
    'Administrators' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">Manage Administrators</h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="add_admin.php" class="btn btn-primary">
            <i class="fas fa-user-plus me-1"></i> Add New Admin
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <div class="row align-items-center">
            <div class="col-md-8">
                <form action="admins.php" method="get" class="d-flex">
                    <input type="text" class="form-control me-2" name="search" placeholder="Search by username, email, or name..." value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if (!empty($search)): ?>
                        <a href="admins.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-times"></i>
                        </a>
                    <?php endif; ?>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <span class="badge bg-primary"><?php echo number_format($pagination['total']); ?> Total Admins</span>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (count($admins) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($admins as $admin): ?>
                            <tr>
                                <td><?php echo $admin['id']; ?></td>
                                <td><?php echo htmlspecialchars($admin['username']); ?></td>
                                <td><?php echo htmlspecialchars($admin['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($admin['email']); ?></td>
                                <td>
                                    <?php if ($admin['role'] === 'super_admin'): ?>
                                        <span class="badge bg-danger">Super Admin</span>
                                    <?php else: ?>
                                        <span class="badge bg-primary">Admin</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($admin['is_active']): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($admin['last_login']): ?>
                                        <?php echo (new DateTime($admin['last_login']))->format('M d, Y H:i'); ?>
                                    <?php else: ?>
                                        <span class="text-muted">Never</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-primary dropdown-toggle" id="actionDropdown<?php echo $admin['id']; ?>">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu custom-dropdown" id="actionMenu<?php echo $admin['id']; ?>">
                                            <li>
                                                <a href="edit_admin.php?id=<?php echo $admin['id']; ?>" class="dropdown-item">
                                                    <i class="fas fa-edit me-2"></i> Edit
                                                </a>
                                            </li>
                                            <?php if ($admin['id'] !== $adminData['id']): ?>
                                                <li>
                                                    <a href="javascript:void(0);" class="dropdown-item" onclick="document.getElementById('toggleForm<?php echo $admin['id']; ?>').submit();">
                                                        <?php if ($admin['is_active']): ?>
                                                            <i class="fas fa-user-times me-2"></i> Deactivate
                                                        <?php else: ?>
                                                            <i class="fas fa-user-check me-2"></i> Activate
                                                        <?php endif; ?>
                                                    </a>
                                                    <form id="toggleForm<?php echo $admin['id']; ?>" action="admins.php" method="post" style="display: none;">
                                                        <input type="hidden" name="admin_id" value="<?php echo $admin['id']; ?>">
                                                        <input type="hidden" name="current_status" value="<?php echo $admin['is_active']; ?>">
                                                        <input type="hidden" name="toggle_status" value="1">
                                                    </form>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button type="button" class="dropdown-item text-danger"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#deleteModal"
                                                            data-admin-id="<?php echo $admin['id']; ?>"
                                                            data-admin-name="<?php echo htmlspecialchars($admin['username']); ?>">
                                                        <i class="fas fa-trash-alt me-2"></i> Delete
                                                    </button>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($pagination['total_pages'] > 1): ?>
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>">
                                Previous
                            </a>
                        </li>

                        <?php for ($i = max(1, $page - 2); $i <= min($pagination['total_pages'], $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <li class="page-item <?php echo $page >= $pagination['total_pages'] ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>">
                                Next
                            </a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No administrators found matching your criteria.
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Admin Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Admin Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the admin user <strong id="adminName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="admins.php" method="post">
                    <input type="hidden" name="admin_id" id="deleteAdminId">
                    <button type="submit" name="delete_admin" class="btn btn-danger">Delete Admin</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
// Add page-specific scripts
$pageScripts = ['js/admins.js'];
?>

<?php include 'includes/footer.php'; ?>
