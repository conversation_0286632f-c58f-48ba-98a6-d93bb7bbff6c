<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    app:cardBackgroundColor="@color/white"
    android:foreground="?attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/glass_section_background"
        android:padding="16dp">

        <!-- Order Header -->
        <LinearLayout
            android:id="@+id/order_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/order_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Order #12345"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                android:textStyle="bold"
                android:textColor="@color/accent"
                android:background="@drawable/glass_section_background"
                android:padding="8dp"
                android:elevation="1dp"
                tools:text="Order #12345" />

            <View
                android:id="@+id/status_indicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/success" />

        </LinearLayout>

        <!-- Order Date -->
        <TextView
            android:id="@+id/order_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="June 15, 2024"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
            android:textColor="#009688"
            android:background="@drawable/glass_section_background"
            android:padding="6dp"
            android:elevation="1dp"
            app:layout_constraintTop_toBottomOf="@id/order_header"
            tools:text="June 15, 2024" />

        <!-- Order Status -->
        <LinearLayout
            android:id="@+id/status_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@id/order_date">

            <ImageView
                android:id="@+id/status_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_check"
                android:layout_marginEnd="6dp"
                app:tint="@color/success" />

            <TextView
                android:id="@+id/order_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Delivered"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                android:textColor="@color/success"
                android:textStyle="bold"
                tools:text="Delivered" />

        </LinearLayout>

        <!-- Order Items Preview -->
        <TextView
            android:id="@+id/order_items"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="3 items • Wash &amp; Fold"
            android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
            android:textColor="@color/text_secondary"
            app:layout_constraintTop_toBottomOf="@id/status_layout"
            tools:text="3 items • Wash &amp; Fold" />

        <!-- Divider -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@color/home_divider_subtle"
            app:layout_constraintTop_toBottomOf="@id/order_items" />

        <!-- Order Total and Action -->
        <LinearLayout
            android:id="@+id/bottom_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@id/divider">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Total"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Caption"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/order_total"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="৳ 450"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                    android:textStyle="bold"
                    android:textColor="@color/home_accent_blue"
                    tools:text="৳ 450" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_receipt"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:text="Receipt"
                    android:textAllCaps="false"
                    android:textSize="11sp"
                    android:textColor="@color/background"
                    android:minWidth="0dp"
                    android:background="@drawable/card_background"
                    android:paddingHorizontal="8dp"
                    android:layout_marginEnd="4dp"
                    app:icon="@drawable/ic_receipt"
                    app:iconTint="@color/home_accent_green"
                    app:iconSize="12dp"
                    app:iconPadding="2dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_reorder"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:text="Reorder"
                    android:textAllCaps="false"
                    android:textSize="11sp"
                    android:textColor="@color/background"
                    android:background="@drawable/card_background"
                    android:minWidth="0dp"
                    android:paddingHorizontal="8dp"
                    android:layout_marginEnd="4dp"
                    android:visibility="gone"
                    app:icon="@drawable/ic_refresh"
                    app:iconTint="@color/primary"
                    app:iconSize="12dp"
                    app:iconPadding="2dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_track_order"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:text="Track"
                    android:textAllCaps="false"
                    android:textSize="11sp"
                    android:textColor="#FFF7F6"
                    android:minWidth="0dp"
                    android:background="@drawable/card_background"
                    android:paddingHorizontal="8dp"
                    app:icon="@drawable/ic_location"
                    app:iconTint="@color/accent_dark"
                    app:iconSize="12dp"
                    app:iconPadding="2dp" />


            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
