<?xml version="1.0" encoding="utf-8"?>
<!-- Popular Service Item Layout - Matches Popular Items Design -->
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:strokeColor="@color/card_stroke_light"
    app:strokeWidth="0.5dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <!-- Service Image Container -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/image_card"
            android:layout_width="0dp"
            android:layout_height="188dp"
            app:cardBackgroundColor="@color/background_light"
            app:cardCornerRadius="12dp"
            app:cardElevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/service_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:contentDescription="@string/service_icon"
                android:scaleType="fitXY"
                tools:src="@drawable/ic_washing_machine" />

        </com.google.android.material.card.MaterialCardView>

        <!-- Service Details Container -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="6dp"
            android:padding="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/image_card">

            <!-- Service Name Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@drawable/clean_section_background"
                android:padding="8dp"
                android:elevation="2dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Service:"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-condensed"
                    android:layout_marginEnd="6dp" />

                <TextView
                    android:id="@+id/service_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-condensed"
                    android:background="@drawable/clean_section_background"
                    android:paddingStart="8dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="8dp"
                    android:paddingBottom="4dp"
                    android:elevation="1dp"
                    tools:text="Dry Cleaning" />
            </LinearLayout>

            <!-- Service Description Section -->
            <LinearLayout
                android:id="@+id/description_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="4dp"
                android:background="@drawable/clean_section_background"
                android:padding="8dp"
                android:elevation="1dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="top">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Info:"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:fontFamily="sans-serif-condensed"
                        android:layout_marginEnd="6dp"
                        android:layout_marginTop="1dp" />

                    <TextView
                        android:id="@+id/service_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="@color/text_secondary"
                        android:textSize="13sp"
                        android:fontFamily="sans-serif"
                        android:lineSpacingExtra="2dp"
                        android:background="@drawable/clean_section_background"
                        android:paddingStart="8dp"
                        android:paddingTop="6dp"
                        android:paddingEnd="8dp"
                        android:paddingBottom="6dp"
                        android:elevation="1dp"
                        tools:text="Professional cleaning service for delicate fabrics" />
                </LinearLayout>
            </LinearLayout>

            <!-- View Items Button Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:gravity="center"
                android:background="@drawable/clean_section_background"
                android:padding="6dp"
                android:elevation="2dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_view_items"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:minWidth="120dp"
                    android:text="View Items"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-condensed"
                    android:backgroundTint="@color/primary"
                    android:elevation="6dp"
                    app:cornerRadius="20dp"
                    app:icon="@drawable/ic_arrow_forward"
                    app:iconTint="@android:color/white"
                    app:iconSize="14dp"
                    app:iconGravity="end"
                    app:iconPadding="4dp"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    android:insetLeft="0dp"
                    android:insetRight="0dp"
                    app:rippleColor="@android:color/white"
                    android:letterSpacing="0.05"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />
            </LinearLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
