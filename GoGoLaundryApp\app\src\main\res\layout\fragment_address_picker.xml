<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <!-- Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="4dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:title="Select Address"
            app:titleTextColor="@color/white"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Search Bar -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_search"
                    app:tint="@color/text_secondary"
                    android:layout_marginEnd="12dp" />

                <EditText
                    android:id="@+id/searchEditText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="Search for a location..."
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_color"
                    android:inputType="text"
                    android:imeOptions="actionSearch"
                    android:maxLines="1" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Map Container -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp">

            <!-- Map View -->
            <org.osmdroid.views.MapView
                android:id="@+id/mapView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/background_light" />

            <!-- Current Location FAB -->
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/currentLocationFab"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|end"
                android:layout_margin="16dp"
                android:src="@drawable/ic_my_location"
                app:backgroundTint="@color/colorPrimary"
                app:tint="@color/white"
                app:elevation="8dp"
                app:borderWidth="0dp" />

            <!-- Instructions Overlay -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:layout_margin="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/colorPrimary"
                app:strokeWidth="0dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:text="Tap on the map to select your address"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:drawableStart="@drawable/ic_info"
                    android:drawablePadding="8dp"
                    app:drawableTint="@color/white" />

            </com.google.android.material.card.MaterialCardView>

        </FrameLayout>

        <!-- Selected Address Display -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Selected Address:"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/selectedAddressTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No address selected"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:lineSpacingExtra="2dp"
                    android:visibility="visible" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Confirm Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/confirmButton"
            style="@style/Widget.Material3.Button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_margin="16dp"
            android:text="Confirm Address"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:enabled="false"
            app:icon="@drawable/ic_check"
            app:iconTint="@color/white"
            app:iconSize="20dp"
            app:backgroundTint="@color/colorPrimary"
            app:cornerRadius="28dp" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
