<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AppInsightsSettings">
    <option name="tabSettings">
      <map>
        <entry key="Firebase Crashlytics">
          <value>
            <InsightsFilterSettings>
              <option name="connection">
                <ConnectionSetting>
                  <option name="appId" value="com.mdsadrulhasan.gogolaundry" />
                  <option name="mobileSdkAppId" value="1:523301621504:android:59b7b8816b0e2b7604ede5" />
                  <option name="projectId" value="gogolaundry-c4dd1" />
                  <option name="projectNumber" value="523301621504" />
                </ConnectionSetting>
              </option>
              <option name="signal" value="SIGNAL_UNSPECIFIED" />
              <option name="timeIntervalDays" value="THIRTY_DAYS" />
              <option name="visibilityType" value="ALL" />
            </InsightsFilterSettings>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>