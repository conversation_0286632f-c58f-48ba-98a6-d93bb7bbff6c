<?php
/**
 * Pagination Component
 *
 * This file renders pagination controls for admin pages
 *
 * Required variables:
 * - $pagination: Array containing pagination data (current, total, count, perPage)
 * - Current URL is determined automatically
 */

// Get current URL without query string
$currentUrl = strtok($_SERVER["REQUEST_URI"], '?');

// Get existing query parameters
$queryParams = $_GET;

// Remove page parameter if it exists
if (isset($queryParams['page'])) {
    unset($queryParams['page']);
}

// Build base URL with existing parameters
$baseUrl = $currentUrl . '?' . http_build_query($queryParams);
$baseUrl = $baseUrl . (empty($queryParams) ? '' : '&');

// Calculate pagination range
$range = 2; // Number of pages to show before and after current page
$startPage = max(1, $pagination['current'] - $range);
$endPage = min($pagination['total'], $pagination['current'] + $range);

// Ensure we always show at least 5 pages if available
if ($endPage - $startPage + 1 < 5 && $pagination['total'] >= 5) {
    if ($startPage === 1) {
        $endPage = min($pagination['total'], 5);
    } elseif ($endPage === $pagination['total']) {
        $startPage = max(1, $pagination['total'] - 4);
    }
}
?>

<?php if ($pagination['total'] > 1): ?>
<nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
        <!-- Previous Page -->
        <?php if ($pagination['current'] > 1): ?>
        <li class="page-item">
            <a class="page-link" href="<?php echo $baseUrl; ?>page=<?php echo $pagination['current'] - 1; ?>" aria-label="Previous">
                Previous
            </a>
        </li>
        <?php else: ?>
        <li class="page-item disabled">
            <span class="page-link">Previous</span>
        </li>
        <?php endif; ?>

        <!-- Page Numbers -->
        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
        <li class="page-item <?php echo $i === $pagination['current'] ? 'active' : ''; ?>">
            <a class="page-link" href="<?php echo $baseUrl; ?>page=<?php echo $i; ?>"><?php echo $i; ?></a>
        </li>
        <?php endfor; ?>

        <!-- Next Page -->
        <?php if ($pagination['current'] < $pagination['total']): ?>
        <li class="page-item">
            <a class="page-link" href="<?php echo $baseUrl; ?>page=<?php echo $pagination['current'] + 1; ?>" aria-label="Next">
                Next
            </a>
        </li>
        <?php else: ?>
        <li class="page-item disabled">
            <span class="page-link">Next</span>
        </li>
        <?php endif; ?>
    </ul>
</nav>

<!-- Pagination Info -->
<div class="text-center text-muted small mb-3">
    Showing <?php echo ($pagination['current'] - 1) * $pagination['perPage'] + 1; ?> to
    <?php echo min($pagination['current'] * $pagination['perPage'], $pagination['count']); ?> of
    <?php echo $pagination['count']; ?> entries
</div>
<?php endif; ?>
