<?php
/**
 * Reorder API Endpoint
 *
 * This endpoint allows users to reorder an existing order
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/OrderManager.php';
require_once '../../includes/UserManager.php';
require_once '../../includes/PromoCodeManager.php';
require_once '../../includes/SettingsManager.php';

// Initialize managers
$orderManager = new OrderManager($pdo);
$userManager = new UserManager($pdo);
$promoCodeManager = new PromoCodeManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$orderId = isset($_POST['order_id']) ? (int)$_POST['order_id'] : null;
$userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : null;

// Validate required parameters
if (!$orderId || !$userId) {
    jsonResponse(false, 'Missing required parameters', [], 400);
}

// Verify user exists
$user = $userManager->getUserById($userId);
if (!$user) {
    jsonResponse(false, 'User not found', [], 404);
}

// Get original order
$originalOrder = $orderManager->getOrderById($orderId);
if (!$originalOrder) {
    jsonResponse(false, 'Order not found', [], 404);
}

// Verify the order belongs to the user
if ($originalOrder['user_id'] != $userId) {
    jsonResponse(false, 'Unauthorized access to this order', [], 403);
}

// Get order items
$originalItems = $orderManager->getOrderItems($orderId);
if (empty($originalItems)) {
    jsonResponse(false, 'No items found in the original order', [], 404);
}

try {
    // Start transaction
    $pdo->beginTransaction();

    // Generate unique order number and tracking number
    $orderNumber = 'ORD' . date('ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    $trackingNumber = 'TRK' . date('ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

    // Calculate order totals
    $subtotal = 0;
    foreach ($originalItems as $item) {
        $subtotal += $item['subtotal'];
    }

    // Set default values
    $discount = 0;
    $promoCodeId = null;
    // Get delivery fee from settings
    $deliveryFee = (float)$settingsManager->getSetting('delivery_fee', 50.00);
    $total = $subtotal - $discount + $deliveryFee;

    // Insert new order
    $stmt = $pdo->prepare("
        INSERT INTO orders (
            order_number, tracking_number, user_id, promo_code_id,
            subtotal, discount, delivery_fee, total, payment_method,
            status, pickup_address, pickup_division_id, pickup_district_id,
            pickup_upazilla_id, pickup_date, pickup_time_slot,
            delivery_address, delivery_division_id, delivery_district_id,
            delivery_upazilla_id, notes
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
    ");

    // Use current date for pickup date (7 days from now)
    $pickupDate = date('Y-m-d', strtotime('+7 days'));

    // Use same time slot as original order
    $pickupTimeSlot = $originalOrder['pickup_time_slot'];

    $stmt->execute([
        $orderNumber,
        $trackingNumber,
        $userId,
        $promoCodeId,
        $subtotal,
        $discount,
        $deliveryFee,
        $total,
        $originalOrder['payment_method'],
        'placed', // Initial status
        $originalOrder['pickup_address'],
        $originalOrder['pickup_division_id'],
        $originalOrder['pickup_district_id'],
        $originalOrder['pickup_upazilla_id'],
        $pickupDate,
        $pickupTimeSlot,
        $originalOrder['delivery_address'],
        $originalOrder['delivery_division_id'],
        $originalOrder['delivery_district_id'],
        $originalOrder['delivery_upazilla_id'],
        $originalOrder['notes']
    ]);

    // Get new order ID
    $newOrderId = $pdo->lastInsertId();

    // Insert order items
    $stmt = $pdo->prepare("
        INSERT INTO order_items (
            order_id, item_id, quantity, price, subtotal
        ) VALUES (?, ?, ?, ?, ?)
    ");

    foreach ($originalItems as $item) {
        $stmt->execute([
            $newOrderId,
            $item['item_id'],
            $item['quantity'],
            $item['price'],
            $item['subtotal']
        ]);
    }

    // Add initial status history
    $stmt = $pdo->prepare("
        INSERT INTO order_status_history (
            order_id, status, notes, updated_by, updated_by_type
        ) VALUES (?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $newOrderId,
        'placed',
        'Order placed via reorder',
        $userId,
        'user'
    ]);

    // Commit transaction
    $pdo->commit();

    // Get the new order with all details
    $newOrder = $orderManager->getOrderById($newOrderId);

    // Return success response
    jsonResponse(true, 'Order reordered successfully', $newOrder);

} catch (PDOException $e) {
    // Rollback transaction
    $pdo->rollBack();

    // Log error
    error_log('Database Error in reorder.php: ' . $e->getMessage());

    // Return error response
    jsonResponse(false, 'Failed to reorder: ' . $e->getMessage(), [], 500);
}
