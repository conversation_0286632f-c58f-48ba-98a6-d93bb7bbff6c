<?php
/**
 * Add Sample Data Script
 *
 * This script adds sample services and items to the database if they don't exist
 */

// Include required files
require_once 'config/db.php';
require_once 'includes/functions.php';

// Check if services table exists and has data
$stmt = $pdo->query("SHOW TABLES LIKE 'services'");
$servicesTableExists = $stmt->rowCount() > 0;

if (!$servicesTableExists) {
    echo "Services table does not exist. Creating it...<br>";
    
    // Create services table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `services` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL,
          `bn_name` varchar(100) DEFAULT NULL,
          `description` text DEFAULT NULL,
          `bn_description` text DEFAULT NULL,
          `image_url` varchar(255) DEFAULT NULL,
          `is_active` tinyint(1) NOT NULL DEFAULT 1,
          `sort_order` int(11) NOT NULL DEFAULT 0,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `is_active` (`is_active`),
          KEY `sort_order` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    
    echo "Services table created.<br>";
}

// Check if items table exists
$stmt = $pdo->query("SHOW TABLES LIKE 'items'");
$itemsTableExists = $stmt->rowCount() > 0;

if (!$itemsTableExists) {
    echo "Items table does not exist. Creating it...<br>";
    
    // Create items table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `items` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `service_id` int(11) NOT NULL,
          `name` varchar(100) NOT NULL,
          `bn_name` varchar(100) DEFAULT NULL,
          `description` text DEFAULT NULL,
          `bn_description` text DEFAULT NULL,
          `price` decimal(10,2) NOT NULL,
          `image_url` varchar(255) DEFAULT NULL,
          `is_active` tinyint(1) NOT NULL DEFAULT 1,
          `in_stock` tinyint(1) NOT NULL DEFAULT 1,
          `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
          `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `service_id` (`service_id`),
          KEY `is_active` (`is_active`),
          KEY `in_stock` (`in_stock`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    
    echo "Items table created.<br>";
}

// Check if there are any services
$stmt = $pdo->query("SELECT COUNT(*) FROM services");
$serviceCount = $stmt->fetchColumn();

if ($serviceCount == 0) {
    echo "No services found. Adding sample services...<br>";
    
    // Add sample services
    $services = [
        [
            'name' => 'Wash & Fold',
            'description' => 'Regular laundry service with washing and folding',
            'image_url' => 'wash_fold.jpg',
            'sort_order' => 1
        ],
        [
            'name' => 'Dry Cleaning',
            'description' => 'Professional dry cleaning service for delicate fabrics',
            'image_url' => 'dry_cleaning.jpg',
            'sort_order' => 2
        ],
        [
            'name' => 'Ironing',
            'description' => 'Professional ironing service',
            'image_url' => 'ironing.jpg',
            'sort_order' => 3
        ],
        [
            'name' => 'Wash & Iron',
            'description' => 'Complete laundry service with washing and ironing',
            'image_url' => 'wash_iron.jpg',
            'sort_order' => 4
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO services (name, description, image_url, sort_order)
        VALUES (?, ?, ?, ?)
    ");
    
    foreach ($services as $service) {
        $stmt->execute([
            $service['name'],
            $service['description'],
            $service['image_url'],
            $service['sort_order']
        ]);
        
        echo "Added service: {$service['name']}<br>";
    }
}

// Check if there are any items
$stmt = $pdo->query("SELECT COUNT(*) FROM items");
$itemCount = $stmt->fetchColumn();

if ($itemCount == 0) {
    echo "No items found. Adding sample items...<br>";
    
    // Get service IDs
    $stmt = $pdo->query("SELECT id, name FROM services");
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $serviceMap = [];
    foreach ($services as $service) {
        $serviceMap[$service['name']] = $service['id'];
    }
    
    // Add sample items
    $items = [
        // Wash & Fold items
        [
            'service_name' => 'Wash & Fold',
            'name' => 'T-Shirt',
            'description' => 'Regular T-Shirt',
            'price' => 30.00
        ],
        [
            'service_name' => 'Wash & Fold',
            'name' => 'Pants',
            'description' => 'Regular pants',
            'price' => 40.00
        ],
        [
            'service_name' => 'Wash & Fold',
            'name' => 'Bed Sheet',
            'description' => 'Single or double bed sheet',
            'price' => 60.00
        ],
        
        // Dry Cleaning items
        [
            'service_name' => 'Dry Cleaning',
            'name' => 'Suit',
            'description' => 'Complete suit (jacket and pants)',
            'price' => 250.00
        ],
        [
            'service_name' => 'Dry Cleaning',
            'name' => 'Dress',
            'description' => 'Formal dress',
            'price' => 200.00
        ],
        [
            'service_name' => 'Dry Cleaning',
            'name' => 'Coat',
            'description' => 'Winter coat or jacket',
            'price' => 300.00
        ],
        
        // Ironing items
        [
            'service_name' => 'Ironing',
            'name' => 'Shirt',
            'description' => 'Regular shirt ironing',
            'price' => 15.00
        ],
        [
            'service_name' => 'Ironing',
            'name' => 'Pants',
            'description' => 'Regular pants ironing',
            'price' => 20.00
        ],
        
        // Wash & Iron items
        [
            'service_name' => 'Wash & Iron',
            'name' => 'Shirt',
            'description' => 'Regular shirt wash and iron',
            'price' => 40.00
        ],
        [
            'service_name' => 'Wash & Iron',
            'name' => 'Pants',
            'description' => 'Regular pants wash and iron',
            'price' => 50.00
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO items (service_id, name, description, price)
        VALUES (?, ?, ?, ?)
    ");
    
    foreach ($items as $item) {
        if (isset($serviceMap[$item['service_name']])) {
            $serviceId = $serviceMap[$item['service_name']];
            
            $stmt->execute([
                $serviceId,
                $item['name'],
                $item['description'],
                $item['price']
            ]);
            
            echo "Added item: {$item['name']} for service: {$item['service_name']}<br>";
        } else {
            echo "Warning: Service '{$item['service_name']}' not found for item '{$item['name']}'<br>";
        }
    }
}

echo "<br>Sample data check complete. <a href='admin/create_order.php'>Go to Create Order page</a>";
