<?php
/**
 * Get Current Admin User API
 * 
 * This endpoint returns the current logged-in admin user information
 * 
 * Returns:
 * - success: true/false
 * - data: Admin user information
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Include required files
require_once '../../config/db.php';
require_once '../../includes/functions.php';

// Start session
session_start();

// Initialize response
$response = [
    'success' => false,
    'message' => 'Unknown error occurred',
    'data' => null
];

try {
    // Check if admin is logged in
    if (!isset($_SESSION['admin_id']) || empty($_SESSION['admin_id'])) {
        $response['message'] = 'Admin not logged in';
        echo json_encode($response);
        exit();
    }
    
    $adminId = $_SESSION['admin_id'];
    
    // Get admin user information
    $stmt = $pdo->prepare("
        SELECT id, username, full_name, email, role, created_at
        FROM admin_users 
        WHERE id = ? AND is_active = 1
    ");
    $stmt->execute([$adminId]);
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        $response['message'] = 'Admin user not found or inactive';
        echo json_encode($response);
        exit();
    }
    
    // Remove sensitive information
    unset($admin['password']);
    
    $response['success'] = true;
    $response['message'] = 'Admin user retrieved successfully';
    $response['data'] = $admin;
    
} catch (Exception $e) {
    $response['message'] = 'Server error: ' . $e->getMessage();
    error_log('Get Current Admin User Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
?>
