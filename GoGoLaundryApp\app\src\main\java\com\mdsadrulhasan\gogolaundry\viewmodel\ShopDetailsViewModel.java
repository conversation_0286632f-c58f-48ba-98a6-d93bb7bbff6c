package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.data.Resource;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;
import com.mdsadrulhasan.gogolaundry.repository.LaundryShopRepository;

import java.util.ArrayList;
import java.util.List;

/**
 * ViewModel for Shop Details Fragment
 */
public class ShopDetailsViewModel extends AndroidViewModel {

    private static final String TAG = "ShopDetailsViewModel";

    private final LaundryShopRepository shopRepository;

    // Shop data
    private final MediatorLiveData<LaundryShopEntity> shop = new MediatorLiveData<>();
    private final MediatorLiveData<List<ShopServiceEntity>> services = new MediatorLiveData<>();
    private final MediatorLiveData<List<ShopItemEntity>> items = new MediatorLiveData<>();
    private final MediatorLiveData<List<ShopItemEntity>> filteredItems = new MediatorLiveData<>();

    // UI state
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<Integer> selectedServiceId = new MutableLiveData<>();
    private final MutableLiveData<Integer> selectedTabIndex = new MutableLiveData<>(0);

    // Shop ID
    private final MutableLiveData<Integer> shopId = new MutableLiveData<>();

    public ShopDetailsViewModel(@NonNull Application application) {
        super(application);
        Log.d(TAG, "ShopDetailsViewModel constructor called");

        shopRepository = LaundryShopRepository.getInstance();
        Log.d(TAG, "ShopRepository instance obtained: " + (shopRepository != null));

        // Initialize with empty data
        shop.setValue(null);
        services.setValue(null);
        items.setValue(null);
        filteredItems.setValue(null);
        Log.d(TAG, "ShopDetailsViewModel initialization completed");
    }

    /**
     * Set shop ID and load shop details
     */
    public void setShopId(int shopId) {
        try {
            Log.e(TAG, "=== ShopDetailsViewModel.setShopId() called with shopId: " + shopId + " ===");
            this.shopId.setValue(shopId);
            Log.e(TAG, "About to call loadShopDetails(" + shopId + ")");
            loadShopDetails(shopId);
            Log.e(TAG, "About to call loadShopServices(" + shopId + ")");
            loadShopServices(shopId);
            Log.e(TAG, "About to call loadShopItems(" + shopId + ")");
            loadShopItems(shopId);
            Log.e(TAG, "=== ShopDetailsViewModel.setShopId() completed ===");
        } catch (Exception e) {
            Log.e(TAG, "Exception in setShopId: " + e.getMessage(), e);
        }
    }

    /**
     * Load shop details
     */
    private void loadShopDetails(int shopId) {
        Log.d(TAG, "loadShopDetails() called for shopId: " + shopId);
        isLoading.setValue(true);
        errorMessage.setValue(null);

        LiveData<Resource<LaundryShopEntity>> source = shopRepository.getShopById(shopId);
        shop.addSource(source, resource -> {
            if (resource != null) {
                Log.d(TAG, "Shop data resource received - Status: " + resource.status +
                      ", Data: " + (resource.data != null ? resource.data.getName() : "null") +
                      ", Message: " + resource.message);
                switch (resource.status) {
                    case SUCCESS:
                        if (resource.data != null) {
                            Log.d(TAG, "Shop loaded successfully: " + resource.data.getName());
                            shop.setValue(resource.data);
                        } else {
                            Log.w(TAG, "Shop data is null despite SUCCESS status");
                            errorMessage.setValue("Shop data not found");
                        }
                        break;
                    case ERROR:
                        Log.e(TAG, "Error loading shop: " + resource.message);
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        Log.d(TAG, "Loading shop details...");
                        // Keep loading state
                        break;
                }
                updateLoadingState();
            } else {
                Log.w(TAG, "Received null resource for shop");
            }
        });
    }

    /**
     * Load shop services
     */
    private void loadShopServices(int shopId) {
        Log.e(TAG, "*** VIEWMODEL: loadShopServices() called for shopId: " + shopId + " ***");

        LiveData<Resource<List<ShopServiceEntity>>> source = shopRepository.getShopServices(shopId);
        services.addSource(source, resource -> {
            if (resource != null) {
                Log.d(TAG, "Services resource received - Status: " + resource.status +
                      ", Data count: " + (resource.data != null ? resource.data.size() : "null") +
                      ", Message: " + resource.message);
                switch (resource.status) {
                    case SUCCESS:
                        if (resource.data != null) {
                            Log.e(TAG, "*** REAL DATA: Services loaded successfully: " + resource.data.size() + " services ***");
                            for (ShopServiceEntity service : resource.data) {
                                Log.e(TAG, "*** REAL DATA: Service: " + service.getServiceName() + " (ID: " + service.getServiceId() + ") ***");
                            }
                        } else {
                            Log.e(TAG, "*** REAL DATA: Services data is null despite SUCCESS status ***");
                        }
                        services.setValue(resource.data);
                        break;
                    case ERROR:
                        Log.e(TAG, "Error loading services: " + resource.message);
                        if (errorMessage.getValue() == null) {
                            errorMessage.setValue(resource.message);
                        }
                        break;
                    case LOADING:
                        Log.d(TAG, "Loading services...");
                        // Keep loading state
                        break;
                }
                updateLoadingState();
            }
        });
    }



    /**
     * Load shop items
     */
    private void loadShopItems(int shopId) {
        Log.d(TAG, "loadShopItems() called for shopId: " + shopId);
        LiveData<Resource<List<ShopItemEntity>>> source = shopRepository.getShopItems(shopId);
        items.addSource(source, resource -> {
            if (resource != null) {
                Log.d(TAG, "Items resource received - Status: " + resource.status +
                      ", Data count: " + (resource.data != null ? resource.data.size() : "null") +
                      ", Message: " + resource.message);
                switch (resource.status) {
                    case SUCCESS:
                        if (resource.data != null) {
                            Log.d(TAG, "Items loaded successfully: " + resource.data.size() + " items");
                            for (ShopItemEntity item : resource.data) {
                                Log.d(TAG, "- Item: " + item.getItemName() + " (ID: " + item.getItemId() + ", Service: " + item.getServiceName() + ")");
                            }
                        } else {
                            Log.w(TAG, "Items data is null despite SUCCESS status");
                        }
                        items.setValue(resource.data);
                        filterItemsByService(); // Apply current filter
                        break;
                    case ERROR:
                        Log.e(TAG, "Error loading items: " + resource.message);
                        if (errorMessage.getValue() == null) {
                            errorMessage.setValue(resource.message);
                        }
                        break;
                    case LOADING:
                        Log.d(TAG, "Loading items...");
                        // Keep loading state
                        break;
                }
                updateLoadingState();
            }
        });
    }

    /**
     * Filter items by selected service
     */
    public void filterByService(Integer serviceId) {
        selectedServiceId.setValue(serviceId);
        filterItemsByService();
    }

    /**
     * Apply service filter to items
     */
    private void filterItemsByService() {
        List<ShopItemEntity> allItems = items.getValue();
        Integer serviceId = selectedServiceId.getValue();

        if (allItems == null) {
            filteredItems.setValue(null);
            return;
        }

        if (serviceId == null) {
            // No filter, show all items
            filteredItems.setValue(allItems);
        } else {
            // Filter items by service
            List<ShopItemEntity> filtered = allItems.stream()
                    .filter(item -> {
                        // This would need to be implemented based on your item-service relationship
                        // For now, we'll show all items
                        return true;
                    })
                    .collect(java.util.stream.Collectors.toList());
            filteredItems.setValue(filtered);
        }
    }

    /**
     * Update loading state based on all data sources
     */
    private void updateLoadingState() {
        // Consider loading complete when we have shop data
        // Services and items can load separately
        LaundryShopEntity currentShop = shop.getValue();
        isLoading.setValue(currentShop == null);
    }

    /**
     * Refresh all shop data
     */
    public void refreshShopData() {
        Integer currentShopId = shopId.getValue();
        if (currentShopId != null) {
            loadShopDetails(currentShopId);
            loadShopServices(currentShopId);
            loadShopItems(currentShopId);
        }
    }

    /**
     * Set selected tab index
     */
    public void setSelectedTabIndex(int index) {
        selectedTabIndex.setValue(index);
    }

    /**
     * Check if shop is currently open
     */
    public boolean isShopOpen() {
        LaundryShopEntity currentShop = shop.getValue();
        if (currentShop == null) return false;

        // TODO: Implement operating hours check
        // For now, return true if shop is active
        return currentShop.isActive();
    }

    /**
     * Get shop status text
     */
    public String getShopStatus() {
        if (isShopOpen()) {
            return "Open";
        } else {
            return "Closed";
        }
    }

    /**
     * Calculate estimated delivery time
     */
    public String getEstimatedDeliveryTime() {
        // TODO: Implement based on shop location and current time
        return "24-48 hours";
    }

    // Getters for LiveData

    public LiveData<LaundryShopEntity> getShop() {
        return shop;
    }

    public LiveData<List<ShopServiceEntity>> getServices() {
        return services;
    }

    public LiveData<List<ShopItemEntity>> getItems() {
        return items;
    }

    public LiveData<List<ShopItemEntity>> getFilteredItems() {
        return filteredItems;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<Integer> getSelectedServiceId() {
        return selectedServiceId;
    }

    public LiveData<Integer> getSelectedTabIndex() {
        return selectedTabIndex;
    }

    public LiveData<Integer> getShopId() {
        return shopId;
    }

    // Utility methods

    public void clearErrorMessage() {
        errorMessage.setValue(null);
    }



    /**
     * Get shop rating as formatted string
     */
    public String getFormattedRating() {
        LaundryShopEntity currentShop = shop.getValue();
        if (currentShop == null) return "0.0";
        return String.format("%.1f", currentShop.getRating());
    }

    /**
     * Get review count as formatted string
     */
    public String getFormattedReviewCount() {
        LaundryShopEntity currentShop = shop.getValue();
        if (currentShop == null) return "(0 reviews)";
        int count = currentShop.getTotalReviews();
        return String.format("(%d %s)", count, count == 1 ? "review" : "reviews");
    }

    /**
     * Get distance as formatted string
     */
    public String getFormattedDistance() {
        LaundryShopEntity currentShop = shop.getValue();
        if (currentShop == null || currentShop.getDistance() == 0) return "";
        return String.format("%.1f km away", currentShop.getDistance());
    }
}
