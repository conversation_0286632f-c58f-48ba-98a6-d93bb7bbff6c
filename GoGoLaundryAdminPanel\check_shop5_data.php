<?php
/**
 * Check if Shop ID 5 has services and items data
 */

require_once 'config/config.php';
require_once 'config/db.php';

echo "<h2>🔍 Checking Shop ID 5 Data</h2>";

try {
    // Check if shop exists
    $shopStmt = $pdo->prepare("SELECT id, name FROM laundry_shops WHERE id = 5");
    $shopStmt->execute();
    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$shop) {
        echo "<p style='color: red;'>❌ Shop ID 5 not found!</p>";
        exit;
    }
    
    echo "<p>✅ Shop found: <strong>{$shop['name']}</strong> (ID: {$shop['id']})</p>";
    
    // Check services for shop 5
    $servicesStmt = $pdo->prepare("
        SELECT ss.*, s.name as service_name 
        FROM shop_services ss 
        INNER JOIN services s ON ss.service_id = s.id 
        WHERE ss.shop_id = 5 AND ss.is_available = 1
    ");
    $servicesStmt->execute();
    $services = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>🔧 Services for Shop ID 5:</h3>";
    if (empty($services)) {
        echo "<p style='color: red;'>❌ No services found for shop ID 5</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($services) . " services:</p>";
        foreach ($services as $service) {
            echo "<p>- {$service['service_name']} (Service ID: {$service['service_id']}, Hours: {$service['estimated_hours']})</p>";
        }
    }
    
    // Check items for shop 5
    $itemsStmt = $pdo->prepare("
        SELECT si.*, i.name as item_name, s.name as service_name 
        FROM shop_items si 
        INNER JOIN items i ON si.item_id = i.id 
        INNER JOIN services s ON i.service_id = s.id 
        WHERE si.shop_id = 5 AND si.is_available = 1
    ");
    $itemsStmt->execute();
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>🛍️ Items for Shop ID 5:</h3>";
    if (empty($items)) {
        echo "<p style='color: red;'>❌ No items found for shop ID 5</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($items) . " items:</p>";
        foreach ($items as $item) {
            echo "<p>- {$item['item_name']} (Service: {$item['service_name']}, Price: ৳{$item['custom_price']}, Hours: {$item['estimated_hours']})</p>";
        }
    }
    
    // Test the exact queries used by Android app
    echo "<h3>🤖 Testing Android App Queries:</h3>";
    
    // Test services query (matching the DAO query)
    $androidServicesStmt = $pdo->prepare("
        SELECT ss.*, s.name as serviceName, s.bn_name as serviceBnName, s.image_url as serviceImageUrl 
        FROM shop_services ss 
        INNER JOIN services s ON ss.service_id = s.id 
        WHERE ss.shop_id = ? AND ss.is_available = 1 AND s.is_active = 1 
        ORDER BY s.sort_order ASC, s.name ASC
    ");
    $androidServicesStmt->execute([5]);
    $androidServices = $androidServicesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Services Query Result:</strong> " . count($androidServices) . " services found</p>";
    
    // Test items query (matching the DAO query)
    $androidItemsStmt = $pdo->prepare("
        SELECT si.*, i.name as itemName, i.bn_name as itemBnName, i.image_url as itemImageUrl, 
        i.price as defaultPrice, s.name as serviceName 
        FROM shop_items si 
        INNER JOIN items i ON si.item_id = i.id 
        INNER JOIN services s ON i.service_id = s.id 
        WHERE si.shop_id = ? AND si.is_available = 1 AND i.is_active = 1 
        ORDER BY s.sort_order ASC, i.name ASC
    ");
    $androidItemsStmt->execute([5]);
    $androidItems = $androidItemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Items Query Result:</strong> " . count($androidItems) . " items found</p>";
    
    if (count($androidServices) > 0 && count($androidItems) > 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 SUCCESS! Shop ID 5 has both services and items data.</p>";
        echo "<p>📱 The Android app should now display data for Test Shop 2.</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ ISSUE: Missing data for shop ID 5.</p>";
        if (count($androidServices) == 0) {
            echo "<p>- No services found</p>";
        }
        if (count($androidItems) == 0) {
            echo "<p>- No items found</p>";
        }
        echo "<p>🔧 <a href='add_shop5_services_items.php'>Run the data population script</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #2c3e50; }
h3 { color: #34495e; margin-top: 20px; }
p { margin: 5px 0; }
a { color: #3498db; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
