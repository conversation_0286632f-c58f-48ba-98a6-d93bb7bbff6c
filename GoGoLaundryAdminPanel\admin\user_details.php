<?php
/**
 * User Details
 *
 * This page displays detailed information about a user
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/UserManager.php';
require_once '../includes/LocationManager.php';

// Initialize managers
$userManager = new UserManager($pdo);
$locationManager = new LocationManager($pdo);

// Get user ID from query string
$userId = isset($_GET['id']) && is_numeric($_GET['id']) ? (int)$_GET['id'] : 0;

// Get user data
$user = $userManager->getUserById($userId);

// If user not found, redirect to users page
if (!$user) {
    $_SESSION['error_message'] = 'User not found.';
    header('Location: users.php');
    exit;
}

// Get location data if available
$division = null;
$district = null;
$upazilla = null;

if (!empty($user['division_id'])) {
    $division = $locationManager->getDivisionById($user['division_id']);
}

if (!empty($user['district_id'])) {
    $district = $locationManager->getDistrictById($user['district_id']);
}

if (!empty($user['upazilla_id'])) {
    $upazilla = $locationManager->getUpazillaById($user['upazilla_id']);
}

// Log this view action
$adminManager->logAdminAction(
    $adminData['id'],
    'view_user_details',
    'Viewed details for user ID: ' . $userId . ' (' . $user['full_name'] . ')',
    getClientIp()
);

// Page title and breadcrumbs
$pageTitle = 'User Details: ' . $user['full_name'];
$breadcrumbs = [
    'Users' => 'users.php',
    'User Details' => false
];
?>

<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3 mb-0">User Details</h1>
    </div>
    <div class="col-md-6 text-end">
        <a href="users.php" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left me-1"></i> Back to Users
        </a>
        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-cog me-1"></i> Actions
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a href="reset_password.php?user_id=<?php echo $user['id']; ?>" class="dropdown-item">
                        <i class="fas fa-key me-2"></i> Reset Password
                    </a>
                </li>
                <li>
                    <form action="users.php" method="post" class="d-inline">
                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                        <input type="hidden" name="current_status" value="<?php echo $user['is_verified']; ?>">
                        <button type="submit" name="toggle_verification" class="dropdown-item">
                            <?php if ($user['is_verified']): ?>
                                <i class="fas fa-user-times me-2"></i> Mark as Unverified
                            <?php else: ?>
                                <i class="fas fa-user-check me-2"></i> Mark as Verified
                            <?php endif; ?>
                        </button>
                    </form>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <button type="button" class="dropdown-item text-danger" 
                            data-bs-toggle="modal" 
                            data-bs-target="#deleteModal" 
                            data-user-id="<?php echo $user['id']; ?>"
                            data-user-name="<?php echo htmlspecialchars($user['full_name']); ?>">
                        <i class="fas fa-trash-alt me-2"></i> Delete User
                    </button>
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">User Profile</h5>
            </div>
            <div class="card-body text-center">
                <?php if (!empty($user['profile_picture_url'])): ?>
                    <img src="<?php echo htmlspecialchars($user['profile_picture_url']); ?>" alt="Profile Picture" class="rounded-circle img-thumbnail mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                <?php else: ?>
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 150px; height: 150px; font-size: 4rem; color: #6c757d;">
                        <i class="fas fa-user"></i>
                    </div>
                <?php endif; ?>
                
                <h5 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h5>
                <p class="text-muted mb-3"><?php echo htmlspecialchars($user['phone']); ?></p>
                
                <div class="d-flex justify-content-center mb-2">
                    <?php if ($user['is_verified']): ?>
                        <span class="badge bg-success px-3 py-2">Verified User</span>
                    <?php else: ?>
                        <span class="badge bg-warning px-3 py-2">Unverified User</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Account Information</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>User ID</span>
                        <span class="badge bg-primary rounded-pill"><?php echo $user['id']; ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Account Created</span>
                        <span><?php echo (new DateTime($user['created_at']))->format('M d, Y'); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span>Last Updated</span>
                        <span><?php echo (new DateTime($user['updated_at']))->format('M d, Y'); ?></span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Contact Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <p class="mb-0 fw-bold">Full Name</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0"><?php echo htmlspecialchars($user['full_name']); ?></p>
                    </div>
                </div>
                <hr>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <p class="mb-0 fw-bold">Phone</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0"><?php echo htmlspecialchars($user['phone']); ?></p>
                    </div>
                </div>
                <hr>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <p class="mb-0 fw-bold">Email</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0"><?php echo htmlspecialchars($user['email'] ?? 'Not provided'); ?></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Address Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <p class="mb-0 fw-bold">Address/Village</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0"><?php echo htmlspecialchars($user['address'] ?? 'Not provided'); ?></p>
                    </div>
                </div>
                <hr>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <p class="mb-0 fw-bold">Division</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0"><?php echo $division ? htmlspecialchars($division['name']) : 'Not provided'; ?></p>
                    </div>
                </div>
                <hr>
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <p class="mb-0 fw-bold">District</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0"><?php echo $district ? htmlspecialchars($district['name']) : 'Not provided'; ?></p>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-sm-3">
                        <p class="mb-0 fw-bold">Upazilla</p>
                    </div>
                    <div class="col-sm-9">
                        <p class="text-muted mb-0"><?php echo $upazilla ? htmlspecialchars($upazilla['name']) : 'Not provided'; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm User Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the user <strong id="userName"></strong>?</p>
                <p class="text-warning">This action will soft delete the user. The account will be permanently deleted after 30 days if not restored.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="users.php" method="post">
                    <input type="hidden" name="user_id" id="deleteUserId">
                    <button type="submit" name="delete_user" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set up delete modal
    const deleteModal = document.getElementById('deleteModal');
    if (deleteModal) {
        deleteModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const userName = button.getAttribute('data-user-name');
            
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('userName').textContent = userName;
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
