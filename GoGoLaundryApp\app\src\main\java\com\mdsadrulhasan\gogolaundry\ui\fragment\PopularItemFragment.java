package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.card.MaterialCardView;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.ItemAdapter;
import com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.viewmodel.CartViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.ItemsViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for displaying all popular items in a grid layout
 */
public class PopularItemFragment extends Fragment implements ItemAdapter.ItemClickListener {

    private ItemsViewModel itemsViewModel;
    private CartViewModel cartViewModel;
    private RecyclerView recyclerView;
    private ItemAdapter adapter;
    private SwipeRefreshLayout swipeRefreshLayout;
    private MaterialCardView progressCard;
    private MaterialCardView emptyCard;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_popular_items, container, false);

        // Initialize views
        initViews(view);

        // Set up RecyclerView
        setupRecyclerView();

        // Set up ViewModels
        setupViewModels();

        // Load data
        loadItems();

        return view;
    }

    /**
     * Initialize views
     */
    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.items_recycler_view);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);
        progressCard = view.findViewById(R.id.progress_card);
        emptyCard = view.findViewById(R.id.empty_card);

        // Set up swipe refresh
        swipeRefreshLayout.setOnRefreshListener(this::loadItems);
        swipeRefreshLayout.setColorSchemeResources(R.color.primary);
    }

    /**
     * Set up RecyclerView
     */
    private void setupRecyclerView() {
        recyclerView.setLayoutManager(new GridLayoutManager(requireContext(), 2));
        adapter = new ItemAdapter(new ArrayList<>(), this);
        recyclerView.setAdapter(adapter);

        // Ensure proper scrolling behavior
        recyclerView.setNestedScrollingEnabled(true);
        recyclerView.setHasFixedSize(true);
    }

    /**
     * Set up ViewModels
     */
    private void setupViewModels() {
        itemsViewModel = new ViewModelProvider(this).get(ItemsViewModel.class);
        cartViewModel = new ViewModelProvider(requireActivity()).get(CartViewModel.class);
    }

    /**
     * Load items data
     */
    private void loadItems() {
        // Load popular items from all services
        itemsViewModel.getPopularItems().observe(getViewLifecycleOwner(), resource -> {
            swipeRefreshLayout.setRefreshing(false);

            if (resource.isLoading()) {
                showLoading();
            } else if (resource.isSuccess()) {
                List<ItemEntity> itemEntities = resource.getData();
                if (itemEntities != null && !itemEntities.isEmpty()) {
                    // Convert entities to UI models
                    List<Item> items = convertToItemModels(itemEntities);
                    showItems(items);
                } else {
                    showEmpty();
                }
            } else if (resource.isError()) {
                showError(resource.getMessage());
            }
        });

        // Load popular items
        itemsViewModel.refreshPopularItems();
    }

    /**
     * Convert item entities to UI models
     */
    private List<Item> convertToItemModels(List<ItemEntity> entities) {
        List<Item> items = new ArrayList<>();
        for (ItemEntity entity : entities) {
            Item item = new Item();
            item.setId(entity.getId());
            item.setName(entity.getName());
            item.setDescription(entity.getDescription());
            item.setPrice(entity.getPrice());
            item.setImageUrl(entity.getImageUrl());
            item.setServiceId(entity.getServiceId());
            item.setInStock(entity.isInStock());
            items.add(item);
        }
        return items;
    }

    /**
     * Show loading state
     */
    private void showLoading() {
        progressCard.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.GONE);
    }

    /**
     * Show items data
     */
    private void showItems(List<Item> items) {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        emptyCard.setVisibility(View.GONE);
        adapter.updateItems(items);
    }

    /**
     * Show empty state
     */
    private void showEmpty() {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.VISIBLE);
    }

    /**
     * Show error state
     */
    private void showError(String message) {
        progressCard.setVisibility(View.GONE);
        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();

        // If adapter is empty, show empty view
        if (adapter.getItemCount() == 0) {
            recyclerView.setVisibility(View.GONE);
            emptyCard.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onItemClicked(Item item) {
        // Show item details
        Toast.makeText(getContext(), "Item clicked: " + item.getName(), Toast.LENGTH_SHORT).show();
        // TODO: Implement navigation to item details
    }

    @Override
    public void onAddToCartClicked(Item item, int position) {
        // Add item to cart
        if (cartViewModel.addToCart(item, 1)) {
            Toast.makeText(getContext(), item.getName() + " added to cart", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(getContext(), "Failed to add item to cart", Toast.LENGTH_SHORT).show();
        }
    }
}
