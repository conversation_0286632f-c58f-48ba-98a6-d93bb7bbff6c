<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/notification_header_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/primary"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <!-- Header Title with Back Button -->
                <LinearLayout
                    android:id="@+id/notification_title_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="parent">

                    <!-- Back Button -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/back_button"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_marginEnd="16dp"
                        android:backgroundTint="@android:color/transparent"
                        android:contentDescription="Back"
                        android:minWidth="48dp"
                        android:minHeight="48dp"
                        android:padding="12dp"
                        app:cornerRadius="24dp"
                        app:icon="@drawable/ic_arrow_back"
                        app:iconGravity="textStart"
                        app:iconSize="24dp"
                        app:iconTint="@color/white"
                        app:rippleColor="@color/white" />

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_notification"
                        app:tint="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Notification Details"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>  <!-- Added missing closing tag here -->

        <!-- Notification Content Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/notification_content_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_stroke_light"
            app:strokeWidth="0.5dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="24dp">

                <!-- Notification Icon -->
                <ImageView
                    android:id="@+id/notification_icon"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="@drawable/circle_background"
                    android:backgroundTint="@color/primary_light"
                    android:padding="12dp"
                    android:src="@drawable/ic_notification"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/primary" />

                <!-- Notification Type and Timestamp -->
                <LinearLayout
                    android:id="@+id/notification_meta_container"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/notification_icon"
                    app:layout_constraintTop_toTopOf="@id/notification_icon">

                    <TextView
                        android:id="@+id/notification_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/price_background"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="6dp"
                        android:text="Order Update"
                        android:textColor="@color/info"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:textAllCaps="true"
                        android:letterSpacing="0.1"
                        tools:text="PROMOTION" />

                    <TextView
                        android:id="@+id/notification_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="6dp"
                        android:text="2 hours ago"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:alpha="0.8"
                        tools:text="2 hours ago" />

                </LinearLayout>

                <!-- Title -->
                <TextView
                    android:id="@+id/notification_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="Your order has been confirmed"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/text_primary"
                    android:textStyle="bold"
                    android:lineSpacingExtra="2dp"
                    app:layout_constraintTop_toBottomOf="@id/notification_icon"
                    tools:text="Testing notification" />

                <!-- Message -->
                <TextView
                    android:id="@+id/notification_message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:lineSpacingExtra="6dp"
                    android:text="Great news! Your laundry order has been confirmed and is being processed."
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    android:textColor="@color/text_primary"
                    android:alpha="0.9"
                    app:layout_constraintTop_toBottomOf="@id/notification_title"
                    tools:text="This is a test notification with an image to verify the Android app displays images correctly." />

                <!-- Notification Image Container (if available) -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/notification_image_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:strokeColor="@color/card_stroke_light"
                    app:strokeWidth="0.5dp"
                    app:layout_constraintTop_toBottomOf="@id/notification_message"
                    tools:visibility="visible">

                    <ImageView
                        android:id="@+id/notification_image"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_margin="8dp"
                        android:background="@drawable/rounded_corner_background"
                        android:scaleType="centerCrop"
                        android:contentDescription="@string/notification_image"
                        tools:src="@drawable/placeholder_image" />

                </com.google.android.material.card.MaterialCardView>

                <!-- Order Information Container (if applicable) -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/order_info_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:visibility="gone"
                    app:cardBackgroundColor="@color/white"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:strokeColor="@color/card_stroke_light"
                    app:strokeWidth="0.5dp"
                    app:layout_constraintTop_toBottomOf="@id/notification_image_container"
                    tools:visibility="visible">

                    <LinearLayout
                        android:id="@+id/order_info_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <ImageView
                                android:layout_width="20dp"
                                android_iconid="@drawable/ic_receipt"
                                android:layout_height="20dp"
                                android:layout_marginEnd="8dp"
                                app:tint="@color/primary"
                                android:src="@drawable/ic_receipt" /> <!-- Added missing src attribute here -->

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Order Information"
                                android:textAppearance="@style/TextAppearance.MaterialComponents.Subtitle1"
                                android:textColor="@color/primary"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/order_number"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Order #12345"
                            android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                            android:textColor="@color/text_primary"
                            tools:text="Order #12345" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>  <!-- Added missing closing tag here -->

        <!-- Action Buttons Section -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/notification_actions_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardBackgroundColor="@color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:strokeColor="@color/card_stroke_light"
            app:strokeWidth="0.5dp">

            <!-- Action Buttons Container -->
            <LinearLayout
                android:id="@+id/action_buttons_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:padding="20dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_mark_read"
                    style="@style/Widget.MaterialComponents.Button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:backgroundTint="@color/primary"
                    android:text="Mark as Read"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    app:cornerRadius="12dp"
                    app:icon="@drawable/ic_check"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"
                    app:iconTint="@android:color/white" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_view_order"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="View Order"
                    android:textColor="@color/primary"
                    android:textStyle="bold"
                    android:visibility="gone"
                    app:cornerRadius="12dp"
                    app:icon="@drawable/ic_receipt"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"
                    app:iconTint="@color/primary"
                    app:strokeColor="@color/primary"
                    app:strokeWidth="2dp"
                    tools:visibility="visible" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>