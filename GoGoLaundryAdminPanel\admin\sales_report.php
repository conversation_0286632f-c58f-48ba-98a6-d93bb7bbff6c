<?php
/**
 * Sales Report Page
 *
 * This page displays sales reports and analytics
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OrderManager.php';

// Include authentication middleware
require_once 'auth.php';

// Initialize OrderManager
$orderManager = new OrderManager($pdo);

// Get filter parameters
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$groupBy = isset($_GET['group_by']) ? $_GET['group_by'] : 'day';

// Validate dates
if (strtotime($startDate) > strtotime($endDate)) {
    $_SESSION['error_message'] = 'Start date cannot be after end date';
    $startDate = date('Y-m-d', strtotime('-30 days'));
    $endDate = date('Y-m-d');
}

// Get sales data
$salesData = getSalesData($pdo, $startDate, $endDate, $groupBy);

// Get summary statistics
$totalOrders = getTotalOrders($pdo, $startDate, $endDate);
$totalRevenue = getTotalRevenue($pdo, $startDate, $endDate);
$averageOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;
$topServices = getTopServices($pdo, $startDate, $endDate, 5);
$topItems = getTopItems($pdo, $startDate, $endDate, 5);
$paymentMethodStats = getPaymentMethodStats($pdo, $startDate, $endDate);

// Page title and breadcrumbs
$pageTitle = 'Sales Report';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Reports', 'link' => ''],
    ['text' => 'Sales Report', 'link' => '']
];

// Add Chart.js to page scripts
$pageScripts = [
    '../assets/js/chart.min.js',
    '../assets/js/sales-report-charts.js'
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Sales Report</h1>

    <!-- Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Options</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="sales_report.php" class="row">
                <div class="col-md-3 mb-3">
                    <label for="start_date">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="end_date">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="group_by">Group By</label>
                    <select class="form-control" id="group_by" name="group_by">
                        <option value="day" <?= $groupBy === 'day' ? 'selected' : '' ?>>Day</option>
                        <option value="week" <?= $groupBy === 'week' ? 'selected' : '' ?>>Week</option>
                        <option value="month" <?= $groupBy === 'month' ? 'selected' : '' ?>>Month</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary mr-2">Apply Filters</button>
                    <a href="sales_report.php" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalOrders) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalRevenue, 2) ?> BDT</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Average Order Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($averageOrderValue, 2) ?> BDT</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Date Range</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= date('M d', strtotime($startDate)) ?> - <?= date('M d, Y', strtotime($endDate)) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Chart -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Sales Overview</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Export Options:</div>
                            <a class="dropdown-item" href="export_sales.php?format=csv&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>&group_by=<?= urlencode($groupBy) ?>">Export to CSV</a>
                            <a class="dropdown-item" href="export_sales.php?format=excel&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>&group_by=<?= urlencode($groupBy) ?>">Export to Excel</a>
                            <a class="dropdown-item" href="export_sales.php?format=pdf&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>&group_by=<?= urlencode($groupBy) ?>">Export to PDF</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4">
                        <canvas id="paymentMethodsChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <?php foreach ($paymentMethodStats as $method => $data): ?>
                            <span class="mr-2">
                                <i class="fas fa-circle" style="color: <?= $data['color'] ?>"></i> <?= $data['label'] ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Services and Items -->
    <div class="row">
        <!-- Top Services -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Services</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($topServices)): ?>
                        <p class="text-center">No service data available for the selected period.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Service</th>
                                        <th>Orders</th>
                                        <th>Revenue</th>
                                        <th>% of Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($topServices as $service): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($service['name']) ?></td>
                                            <td><?= number_format($service['orders']) ?></td>
                                            <td><?= number_format($service['revenue'], 2) ?> BDT</td>
                                            <td>
                                                <div class="progress mb-4">
                                                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?= $service['percentage'] ?>%" aria-valuenow="<?= $service['percentage'] ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <?= number_format($service['percentage'], 1) ?>%
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Top Items -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Items</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($topItems)): ?>
                        <p class="text-center">No item data available for the selected period.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Service</th>
                                        <th>Quantity</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($topItems as $item): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($item['name']) ?></td>
                                            <td><?= htmlspecialchars($item['service_name']) ?></td>
                                            <td><?= number_format($item['quantity']) ?></td>
                                            <td><?= number_format($item['revenue'], 2) ?> BDT</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Sales Data Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Sales Data</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="salesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Period</th>
                            <th>Orders</th>
                            <th>Revenue</th>
                            <th>Average Order Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($salesData as $period => $data): ?>
                            <tr>
                                <td><?= htmlspecialchars($period) ?></td>
                                <td><?= number_format($data['orders']) ?></td>
                                <td><?= number_format($data['revenue'], 2) ?> BDT</td>
                                <td><?= number_format($data['avg_order_value'], 2) ?> BDT</td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
    // Pass data to charts
    const salesData = <?= json_encode(array_values($salesData)) ?>;
    const salesLabels = <?= json_encode(array_keys($salesData)) ?>;
    const paymentMethodsData = <?= json_encode(array_map(function($method) { return $method['count']; }, $paymentMethodStats)) ?>;
    const paymentMethodsLabels = <?= json_encode(array_map(function($method) { return $method['label']; }, $paymentMethodStats)) ?>;
    const paymentMethodsColors = <?= json_encode(array_map(function($method) { return $method['color']; }, $paymentMethodStats)) ?>;

    // Initialize charts when the page is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initSalesChart(salesLabels, salesData);
        initPaymentMethodsChart(paymentMethodsLabels, paymentMethodsData, paymentMethodsColors);
    });
</script>

<?php
/**
 * Get sales data grouped by day, week, or month
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param string $groupBy Group by day, week, or month
 * @return array Sales data
 */
function getSalesData($pdo, $startDate, $endDate, $groupBy = 'day') {
    // Determine group by clause and format
    switch ($groupBy) {
        case 'week':
            $groupByClause = "YEARWEEK(created_at, 1)";
            $labelFormat = "Week %v, %Y";
            break;
        case 'month':
            $groupByClause = "DATE_FORMAT(created_at, '%Y-%m')";
            $labelFormat = "%b %Y";
            break;
        default: // day
            $groupByClause = "DATE(created_at)";
            $labelFormat = "%b %e, %Y";
            break;
    }

    // Get sales data
    $stmt = $pdo->prepare("
        SELECT
            $groupByClause AS period,
            DATE_FORMAT(MIN(created_at), '$labelFormat') AS period_label,
            COUNT(*) AS orders,
            SUM(total) AS revenue,
            AVG(total) AS avg_order_value
        FROM orders
        WHERE DATE(created_at) BETWEEN ? AND ?
        AND status != 'cancelled'
        GROUP BY period
        ORDER BY period
    ");
    $stmt->execute([$startDate, $endDate]);
    $results = $stmt->fetchAll();

    // Format results
    $salesData = [];
    foreach ($results as $row) {
        $salesData[$row['period_label']] = [
            'orders' => (int)$row['orders'],
            'revenue' => (float)$row['revenue'],
            'avg_order_value' => (float)$row['avg_order_value']
        ];
    }

    return $salesData;
}

/**
 * Get total number of orders
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return int Total number of orders
 */
function getTotalOrders($pdo, $startDate, $endDate) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM orders
        WHERE DATE(created_at) BETWEEN ? AND ?
        AND status != 'cancelled'
    ");
    $stmt->execute([$startDate, $endDate]);
    return (int)$stmt->fetchColumn();
}

/**
 * Get total revenue
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return float Total revenue
 */
function getTotalRevenue($pdo, $startDate, $endDate) {
    $stmt = $pdo->prepare("
        SELECT SUM(total)
        FROM orders
        WHERE DATE(created_at) BETWEEN ? AND ?
        AND status != 'cancelled'
    ");
    $stmt->execute([$startDate, $endDate]);
    return (float)$stmt->fetchColumn() ?: 0;
}

/**
 * Get top services by revenue
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param int $limit Number of services to retrieve
 * @return array Top services
 */
function getTopServices($pdo, $startDate, $endDate, $limit = 5) {
    $stmt = $pdo->prepare("
        SELECT
            s.id,
            s.name,
            COUNT(DISTINCT o.id) AS orders,
            SUM(oi.subtotal) AS revenue
        FROM services s
        JOIN items i ON i.service_id = s.id
        JOIN order_items oi ON oi.item_id = i.id
        JOIN orders o ON oi.order_id = o.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
        GROUP BY s.id, s.name
        ORDER BY revenue DESC
        LIMIT ?
    ");
    $stmt->execute([$startDate, $endDate, $limit]);
    $services = $stmt->fetchAll();

    // Calculate total revenue for percentage
    $totalRevenue = getTotalRevenue($pdo, $startDate, $endDate);

    // Calculate percentage for each service
    foreach ($services as &$service) {
        $service['percentage'] = $totalRevenue > 0 ? ($service['revenue'] / $totalRevenue) * 100 : 0;
    }

    return $services;
}

/**
 * Get top items by quantity
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param int $limit Number of items to retrieve
 * @return array Top items
 */
function getTopItems($pdo, $startDate, $endDate, $limit = 5) {
    $stmt = $pdo->prepare("
        SELECT
            i.id,
            i.name,
            s.name AS service_name,
            SUM(oi.quantity) AS quantity,
            SUM(oi.subtotal) AS revenue
        FROM items i
        JOIN services s ON i.service_id = s.id
        JOIN order_items oi ON oi.item_id = i.id
        JOIN orders o ON oi.order_id = o.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
        GROUP BY i.id, i.name, s.name
        ORDER BY quantity DESC
        LIMIT ?
    ");
    $stmt->execute([$startDate, $endDate, $limit]);
    return $stmt->fetchAll();
}

/**
 * Get payment method statistics
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return array Payment method statistics
 */
function getPaymentMethodStats($pdo, $startDate, $endDate) {
    $stmt = $pdo->prepare("
        SELECT
            payment_method,
            COUNT(*) AS count,
            SUM(total) AS revenue
        FROM orders
        WHERE DATE(created_at) BETWEEN ? AND ?
        AND status != 'cancelled'
        GROUP BY payment_method
    ");
    $stmt->execute([$startDate, $endDate]);
    $results = $stmt->fetchAll();

    // Define labels and colors for payment methods
    $methodLabels = [
        'cash' => 'Cash on Delivery',
        'card' => 'Card Payment',
        'mobile_banking' => 'Mobile Banking'
    ];

    $methodColors = [
        'cash' => '#4e73df', // primary
        'card' => '#1cc88a', // success
        'mobile_banking' => '#f6c23e' // warning
    ];

    // Format results
    $stats = [];
    foreach ($results as $row) {
        $method = $row['payment_method'];
        $stats[$method] = [
            'label' => $methodLabels[$method] ?? ucfirst($method),
            'count' => (int)$row['count'],
            'revenue' => (float)$row['revenue'],
            'color' => $methodColors[$method] ?? '#6c757d' // default to secondary color
        ];
    }

    return $stats;
}
?>
