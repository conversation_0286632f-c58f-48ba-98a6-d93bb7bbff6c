<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services Pagination Example - GoGoLaundry</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .service-card {
            transition: transform 0.2s;
        }
        .service-card:hover {
            transform: translateY(-5px);
        }
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: 1px solid #dee2e6;
        }
        .pagination .page-item.active .page-link {
            background-color: #007bff;
            border-color: #007bff;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">GoGoLaundry Services with Pagination</h1>
        
        <!-- Search and Filter Controls -->
        <div class="row mb-4">
            <div class="col-md-6">
                <input type="text" id="searchInput" class="form-control" placeholder="Search services...">
            </div>
            <div class="col-md-3">
                <select id="activeFilter" class="form-select">
                    <option value="">All Services</option>
                    <option value="1">Active Only</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="perPageSelect" class="form-select">
                    <option value="5">5 per page</option>
                    <option value="10">10 per page</option>
                    <option value="20">20 per page</option>
                </select>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading services...</p>
        </div>

        <!-- Services Grid -->
        <div id="servicesContainer" class="row">
            <!-- Services will be loaded here -->
        </div>

        <!-- Pagination -->
        <nav aria-label="Services pagination" class="mt-4">
            <ul id="pagination" class="pagination justify-content-center">
                <!-- Pagination will be generated here -->
            </ul>
        </nav>

        <!-- Results Info -->
        <div id="resultsInfo" class="text-center mt-3 text-muted">
            <!-- Results info will be displayed here -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class ServicesPagination {
            constructor() {
                this.currentPage = 1;
                this.perPage = 5;
                this.search = '';
                this.activeOnly = '';
                this.apiUrl = 'http://*************/GoGoLaundry/GoGoLaundryAdminPanel/api/services.php';
                
                this.initEventListeners();
                this.loadServices();
            }

            initEventListeners() {
                // Search input
                document.getElementById('searchInput').addEventListener('input', (e) => {
                    this.search = e.target.value;
                    this.currentPage = 1;
                    this.loadServices();
                });

                // Active filter
                document.getElementById('activeFilter').addEventListener('change', (e) => {
                    this.activeOnly = e.target.value;
                    this.currentPage = 1;
                    this.loadServices();
                });

                // Per page selector
                document.getElementById('perPageSelect').addEventListener('change', (e) => {
                    this.perPage = parseInt(e.target.value);
                    this.currentPage = 1;
                    this.loadServices();
                });
            }

            async loadServices() {
                this.showLoading(true);
                
                try {
                    const params = new URLSearchParams({
                        page: this.currentPage,
                        per_page: this.perPage
                    });

                    if (this.search) params.append('search', this.search);
                    if (this.activeOnly) params.append('active_only', this.activeOnly);

                    const response = await fetch(`${this.apiUrl}?${params}`);
                    const data = await response.json();

                    if (data.success) {
                        this.renderServices(data.data.services);
                        this.renderPagination(data.data.pagination);
                        this.renderResultsInfo(data.data.pagination);
                    } else {
                        this.showError('Failed to load services');
                    }
                } catch (error) {
                    console.error('Error loading services:', error);
                    this.showError('Error loading services');
                } finally {
                    this.showLoading(false);
                }
            }

            renderServices(services) {
                const container = document.getElementById('servicesContainer');
                
                if (services.length === 0) {
                    container.innerHTML = '<div class="col-12 text-center"><p class="text-muted">No services found</p></div>';
                    return;
                }

                container.innerHTML = services.map(service => `
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card service-card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <img src="${service.image_url}" alt="${service.name}" 
                                         class="me-3" style="width: 48px; height: 48px; object-fit: cover;">
                                    <h5 class="card-title mb-0">${service.name}</h5>
                                </div>
                                <p class="card-text">${service.description}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge ${service.is_active ? 'bg-success' : 'bg-secondary'}">
                                        ${service.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                    <small class="text-muted">ID: ${service.id}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            renderPagination(pagination) {
                const paginationContainer = document.getElementById('pagination');
                
                if (pagination.total <= 1) {
                    paginationContainer.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                // Previous button
                paginationHTML += `
                    <li class="page-item ${pagination.current === 1 ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="servicesPagination.goToPage(${pagination.current - 1})">Previous</a>
                    </li>
                `;

                // Page numbers
                for (let i = 1; i <= pagination.total; i++) {
                    paginationHTML += `
                        <li class="page-item ${i === pagination.current ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="servicesPagination.goToPage(${i})">${i}</a>
                        </li>
                    `;
                }

                // Next button
                paginationHTML += `
                    <li class="page-item ${pagination.current === pagination.total ? 'disabled' : ''}">
                        <a class="page-link" href="#" onclick="servicesPagination.goToPage(${pagination.current + 1})">Next</a>
                    </li>
                `;

                paginationContainer.innerHTML = paginationHTML;
            }

            renderResultsInfo(pagination) {
                const info = document.getElementById('resultsInfo');
                const start = ((pagination.current - 1) * pagination.perPage) + 1;
                const end = Math.min(pagination.current * pagination.perPage, pagination.count);
                
                info.innerHTML = `Showing ${start}-${end} of ${pagination.count} services`;
            }

            goToPage(page) {
                this.currentPage = page;
                this.loadServices();
            }

            showLoading(show) {
                document.getElementById('loading').style.display = show ? 'block' : 'none';
                document.getElementById('servicesContainer').style.display = show ? 'none' : 'block';
            }

            showError(message) {
                const container = document.getElementById('servicesContainer');
                container.innerHTML = `<div class="col-12 text-center"><p class="text-danger">${message}</p></div>`;
            }
        }

        // Initialize the pagination system
        const servicesPagination = new ServicesPagination();
    </script>
</body>
</html>
