<?php
/**
 * Order Manager Class
 *
 * This class handles order-related operations
 */

class OrderManager {
    private $pdo;

    /**
     * Constructor
     *
     * @param PDO $pdo PDO database connection
     */
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Get total number of orders
     *
     * @param string $startDate Optional start date in Y-m-d format
     * @param string $endDate Optional end date in Y-m-d format
     * @return int Total number of orders
     */
    public function getTotalOrders($startDate = null, $endDate = null) {
        if ($startDate && $endDate) {
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) BETWEEN ? AND ?");
            $stmt->execute([$startDate, $endDate]);
        } else {
            $stmt = $this->pdo->query("SELECT COUNT(*) FROM orders");
        }
        return (int)$stmt->fetchColumn();
    }

    /**
     * Get order count by status
     *
     * @param string $status Order status
     * @return int Number of orders with the specified status
     */
    public function getOrderCountByStatus($status) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM orders WHERE status = ?");
        $stmt->execute([$status]);
        return (int)$stmt->fetchColumn();
    }

    /**
     * Get order count by date
     *
     * @param string $date Date in Y-m-d format
     * @return int Number of orders on the specified date
     */
    public function getOrderCountByDate($date) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?");
        $stmt->execute([$date]);
        return (int)$stmt->fetchColumn();
    }

    /**
     * Get revenue for a specific date range
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format (optional)
     * @return float Total revenue for the specified date range
     */
    public function getRevenue($startDate, $endDate = null) {
        if ($endDate === null) {
            $endDate = $startDate;
        }

        try {
            // Query to get actual revenue from database
            $stmt = $this->pdo->prepare("
                SELECT SUM(total)
                FROM orders
                WHERE DATE(created_at) BETWEEN ? AND ?
                AND status != 'cancelled'
            ");
            $stmt->execute([$startDate, $endDate]);
            $result = $stmt->fetchColumn();

            // Log the query and result for debugging
            error_log("Revenue query for dates $startDate to $endDate returned: " . ($result ?: 0));

            return (float)$result ?: 0;
        } catch (PDOException $e) {
            error_log("Error in getRevenue method: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get daily revenue for a specific date range
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @return array Array of daily revenue data
     */
    public function getDailyRevenue($startDate, $endDate) {
        try {
            // Query to get actual daily revenue from database
            $stmt = $this->pdo->prepare("
                SELECT DATE(created_at) as date, SUM(total) as revenue
                FROM orders
                WHERE DATE(created_at) BETWEEN ? AND ?
                AND status != 'cancelled'
                GROUP BY DATE(created_at)
                ORDER BY DATE(created_at)
            ");
            $stmt->execute([$startDate, $endDate]);
            $result = $stmt->fetchAll();

            // Log the query and result count for debugging
            error_log("Daily revenue query for dates $startDate to $endDate returned " . count($result) . " rows");

            return $result;
        } catch (PDOException $e) {
            error_log("Error in getDailyRevenue method: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get monthly revenue for a specific year
     *
     * @param int $year Year
     * @return array Array of monthly revenue data
     */
    public function getMonthlyRevenue($year) {
        try {
            // Query to get actual monthly revenue from database
            $stmt = $this->pdo->prepare("
                SELECT MONTH(created_at) as month, SUM(total) as revenue
                FROM orders
                WHERE YEAR(created_at) = ?
                AND status != 'cancelled'
                GROUP BY MONTH(created_at)
                ORDER BY MONTH(created_at)
            ");
            $stmt->execute([$year]);
            $result = $stmt->fetchAll();

            // Log the query and result count for debugging
            error_log("Monthly revenue query for year $year returned " . count($result) . " rows");

            return $result;
        } catch (PDOException $e) {
            error_log("Error in getMonthlyRevenue method: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get total revenue for a date range
     *
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @return float Total revenue
     */
    public function getTotalRevenue($startDate = null, $endDate = null) {
        if ($startDate && $endDate) {
            return $this->getRevenue($startDate, $endDate);
        }

        // If no date range is provided, get all-time revenue
        try {
            $stmt = $this->pdo->query("
                SELECT SUM(total)
                FROM orders
                WHERE status != 'cancelled'
            ");
            $result = $stmt->fetchColumn();
            return (float)$result ?: 0;
        } catch (PDOException $e) {
            error_log("Error in getTotalRevenue method: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get recent orders
     *
     * @param int $limit Number of orders to retrieve
     * @return array Array of recent orders
     */
    public function getRecentOrders($limit = 5) {
        $stmt = $this->pdo->prepare("
            SELECT o.*, u.full_name as customer_name
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            ORDER BY o.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    }

    /**
     * Get order details by ID
     *
     * @param int $orderId Order ID
     * @return array|bool Order details or false if not found
     */
    public function getOrderById($orderId) {
        $stmt = $this->pdo->prepare("
            SELECT o.*,
                   u.full_name as customer_name,
                   u.phone as customer_phone,
                   u.email as customer_email,
                   dp.full_name as delivery_person_name,
                   dp.phone as delivery_person_phone,
                   pd.name as pickup_division_name,
                   pdi.name as pickup_district_name,
                   pu.name as pickup_upazilla_name,
                   dd.name as delivery_division_name,
                   ddi.name as delivery_district_name,
                   du.name as delivery_upazilla_name,
                   pc.code as promo_code
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            LEFT JOIN delivery_personnel dp ON o.delivery_personnel_id = dp.id
            LEFT JOIN divisions pd ON o.pickup_division_id = pd.id
            LEFT JOIN districts pdi ON o.pickup_district_id = pdi.id
            LEFT JOIN upazillas pu ON o.pickup_upazilla_id = pu.id
            LEFT JOIN divisions dd ON o.delivery_division_id = dd.id
            LEFT JOIN districts ddi ON o.delivery_district_id = ddi.id
            LEFT JOIN upazillas du ON o.delivery_upazilla_id = du.id
            LEFT JOIN promo_codes pc ON o.promo_code_id = pc.id
            WHERE o.id = ?
        ");
        $stmt->execute([$orderId]);
        return $stmt->fetch();
    }

    /**
     * Get order items by order ID
     *
     * @param int $orderId Order ID
     * @return array Array of order items
     */
    public function getOrderItems($orderId) {
        $stmt = $this->pdo->prepare("
            SELECT oi.*,
                   i.name,
                   i.bn_name,
                   i.image_url,
                   s.name as service_name,
                   s.bn_name as service_bn_name
            FROM order_items oi
            JOIN items i ON oi.item_id = i.id
            JOIN services s ON i.service_id = s.id
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$orderId]);
        return $stmt->fetchAll();
    }

    /**
     * Get order status history by order ID
     *
     * @param int $orderId Order ID
     * @return array Array of order status history
     */
    public function getOrderStatusHistory($orderId) {
        $stmt = $this->pdo->prepare("
            SELECT osh.*,
                   CASE
                       WHEN osh.updated_by_type = 'admin' THEN au.full_name
                       WHEN osh.updated_by_type = 'user' THEN u.full_name
                       WHEN osh.updated_by_type = 'delivery_personnel' THEN dp.full_name
                       ELSE 'System'
                   END as updated_by_name
            FROM order_status_history osh
            LEFT JOIN admin_users au ON osh.updated_by = au.id AND osh.updated_by_type = 'admin'
            LEFT JOIN users u ON osh.updated_by = u.id AND osh.updated_by_type = 'user'
            LEFT JOIN delivery_personnel dp ON osh.updated_by = dp.id AND osh.updated_by_type = 'delivery_personnel'
            WHERE osh.order_id = ?
            ORDER BY osh.created_at ASC
        ");
        $stmt->execute([$orderId]);
        return $stmt->fetchAll();
    }

    /**
     * Update order status
     *
     * @param int $orderId Order ID
     * @param string $status New status
     * @param string $notes Notes
     * @param int $updatedBy ID of the user who updated the status
     * @param string $updatedByType Type of user who updated the status
     * @param int $deliveryPersonnelId ID of the delivery personnel
     * @return bool True on success, false on failure
     */
    public function updateOrderStatus($orderId, $status, $notes = null, $updatedBy = null, $updatedByType = 'admin', $deliveryPersonnelId = null) {
        try {
            // Start transaction
            $this->pdo->beginTransaction();

            // Call stored procedure
            $stmt = $this->pdo->prepare("
                CALL update_order_status(?, ?, ?, ?, ?, ?)
            ");

            // Execute stored procedure
            $stmt->execute([
                $orderId,
                $status,
                $notes,
                $updatedBy,
                $updatedByType,
                $deliveryPersonnelId
            ]);

            // Commit transaction
            $this->pdo->commit();

            return true;
        } catch (PDOException $e) {
            // Rollback transaction
            $this->pdo->rollBack();

            // Log error
            error_log('Database Error: ' . $e->getMessage());

            return false;
        }
    }

    /**
     * Get orders by filter
     *
     * @param array $filters Filters (status, search, start_date, end_date)
     * @param int $page Page number
     * @param int $limit Number of orders per page
     * @return array Array containing orders and total count
     */
    public function getOrdersByFilter($filters = [], $page = 1, $limit = 10) {
        $offset = ($page - 1) * $limit;
        $params = [];

        // Build query
        $query = "
            SELECT o.*,
                   u.full_name as customer_name,
                   u.phone as customer_phone,
                   dp.full_name as delivery_person_name
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.id
            LEFT JOIN delivery_personnel dp ON o.delivery_personnel_id = dp.id
            WHERE 1=1
        ";

        $countQuery = "SELECT COUNT(*) FROM orders o WHERE 1=1";

        // Add search condition
        if (!empty($filters['search'])) {
            $searchCondition = " AND (o.order_number LIKE ? OR o.tracking_number LIKE ? OR u.full_name LIKE ? OR u.phone LIKE ?)";
            $query .= $searchCondition;
            $countQuery .= $searchCondition;
            $searchParam = "%" . $filters['search'] . "%";
            $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
        }

        // Add status condition
        if (!empty($filters['status'])) {
            $statusCondition = " AND o.status = ?";
            $query .= $statusCondition;
            $countQuery .= $statusCondition;
            $params[] = $filters['status'];
        }

        // Add date range condition
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $dateCondition = " AND DATE(o.created_at) BETWEEN ? AND ?";
            $query .= $dateCondition;
            $countQuery .= $dateCondition;
            $params[] = $filters['start_date'];
            $params[] = $filters['end_date'];
        }

        // Add order by
        $query .= " ORDER BY o.created_at DESC";

        // Add limit and offset
        $query .= " LIMIT ? OFFSET ?";
        $limitParams = $params;
        $limitParams[] = $limit;
        $limitParams[] = $offset;

        // Execute count query
        $stmt = $this->pdo->prepare($countQuery);
        $stmt->execute($params);
        $totalOrders = $stmt->fetchColumn();

        // Execute main query
        $stmt = $this->pdo->prepare($query);
        $stmt->execute($limitParams);
        $orders = $stmt->fetchAll();

        return [
            'orders' => $orders,
            'total' => $totalOrders,
            'pages' => ceil($totalOrders / $limit)
        ];
    }
}
