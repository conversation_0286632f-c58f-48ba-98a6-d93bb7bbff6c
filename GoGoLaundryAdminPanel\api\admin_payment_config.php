<?php
/**
 * Admin Payment Configuration API
 * Provides admin payment numbers for mobile app checkout
 * Users pay to admin accounts, not directly to shop owners
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../includes/functions.php';

/**
 * Send JSON response for admin payment config
 */
function sendPaymentConfigResponse($success, $message, $data = [], $httpCode = 200) {
    http_response_code($httpCode);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

try {
    // Only allow GET requests for this endpoint
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendPaymentConfigResponse(false, 'Method not allowed', [], 405);
    }

    // Get admin payment configuration
    $stmt = $pdo->prepare("
        SELECT
            setting_key,
            setting_value,
            description
        FROM admin_settings
        WHERE setting_key IN (
            'admin_bkash_number',
            'admin_nagad_number',
            'admin_rocket_number',
            'admin_payment_instructions',
            'payment_verification_required'
        )
        ORDER BY setting_key
    ");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Convert to associative array
    $paymentConfig = [];
    foreach ($settings as $setting) {
        $paymentConfig[$setting['setting_key']] = $setting['setting_value'];
    }

    // Set default values if not configured
    $defaultConfig = [
        'admin_bkash_number' => '***********',
        'admin_nagad_number' => '***********',
        'admin_rocket_number' => '***********',
        'admin_payment_instructions' => 'Please send payment to the admin account numbers provided. Include your order number in the transaction reference.',
        'payment_verification_required' => '1'
    ];

    // Merge with defaults
    $paymentConfig = array_merge($defaultConfig, $paymentConfig);

    // Structure the response data
    $responseData = [
        'admin_payment_numbers' => [
            'bkash' => [
                'number' => $paymentConfig['admin_bkash_number'],
                'display_name' => 'bKash',
                'instructions' => 'Send money to this bKash number'
            ],
            'nagad' => [
                'number' => $paymentConfig['admin_nagad_number'],
                'display_name' => 'Nagad',
                'instructions' => 'Send money to this Nagad number'
            ],
            'rocket' => [
                'number' => $paymentConfig['admin_rocket_number'],
                'display_name' => 'Rocket',
                'instructions' => 'Send money to this Rocket number'
            ]
        ],
        'payment_instructions' => $paymentConfig['admin_payment_instructions'],
        'verification_required' => (bool)$paymentConfig['payment_verification_required'],
        'important_note' => 'All payments must be made to admin accounts only. Do not pay directly to shop owners.'
    ];

    sendPaymentConfigResponse(true, 'Admin payment configuration retrieved successfully', $responseData);

} catch (PDOException $e) {
    error_log('Database error in admin_payment_config.php: ' . $e->getMessage());
    sendPaymentConfigResponse(false, 'Database error occurred', [], 500);
} catch (Exception $e) {
    error_log('General error in admin_payment_config.php: ' . $e->getMessage());
    sendPaymentConfigResponse(false, 'An error occurred while retrieving payment configuration', [], 500);
}
?>
