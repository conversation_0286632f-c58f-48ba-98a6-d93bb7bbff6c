<?php
/**
 * API endpoint to mark all notifications as read for a user
 *
 * Required parameters:
 * - user_id: ID of the user
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 * - count: Number of notifications marked as read
 */

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/NotificationManager.php';
require_once '../../includes/UserManager.php';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'count' => 0
];

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit();
}

// Check if user_id is provided
if (!isset($_POST['user_id']) || empty($_POST['user_id'])) {
    $response['message'] = 'User ID is required';
    echo json_encode($response);
    exit();
}

// Get parameters
$user_id = intval($_POST['user_id']);

try {
    // Initialize managers
    $userManager = new UserManager($pdo);
    $notificationManager = new NotificationManager($pdo);

    // Validate user exists
    $user = $userManager->getUserById($user_id);
    if (!$user) {
        $response['message'] = 'User not found';
        echo json_encode($response);
        exit();
    }

    // Check if user is verified
    if (!$user['is_verified']) {
        $response['message'] = 'User account is not verified';
        echo json_encode($response);
        exit();
    }

    // Get unread count before marking as read
    $unread_count = $notificationManager->getUnreadNotificationsCount($user_id);

    // Mark all notifications as read
    $marked_count = $notificationManager->markAllAsRead($user_id);

    $response['count'] = $unread_count;

    if ($marked_count > 0) {
        $response['success'] = true;
        $response['message'] = 'All notifications marked as read successfully';
    } else {
        $response['success'] = true;
        $response['message'] = 'No unread notifications found';
    }

} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    // Log the error for server-side debugging
    error_log('Notification API Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
