<?php
/**
 * Simple test endpoint to verify API is working
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$response = [
    'success' => true,
    'message' => 'FCM API endpoint is working',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'data' => [
        'get' => $_GET,
        'post' => $_POST,
        'raw_input' => file_get_contents('php://input')
    ]
];

echo json_encode($response, JSON_PRETTY_PRINT);
?>
