package com.mdsadrulhasan.gogolaundry.model;

import com.google.gson.annotations.SerializedName;

/**
 * Model class for order status history
 */
public class OrderStatusHistory {
    
    @SerializedName("id")
    private int id;
    
    @SerializedName("order_id")
    private int orderId;
    
    @SerializedName("status")
    private String status;
    
    @SerializedName("notes")
    private String notes;
    
    @SerializedName("updated_by")
    private int updatedBy;
    
    @SerializedName("updated_by_type")
    private String updatedByType;
    
    @SerializedName("created_at")
    private String createdAt;
    
    // Getters and setters
    
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getOrderId() {
        return orderId;
    }
    
    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public int getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(int updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    public String getUpdatedByType() {
        return updatedByType;
    }
    
    public void setUpdatedByType(String updatedByType) {
        this.updatedByType = updatedByType;
    }
    
    public String getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }
}
