#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 369098752 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3600), pid=3628, tid=12748
#
# JRE version:  (21.0.2+13) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: 

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
Time: Thu May 22 05:51:06 2025 Bangladesh Standard Time elapsed time: 0.011884 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000170fac3e900):  JavaThread "Unknown thread" [_thread_in_vm, id=12748, stack(0x0000002671f00000,0x0000002672000000) (1024K)]

Stack: [0x0000002671f00000,0x0000002672000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6cade9]
V  [jvm.dll+0x8569c1]
V  [jvm.dll+0x858d2e]
V  [jvm.dll+0x859413]
V  [jvm.dll+0x280e56]
V  [jvm.dll+0x6c74d5]
V  [jvm.dll+0x6bbeca]
V  [jvm.dll+0x355bca]
V  [jvm.dll+0x35d816]
V  [jvm.dll+0x3ae67e]
V  [jvm.dll+0x3ae928]
V  [jvm.dll+0x3295dc]
V  [jvm.dll+0x32a16b]
V  [jvm.dll+0x81efa9]
V  [jvm.dll+0x3bba01]
V  [jvm.dll+0x807928]
V  [jvm.dll+0x44f50e]
V  [jvm.dll+0x450e11]
C  [jli.dll+0x52a3]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff8614bbb88, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000170fce55f40 WorkerThread "GC Thread#0"                     [id=9112, stack(0x0000002672000000,0x0000002672100000) (1024K)]
  0x00000170fce63070 ConcurrentGCThread "G1 Main Marker"            [id=1816, stack(0x0000002672100000,0x0000002672200000) (1024K)]
  0x00000170fce64310 WorkerThread "G1 Conc#0"                       [id=6880, stack(0x0000002672200000,0x0000002672300000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff860c2c6a7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff86152ce68] Heap_lock - owner thread: 0x00000170fac3e900

Heap address: 0x00000006a0c00000, size: 5620 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x00000006a0c00000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff8610139f9]
GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.007 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff654c80000 - 0x00007ff654c90000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ff8cebf0000 - 0x00007ff8cede8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8cd350000 - 0x00007ff8cd412000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8cc4c0000 - 0x00007ff8cc7b6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8cc850000 - 0x00007ff8cc950000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8bb4f0000 - 0x00007ff8bb509000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ff8ab270000 - 0x00007ff8ab28b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ff8cd6f0000 - 0x00007ff8cd7a1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8cce60000 - 0x00007ff8ccefe000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8ce9c0000 - 0x00007ff8cea5f000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8cd1e0000 - 0x00007ff8cd303000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8cc3e0000 - 0x00007ff8cc407000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ff8cdf20000 - 0x00007ff8ce0bd000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8ccbe0000 - 0x00007ff8ccc02000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8bcf80000 - 0x00007ff8bd21a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4\COMCTL32.dll
0x00007ff8cd320000 - 0x00007ff8cd34b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8ccac0000 - 0x00007ff8ccbd9000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8cc950000 - 0x00007ff8cc9ed000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8c39b0000 - 0x00007ff8c39ba000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8cd6c0000 - 0x00007ff8cd6ef000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8b5360000 - 0x00007ff8b536c000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ff8a1810000 - 0x00007ff8a189e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ff8608f0000 - 0x00007ff861607000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ff8cd550000 - 0x00007ff8cd5bb000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8cc0d0000 - 0x00007ff8cc11b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8c0fc0000 - 0x00007ff8c0fe7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8cc0b0000 - 0x00007ff8cc0c2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8caaf0000 - 0x00007ff8cab02000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8ab490000 - 0x00007ff8ab49a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ff8ca880000 - 0x00007ff8caa81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8baf20000 - 0x00007ff8baf54000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8cc7c0000 - 0x00007ff8cc842000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8a3db0000 - 0x00007ff8a3dcf000 	C:\Program Files\Java\jdk-21\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5912_none_60b4fe2971f97ae4;C:\Program Files\Java\jdk-21\bin\server

VM Arguments:
java_command: <unknown>
java_class_path (initial): <not set>
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 5892997120                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 5892997120                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand
USERNAME=ntc
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5912)
OS uptime: 0 days 9:22 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (4475M free)
TotalPageFile size 22476M (AvailPageFile size 269M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 60M, peak: 412M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.2+13-LTS-58) for windows-amd64 JRE (21.0.2+13-LTS-58), built on 2024-01-05T18:32:24Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
