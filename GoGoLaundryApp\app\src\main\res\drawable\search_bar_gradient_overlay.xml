<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Base search bar -->
    <item android:drawable="@drawable/search_bar_ripple_effect" />

    <!-- Subtle gradient overlay for premium look -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#10FFFFFF"
                android:centerColor="#05FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="45" />
            <corners android:radius="26dp" />
        </shape>
    </item>

    <!-- Top highlight for depth -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="28dp">
        <shape android:shape="rectangle">
            <solid android:color="#15FFFFFF" />
            <corners
                android:topLeftRadius="25dp"
                android:topRightRadius="25dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>

</layer-list>
