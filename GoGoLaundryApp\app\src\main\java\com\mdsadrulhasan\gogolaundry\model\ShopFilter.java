package com.mdsadrulhasan.gogolaundry.model;

import java.io.Serializable;

/**
 * Shop filter criteria model
 */
public class ShopFilter implements Serializable {

    private static final long serialVersionUID = 1L;

    // Distance filter
    private double maxDistance = 10.0; // Default 10km

    // Rating filter
    private double minRating = 0.0; // Default any rating

    // Service filter
    private Integer serviceId = null; // null means all services

    // Location filters
    private Integer divisionId = null;
    private Integer districtId = null;
    private Integer upazillaId = null;

    // Status filters
    private boolean openNowOnly = false;
    private boolean verifiedOnly = true; // Default to verified shops only
    private boolean activeOnly = true; // Default to active shops only

    // Search query
    private String searchQuery = null;

    // Constructors
    public ShopFilter() {
    }

    public ShopFilter(double maxDistance, double minRating, Integer serviceId,
                     Integer divisionId, Integer districtId, Integer upazillaId,
                     boolean openNowOnly, boolean verifiedOnly, boolean activeOnly,
                     String searchQuery) {
        this.maxDistance = maxDistance;
        this.minRating = minRating;
        this.serviceId = serviceId;
        this.divisionId = divisionId;
        this.districtId = districtId;
        this.upazillaId = upazillaId;
        this.openNowOnly = openNowOnly;
        this.verifiedOnly = verifiedOnly;
        this.activeOnly = activeOnly;
        this.searchQuery = searchQuery;
    }

    // Getters and setters

    public double getMaxDistance() {
        return maxDistance;
    }

    public void setMaxDistance(double maxDistance) {
        this.maxDistance = maxDistance;
    }

    public double getMinRating() {
        return minRating;
    }

    public void setMinRating(double minRating) {
        this.minRating = minRating;
    }

    public Integer getServiceId() {
        return serviceId;
    }

    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    public Integer getDivisionId() {
        return divisionId;
    }

    public void setDivisionId(Integer divisionId) {
        this.divisionId = divisionId;
    }

    public Integer getDistrictId() {
        return districtId;
    }

    public void setDistrictId(Integer districtId) {
        this.districtId = districtId;
    }

    public Integer getUpazillaId() {
        return upazillaId;
    }

    public void setUpazillaId(Integer upazillaId) {
        this.upazillaId = upazillaId;
    }

    public boolean isOpenNowOnly() {
        return openNowOnly;
    }

    public void setOpenNowOnly(boolean openNowOnly) {
        this.openNowOnly = openNowOnly;
    }

    public boolean isVerifiedOnly() {
        return verifiedOnly;
    }

    public void setVerifiedOnly(boolean verifiedOnly) {
        this.verifiedOnly = verifiedOnly;
    }

    public boolean isActiveOnly() {
        return activeOnly;
    }

    public void setActiveOnly(boolean activeOnly) {
        this.activeOnly = activeOnly;
    }

    public String getSearchQuery() {
        return searchQuery;
    }

    public void setSearchQuery(String searchQuery) {
        this.searchQuery = searchQuery;
    }

    // Utility methods

    /**
     * Check if any location filter is applied
     */
    public boolean hasLocationFilter() {
        return divisionId != null || districtId != null || upazillaId != null;
    }

    /**
     * Check if any filter is applied (excluding defaults)
     */
    public boolean hasActiveFilters() {
        return maxDistance != 10.0 ||
               minRating > 0.0 ||
               serviceId != null ||
               hasLocationFilter() ||
               openNowOnly ||
               !verifiedOnly ||
               !activeOnly ||
               (searchQuery != null && !searchQuery.trim().isEmpty());
    }

    /**
     * Reset all filters to default values
     */
    public void clearAll() {
        maxDistance = 10.0;
        minRating = 0.0;
        serviceId = null;
        divisionId = null;
        districtId = null;
        upazillaId = null;
        openNowOnly = false;
        verifiedOnly = true;
        activeOnly = true;
        searchQuery = null;
    }

    /**
     * Create a copy of this filter
     */
    public ShopFilter copy() {
        return new ShopFilter(maxDistance, minRating, serviceId,
                             divisionId, districtId, upazillaId,
                             openNowOnly, verifiedOnly, activeOnly, searchQuery);
    }

    /**
     * Get filter summary for display
     */
    public String getFilterSummary() {
        StringBuilder summary = new StringBuilder();

        if (maxDistance != 10.0) {
            summary.append("Within ").append((int)maxDistance).append("km, ");
        }

        if (minRating > 0.0) {
            summary.append("Rating ").append(minRating).append("+, ");
        }

        if (serviceId != null) {
            summary.append("Specific service, ");
        }

        if (hasLocationFilter()) {
            summary.append("Location filter, ");
        }

        if (openNowOnly) {
            summary.append("Open now, ");
        }

        if (!verifiedOnly) {
            summary.append("All shops, ");
        }

        if (summary.length() > 0) {
            // Remove trailing comma and space
            summary.setLength(summary.length() - 2);
            return summary.toString();
        }

        return "No filters applied";
    }

    @Override
    public String toString() {
        return "ShopFilter{" +
                "maxDistance=" + maxDistance +
                ", minRating=" + minRating +
                ", serviceId=" + serviceId +
                ", divisionId=" + divisionId +
                ", districtId=" + districtId +
                ", upazillaId=" + upazillaId +
                ", openNowOnly=" + openNowOnly +
                ", verifiedOnly=" + verifiedOnly +
                ", activeOnly=" + activeOnly +
                ", searchQuery='" + searchQuery + '\'' +
                '}';
    }
}
