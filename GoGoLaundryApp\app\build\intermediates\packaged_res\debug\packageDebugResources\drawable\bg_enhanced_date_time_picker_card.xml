<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Enhanced shadow layer with blur effect -->
    <item android:top="4dp" android:left="2dp" android:right="6dp" android:bottom="8dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/enhanced_picker_card_shadow" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Main background with subtle gradient -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="@color/enhanced_picker_card_background"
                android:endColor="#FAFBFF" />
            <corners android:radius="20dp" />
            <stroke
                android:width="1dp"
                android:color="@color/enhanced_picker_border" />
        </shape>
    </item>
    
    <!-- Subtle inner highlight -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#08FFFFFF"
                android:centerColor="#04FFFFFF"
                android:endColor="#00FFFFFF" />
            <corners android:radius="19dp" />
        </shape>
    </item>
    
    <!-- Micro interaction glow (subtle) -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="radial"
                android:gradientRadius="100dp"
                android:centerX="0.3"
                android:centerY="0.3"
                android:startColor="#03667eea"
                android:endColor="#00667eea" />
            <corners android:radius="18dp" />
        </shape>
    </item>
    
</layer-list>
