<?php
/**
 * SMS Service Class
 *
 * Handles sending SMS using BulkSMSBD API
 */
class SmsService {
    private $apiUrl;
    private $apiKey;
    private $senderId;

    /**
     * Constructor
     */
    public function __construct() {
        $this->apiUrl = SMS_API_URL;
        $this->apiKey = SMS_API_KEY;
        $this->senderId = SMS_SENDER_ID;
    }

    /**
     * Send SMS
     *
     * @param string $phone Recipient phone number
     * @param string $message Message content
     * @return array Response from API
     */
    public function sendSms($phone, $message) {
        try {
            // Format phone number using the standard function
            $phone = formatPhone($phone);
            error_log('SmsService::sendSms - Phone after standard formatting: ' . $phone);

            // For SMS API, we need just the number without country code
            $apiPhone = $phone;
            if (substr($apiPhone, 0, 3) === '880') {
                $apiPhone = substr($apiPhone, 3);
            }

            error_log('SmsService::sendSms - Phone formatted for SMS API: ' . $apiPhone);

            // Prepare API parameters
            $params = [
                'api_key' => $this->apiKey,
                'type' => SMS_TYPE,
                'number' => $apiPhone,
                'senderid' => $this->senderId,
                'message' => $message
            ];

            // Log API parameters for debugging
            error_log('SmsService::sendSms - BulkSMSBD API Parameters: ' . json_encode($params));
            error_log('SmsService::sendSms - API URL: ' . $this->apiUrl);
            error_log('SmsService::sendSms - API Key: ' . substr($this->apiKey, 0, 5) . '...');
            error_log('SmsService::sendSms - Sender ID: ' . $this->senderId);

            // For development/testing, return success without actually sending SMS
            // Comment this out in production
            $developmentMode = false; // Set to false in production

            if ($developmentMode) {
                error_log('SmsService::sendSms - DEVELOPMENT MODE: Returning success without sending actual SMS');
                return [
                    'success' => true,
                    'message' => 'SMS sent successfully (DEVELOPMENT MODE)',
                    'data' => [
                        'response_code' => 202,
                        'message' => 'Success (DEVELOPMENT MODE)',
                        'otp' => $message
                    ]
                ];
            }

            // Initialize cURL session
            $ch = curl_init();

            // Set cURL options
            curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // For development only, enable in production
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Set timeout to 30 seconds

            // Execute cURL session and get response
            $response = curl_exec($ch);
            error_log('SmsService::sendSms - cURL Response: ' . $response);

            // Check for cURL errors
            if (curl_errno($ch)) {
                $error = curl_error($ch);
                $errorNo = curl_errno($ch);
                curl_close($ch);
                error_log('SmsService::sendSms - cURL Error #' . $errorNo . ': ' . $error);
                return [
                    'success' => false,
                    'message' => 'cURL Error: ' . $error,
                    'error_code' => $errorNo
                ];
            }

            // Close cURL session
            curl_close($ch);

            // Parse response
            $result = json_decode($response, true);

            // If response is not JSON, create a custom response
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('SmsService::sendSms - Invalid JSON response: ' . $response);
                return [
                    'success' => false,
                    'message' => 'Invalid API response',
                    'raw_response' => $response
                ];
            }

            // Log the raw response for debugging
            error_log('SmsService::sendSms - BulkSMSBD API Raw Response: ' . $response);

            // Check for success based on response code
            // According to BulkSMSBD API docs, code 202 means success
            if (isset($result['response_code']) && $result['response_code'] == 202) {
                error_log('SmsService::sendSms - SMS sent successfully');
                return [
                    'success' => true,
                    'message' => 'SMS sent successfully',
                    'data' => $result
                ];
            }

            // Handle error codes based on BulkSMSBD API documentation
            $errorMessage = 'SMS sending failed';
            if (isset($result['response_code'])) {
                switch ($result['response_code']) {
                    case 1001:
                        $errorMessage = 'Invalid phone number';
                        break;
                    case 1002:
                        $errorMessage = 'Invalid sender ID';
                        break;
                    case 1003:
                        $errorMessage = 'Missing required parameters';
                        break;
                    case 1005:
                        $errorMessage = 'Internal server error';
                        break;
                    case 1006:
                    case 1007:
                        $errorMessage = 'Insufficient balance';
                        break;
                    default:
                        $errorMessage = 'SMS sending failed with code: ' . $result['response_code'];
                }
            }

            error_log('SmsService::sendSms - Error: ' . $errorMessage);
            return [
                'success' => false,
                'message' => $errorMessage,
                'data' => $result
            ];
        } catch (Exception $e) {
            error_log('SmsService::sendSms - Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send OTP via SMS
     *
     * @param string $phone Recipient phone number
     * @param string $otp OTP code
     * @return array Response from API
     */
    public function sendOtp($phone, $otp) {
        $message = "Your verification code is: $otp. Valid for " . (OTP_EXPIRY / 60) . " minutes. Do not share this code with anyone.";
        return $this->sendSms($phone, $message);
    }
}
