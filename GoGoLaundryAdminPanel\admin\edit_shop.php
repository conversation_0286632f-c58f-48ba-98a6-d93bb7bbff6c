<?php
// Include authentication middleware
require_once 'auth.php';

$shop_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($shop_id <= 0) {
    $_SESSION['error_message'] = "Invalid shop ID.";
    header('Location: shops.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate required fields
        $required_fields = ['shop_name', 'owner_name', 'phone', 'address', 'latitude', 'longitude'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("$field is required");
            }
        }

        // Sanitize and validate input
        $shop_name = trim($_POST['shop_name']);
        $owner_name = trim($_POST['owner_name']);
        $phone = trim($_POST['phone']);
        $email = !empty($_POST['email']) ? trim($_POST['email']) : null;
        $address = trim($_POST['address']);
        $latitude = floatval($_POST['latitude']);
        $longitude = floatval($_POST['longitude']);
        $commission_rate = floatval($_POST['commission_rate'] ?? 15.0);
        $is_verified = intval($_POST['is_verified'] ?? 0);
        $is_active = intval($_POST['is_active'] ?? 1);

        // Validate coordinates
        if ($latitude < -90 || $latitude > 90) {
            throw new Exception("Invalid latitude. Must be between -90 and 90.");
        }
        if ($longitude < -180 || $longitude > 180) {
            throw new Exception("Invalid longitude. Must be between -180 and 180.");
        }

        // Validate commission rate
        if ($commission_rate < 0 || $commission_rate > 50) {
            throw new Exception("Commission rate must be between 0 and 50 percent.");
        }

        // Check if phone number already exists (excluding current shop)
        $checkPhoneStmt = $pdo->prepare("SELECT id FROM laundry_shops WHERE phone = ? AND id != ?");
        $checkPhoneStmt->execute([$phone, $shop_id]);
        if ($checkPhoneStmt->fetch()) {
            throw new Exception("A shop with this phone number already exists.");
        }

        // Update shop
        $sql = "
            UPDATE laundry_shops SET
                name = ?, owner_name = ?, phone = ?, email = ?, address = ?,
                latitude = ?, longitude = ?, commission_percentage = ?,
                is_verified = ?, is_active = ?, updated_at = NOW()
            WHERE id = ?
        ";

        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $shop_name, $owner_name, $phone, $email, $address,
            $latitude, $longitude, $commission_rate,
            $is_verified, $is_active, $shop_id
        ]);

        if ($result) {
            // Log the action
            $admin_id = $_SESSION['admin_id'];
            $log_message = "Updated shop: $shop_name (ID: $shop_id)";
            logAdminActivity($pdo, $admin_id, 'shop_update', $log_message);

            $_SESSION['success_message'] = "Shop '$shop_name' has been updated successfully!";
            header('Location: shop_details.php?id=' . $shop_id);
            exit();
        } else {
            throw new Exception("Failed to update shop. Please try again.");
        }

    } catch (Exception $e) {
        $_SESSION['error_message'] = $e->getMessage();
    }
}

// Get shop details
$sql = "SELECT * FROM laundry_shops WHERE id = ?";
$stmt = $pdo->prepare($sql);
$stmt->execute([$shop_id]);
$shop = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$shop) {
    $_SESSION['error_message'] = "Shop not found.";
    header('Location: shops.php');
    exit();
}

$pageTitle = 'Edit Shop - ' . htmlspecialchars($shop['name']);
$currentPage = 'shops';

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <a href="shop_details.php?id=<?= $shop['id'] ?>" class="text-decoration-none me-2">
            <i class="fas fa-arrow-left"></i>
        </a>
        Edit Shop
    </h1>
</div>

            <?php if (isset($_SESSION['error_message'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_SESSION['error_message']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['error_message']); ?>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Shop Information</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="shop_name" class="form-label">Shop Name *</label>
                                    <input type="text" class="form-control" id="shop_name" name="shop_name"
                                           value="<?= htmlspecialchars($shop['name']) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="owner_name" class="form-label">Owner Name *</label>
                                    <input type="text" class="form-control" id="owner_name" name="owner_name"
                                           value="<?= htmlspecialchars($shop['owner_name']) ?>" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?= htmlspecialchars($shop['phone']) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?= htmlspecialchars($shop['email'] ?? '') ?>">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address *</label>
                            <textarea class="form-control" id="address" name="address" rows="2" required><?= htmlspecialchars($shop['address']) ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">Latitude *</label>
                                    <input type="number" class="form-control" id="latitude" name="latitude"
                                           step="any" min="-90" max="90" value="<?= $shop['latitude'] ?>" required>
                                    <div class="form-text">GPS coordinates for map location</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">Longitude *</label>
                                    <input type="number" class="form-control" id="longitude" name="longitude"
                                           step="any" min="-180" max="180" value="<?= $shop['longitude'] ?>" required>
                                    <div class="form-text">GPS coordinates for map location</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="commission_rate" class="form-label">Commission Rate (%)</label>
                                    <input type="number" class="form-control" id="commission_rate" name="commission_rate"
                                           min="0" max="50" step="0.1" value="<?= $shop['commission_percentage'] ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="is_verified" class="form-label">Verification Status</label>
                                    <select class="form-select" id="is_verified" name="is_verified">
                                        <option value="0" <?= $shop['is_verified'] == 0 ? 'selected' : '' ?>>Unverified</option>
                                        <option value="1" <?= $shop['is_verified'] == 1 ? 'selected' : '' ?>>Verified</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="is_active" class="form-label">Status</label>
                                    <select class="form-select" id="is_active" name="is_active">
                                        <option value="1" <?= $shop['is_active'] == 1 ? 'selected' : '' ?>>Active</option>
                                        <option value="0" <?= $shop['is_active'] == 0 ? 'selected' : '' ?>>Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="shop_details.php?id=<?= $shop['id'] ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Shop
                            </button>
                        </div>
                    </form>
                </div>
            </div>

<?php include 'includes/footer.php'; ?>

<?php
function logAdminActivity($pdo, $admin_id, $action, $description) {
    try {
        $sql = "INSERT INTO admin_activity_logs (admin_id, action, description, ip_address, created_at)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$admin_id, $action, $description, $_SERVER['REMOTE_ADDR'] ?? 'unknown']);
    } catch (Exception $e) {
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}
?>
