<?php
/**
 * Items API Endpoint
 *
 * This endpoint returns items, optionally filtered by service ID
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ItemManager.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Initialize item manager
$itemManager = new ItemManager($pdo);

// Check if service ID is provided
$serviceId = isset($_GET['service_id']) && is_numeric($_GET['service_id']) ? (int)$_GET['service_id'] : null;
$activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === '1';

// Get items
if ($serviceId) {
    // Get items by service ID
    $items = $itemManager->getItemsByServiceId($serviceId, $activeOnly);
} else {
    // Get all items
    $result = $itemManager->getAllItems(1, 1000); // Get up to 1000 items
    $items = $result['items'];
    
    // Filter active items if requested
    if ($activeOnly) {
        $items = array_filter($items, function($item) {
            return $item['is_active'] == 1;
        });
    }
}

// Process items to include image URLs
foreach ($items as &$item) {
    // If image URL is relative, convert to absolute URL
    if (!empty($item['image_url']) && strpos($item['image_url'], 'http') !== 0) {
        $item['image_url'] = BASE_URL . 'uploads/items/' . $item['image_url'];
    }
}

// Return success response
jsonResponse(true, 'Items retrieved successfully', $items);
