-- Create fcm_tokens table with proper constraints
-- This <PERSON><PERSON> creates the fcm_tokens table for FCM token management

CREATE TABLE `fcm_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `token` text NOT NULL,
  `device_type` enum('android','ios','web') NOT NULL DEFAULT 'android',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_device` (`user_id`,`device_id`),
  KEY `user_id` (`user_id`),
  KEY `is_active` (`is_active`),
  KEY `device_type` (`device_type`),
  CONSTRAINT `fcm_tokens_ibfk_1` <PERSON><PERSON>EI<PERSON>N KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Alternative: If the table already exists and you want to modify it
-- Use these ALTER statements instead:

-- Add missing columns if they don't exist
-- ALTER TABLE `fcm_tokens` 
-- ADD COLUMN `device_type` enum('android','ios','web') NOT NULL DEFAULT 'android',
-- ADD COLUMN `is_active` tinyint(1) NOT NULL DEFAULT 1,
-- ADD COLUMN `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp();

-- Add indexes if they don't exist
-- ALTER TABLE `fcm_tokens` 
-- ADD UNIQUE KEY `user_device` (`user_id`,`device_id`),
-- ADD KEY `is_active` (`is_active`),
-- ADD KEY `device_type` (`device_type`);

-- Add foreign key constraint if it doesn't exist
-- ALTER TABLE `fcm_tokens` 
-- ADD CONSTRAINT `fcm_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Check if table exists and show structure
-- DESCRIBE fcm_tokens;

-- View current tokens (for debugging)
-- SELECT 
--     ft.id,
--     u.full_name,
--     ft.device_id,
--     ft.device_type,
--     LEFT(ft.token, 50) as token_preview,
--     ft.is_active,
--     ft.created_at,
--     ft.updated_at
-- FROM fcm_tokens ft 
-- LEFT JOIN users u ON ft.user_id = u.id 
-- ORDER BY ft.updated_at DESC
-- LIMIT 10;
