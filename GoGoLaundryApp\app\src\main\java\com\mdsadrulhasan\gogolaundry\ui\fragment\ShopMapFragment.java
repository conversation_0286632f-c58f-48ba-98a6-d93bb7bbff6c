package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.model.ShopFilter;
import com.mdsadrulhasan.gogolaundry.ui.dialog.ShopFilterDialog;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.ShopMapViewModel;

import org.osmdroid.api.IMapController;
import org.osmdroid.config.Configuration;
import org.osmdroid.tileprovider.tilesource.TileSourceFactory;
import org.osmdroid.util.GeoPoint;
import org.osmdroid.views.MapView;
import org.osmdroid.views.overlay.Marker;
import org.osmdroid.views.overlay.mylocation.GpsMyLocationProvider;
import org.osmdroid.views.overlay.mylocation.MyLocationNewOverlay;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for displaying laundry shops on a map using OSMDroid
 */
public class ShopMapFragment extends Fragment implements ShopFilterDialog.OnFilterAppliedListener {

    private static final String TAG = "ShopMapFragment";
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;
    private static final double DEFAULT_ZOOM = 15.0;
    private static final double DHAKA_LAT = 23.8103;
    private static final double DHAKA_LNG = 90.4125; // Correct variable name
    private static final long SYNC_INTERVAL_MS = 30000; // 30 seconds

    private MapView mapView;
    private ShopMapViewModel viewModel;
    private FusedLocationProviderClient fusedLocationClient;
    private MyLocationNewOverlay myLocationOverlay;
    private IMapController mapController;

    // Enhanced UI components
    private SwipeRefreshLayout swipeRefreshLayout;
    private ProgressBar loadingProgressBar;
    private TextView statusTextView;
    private ChipGroup filterChipGroup;
    private Handler syncHandler;
    private Runnable syncRunnable;
    private boolean isAutoSyncEnabled = true;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize OSMDroid configuration
        Configuration.getInstance().setUserAgentValue(requireContext().getPackageName());

        // Initialize location client
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(requireActivity());

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(ShopMapViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_shop_map, container, false);

        initializeViews(view);
        setupMap();
        observeViewModel();

        return view;
    }

    private void initializeViews(View view) {
        mapView = view.findViewById(R.id.mapView);

        // Initialize enhanced UI components
        swipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout);
        loadingProgressBar = view.findViewById(R.id.loadingProgressBar);
        statusTextView = view.findViewById(R.id.statusTextView);
        filterChipGroup = view.findViewById(R.id.filterChipGroup);

        // Initialize search components
        EditText searchEditText = view.findViewById(R.id.searchEditText);
        ImageButton filterButton = view.findViewById(R.id.filterButton);

        // Initialize floating action buttons
        FloatingActionButton currentLocationFab = view.findViewById(R.id.currentLocationFab);
        FloatingActionButton shopListFab = view.findViewById(R.id.shopListFab);

        // Setup enhanced UI components
        setupSwipeRefresh();
        setupFilterChips();
        setupAutoSync();

        // Setup search functionality
        setupSearchBar(searchEditText, filterButton);

        // Setup floating action buttons
        setupFloatingActionButtons(currentLocationFab, shopListFab);
    }

    private void setupSwipeRefresh() {
        if (swipeRefreshLayout != null) {
            // Call the fragment's own method to reload data
            swipeRefreshLayout.setOnRefreshListener(this::reloadDataBasedOnState);
            swipeRefreshLayout.setColorSchemeResources(
                    R.color.colorPrimary,
                    R.color.colorAccent
            );
        }
    }

    private void setupFilterChips() {
        if (filterChipGroup != null) {
            // Add filter chips dynamically
            // Add filter chips dynamically
            // Ensure "All Shops" chip is added first and is checked initially
            addFilterChip("All Shops", true);
            addFilterChip("Nearby", false);
            addFilterChip("Verified", false); // TODO: Implement verified filter logic
            addFilterChip("High Rated", false); // TODO: Implement rating filter logic
        }
    }

    private void addFilterChip(String text, boolean isChecked) {
        Chip chip = new Chip(requireContext());
        chip.setText(text);
        chip.setCheckable(true);
        chip.setChecked(isChecked);

        // Ensure only one chip in the group is checked at a time for these specific filters
        if (filterChipGroup != null && text.equals("All Shops")) {
            // Add "All Shops" chip and make it the default checked chip
            chip.setOnCheckedChangeListener((buttonView, checked) -> {
                if (checked) {
                    applyFilter(text);
                } else {
                    // Prevent unchecking the "All Shops" chip if it's the only one checked.
                    // This might require custom logic or ensuring another chip is checked first.
                    // For simplicity, we'll just re-check it if it's unchecked directly.
                    // A better approach is to manage selection within the listener or add a "none" chip.
                    // Let's rely on the ChipGroup's singleSelection for now if it's enabled.
                    if (!checked) {
                        // If single selection is enabled, checking another chip will uncheck this one.
                        // If single selection is not enabled, we need manual handling or
                        // maybe just don't allow unchecking 'All Shops' unless another is checked.
                        // Simple re-check workaround:
                        if (!isAnyOtherChipChecked(buttonView.getId())) {
                            //buttonView.setChecked(true); // This can cause loop
                            // Instead of re-checking here, apply a default or show message
                            // or handle this logic within applyFilter or outside.
                            // For this example, we'll just let it uncheck but maybe re-apply All Shops filter
                            // if no other filter is applied. Let's keep it simple and assume single selection.
                        }
                    }
                }
            });
            filterChipGroup.addView(chip);

        } else if (filterChipGroup != null) {
            // Add other chips
            chip.setOnCheckedChangeListener((buttonView, checked) -> {
                if (checked) {
                    // If this chip is checked, apply its filter
                    applyFilter(text);
                } else {
                    // If this chip is unchecked, we need to decide what filter applies next.
                    // If single selection is true, another chip getting checked handles this.
                    // If single selection is false, the user might be clearing this specific filter
                    // while others are active. The current applyFilter method doesn't support combining filters.
                    // Let's assume single selection behavior for now based on the original structure.
                    // If it's unchecked, and no other filter is selected, maybe default back to "All Shops"?
                    // This requires more complex state management.
                    // For simplicity, let's rely on the ChipGroup's single selection if enabled.
                    // If single selection is NOT enabled, this listener needs overhaul to manage multiple filters.
                    Log.d(TAG, "Chip unchecked: " + text + ". Need logic to determine active filters.");
                    // TODO: Implement logic to re-evaluate active filters when a chip is unchecked (if multi-select is desired)
                }
            });
            filterChipGroup.addView(chip);
        }
    }

    // Helper to check if any other chip in the group is checked
    private boolean isAnyOtherChipChecked(int currentChipId) {
        if (filterChipGroup == null) return false;
        for (int i = 0; i < filterChipGroup.getChildCount(); i++) {
            View view = filterChipGroup.getChildAt(i);
            if (view instanceof Chip) {
                Chip chip = (Chip) view;
                if (chip.getId() != currentChipId && chip.isChecked()) {
                    return true;
                }
            }
        }
        return false;
    }


    private void applyFilter(String filterType) {
        Log.d(TAG, "Applying filter: " + filterType);
        updateStatusText("Applying filter: " + filterType);

        // Ensure single selection logic for chips if ChipGroup singleSelection is not used
        // Or, update ChipGroup properties if singleSelection is desired.
        // Assuming single selection behavior is intended by the chip listener structure.

        switch (filterType) {
            case "All Shops":
                viewModel.loadAllShops();
                // Ensure 'All Shops' chip is checked and others are unchecked if managing manually
                setCheckedChip("All Shops");
                break;
            case "Nearby":
                getCurrentLocationAndLoadNearbyShops();
                // Ensure 'Nearby' chip is checked and others are unchecked if managing manually
                setCheckedChip("Nearby");
                break;
            case "Verified":
                // Apply verified filter
                // TODO: Add verified filter logic to ViewModel/Repository
                ToastUtils.showInfo(requireContext(), "Verified filter not fully implemented yet.");
                // Ensure 'Verified' chip is checked and others are unchecked if managing manually
                setCheckedChip("Verified");
                // Example: viewModel.loadShopsByFilter(new ShopFilter().setVerified(true));
                viewModel.loadAllShops(); // Fallback for now
                break;
            case "High Rated":
                // Apply high rated filter
                // TODO: Add rating filter logic to ViewModel/Repository
                ToastUtils.showInfo(requireContext(), "High Rated filter not fully implemented yet.");
                // Ensure 'High Rated' chip is checked and others are unchecked if managing manually
                setCheckedChip("High Rated");
                // Example: viewModel.loadShopsByFilter(new ShopFilter().setMinRating(4.0));
                viewModel.loadAllShops(); // Fallback for now
                break;
            // TODO: Handle other filters from the dialog here or separate the logic
        }
    }

    // Helper to programmatically check a chip and uncheck others in the group
    private void setCheckedChip(String text) {
        if (filterChipGroup != null) {
            for (int i = 0; i < filterChipGroup.getChildCount(); i++) {
                View view = filterChipGroup.getChildAt(i);
                if (view instanceof Chip) {
                    Chip chip = (Chip) view;
                    chip.setChecked(chip.getText().toString().equals(text));
                }
            }
        }
    }


    private void setupAutoSync() {
        syncHandler = new Handler(Looper.getMainLooper());
        syncRunnable = new Runnable() {
            @Override
            public void run() {
                if (isAutoSyncEnabled && isAdded()) {
                    Log.d(TAG, "Auto-sync triggered");
                    // Call the method to check for updates and potentially reload data
                    checkForShopUpdatesAndReload(); // Renamed for clarity
                    syncHandler.postDelayed(this, SYNC_INTERVAL_MS);
                }
            }
        };

        // Start auto-sync
        if (isAutoSyncEnabled) {
            syncHandler.postDelayed(syncRunnable, SYNC_INTERVAL_MS);
        }
    }

    // Renamed the method to reflect its purpose more accurately
    private void checkForShopUpdatesAndReload() {
        // TODO: Implement API call to check for shop updates (e.g., sync_status.php)
        // If updates are detected, then trigger a data reload.
        Log.d(TAG, "Checking for shop updates...");
        updateStatusText("Checking for updates...");

        // For now, as per the original comment, we just reload the data periodically.
        // A proper implementation would check sync_status first.
        // Call the fragment's method to reload data based on the current state.
        reloadDataBasedOnState();
    }

    // This method replaces the old refreshShopData and implements its logic
    private void reloadDataBasedOnState() {
        Log.d(TAG, "Reloading shop data based on current state");
        updateStatusText("Refreshing shops...");

        // Clear existing markers
        clearShopMarkers();

        // Test network connectivity and find working URL first
        testNetworkConnectivityAndFindWorkingUrl();


    }

    private void testNetworkConnectivityAndFindWorkingUrl() {
        Log.d(TAG, "Testing network connectivity and finding working URL...");

        com.mdsadrulhasan.gogolaundry.api.ApiClient.findWorkingUrl(new com.mdsadrulhasan.gogolaundry.api.ApiClient.WorkingUrlCallback() {
            @Override
            public void onWorkingUrlFound(String workingUrl) {
                Log.d(TAG, "Working URL found: " + workingUrl);
                // Now proceed with loading shops
                requireActivity().runOnUiThread(() -> {
                    proceedWithDataLoading();
                });
            }

            @Override
            public void onNoWorkingUrlFound() {
                Log.w(TAG, "No working URL found, proceeding with default");
                // Still try to proceed, maybe the default will work
                requireActivity().runOnUiThread(() -> {
                    proceedWithDataLoading();
                });
            }
        });
    }

    private void proceedWithDataLoading() {
        Log.d(TAG, "Proceeding with data loading using URL: " + com.mdsadrulhasan.gogolaundry.api.ApiClient.getBaseUrl());

        // Test API service directly first
        testApiServiceDirectly();

        // First, ensure we have some shops in the database by loading all shops
        // This is important for the nearby shops functionality to work properly
        Log.d(TAG, "Loading all shops first to populate database, then loading location-based shops");
        viewModel.loadAllShops();

        // Then determine what data to load based on the current state
        if (hasLocationPermission()) {
            getCurrentLocationAndLoadShops();
        }
    }

    private void testApiServiceDirectly() {
        Log.d(TAG, "Testing API service directly...");

        new Thread(() -> {
            try {
                com.mdsadrulhasan.gogolaundry.api.ApiService apiService =
                        com.mdsadrulhasan.gogolaundry.api.ApiClient.getApiService(requireContext());

                Log.d(TAG, "API Service created successfully");

                // Test getAllShops call
                retrofit2.Call<com.mdsadrulhasan.gogolaundry.api.ApiResponse<java.util.List<com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity>>> call =
                        apiService.getAllShops(1, 1, 10, 0, null, null, null);

                Log.d(TAG, "API call created, executing...");

                retrofit2.Response<com.mdsadrulhasan.gogolaundry.api.ApiResponse<java.util.List<com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity>>> response =
                        call.execute();

                Log.d(TAG, "API call executed - Response code: " + response.code());
                Log.d(TAG, "API call executed - Response successful: " + response.isSuccessful());

                if (response.isSuccessful() && response.body() != null) {
                    Log.d(TAG, "API Response - Success: " + response.body().isSuccess());
                    Log.d(TAG, "API Response - Message: " + response.body().getMessage());
                    Log.d(TAG, "API Response - Data count: " + (response.body().getData() != null ? response.body().getData().size() : "null"));
                } else {
                    Log.e(TAG, "API call failed or empty response");
                }

            } catch (Exception e) {
                Log.e(TAG, "Direct API test failed: " + e.getMessage(), e);
            }
        }).start();
    }


    private void updateStatusText(String status) {
        if (statusTextView != null) {
            statusTextView.setText(status);
            statusTextView.setVisibility(View.VISIBLE);

            // Hide status text after 3 seconds
            statusTextView.postDelayed(() -> {
                if (statusTextView != null) {
                    statusTextView.setVisibility(View.GONE);
                }
            }, 3000);
        }
    }

    private boolean hasLocationPermission() {
        return ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                == PackageManager.PERMISSION_GRANTED;
    }

    private void getCurrentLocationAndLoadNearbyShops() {
        if (!hasLocationPermission()) {
            requestLocationPermissionAndSetup();
            return;
        }

        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        Log.d(TAG, "Loaded nearby shops based on current location");
                        viewModel.loadNearbyShops(location.getLatitude(), location.getLongitude(), 20.0);
                    } else {
                        ToastUtils.showWarning(requireContext(), "Unable to get current location for 'Nearby' filter, loading all shops.");
                        Log.w(TAG, "Unable to get current location for 'Nearby' filter, loading all shops.");
                        viewModel.loadAllShops();
                        // Reset filter chip if location failed for Nearby
                        setCheckedChip("All Shops");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Failed to get location for nearby shops", e);
                    ToastUtils.showError(requireContext(), "Failed to get location for 'Nearby' filter, loading all shops.");
                    viewModel.loadAllShops();
                    // Reset filter chip if location failed for Nearby
                    setCheckedChip("All Shops");
                });
    }

    private void setupFloatingActionButtons(FloatingActionButton currentLocationFab, FloatingActionButton shopListFab) {
        Log.d(TAG, "Setting up floating action buttons");

        // Current Location FAB - Get user's current location and center map
        if (currentLocationFab != null) {
            currentLocationFab.setOnClickListener(v -> {
                Log.d(TAG, "Current location FAB clicked");
                getCurrentLocationAndCenterMap();
            });
        }

        // Shop List FAB - Toggle between map and list view
        if (shopListFab != null) {
            shopListFab.setOnClickListener(v -> {
                Log.d(TAG, "Shop list FAB clicked");
                showShopListDialog();
            });
        }
    }

    private void setupSearchBar(EditText searchEditText, ImageButton filterButton) {
        Log.d(TAG, "Setting up search bar");

        // Setup search text listener
        searchEditText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH) {
                String query = searchEditText.getText().toString().trim();
                Log.d(TAG, "Search action triggered with query: '" + query + "'");

                if (!query.isEmpty()) {
                    Log.d(TAG, "Calling viewModel.searchShops() with query: '" + query + "'");

                    // Clear existing markers before search
                    clearShopMarkers();

                    viewModel.searchShops(query);

                    // Hide keyboard
                    android.view.inputmethod.InputMethodManager imm =
                            (android.view.inputmethod.InputMethodManager) requireActivity().getSystemService(android.content.Context.INPUT_METHOD_SERVICE);
                    if (imm != null) {
                        imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                    }

                    // Deselect filter chips when searching
                    if (filterChipGroup != null) {
                        filterChipGroup.clearCheck();
                    }

                } else {
                    Log.w(TAG, "Empty search query, not performing search");
                    // Optionally, reload default data when search is cleared
                    reloadDataBasedOnState(); // or loadAllShops()
                    // Re-select 'All Shops' chip or current default
                    setCheckedChip("All Shops");
                }
                return true;
            }
            return false;
        });

        // Setup filter button click
        filterButton.setOnClickListener(v -> {
            Log.d(TAG, "Filter button clicked, showing filter dialog");
            showFilterDialog();
        });
    }

    private void setupMap() {
        // Set tile source
        mapView.setTileSource(TileSourceFactory.MAPNIK);

        // Enable multi-touch controls
        mapView.setMultiTouchControls(true);

        // Get map controller
        mapController = mapView.getController();
        mapController.setZoom(DEFAULT_ZOOM);

        // Set default location (Dhaka, Bangladesh)
        GeoPoint defaultPoint = new GeoPoint(DHAKA_LAT, DHAKA_LNG);
        mapController.setCenter(defaultPoint);

        // Setup my location overlay
        setupMyLocationOverlay();

        // Request location permission and get current location
        requestLocationPermissionAndSetup();
    }

    private void setupMyLocationOverlay() {
        myLocationOverlay = new MyLocationNewOverlay(new GpsMyLocationProvider(requireContext()), mapView);
        myLocationOverlay.enableMyLocation();
        mapView.getOverlays().add(myLocationOverlay);
    }

    private void requestLocationPermissionAndSetup() {
        if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(requireActivity(),
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    LOCATION_PERMISSION_REQUEST_CODE);
        } else {
            // Permission already granted, proceed to get location and load shops
            getCurrentLocationAndLoadShops();
        }
    }

    private void getCurrentLocationAndLoadShops() {
        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Location permission not granted, cannot get current location.");
            // Fallback to loading all shops if location permission is not granted
            viewModel.loadAllShops();
            return;
        }

        // Show loading for location attempt
        updateStatusText("Getting current location...");

        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        Log.d(TAG, "Current location found: " + location.getLatitude() + ", " + location.getLongitude());
                        GeoPoint userLocation = new GeoPoint(location.getLatitude(), location.getLongitude());

                        // Center map on user location
                        mapController.setCenter(userLocation);
                        mapController.setZoom(15.0); // Zoom level for initial load near user

                        // Load nearby shops
                        viewModel.loadNearbyShops(location.getLatitude(), location.getLongitude(), 20.0); // 20km radius
                        updateStatusText("Loading nearby shops...");

                        // Ensure 'Nearby' chip is selected visually if successful
                        setCheckedChip("Nearby");

                    } else {
                        Log.w(TAG, "Current location not available, loading all shops.");
                        // Use default location and load all shops
                        // Center map on default location if user location not available
                        GeoPoint defaultPoint = new GeoPoint(DHAKA_LAT, DHAKA_LNG); // Corrected
                        mapController.setCenter(defaultPoint);
                        mapController.setZoom(DEFAULT_ZOOM);

                        viewModel.loadAllShops();
                        updateStatusText("Current location unavailable, loading all shops...");

                        // Ensure 'All Shops' chip is selected visually
                        setCheckedChip("All Shops");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Failed to get current location: " + e.getMessage());
                    ToastUtils.showError(requireContext(), "Failed to get current location, loading all shops.");

                    // Use default location and load all shops on failure
                    GeoPoint defaultPoint = new GeoPoint(DHAKA_LAT, DHAKA_LNG); // Corrected
                    mapController.setCenter(defaultPoint);
                    mapController.setZoom(DEFAULT_ZOOM);

                    viewModel.loadAllShops();
                    updateStatusText("Failed to get current location, loading all shops...");

                    // Ensure 'All Shops' chip is selected visually
                    setCheckedChip("All Shops");
                });
    }

    private void observeViewModel() {
        // Observe shops data
        viewModel.getShops().observe(getViewLifecycleOwner(), shops -> {
            Log.d(TAG, "observeViewModel: Received shops data - " + (shops != null ? shops.size() : 0) + " shops");

            // Stop swipe refresh
            if (swipeRefreshLayout != null) {
                swipeRefreshLayout.setRefreshing(false);
            }

            // Clear existing markers
            clearShopMarkers();

            if (shops != null && !shops.isEmpty()) {
                Log.d(TAG, "Adding " + shops.size() + " shop markers to map");
                addShopMarkersToMap(shops);
                updateStatusText("Found " + shops.size() + " shops");

                // Zoom to fit all markers so they're visible
                zoomToFitMarkers(shops);

                // Run diagnostics to help debug any issues
                runMapDiagnostics();
            } else {
                Log.w(TAG, "No shops to display on map");
                updateStatusText("No shops found matching criteria");

                // Run diagnostics when no shops are found
                runMapDiagnostics();
            }
        });

        // Observe loading state
        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            Log.d(TAG, "Loading state changed: " + isLoading);

            // Show/hide loading indicator
            if (loadingProgressBar != null) {
                loadingProgressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            }

            // Update status text only if not already set by filter/refresh action
            if (isLoading && (statusTextView == null || !statusTextView.getText().toString().startsWith("Loading"))) {
                updateStatusText("Loading...");
            } else if (!isLoading && (statusTextView == null || statusTextView.getText().toString().equals("Loading..."))) {
                // If loading finished and text was generic "Loading...", clear it or update.
                // The shops observer will set a success/not found message.
                // If swipe refresh was active, its listener will set refreshing(false).
                // This might need refinement based on desired UX for various loading scenarios.
            }
        });

        // Observe error messages
        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                Log.e(TAG, "Error message received: " + errorMessage);
                ToastUtils.showError(requireContext(), errorMessage);
                updateStatusText("Error: " + errorMessage);

                // Stop swipe refresh on error
                if (swipeRefreshLayout != null) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }
        });

        // Observe current filter to potentially update UI (like the filter button color)
        viewModel.getCurrentFilter().observe(getViewLifecycleOwner(), filter -> {
            Log.d(TAG, "Current filter updated in ViewModel: " + (filter != null ? filter.toString() : "null"));
            updateFilterButtonState(filter != null && filter.hasActiveFilters());
        });
    }

    private void clearShopMarkers() {
        Log.d(TAG, "Clearing existing shop markers");
        // Remove all overlays except the MyLocationNewOverlay
        mapView.getOverlays().removeIf(overlay -> !(overlay instanceof MyLocationNewOverlay));
        mapView.invalidate();
    }

    private void addShopMarkersToMap(List<LaundryShopEntity> shops) {
        Log.d(TAG, "addShopMarkersToMap: Adding " + shops.size() + " markers");

        int markersAdded = 0;
        int invalidCoordinates = 0;

        for (LaundryShopEntity shop : shops) {
            if (isValidCoordinate(shop.getLatitude(), shop.getLongitude())) {
                try {
                    addShopMarker(shop);
                    markersAdded++;
                } catch (Exception e) {
                    Log.e(TAG, "Error adding marker for shop " + shop.getName() + ": " + e.getMessage());
                }
            } else {
                invalidCoordinates++;
                Log.w(TAG, "Shop '" + shop.getName() + "' has invalid coordinates: (" +
                        shop.getLatitude() + ", " + shop.getLongitude() + ")");
            }
        }

        Log.d(TAG, "Markers added: " + markersAdded + ", Invalid coordinates: " + invalidCoordinates);

        // Update status with more detailed information
        if (markersAdded > 0) {
            updateStatusText("Showing " + markersAdded + " shops on map" +
                    (invalidCoordinates > 0 ? " (" + invalidCoordinates + " shops have invalid locations)" : ""));
        } else if (invalidCoordinates > 0) {
            updateStatusText("Found " + shops.size() + " shops but none have valid locations");
        } else {
            updateStatusText("No shops found");
        }

        Log.d(TAG, "Successfully added " + markersAdded + " markers to map");

        // Refresh map
        mapView.invalidate();

        // Consider zooming to fit only if it makes sense (e.g., after a search or filter that might cover a new area)
        // Or perhaps only zoom to fit if the number/location of shops is significantly different from the previous view.
    }

    /**
     * Validate if coordinates are valid for mapping
     * @param latitude Latitude value
     * @param longitude Longitude value
     * @return true if coordinates are valid, false otherwise
     */
    private boolean isValidCoordinate(double latitude, double longitude) {
        // Check for null/zero coordinates - Note: Some valid locations can be near (0,0),
        // but for typical use cases, 0,0 is often a default/error state.
        // Consider if (0,0) should be treated as invalid based on your data source.
        // For general validity checks, the range checks below are more standard.
        // if (latitude == 0.0 && longitude == 0.0) {
        //     return false;
        // }

        // Check for valid latitude range (-90 to 90)
        if (latitude < -90.0 || latitude > 90.0) {
            return false;
        }

        // Check for valid longitude range (-180 to 180)
        if (longitude < -180.0 || longitude > 180.0) {
            return false;
        }

        // Check for obviously invalid values (like default database values)
        if (Double.isNaN(latitude) || Double.isNaN(longitude) ||
                Double.isInfinite(latitude) || Double.isInfinite(longitude)) {
            return false;
        }

        return true;
    }

    private void addShopMarker(LaundryShopEntity shop) {
        try {
            // Validate coordinates before creating marker
            if (!isValidCoordinate(shop.getLatitude(), shop.getLongitude())) {
                Log.w(TAG, "Skipping marker for shop '" + shop.getName() + "' - invalid coordinates");
                return;
            }

            GeoPoint shopLocation = new GeoPoint(shop.getLatitude(), shop.getLongitude());
            // Log.d(TAG, "Adding marker for shop: " + shop.getName() + " at " + shopLocation); // Too verbose

            Marker marker = new Marker(mapView);
            marker.setPosition(shopLocation);
            marker.setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM);

            // Set title with null check
            marker.setTitle(shop.getName() != null ? shop.getName() : "Unknown Shop");

            // Create snippet with shop info and null checks
            StringBuilder snippetBuilder = new StringBuilder();

            if (shop.getRating() > 0) {
                snippetBuilder.append("⭐ ").append(String.format("%.1f", shop.getRating()));
            }

            if (shop.getDistance() > 0) {
                if (snippetBuilder.length() > 0) snippetBuilder.append(" | ");
                snippetBuilder.append("📍 ").append(String.format("%.1f km", shop.getDistance()));
            }

            if (shop.getAddress() != null && !shop.getAddress().trim().isEmpty()) {
                if (snippetBuilder.length() > 0) snippetBuilder.append("\n");
                snippetBuilder.append(shop.getAddress());
            }

            // Add status indicators
            if (shop.isVerified()) {
                if (snippetBuilder.length() > 0) snippetBuilder.append("\n");
                snippetBuilder.append("✅ Verified");
            }

            if (!shop.isActive()) {
                if (snippetBuilder.length() > 0) snippetBuilder.append("\n");
                snippetBuilder.append("⚠️ Inactive");
            }

            marker.setSnippet(snippetBuilder.toString());

            // Set marker icon with better error handling
            try {
                Drawable icon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_shop_marker);
                if (icon != null) {
                    // Create a copy to avoid shared state issues
                    icon = icon.getConstantState().newDrawable().mutate();
                    icon.setBounds(0, 0, icon.getIntrinsicWidth(), icon.getIntrinsicHeight());
                    marker.setIcon(icon);
                } else {
                    Log.w(TAG, "Shop marker icon not found, using default marker");
                }
            } catch (Exception e) {
                Log.w(TAG, "Error setting marker icon, using default: " + e.getMessage());
            }

            // Set click listener with error handling
            marker.setOnMarkerClickListener((clickedMarker, mapView) -> {
                try {
                    openShopDetails(shop);
                    return true;
                } catch (Exception e) {
                    Log.e(TAG, "Error opening shop details for " + shop.getName(), e);
                    return false;
                }
            });

            mapView.getOverlays().add(marker);

        } catch (Exception e) {
            Log.e(TAG, "Error creating marker for shop " + shop.getName() + ": " + e.getMessage(), e);
            throw e; // Re-throw to be caught by calling method
        }
    }



    private void zoomToFitMarkers(List<LaundryShopEntity> shops) {
        if (shops == null || shops.isEmpty()) {
            Log.d(TAG, "No shops to fit map view to.");
            return;
        }

        try {
            // Filter out markers with invalid coordinates before calculating bounds
            List<GeoPoint> validPoints = new ArrayList<>();
            for (LaundryShopEntity shop : shops) {
                if (isValidCoordinate(shop.getLatitude(), shop.getLongitude())) {
                    validPoints.add(new GeoPoint(shop.getLatitude(), shop.getLongitude()));
                }
            }

            if (validPoints.isEmpty()) {
                Log.d(TAG, "No valid shop coordinates to fit map view to.");
                // Maybe zoom back to default or current location
                return;
            }

            // Create a bounding box that includes all valid points
            mapView.zoomToBoundingBox(org.osmdroid.util.BoundingBox.fromGeoPoints(validPoints), true);

            // Optionally adjust zoom slightly after fitting
            // mapController.setZoom(mapController.getZoomLevelDouble() - 0.5); // Example: zoom out a bit

            Log.d(TAG, "Zoomed to fit " + validPoints.size() + " valid markers.");

        } catch (Exception e) {
            Log.e(TAG, "Error zooming to fit markers", e);
        }
    }


    private void openShopDetails(LaundryShopEntity shop) {
        // Navigate to shop details fragment
        ShopDetailsFragment shopDetailsFragment = ShopDetailsFragment.newInstance(shop.getId());

        requireActivity().getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fragment_container, shopDetailsFragment)
                .addToBackStack(null)
                .commit();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Location permission granted.");
                // Permission granted, now get location and load shops
                getCurrentLocationAndLoadShops();
            } else {
                Log.w(TAG, "Location permission denied.");
                ToastUtils.showWarning(requireContext(), "Location permission denied, cannot get nearby shops. Loading all shops.");
                // Permission denied, load all shops as fallback
                viewModel.loadAllShops();
                // Ensure 'All Shops' chip is selected visually
                setCheckedChip("All Shops");
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mapView != null) {
            mapView.onResume(); // Handles the lifecycle of the map view
        }
        if (myLocationOverlay != null) {
            myLocationOverlay.enableMyLocation(); // Re-enable location updates
        }

        // Resume auto-sync only if enabled and handler is ready
        if (isAutoSyncEnabled && syncHandler != null && syncRunnable != null) {
            Log.d(TAG, "Resuming auto-sync");
            syncHandler.postDelayed(syncRunnable, SYNC_INTERVAL_MS);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mapView != null) {
            mapView.onPause(); // Handles the lifecycle of the map view
        }
        if (myLocationOverlay != null) {
            myLocationOverlay.disableMyLocation(); // Disable location updates to save battery
        }

        // Pause auto-sync
        if (syncHandler != null && syncRunnable != null) {
            Log.d(TAG, "Pausing auto-sync");
            syncHandler.removeCallbacks(syncRunnable);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mapView != null) {
            mapView.onDetach(); // Clean up map resources
        }

        // Clean up auto-sync
        if (syncHandler != null && syncRunnable != null) {
            syncHandler.removeCallbacks(syncRunnable);
        }
        syncHandler = null;
        syncRunnable = null;

        // Clean up location client if necessary (though FusedLocationProviderClient is generally app-scoped)
        // fusedLocationClient = null; // Depends on how you manage the client lifecycle
    }

    /**
     * Get current location and center map on it
     */
    private void getCurrentLocationAndCenterMap() {
        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Location permission not granted for centering map.");
            ToastUtils.showWarning(requireContext(), "Location permission required to center on your location");
            // Request permission again if needed
            requestLocationPermissionAndSetup(); // This will also load data after permission
            return;
        }

        Log.d(TAG, "Getting current location to center map");
        updateStatusText("Centering on your location...");
        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        Log.d(TAG, "Current location found: " + location.getLatitude() + ", " + location.getLongitude());
                        GeoPoint userLocation = new GeoPoint(location.getLatitude(), location.getLongitude());

                        // Animate map to user's location
                        mapController.animateTo(userLocation, 16.0, 1000L); // Zoom in closer and animate

                        // Reload shops around the new center if needed, or just rely on existing markers
                        // viewModel.loadNearbyShops(location.getLatitude(), location.getLongitude(), 5.0); // Optional reload

                        ToastUtils.showSuccess(requireContext(), "Centered on your location");
                        updateStatusText("Centered on your location");

                        // If centering implies nearby, potentially update filter chip
                        // setCheckedChip("Nearby"); // Optional: If centering should imply nearby filter

                    } else {
                        Log.w(TAG, "Current location not available for centering.");
                        ToastUtils.showWarning(requireContext(), "Current location not available");
                        updateStatusText("Current location unavailable");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Failed to get current location for centering: " + e.getMessage());
                    ToastUtils.showError(requireContext(), "Failed to get current location");
                    updateStatusText("Failed to get current location");
                });
    }

    /**
     * Show shop list dialog
     */
    private void showShopListDialog() {
        Log.d(TAG, "Showing shop list dialog");

        // Get current shops from ViewModel
        List<LaundryShopEntity> shops = viewModel.getShops().getValue();
        if (shops != null && !shops.isEmpty()) {
            // TODO: Implement proper shop list dialog that displays the 'shops' list
            String message = "Found " + shops.size() + " shop(s) currently displayed on map";
            ToastUtils.showInfo(requireContext(), message);

            // Log shop details for debugging
            Log.d(TAG, "Current shops on map (" + shops.size() + " total):");
            for (int i = 0; i < Math.min(10, shops.size()); i++) { // Log up to 10 for brevity
                LaundryShopEntity shop = shops.get(i);
                Log.d(TAG, String.format("  %d. %s (ID: %d) - %.6f, %.6f - %.1f⭐ - %.1f km",
                        (i+1), shop.getName(), shop.getId(), shop.getLatitude(), shop.getLongitude(), shop.getRating(), shop.getDistance()));
            }
        } else {
            ToastUtils.showInfo(requireContext(), "No shops found in current view");
        }
    }

    /**
     * Show filter dialog
     */
    private void showFilterDialog() {
        Log.d(TAG, "Showing filter dialog");
        // Get current filter state from ViewModel
        ShopFilter currentFilter = viewModel.getCurrentFilter().getValue();
        if (currentFilter == null) {
            currentFilter = new ShopFilter(); // Use default if none is set yet
        }

        ShopFilterDialog dialog = ShopFilterDialog.newInstance(currentFilter);
        // Use getChildFragmentManager() for dialogs within fragments
        dialog.show(getChildFragmentManager(), "ShopFilterDialog");
    }

    /**
     * Callback when filter is applied from the dialog
     */
    @Override
    public void onFilterApplied(ShopFilter filter) {
        Log.d(TAG, "onFilterApplied() called with filter: " + filter.toString());
        Log.d(TAG, "Filter has active filters: " + filter.hasActiveFilters());

        // Clear any currently selected filter chips in the UI, as the dialog filter is different
        if (filterChipGroup != null) {
            filterChipGroup.clearCheck();
            // Optional: Select 'All Shops' chip if the applied dialog filter is empty/default
            if (!filter.hasActiveFilters()) {
                setCheckedChip("All Shops");
            }
        }

        // Pass the complex filter object to the ViewModel
        viewModel.applyFilter(filter); // ViewModel handles the loading based on this filter

        // Show filter summary (optional, depends on how detailed you want the UI feedback)
        String summary = filter.getFilterSummary();
        Log.d(TAG, "Dialog filter summary: " + summary);

        if (filter.hasActiveFilters()) {
            ToastUtils.showSuccess(requireContext(), "Dialog filter applied: " + summary);
            updateStatusText("Filter applied: " + summary);
        } else {
            ToastUtils.showInfo(requireContext(), "All dialog filters cleared");
            updateStatusText("All dialog filters cleared");
        }

        // Update filter button appearance to show active state
        updateFilterButtonState(filter.hasActiveFilters());
    }

    /**
     * Update filter button appearance based on active state (from dialog filter)
     */
    private void updateFilterButtonState(boolean hasActiveFilters) {
        ImageButton filterButton = getView() != null ? getView().findViewById(R.id.filterButton) : null;
        if (filterButton != null) {
            if (hasActiveFilters) {
                // Show active state (e.g., different color or icon)
                filterButton.setColorFilter(ContextCompat.getColor(requireContext(), R.color.home_accent_blue));
            } else {
                // Show normal state
                filterButton.setColorFilter(ContextCompat.getColor(requireContext(), R.color.home_text_on_gradient));
            }
        }
    }


    /**
     * Diagnostic method to help identify map issues
     */
    private void runMapDiagnostics() {
        Log.d(TAG, "=== MAP DIAGNOSTICS START ===");

        // Check map view state
        if (mapView != null) {
            // FIX: Use mapView to get the zoom level, as IMapController doesn't have getZoomLevel()
            // Using getZoomLevelDouble() from MapView for more precision in diagnostics.
            Log.d(TAG, "Map View: Initialized, Zoom: " + mapView.getZoomLevelDouble());
            Log.d(TAG, "Map Center: " + mapView.getMapCenter());
            Log.d(TAG, "Map Overlays: " + mapView.getOverlays().size());
        } else {
            Log.e(TAG, "Map View: NULL - Map not initialized!");
        }

        // Check location permission
        Log.d(TAG, "Location Permission: " + hasLocationPermission());

        // Check current location
        Location currentLoc = viewModel.getCurrentLocationLiveData().getValue();
        if (currentLoc != null) {
            Log.d(TAG, "Current Location: " + currentLoc.getLatitude() + ", " + currentLoc.getLongitude());
        } else {
            Log.w(TAG, "Current Location: NULL");
        }

        // Check shops data
        List<LaundryShopEntity> shops = viewModel.getShops().getValue();
        if (shops != null) {
            Log.d(TAG, "Shops Data: " + shops.size() + " shops loaded");
            int validCoords = 0;
            int invalidCoords = 0;
            for (LaundryShopEntity shop : shops) {
                if (isValidCoordinate(shop.getLatitude(), shop.getLongitude())) {
                    validCoords++;
                } else {
                    invalidCoords++;
                }
            }
            Log.d(TAG, "Coordinate Status: " + validCoords + " valid, " + invalidCoords + " invalid");

            // Log first few shops for debugging
            for (int i = 0; i < Math.min(3, shops.size()); i++) {
                LaundryShopEntity shop = shops.get(i);
                Log.d(TAG, "Shop " + (i+1) + ": " + shop.getName() + " (" + shop.getLatitude() + ", " + shop.getLongitude() + ")");
            }
        } else {
            Log.w(TAG, "Shops Data: NULL - No shops loaded!");
        }

        // Check loading state
        Boolean isLoading = viewModel.getIsLoading().getValue();
        Log.d(TAG, "Loading State: " + (isLoading != null ? isLoading : "NULL"));

        // Check error state
        String errorMsg = viewModel.getErrorMessage().getValue();
        if (errorMsg != null && !errorMsg.isEmpty()) {
            Log.e(TAG, "Error Message: " + errorMsg);
        } else {
            Log.d(TAG, "Error Message: None");
        }

        // Check API connectivity
        Log.d(TAG, "API Base URL: " + com.mdsadrulhasan.gogolaundry.api.ApiClient.getBaseUrl());

        Log.d(TAG, "=== MAP DIAGNOSTICS END ===");
    }

    /**
     * Factory method to create new instance
     */
    public static ShopMapFragment newInstance() {
        return new ShopMapFragment();
    }
}