package com.mdsadrulhasan.gogolaundry.repository;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.GoGoLaundryApp;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.database.dao.ItemDao;
import com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.utils.AppExecutors;
import com.mdsadrulhasan.gogolaundry.utils.Resource;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Repository for item-related operations
 */
public class ItemRepository {
    private static final String TAG = "ItemRepository";

    private static ItemRepository instance;

    private final ItemDao itemDao;
    private final ApiService apiService;
    private final AppExecutors executors;

    /**
     * Private constructor
     */
    private ItemRepository() {
        itemDao = GoGoLaundryApp.getInstance().getDatabase().itemDao();
        apiService = ApiClient.getApiService(GoGoLaundryApp.getInstance());
        executors = AppExecutors.getInstance();
    }

    /**
     * Get repository instance
     *
     * @return Repository instance
     */
    public static synchronized ItemRepository getInstance() {
        if (instance == null) {
            instance = new ItemRepository();
        }
        return instance;
    }

    /**
     * Get all items
     *
     * @param forceRefresh Whether to force refresh from network
     * @return LiveData of items
     */
    public LiveData<Resource<List<ItemEntity>>> getAllItems(boolean forceRefresh) {
        return getAllItems(forceRefresh, null, true);
    }

    /**
     * Get items by service ID
     *
     * @param forceRefresh Whether to force refresh from network
     * @param serviceId Service ID
     * @return LiveData of items
     */
    public LiveData<Resource<List<ItemEntity>>> getItemsByServiceId(boolean forceRefresh, int serviceId) {
        return getAllItems(forceRefresh, serviceId, true);
    }

    /**
     * Get all items
     *
     * @param forceRefresh Whether to force refresh from network
     * @param serviceId Service ID (optional)
     * @param activeOnly Whether to get only active items
     * @return LiveData of items
     */
    private LiveData<Resource<List<ItemEntity>>> getAllItems(boolean forceRefresh, Integer serviceId, boolean activeOnly) {
        MutableLiveData<Resource<List<ItemEntity>>> result = new MutableLiveData<>();

        // First, load from database
        executors.diskIO().execute(() -> {
            List<ItemEntity> items;

            if (serviceId != null) {
                items = activeOnly ?
                        itemDao.getActiveItemsByServiceId(serviceId) :
                        itemDao.getItemsByServiceId(serviceId);
            } else {
                items = activeOnly ?
                        itemDao.getAllActiveItems() :
                        itemDao.getAllItems();
            }

            executors.mainThread().execute(() -> {
                if (items.isEmpty() || forceRefresh) {
                    // If database is empty or force refresh, load from network
                    fetchItemsFromNetwork(result, serviceId, activeOnly);
                } else {
                    // Otherwise, return data from database
                    result.setValue(Resource.success(items));
                }
            });
        });

        return result;
    }

    /**
     * Fetch items from network
     *
     * @param result Result LiveData
     * @param serviceId Service ID (optional)
     * @param activeOnly Whether to get only active items
     */
    private void fetchItemsFromNetwork(MutableLiveData<Resource<List<ItemEntity>>> result, Integer serviceId, boolean activeOnly) {
        result.setValue(Resource.loading(null));

        // Make API call to get items
        Call<ApiResponse<List<Item>>> call = apiService.getItems(serviceId, activeOnly ? 1 : 0);

        call.enqueue(new Callback<ApiResponse<List<Item>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<Item>>> call, Response<ApiResponse<List<Item>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<Item> itemModels = response.body().getData();

                    if (itemModels != null && !itemModels.isEmpty()) {
                        // Convert API models to database entities
                        List<ItemEntity> itemEntities = convertToItemEntities(itemModels);

                        // Save to database
                        executors.diskIO().execute(() -> {
                            itemDao.insertAll(itemEntities);

                            executors.mainThread().execute(() -> {
                                result.setValue(Resource.success(itemEntities));
                            });
                        });
                    } else {
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.success(new ArrayList<>()));
                        });
                    }
                } else {
                    String errorMsg = "Failed to load items";
                    if (response.body() != null) {
                        errorMsg = response.body().getMessage();
                    }
                    final String finalErrorMsg = errorMsg;
                    executors.mainThread().execute(() -> {
                        result.setValue(Resource.error(finalErrorMsg, null));
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<Item>>> call, Throwable t) {
                executors.mainThread().execute(() -> {
                    result.setValue(Resource.error("Network error: " + t.getMessage(), null));

                    // Show error toast
                    ToastUtils.showErrorToast(GoGoLaundryApp.getInstance(),
                            "Failed to load items: " + t.getMessage());
                });
            }
        });
    }

    /**
     * Get popular items (featured or most ordered)
     *
     * @param forceRefresh Whether to force refresh from network
     * @return LiveData of popular items resource
     */
    public LiveData<Resource<List<ItemEntity>>> getPopularItems(boolean forceRefresh) {
        MutableLiveData<Resource<List<ItemEntity>>> result = new MutableLiveData<>();
        result.setValue(Resource.loading(null));

        // First, try to get from database
        executors.diskIO().execute(() -> {
            // For now, just get a few active items as popular items
            // In a real implementation, this would be based on order frequency or featured flag
            List<ItemEntity> items = itemDao.getAllActiveItems();

            executors.mainThread().execute(() -> {
                if (items.isEmpty() || forceRefresh) {
                    // If database is empty or force refresh, load from network
                    fetchPopularItemsFromNetwork(result);
                } else {
                    // Otherwise, return data from database (limit to 10 items)
                    List<ItemEntity> limitedItems = items.size() > 10 ? items.subList(0, 10) : items;
                    result.setValue(Resource.success(limitedItems));
                }
            });
        });

        return result;
    }

    /**
     * Fetch popular items from network
     *
     * @param result Result LiveData
     */
    private void fetchPopularItemsFromNetwork(MutableLiveData<Resource<List<ItemEntity>>> result) {
        result.setValue(Resource.loading(null));

        // Make API call to get popular items
        // For now, we'll just get all active items and consider them as popular
        Call<ApiResponse<List<Item>>> call = apiService.getItems(null, 1);

        call.enqueue(new Callback<ApiResponse<List<Item>>>() {
            @Override
            public void onResponse(Call<ApiResponse<List<Item>>> call, Response<ApiResponse<List<Item>>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    List<Item> itemModels = response.body().getData();

                    if (itemModels != null && !itemModels.isEmpty()) {
                        // Limit to 10 items for popular items section
                        if (itemModels.size() > 10) {
                            itemModels = itemModels.subList(0, 10);
                        }

                        // Convert API models to database entities
                        List<ItemEntity> itemEntities = convertToItemEntities(itemModels);

                        // Save to database
                        executors.diskIO().execute(() -> {
                            itemDao.insertAll(itemEntities);

                            executors.mainThread().execute(() -> {
                                result.setValue(Resource.success(itemEntities));
                            });
                        });
                    } else {
                        executors.mainThread().execute(() -> {
                            result.setValue(Resource.success(new ArrayList<>()));
                        });
                    }
                } else {
                    String errorMsg = "Failed to load popular items";
                    if (response.body() != null) {
                        errorMsg = response.body().getMessage();
                    }
                    final String finalErrorMsg = errorMsg;
                    executors.mainThread().execute(() -> {
                        result.setValue(Resource.error(finalErrorMsg, null));
                    });
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<List<Item>>> call, Throwable t) {
                executors.mainThread().execute(() -> {
                    result.setValue(Resource.error("Network error: " + t.getMessage(), null));

                    // Show error toast
                    ToastUtils.showErrorToast(GoGoLaundryApp.getInstance(),
                            "Failed to load popular items: " + t.getMessage());
                });
            }
        });
    }

    /**
     * Convert Item models to ItemEntity objects
     *
     * @param items List of Item models from API
     * @return List of ItemEntity objects for database
     */
    private List<ItemEntity> convertToItemEntities(List<Item> items) {
        List<ItemEntity> itemEntities = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);

        for (Item item : items) {
            ItemEntity entity = new ItemEntity();
            entity.setId(item.getId());
            entity.setServiceId(item.getServiceId());
            entity.setName(item.getName());
            entity.setBnName(item.getBnName());
            entity.setDescription(item.getDescription());
            entity.setBnDescription(item.getBnDescription());
            entity.setPrice(item.getPrice());
            entity.setImageUrl(item.getImageUrl());

            // Handle is_active which might be a number (1/0) or boolean
            if (item.getIsActiveRaw() != null) {
                entity.setActive(item.getIsActiveRaw() == 1);
            } else {
                entity.setActive(item.isActive());
            }

            // Handle in_stock which might be a number (1/0) or boolean
            if (item.getInStockRaw() != null) {
                entity.setInStock(item.getInStockRaw() == 1);
            } else {
                entity.setInStock(item.isInStock());
            }

            entity.setServiceName(item.getServiceName());

            // Parse dates if available
            try {
                if (item.getCreatedAt() != null && !item.getCreatedAt().isEmpty()) {
                    entity.setCreatedAt(dateFormat.parse(item.getCreatedAt()));
                } else {
                    entity.setCreatedAt(new Date());
                }

                if (item.getUpdatedAt() != null && !item.getUpdatedAt().isEmpty()) {
                    entity.setUpdatedAt(dateFormat.parse(item.getUpdatedAt()));
                } else {
                    entity.setUpdatedAt(new Date());
                }
            } catch (ParseException e) {
                // Use current date if parsing fails
                entity.setCreatedAt(new Date());
                entity.setUpdatedAt(new Date());
            }

            itemEntities.add(entity);
        }

        return itemEntities;
    }
}
