// Set new default font family and font color to mimic Bootstrap's default styling
Chart.defaults.font.family = 'Nunito, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';
Chart.defaults.color = '#858796';

// Function to format numbers with commas
function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Function to get random data for demo purposes
function getRandomData(count, min, max) {
    var data = [];
    for (var i = 0; i < count; i++) {
        data.push(Math.floor(Math.random() * (max - min + 1)) + min);
    }
    return data;
}

// Function to get dates for the last n days
function getLastNDays(n) {
    var result = [];
    for (var i = n - 1; i >= 0; i--) {
        var d = new Date();
        d.setDate(d.getDate() - i);
        result.push(d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
    }
    return result;
}

// Function to get months
function getMonths() {
    return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
}

// Revenue Chart
var ctx = document.getElementById("revenueChart");
var revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: getLastNDays(7),
        datasets: [{
            label: "Revenue",
            lineTension: 0.3,
            backgroundColor: "rgba(78, 115, 223, 0.05)",
            borderColor: "rgba(78, 115, 223, 1)",
            pointRadius: 3,
            pointBackgroundColor: "rgba(78, 115, 223, 1)",
            pointBorderColor: "rgba(78, 115, 223, 1)",
            pointHoverRadius: 3,
            pointHoverBackgroundColor: "rgba(78, 115, 223, 1)",
            pointHoverBorderColor: "rgba(78, 115, 223, 1)",
            pointHitRadius: 10,
            pointBorderWidth: 2,
            data: getRandomData(7, 1000, 5000),
        }],
    },
    options: {
        maintainAspectRatio: false,
        layout: {
            padding: {
                left: 10,
                right: 25,
                top: 25,
                bottom: 0
            }
        },
        scales: {
            x: {
                time: {
                    unit: 'date'
                },
                grid: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    maxTicksLimit: 7
                }
            },
            y: {
                ticks: {
                    maxTicksLimit: 5,
                    padding: 10,
                    // Include a BDT sign in the ticks
                    callback: function(value) {
                        return numberWithCommas(value) + ' BDT';
                    }
                },
                grid: {
                    color: "rgb(234, 236, 244)",
                    drawBorder: false,
                    borderDash: [2],
                    zeroLineBorderDash: [2]
                },
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: "rgb(255,255,255)",
                bodyColor: "#858796",
                titleMarginBottom: 10,
                titleColor: '#6e707e',
                titleFont: {
                    size: 14
                },
                borderColor: '#dddfeb',
                borderWidth: 1,
                padding: {
                    x: 15,
                    y: 15
                },
                displayColors: false,
                intersect: false,
                mode: 'index',
                caretPadding: 10,
                callbacks: {
                    label: function(context) {
                        var datasetLabel = context.dataset.label || '';
                        return datasetLabel + ': ' + numberWithCommas(context.parsed.y) + ' BDT';
                    }
                }
            }
        }
    }
});

// Order Status Pie Chart
var ctx2 = document.getElementById("orderStatusChart");
var orderStatusChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ["Pending", "Processing", "Delivered", "Cancelled"],
        datasets: [{
            data: getRandomData(4, 10, 100),
            backgroundColor: ['#4e73df', '#f6c23e', '#1cc88a', '#e74a3b'],
            hoverBackgroundColor: ['#2e59d9', '#dda20a', '#17a673', '#c23321'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
            borderWidth: 2,
            hoverBorderWidth: 3,
            hoverOffset: 5,
            spacing: 2,
            weight: 1
        }],
    },
    options: {
        maintainAspectRatio: false,
        responsive: true,
        animation: {
            animateScale: true,
            animateRotate: true,
            duration: 1000
        },
        interaction: {
            mode: 'nearest',
            intersect: false,
            axis: 'xy'
        },
        plugins: {
            tooltip: {
                backgroundColor: "rgb(255,255,255)",
                bodyColor: "#858796",
                borderColor: '#dddfeb',
                borderWidth: 1,
                padding: {
                    x: 10,
                    y: 10
                },
                displayColors: true,
                boxWidth: 10,
                boxHeight: 10,
                boxPadding: 3,
                usePointStyle: true,
                titleFont: {
                    size: 12
                },
                bodyFont: {
                    size: 11
                },
                position: 'nearest',
                xAlign: 'center',
                yAlign: 'center',
                caretSize: 6,
                callbacks: {
                    title: function(tooltipItems) {
                        return tooltipItems[0].label;
                    },
                    label: function(context) {
                        const value = context.parsed;
                        const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                        const percentage = Math.round((value / total) * 100);
                        return ` ${value} orders (${percentage}%)`;
                    }
                }
            },
            legend: {
                display: false  // Hide the built-in legend since we're using a custom one
            }
        },
        cutout: '80%',
    },
});

// Update charts when dropdown items are clicked
document.addEventListener('DOMContentLoaded', function() {
    // Get dropdown items
    var dropdownItems = document.querySelectorAll('.dropdown-item[data-period]');

    // Add click event listener to each dropdown item
    dropdownItems.forEach(function(item) {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            var period = this.getAttribute('data-period');

            // Update revenue chart based on selected period
            if (period === 'week') {
                revenueChart.data.labels = getLastNDays(7);
                revenueChart.data.datasets[0].data = getRandomData(7, 1000, 5000);
            } else if (period === 'month') {
                revenueChart.data.labels = getLastNDays(30);
                revenueChart.data.datasets[0].data = getRandomData(30, 1000, 5000);
            } else if (period === 'year') {
                revenueChart.data.labels = getMonths();
                revenueChart.data.datasets[0].data = getRandomData(12, 10000, 50000);
            }

            // Update chart
            revenueChart.update();
        });
    });

    // Fetch real data from API
    fetchChartData();
});

// Function to update the order status legend
function updateOrderStatusLegend(labels, colors) {
    if (!labels || !colors || labels.length === 0 || colors.length === 0) {
        return;
    }

    // Get the legend container
    const legendContainer = document.getElementById('orderStatusLegend');
    if (!legendContainer) {
        return;
    }

    // Clear the current legend
    legendContainer.innerHTML = '';

    // Create a row div for better alignment
    const row = document.createElement('div');
    row.className = 'd-flex justify-content-center flex-wrap';
    legendContainer.appendChild(row);

    // Add a legend item for each status
    for (let i = 0; i < labels.length; i++) {
        const item = document.createElement('div');
        item.className = 'legend-item';

        const icon = document.createElement('i');
        icon.className = 'fas fa-circle';
        icon.style.color = colors[i];

        const text = document.createElement('span');
        text.textContent = labels[i];

        item.appendChild(icon);
        item.appendChild(text);
        row.appendChild(item);
    }
}

// Function to fetch real chart data from API
function fetchChartData() {
    console.log('Fetching chart data...');

    // Fetch revenue data
    fetch('/GoGoLaundry/GoGoLaundryAdminPanel/api/reports/revenue.php?period=week')
        .then(response => {
            console.log('Revenue API response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Revenue API data:', data);
            if (data.success) {
                // Update revenue chart with real data
                revenueChart.data.labels = data.data.labels;
                revenueChart.data.datasets[0].data = data.data.values;
                revenueChart.update();
            } else {
                console.error('Revenue API returned error:', data.message || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('Error fetching revenue data:', error);
            // Show error message on the chart
            document.getElementById('revenueChartError').innerHTML = 'Error loading revenue data. Please check the console for details.';
        });

    // Fetch order status data
    fetch('/GoGoLaundry/GoGoLaundryAdminPanel/api/reports/order_status.php')
        .then(response => {
            console.log('Order status API response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Order status API data:', data);
            if (data.success) {
                // Update order status chart with real data
                orderStatusChart.data.labels = data.data.labels;
                orderStatusChart.data.datasets[0].data = data.data.values;

                // Update colors if provided
                if (data.data.colors && data.data.colors.length > 0) {
                    orderStatusChart.data.datasets[0].backgroundColor = data.data.colors;
                }

                // Update hover colors if provided
                if (data.data.hoverColors && data.data.hoverColors.length > 0) {
                    orderStatusChart.data.datasets[0].hoverBackgroundColor = data.data.hoverColors;
                }

                // Update the legend
                updateOrderStatusLegend(data.data.labels, data.data.colors);

                orderStatusChart.update();
            } else {
                console.error('Order status API returned error:', data.message || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('Error fetching order status data:', error);
            // Show error message on the chart
            document.getElementById('orderStatusChartError').innerHTML = 'Error loading order status data. Please check the console for details.';
        });
}
