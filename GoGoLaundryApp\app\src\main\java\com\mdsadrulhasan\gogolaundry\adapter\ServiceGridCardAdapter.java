package com.mdsadrulhasan.gogolaundry.adapter;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.model.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Adapter for displaying services in a grid layout with card design
 */
public class ServiceGridCardAdapter extends RecyclerView.Adapter<ServiceGridCardAdapter.ServiceViewHolder> {

    private List<Service> services;
    private final OnServiceClickListener listener;

    /**
     * Interface for handling service item clicks
     */
    public interface OnServiceClickListener {
        void onServiceClick(Service service);
        void onAddToCartClick(Service service);
    }

    /**
     * Constructor
     *
     * @param services List of services
     * @param listener Click listener
     */
    public ServiceGridCardAdapter(List<Service> services, OnServiceClickListener listener) {
        this.services = services;
        this.listener = listener;
    }

    /**
     * Update services list and refresh adapter
     *
     * @param services New list of services
     */
    public void updateServices(List<Service> services) {
        if (services == null) {
            services = new ArrayList<>();
        }
        this.services = services;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ServiceViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_service_card, parent, false);
        return new ServiceViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ServiceViewHolder holder, int position) {
        if (services != null && position < services.size()) {
            Service service = services.get(position);
            holder.bind(service, listener);
        }
    }

    @Override
    public int getItemCount() {
        return services != null ? services.size() : 0;
    }

    /**
     * ViewHolder for service items
     */
    static class ServiceViewHolder extends RecyclerView.ViewHolder {

        private final ImageView serviceIcon;
        private final TextView serviceName;
        private final TextView serviceDescription;
        // Add to Cart button removed

        public ServiceViewHolder(@NonNull View itemView) {
            super(itemView);
            serviceIcon = itemView.findViewById(R.id.service_icon);
            serviceName = itemView.findViewById(R.id.service_name);
            serviceDescription = itemView.findViewById(R.id.service_description);
            // Add to Cart button removed
        }

        /**
         * Bind service data to views
         *
         * @param service Service to display
         * @param listener Click listener
         */
        public void bind(Service service, OnServiceClickListener listener) {
            serviceName.setText(service.getName());
            serviceDescription.setText(service.getDescription());

            // Load service icon
            if (service.getIconUrl() != null && !service.getIconUrl().isEmpty()) {
                String iconUrl = service.getIconUrl();
                String imageUrl;

                // Check if the URL is already absolute
                if (iconUrl.startsWith("http://") || iconUrl.startsWith("https://")) {
                    imageUrl = iconUrl;
                } else {
                    // Construct URL based on the API base URL
                    String baseUrl = ApiClient.getBaseUrl();
                    // Remove any leading slashes from iconUrl to avoid double slashes
                    if (iconUrl.startsWith("/")) {
                        iconUrl = iconUrl.substring(1);
                    }

                    // If the icon URL contains "uploads/", assume it's relative to the admin panel root
                    if (iconUrl.contains("uploads/")) {
                        // Go up one level from the API directory to the admin panel root
                        imageUrl = baseUrl + "../" + iconUrl;
                    } else {
                        // Assume it's in the uploads/services directory
                        imageUrl = baseUrl + "../uploads/services/" + iconUrl;
                    }
                }

                // Log the image URL for debugging
                Log.d("ServiceGridCardAdapter", "Loading service icon from: " + imageUrl);

                // Load the image with Glide
                Glide.with(itemView.getContext())
                        .load(imageUrl)
                        .apply(new RequestOptions()
                                .placeholder(getServiceDefaultIcon(service))
                                .error(getServiceDefaultIcon(service))
                                .fitCenter() // Ensure image fits properly
                                .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.ALL))
                        .into(serviceIcon);

                // Remove tint to show actual colors from API
                serviceIcon.setColorFilter(null);
            } else {
                // Load default icon with Glide for consistent handling
                Glide.with(itemView.getContext())
                        .load(getServiceDefaultIcon(service))
                        .apply(new RequestOptions()
                                .fitCenter()
                                .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.ALL))
                        .into(serviceIcon);

                // Remove tint for default icons as well
                serviceIcon.setColorFilter(null);
                Log.d("ServiceGridCardAdapter", "No icon URL available for service: " + service.getName());
            }

            // Set click listener for the entire item
            itemView.setOnClickListener(v -> listener.onServiceClick(service));
        }

        /**
         * Get default icon based on service name
         *
         * @param service Service
         * @return Resource ID for default icon
         */
        private int getServiceDefaultIcon(Service service) {
            String serviceName = service.getName().toLowerCase();

            if (serviceName.contains("wash") || serviceName.contains("laundry")) {
                return R.drawable.ic_washing_machine;
            } else if (serviceName.contains("iron") || serviceName.contains("press")) {
                return R.drawable.ic_iron;
            } else if (serviceName.contains("dry") || serviceName.contains("clean")) {
                return R.drawable.ic_dry_cleaning;
            } else if (serviceName.contains("stain") || serviceName.contains("remov")) {
                return R.drawable.ic_stain_removal;
            } else {
                return R.drawable.ic_washing_machine; // Default
            }
        }
    }
}
