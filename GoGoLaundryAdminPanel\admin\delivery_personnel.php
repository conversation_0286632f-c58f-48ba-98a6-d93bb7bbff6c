<?php
/**
 * Delivery Personnel Management Page
 *
 * This page handles the delivery personnel management in the admin panel
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';

// Include authentication middleware
require_once 'auth.php';

// Initialize variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';

// Build query
$query = "
    SELECT *
    FROM delivery_personnel
    WHERE 1=1
";

$countQuery = "SELECT COUNT(*) FROM delivery_personnel WHERE 1=1";
$params = [];
$countParams = [];

// Add search condition
if (!empty($search)) {
    $searchCondition = " AND (full_name LIKE ? OR phone LIKE ? OR email LIKE ?)";
    $query .= $searchCondition;
    $countQuery .= $searchCondition;
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
    $countParams = array_merge($countParams, [$searchParam, $searchParam, $searchParam]);
}

// Add status condition
if ($status === 'active') {
    $statusCondition = " AND is_active = 1";
    $query .= $statusCondition;
    $countQuery .= $statusCondition;
} elseif ($status === 'inactive') {
    $statusCondition = " AND is_active = 0";
    $query .= $statusCondition;
    $countQuery .= $statusCondition;
}

// Add order by
$query .= " ORDER BY full_name ASC";

// Add limit and offset
$query .= " LIMIT ? OFFSET ?";
$params[] = $limit;
$params[] = $offset;

// Execute count query
$stmt = $pdo->prepare($countQuery);
$stmt->execute($countParams);
$totalPersonnel = $stmt->fetchColumn();

// Calculate total pages
$totalPages = ceil($totalPersonnel / $limit);

// Execute main query
$stmt = $pdo->prepare($query);
$stmt->execute($params);
$personnel = $stmt->fetchAll();

// Handle form submission for adding new delivery personnel
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_personnel'])) {
    $fullName = $_POST['full_name'];
    $phone = $_POST['phone'];
    $email = $_POST['email'] ?? null;
    $address = $_POST['address'] ?? null;
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    $errors = [];

    if (empty($fullName)) {
        $errors[] = 'Full name is required';
    }

    if (empty($phone)) {
        $errors[] = 'Phone number is required';
    } elseif (!preg_match('/^\+?[0-9]{10,15}$/', $phone)) {
        $errors[] = 'Invalid phone number format';
    }

    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }

    // Check if phone number already exists
    if (!empty($phone)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM delivery_personnel WHERE phone = ?");
        $stmt->execute([$phone]);
        if ($stmt->fetchColumn() > 0) {
            $errors[] = 'Phone number already exists';
        }
    }

    // Check if email already exists
    if (!empty($email)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM delivery_personnel WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetchColumn() > 0) {
            $errors[] = 'Email already exists';
        }
    }

    if (empty($errors)) {
        // Insert new delivery personnel
        $stmt = $pdo->prepare("
            INSERT INTO delivery_personnel (full_name, phone, email, address, is_active)
            VALUES (?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([$fullName, $phone, $email, $address, $isActive]);

        if ($result) {
            $_SESSION['success_message'] = 'Delivery personnel added successfully';
        } else {
            $_SESSION['error_message'] = 'Failed to add delivery personnel';
        }

        // Redirect to maintain pagination and search
        header("Location: delivery_personnel.php?page=$page&search=" . urlencode($search) . "&status=" . urlencode($status));
        exit;
    } else {
        $_SESSION['error_message'] = implode('<br>', $errors);
    }
}

// Handle form submission for updating delivery personnel status
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $personnelId = (int)$_POST['personnel_id'];
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Update delivery personnel status
    $stmt = $pdo->prepare("
        UPDATE delivery_personnel
        SET is_active = ?
        WHERE id = ?
    ");

    $result = $stmt->execute([$isActive, $personnelId]);

    if ($result) {
        $_SESSION['success_message'] = 'Delivery personnel status updated successfully';
    } else {
        $_SESSION['error_message'] = 'Failed to update delivery personnel status';
    }

    // Redirect to maintain pagination and search
    header("Location: delivery_personnel.php?page=$page&search=" . urlencode($search) . "&status=" . urlencode($status));
    exit;
}

// Handle form submission for deleting delivery personnel
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_personnel'])) {
    $personnelId = (int)$_POST['personnel_id'];

    // Check if delivery personnel is assigned to any orders
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM orders
        WHERE delivery_personnel_id = ?
    ");
    $stmt->execute([$personnelId]);
    $orderCount = $stmt->fetchColumn();

    if ($orderCount > 0) {
        $_SESSION['error_message'] = 'Cannot delete delivery personnel assigned to orders. Please reassign the orders first.';
    } else {
        // Delete delivery personnel
        $stmt = $pdo->prepare("
            DELETE FROM delivery_personnel
            WHERE id = ?
        ");

        $result = $stmt->execute([$personnelId]);

        if ($result) {
            $_SESSION['success_message'] = 'Delivery personnel deleted successfully';
        } else {
            $_SESSION['error_message'] = 'Failed to delete delivery personnel';
        }
    }

    // Redirect to maintain pagination and search
    header("Location: delivery_personnel.php?page=$page&search=" . urlencode($search) . "&status=" . urlencode($status));
    exit;
}

// Page title and breadcrumbs
$pageTitle = 'Delivery Personnel Management';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Delivery Personnel', 'link' => '']
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Delivery Personnel Management</h1>

    <?php include 'includes/alerts.php'; ?>

    <!-- Search and Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search and Filter</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="delivery_personnel.php" class="row">
                <div class="col-md-6 mb-3">
                    <label for="search">Search</label>
                    <input type="text" class="form-control" id="search" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Name, Phone, Email">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="status">Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">All</option>
                        <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                        <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary mr-2">Search</button>
                    <a href="delivery_personnel.php" class="btn btn-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Delivery Personnel Button -->
    <div class="mb-4">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPersonnelModal">
            <i class="fas fa-plus"></i> Add Delivery Personnel
        </button>
    </div>

    <!-- Delivery Personnel Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Delivery Personnel</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Address</th>
                            <th>Status</th>
                            <th>Last Location Update</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($personnel)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No delivery personnel found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($personnel as $person): ?>
                                <tr>
                                    <td><?= htmlspecialchars($person['full_name']) ?></td>
                                    <td><?= htmlspecialchars($person['phone']) ?></td>
                                    <td><?= htmlspecialchars($person['email'] ?? 'N/A') ?></td>
                                    <td><?= htmlspecialchars($person['address'] ?? 'N/A') ?></td>
                                    <td>
                                        <span class="badge bg-<?= $person['is_active'] ? 'success' : 'danger' ?>">
                                            <?= $person['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($person['last_location_update']): ?>
                                            <?= date('M d, Y H:i', strtotime($person['last_location_update'])) ?>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary view-location" data-id="<?= $person['id'] ?>" data-lat="<?= $person['current_location_lat'] ?>" data-lng="<?= $person['current_location_lng'] ?>" <?= !$person['current_location_lat'] ? 'disabled' : '' ?>>
                                            <i class="fas fa-map-marker-alt"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-info edit-personnel" data-id="<?= $person['id'] ?>" data-name="<?= htmlspecialchars($person['full_name']) ?>" data-phone="<?= htmlspecialchars($person['phone']) ?>" data-email="<?= htmlspecialchars($person['email'] ?? '') ?>" data-address="<?= htmlspecialchars($person['address'] ?? '') ?>" data-active="<?= $person['is_active'] ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger delete-personnel" data-id="<?= $person['id'] ?>" data-name="<?= htmlspecialchars($person['full_name']) ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center mt-4">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                    Previous
                                </a>
                            </li>
                        <?php endif; ?>

                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>

                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>">
                                    Next
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Delivery Personnel Modal -->
<div class="modal fade" id="addPersonnelModal" tabindex="-1" role="dialog" aria-labelledby="addPersonnelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPersonnelModalLabel">Add Delivery Personnel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="delivery_personnel.php">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="phone" name="phone" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email (Optional)</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address (Optional)</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="add_personnel" class="btn btn-primary">Add Personnel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Delivery Personnel Modal -->
<div class="modal fade" id="editPersonnelModal" tabindex="-1" role="dialog" aria-labelledby="editPersonnelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPersonnelModalLabel">Edit Delivery Personnel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="edit_delivery_personnel.php">
                <div class="modal-body">
                    <input type="hidden" id="edit_personnel_id" name="personnel_id">
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="edit_phone" name="phone" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email (Optional)</label>
                        <input type="email" class="form-control" id="edit_email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="edit_address" class="form-label">Address (Optional)</label>
                        <textarea class="form-control" id="edit_address" name="address" rows="3"></textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_personnel" class="btn btn-primary">Update Personnel</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Delivery Personnel Modal -->
<div class="modal fade" id="deletePersonnelModal" tabindex="-1" role="dialog" aria-labelledby="deletePersonnelModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deletePersonnelModalLabel">Delete Delivery Personnel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <span id="delete_personnel_name"></span>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <form method="POST" action="delivery_personnel.php">
                    <input type="hidden" id="delete_personnel_id" name="personnel_id">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="delete_personnel" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- View Location Modal -->
<div class="modal fade" id="viewLocationModal" tabindex="-1" role="dialog" aria-labelledby="viewLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewLocationModalLabel">Delivery Personnel Location</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="map" style="height: 400px;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script>
<script>
    // Initialize map
    let map;
    let marker;

    function initMap() {
        // Default map center (Dhaka, Bangladesh)
        const defaultCenter = { lat: 23.8103, lng: 90.4125 };

        // Create map
        map = new google.maps.Map(document.getElementById("map"), {
            zoom: 15,
            center: defaultCenter,
        });

        // Create marker
        marker = new google.maps.Marker({
            position: defaultCenter,
            map: map,
            title: "Delivery Personnel Location"
        });
    }

    // Handle view location button click
    $('.view-location').click(function() {
        const lat = parseFloat($(this).data('lat'));
        const lng = parseFloat($(this).data('lng'));

        if (lat && lng) {
            const position = { lat, lng };

            // Update marker position
            marker.setPosition(position);

            // Center map on marker
            map.setCenter(position);
        }

        // Show modal
        $('#viewLocationModal').modal('show');
    });

    // Handle edit personnel button click
    $('.edit-personnel').click(function() {
        const id = $(this).data('id');
        const name = $(this).data('name');
        const phone = $(this).data('phone');
        const email = $(this).data('email');
        const address = $(this).data('address');
        const active = $(this).data('active');

        // Set form values
        $('#edit_personnel_id').val(id);
        $('#edit_full_name').val(name);
        $('#edit_phone').val(phone);
        $('#edit_email').val(email);
        $('#edit_address').val(address);
        $('#edit_is_active').prop('checked', active === 1);

        // Show modal
        $('#editPersonnelModal').modal('show');
    });

    // Handle delete personnel button click
    $('.delete-personnel').click(function() {
        const id = $(this).data('id');
        const name = $(this).data('name');

        // Set form values
        $('#delete_personnel_id').val(id);
        $('#delete_personnel_name').text(name);

        // Show modal
        $('#deletePersonnelModal').modal('show');
    });
</script>
