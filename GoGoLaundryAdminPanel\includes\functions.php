<?php
/**
 * Common Functions
 *
 * This file contains common utility functions used throughout the application
 */

// Include configuration and database connection
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/db.php';

/**
 * Sanitize input data
 *
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate phone number (Bangladesh format)
 *
 * @param string $phone Phone number to validate
 * @return bool True if valid, false otherwise
 */
function validatePhone($phone) {
    // Remove any non-digit characters
    $phone = preg_replace('/\D/', '', $phone);

    // Check if it's a valid Bangladesh phone number
    // Format: +8801XXXXXXXXX or 01XXXXXXXXX (X = digit)
    if (preg_match('/^(?:\+?880|0)1[3-9]\d{8}$/', $phone)) {
        return true;
    }

    return false;
}

/**
 * Format phone number to standard format
 *
 * @param string $phone Phone number to format
 * @return string Formatted phone number
 */
function formatPhone($phone) {
    // First, log the original phone number for debugging
    error_log("formatPhone - Original phone: {$phone}");

    // Remove any non-digit characters except for the leading '+'
    $hasPlus = (substr($phone, 0, 1) === '+');
    $phone = preg_replace('/\D/', '', $phone);

    // If the number starts with 0, replace with 880
    if (substr($phone, 0, 1) === '0') {
        $phone = '880' . substr($phone, 1);
    }

    // If the number doesn't have country code, add it
    if (substr($phone, 0, 3) !== '880') {
        $phone = '880' . $phone;
    }

    // Ensure we don't have duplicate country codes
    if (substr($phone, 0, 6) === '880880') {
        $phone = substr($phone, 3);
    }

    // Log the final formatted phone number
    error_log("formatPhone - Formatted phone: {$phone}");

    return $phone;
}

/**
 * Validate email address
 *
 * @param string $email Email to validate
 * @return bool True if valid, false otherwise
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate JSON response
 *
 * @param bool $success Whether the request was successful
 * @param string $message Response message
 * @param array $data Additional data to include
 * @param int $statusCode HTTP status code
 * @return void Outputs JSON and exits
 */
function jsonResponse($success, $message, $data = [], $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');

    // Process user data for Android compatibility
    if (isset($data['user'])) {
        // Convert numeric is_verified to boolean
        if (isset($data['user']['is_verified'])) {
            $data['user']['is_verified'] = (bool)$data['user']['is_verified'];
        }

        // Ensure location IDs are integers
        if (isset($data['user']['division_id'])) {
            $data['user']['division_id'] = (int)$data['user']['division_id'];
        }
        if (isset($data['user']['district_id'])) {
            $data['user']['district_id'] = (int)$data['user']['district_id'];
        }
        if (isset($data['user']['upazilla_id'])) {
            $data['user']['upazilla_id'] = (int)$data['user']['upazilla_id'];
        }

        // Log location data for debugging
        error_log("User location data in response:");
        if (isset($data['user']['division_id'])) {
            error_log("Division ID: " . $data['user']['division_id'] .
                      ", Name: " . ($data['user']['division_name'] ?? 'null'));
        }
        if (isset($data['user']['district_id'])) {
            error_log("District ID: " . $data['user']['district_id'] .
                      ", Name: " . ($data['user']['district_name'] ?? 'null'));
        }
        if (isset($data['user']['upazilla_id'])) {
            error_log("Upazilla ID: " . $data['user']['upazilla_id'] .
                      ", Name: " . ($data['user']['upazilla_name'] ?? 'null'));
        }
    }

    $response = [
        'success' => $success,
        'message' => $message
    ];

    if (!empty($data)) {
        $response['data'] = $data;
    }

    echo json_encode($response);
    exit;
}

/**
 * Check if a request is rate limited
 *
 * @param string $phone Phone number
 * @param string $ip IP address
 * @param PDO $pdo Database connection
 * @param string $purpose Purpose of OTP (optional)
 * @return bool True if rate limited, false otherwise
 */
function isRateLimited($phone, $ip, $pdo, $purpose = null) {
    // For testing purposes, clear rate limits for reset_password
    if ($purpose === 'reset_password') {
        // Clear recent OTP logs for this phone number
        $stmt = $pdo->prepare("
            DELETE FROM otp_logs
            WHERE phone = ?
            AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute([$phone]);
        return false; // Never rate limit password reset requests
    }

    // Check daily limit
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM otp_logs
        WHERE phone = ?
        AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        AND status = 'sent'
    ");
    $stmt->execute([$phone]);
    $result = $stmt->fetch();

    if ($result['count'] >= OTP_DAILY_LIMIT) {
        return true;
    }

    // Check for too many requests in a short time (1 minute)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM otp_logs
        WHERE ip_address = ?
        AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
    ");
    $stmt->execute([$ip]);
    $result = $stmt->fetch();

    if ($result['count'] >= 3) { // Max 3 requests per minute
        return true;
    }

    return false;
}

/**
 * Log OTP activity
 *
 * @param string $phone Phone number
 * @param string $ip IP address
 * @param string $status Status of the OTP
 * @param PDO $pdo Database connection
 * @return void
 */
function logOtpActivity($phone, $ip, $status, $pdo) {
    $stmt = $pdo->prepare("
        INSERT INTO otp_logs (phone, ip_address, status)
        VALUES (?, ?, ?)
    ");
    $stmt->execute([$phone, $ip, $status]);
}

/**
 * Get client IP address
 *
 * @return string IP address
 */
function getClientIp() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    } else {
        // Default IP for CLI or when REMOTE_ADDR is not available
        $ip = '127.0.0.1';
    }
    return $ip;
}

/**
 * Check if shop is currently open based on operating hours
 *
 * @param string $operatingHoursJson JSON string of operating hours
 * @return bool True if open, false if closed
 */
function isShopOpen($operatingHoursJson) {
    if (!$operatingHoursJson) {
        return true; // Assume open if no hours specified
    }

    try {
        $operatingHours = json_decode($operatingHoursJson, true);
        if (!$operatingHours) {
            return true;
        }

        // Check if it's text format stored in JSON
        if (isset($operatingHours['format']) && $operatingHours['format'] === 'text') {
            return true; // For text format, assume always open (can't parse complex text)
        }

        $currentDay = strtolower(date('l')); // e.g., 'monday'
        $currentTime = date('H:i'); // e.g., '14:30'

        if (!isset($operatingHours[$currentDay])) {
            return false; // Closed if day not specified
        }

        $dayHours = $operatingHours[$currentDay];
        if (!$dayHours['is_open']) {
            return false; // Explicitly closed
        }

        $openTime = $dayHours['open_time'];
        $closeTime = $dayHours['close_time'];

        // Handle overnight hours (e.g., 22:00 to 06:00)
        if ($closeTime < $openTime) {
            return ($currentTime >= $openTime || $currentTime <= $closeTime);
        } else {
            return ($currentTime >= $openTime && $currentTime <= $closeTime);
        }

    } catch (Exception $e) {
        return true; // Default to open if parsing fails
    }
}

/**
 * Format operating hours for display
 *
 * @param string $operatingHours Operating hours (JSON or text)
 * @return string Formatted operating hours
 */
function formatOperatingHours($operatingHours) {
    if (empty($operatingHours)) {
        return 'Not specified';
    }

    // Try to decode as JSON first
    $decoded = json_decode($operatingHours, true);
    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
        // Check if it's text format stored in JSON
        if (isset($decoded['format']) && $decoded['format'] === 'text') {
            return $decoded['text'] ?? 'Not specified';
        }

        // Format structured data
        $days = [
            'monday' => 'Monday',
            'tuesday' => 'Tuesday',
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
            'saturday' => 'Saturday',
            'sunday' => 'Sunday'
        ];

        $formatted = [];
        foreach ($days as $dayKey => $dayName) {
            if (isset($decoded[$dayKey])) {
                $dayData = $decoded[$dayKey];
                if ($dayData['is_open']) {
                    $openTime = date('g:i A', strtotime($dayData['open_time']));
                    $closeTime = date('g:i A', strtotime($dayData['close_time']));
                    $formatted[] = "$dayName: $openTime - $closeTime";
                } else {
                    $formatted[] = "$dayName: Closed";
                }
            }
        }

        return implode("\n", $formatted);
    } else {
        // Return as-is for text format
        return $operatingHours;
    }
}

/**
 * Validate operating hours data
 *
 * @param array $operatingHoursData Array of operating hours data
 * @return array Array with 'valid' boolean and 'errors' array
 */
function validateOperatingHours($operatingHoursData) {
    $errors = [];
    $hasOpenDay = false;

    $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    foreach ($days as $day) {
        if (isset($operatingHoursData[$day]) && $operatingHoursData[$day]['is_open']) {
            $hasOpenDay = true;

            $openTime = $operatingHoursData[$day]['open_time'] ?? '';
            $closeTime = $operatingHoursData[$day]['close_time'] ?? '';

            if (empty($openTime) || empty($closeTime)) {
                $errors[] = ucfirst($day) . ': Open and close times are required when day is marked as open';
            }

            // Validate time format
            if (!empty($openTime) && !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $openTime)) {
                $errors[] = ucfirst($day) . ': Invalid open time format';
            }

            if (!empty($closeTime) && !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $closeTime)) {
                $errors[] = ucfirst($day) . ': Invalid close time format';
            }
        }
    }

    if (!$hasOpenDay) {
        $errors[] = 'At least one day must be marked as open';
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}
