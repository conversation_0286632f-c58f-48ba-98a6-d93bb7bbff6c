<?php
/**
 * General Configuration
 *
 * This file contains general configuration settings for the application
 */

// Application settings
define('APP_NAME', 'GoGoLaundry');
define('APP_URL', 'http://192.168.0.106/GoGoLaundry/GoGoLaundryAdminPanel');
define('APP_VERSION', '1.0.0');

// SMS API Configuration (BulkSMSBD)
define('SMS_API_URL', 'https://bulksmsbd.net/api/smsapi');
define('SMS_API_KEY', 'XI5Ba63BRRcKBvrPzQq0'); // Replace with your actual API key
define('SMS_SENDER_ID', '8809617625765'); // Replace with your sender ID
define('SMS_TYPE', 'text'); // Message type

// OTP Configuration
// Note: OTP_ENABLED is now controlled by the database setting 'otp_enabled'
define('OTP_LENGTH', 6); // Length of OTP (fallback if not in database)
define('OTP_EXPIRY', 10 * 60); // OTP expiry time in seconds (10 minutes)
define('OTP_MAX_ATTEMPTS', 3); // Maximum verification attempts
define('OTP_DAILY_LIMIT', 5); // Maximum OTPs per day per user

// Security settings
define('HASH_COST', 10); // Password hashing cost

// Environment setting (development, production)
define('ENVIRONMENT', 'development');

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Session configuration
// Only configure session if it hasn't started yet
if (session_status() === PHP_SESSION_NONE) {
    // Set session cookie parameters
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);

    // For local development, don't require secure cookies
    // In production with HTTPS, set cookie_secure to 1
    ini_set('session.cookie_secure', 0);

    // Set SameSite to Lax for better compatibility with mobile browsers
    // 'None' requires secure=1, which we don't have in local development
    ini_set('session.cookie_samesite', 'Lax');

    // Session lifetime settings
    ini_set('session.gc_maxlifetime', 86400); // Session lifetime: 24 hours
    ini_set('session.cookie_lifetime', 86400); // Cookie lifetime: 24 hours

    // Set session name
    session_name('PHPSESSID');

    // Start session
    session_start();

    // Log session start for debugging
    error_log('Session started/resumed with ID: ' . session_id());
} else {
    // Log that session was already active
    error_log('Session already active with ID: ' . session_id());
}
