<?php
/**
 * Reports Dashboard Page
 *
 * This page serves as a central hub for all reports and analytics
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OrderManager.php';
require_once '../includes/ServiceManager.php';
require_once '../includes/UserManager.php';

// Include authentication middleware
require_once 'auth.php';

// Initialize managers
$orderManager = new OrderManager($pdo);
$serviceManager = new ServiceManager($pdo);
$userManager = new UserManager($pdo);

// Get date range parameters (default to last 30 days)
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Get summary statistics
$totalOrders = $orderManager->getTotalOrders($startDate, $endDate);
$totalRevenue = $orderManager->getTotalRevenue($startDate, $endDate);
$totalCustomers = $userManager->getTotalCustomers();
$totalServices = $serviceManager->getTotalServices();

// Page title and breadcrumbs
$pageTitle = 'Reports Dashboard';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Reports', 'link' => '']
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Reports Dashboard</h1>
    
    <!-- Date Range Filter -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Date Range Filter</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="" class="form-inline">
                <div class="row align-items-center">
                    <div class="col-md-4 mb-2">
                        <label for="start_date" class="mr-2">Start Date:</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label for="end_date" class="mr-2">End Date:</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>">
                    </div>
                    <div class="col-md-4 mb-2">
                        <button type="submit" class="btn btn-primary">Apply Filter</button>
                        <a href="reports.php" class="btn btn-secondary ml-2">Reset</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row">
        <!-- Total Orders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalOrders) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Revenue Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalRevenue, 2) ?> BDT</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Customers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalCustomers) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Services Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Services</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalServices) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-concierge-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Cards -->
    <div class="row">
        <!-- Sales Report Card -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Sales Report</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-chart-line fa-4x text-primary mb-3"></i>
                    </div>
                    <p>View detailed sales analytics, revenue trends, and payment method statistics.</p>
                    <a href="sales_report.php?start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>" class="btn btn-primary btn-block">
                        <i class="fas fa-arrow-right"></i> Go to Sales Report
                    </a>
                </div>
            </div>
        </div>

        <!-- Service Report Card -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Service Analytics</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-chart-pie fa-4x text-success mb-3"></i>
                    </div>
                    <p>Analyze service performance, usage patterns, and item popularity by service.</p>
                    <a href="service_report.php?start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>" class="btn btn-success btn-block">
                        <i class="fas fa-arrow-right"></i> Go to Service Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Customer Report Card -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Customer Analytics</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-users fa-4x text-info mb-3"></i>
                    </div>
                    <p>View customer demographics, retention rates, and ordering patterns.</p>
                    <a href="customer_report.php?start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>" class="btn btn-info btn-block">
                        <i class="fas fa-arrow-right"></i> Go to Customer Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize any JavaScript functionality here
    });
</script>
