<?php
/**
 * Admin Setup Script
 * 
 * This script creates a default admin user for testing purposes
 * Run this script once to create an admin account
 */

// Include required files
require_once 'config/config.php';
require_once 'config/db.php';
require_once 'includes/functions.php';
require_once 'includes/AdminManager.php';

try {
    echo "<h2>GoGoLaundry Admin Setup</h2>\n";
    echo "<p>Setting up default admin account...</p>\n";

    // Initialize admin manager
    $adminManager = new AdminManager($pdo);

    // Check if admin_users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() == 0) {
        // Create admin_users table
        $createTableSQL = "
        CREATE TABLE IF NOT EXISTS `admin_users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `email` varchar(100) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `role` enum('admin','super_admin') NOT NULL DEFAULT 'admin',
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `last_login` timestamp NULL DEFAULT NULL,
            `failed_login_attempts` int(11) NOT NULL DEFAULT 0,
            `locked_until` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_username` (`username`),
            KEY `idx_email` (`email`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createTableSQL);
        echo "<p>✅ Admin users table created successfully.</p>\n";
    }

    // Default admin credentials
    $defaultAdmin = [
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => 'admin123',
        'full_name' => 'System Administrator',
        'role' => 'super_admin'
    ];

    // Check if admin already exists
    $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE username = ? OR email = ?");
    $stmt->execute([$defaultAdmin['username'], $defaultAdmin['email']]);
    
    if ($stmt->rowCount() > 0) {
        echo "<p>⚠️ Admin user already exists.</p>\n";
        echo "<p><strong>Login Credentials:</strong></p>\n";
        echo "<ul>\n";
        echo "<li>Username: <strong>" . $defaultAdmin['username'] . "</strong></li>\n";
        echo "<li>Password: <strong>" . $defaultAdmin['password'] . "</strong></li>\n";
        echo "</ul>\n";
    } else {
        // Create admin user
        $result = $adminManager->addAdmin(
            $defaultAdmin['username'],
            $defaultAdmin['email'],
            $defaultAdmin['password'],
            $defaultAdmin['full_name'],
            $defaultAdmin['role'],
            true
        );

        if ($result) {
            echo "<p>✅ Default admin user created successfully!</p>\n";
            echo "<p><strong>Login Credentials:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Username: <strong>" . $defaultAdmin['username'] . "</strong></li>\n";
            echo "<li>Password: <strong>" . $defaultAdmin['password'] . "</strong></li>\n";
            echo "<li>Role: <strong>" . $defaultAdmin['role'] . "</strong></li>\n";
            echo "</ul>\n";
        } else {
            echo "<p>❌ Failed to create admin user.</p>\n";
        }
    }

    echo "<p><strong>Admin Panel URL:</strong> <a href='admin/login.php'>admin/login.php</a></p>\n";
    echo "<p><strong>Note:</strong> Please change the default password after first login for security.</p>\n";

} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 40px; }
h2 { color: #333; }
p { margin: 10px 0; }
ul { margin: 10px 0; padding-left: 20px; }
li { margin: 5px 0; }
strong { color: #d63384; }
a { color: #0d6efd; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
