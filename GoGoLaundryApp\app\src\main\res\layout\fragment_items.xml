<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:padding="6dp">

    <!-- Header Section -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/header_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="@color/primary"
        app:cardCornerRadius="20dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="24dp">

                <!-- Header Title with Icon -->
                <LinearLayout
                    android:id="@+id/title_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginEnd="12dp"
                        android:src="@drawable/ic_services"
                        app:tint="@color/white" />

                    <TextView
                        android:id="@+id/service_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="19sp"
                        android:textColor="@color/white"
                        android:textStyle="bold"
                        android:letterSpacing="0.02"
                        tools:text="Dry Cleaning" />

                </LinearLayout>

                <!-- Subtitle -->
                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:alpha="0.9"
                    android:fontFamily="sans-serif"
                    android:letterSpacing="0.01"
                    android:text="Choose from our quality items"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_small"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title_container" />

            </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Items Content Section with RecyclerView -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_card">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/items_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:clipChildren="false"
            android:nestedScrollingEnabled="true"
            android:paddingTop="2dp"
            android:paddingBottom="8dp"
            android:scrollbars="vertical"
            android:overScrollMode="never"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbarFadeDuration="1000"
            android:fadeScrollbars="true"
            android:background="@android:color/transparent"
            tools:listitem="@layout/item_laundry_item" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Loading State -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/progress_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_card"
        app:strokeColor="@color/card_stroke_light"
        app:strokeWidth="0.5dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="40dp">

                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:indeterminateTint="@color/primary" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Loading items..."
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Empty State -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/empty_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="16dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_card"
        app:strokeColor="@color/card_stroke_light"
        app:strokeWidth="0.5dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="40dp">

                <ImageView
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:alpha="0.7"
                    android:contentDescription="@string/no_items_found"
                    android:src="@drawable/ic_empty_services"
                    app:tint="@color/primary" />

                <TextView
                    android:id="@+id/empty_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center"
                    android:text="@string/no_items_found"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="No items available for this service at the moment."
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
