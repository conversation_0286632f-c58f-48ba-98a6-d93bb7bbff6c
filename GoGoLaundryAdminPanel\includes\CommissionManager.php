<?php
/**
 * Commission Manager Class
 * 
 * Handles commission calculations and tracking for laundry shop orders
 */

class CommissionManager {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Calculate commission for an order
     * 
     * @param int $shopId Shop ID
     * @param float $orderSubtotal Order subtotal amount
     * @param array $orderItems Array of order items
     * @return array Commission calculation details
     */
    public function calculateCommission($shopId, $orderSubtotal, $orderItems = []) {
        try {
            // Get shop commission percentage
            $stmt = $this->pdo->prepare("SELECT commission_percentage FROM laundry_shops WHERE id = ?");
            $stmt->execute([$shopId]);
            $shop = $stmt->fetch();

            if (!$shop) {
                throw new Exception("Shop not found");
            }

            $commissionPercentage = floatval($shop['commission_percentage']);
            
            // Calculate base commission
            $baseCommission = ($orderSubtotal * $commissionPercentage) / 100;

            // Apply any commission rules or bonuses
            $adjustedCommission = $this->applyCommissionRules($shopId, $baseCommission, $orderSubtotal, $orderItems);

            return [
                'shop_id' => $shopId,
                'order_subtotal' => $orderSubtotal,
                'commission_percentage' => $commissionPercentage,
                'base_commission' => round($baseCommission, 2),
                'adjusted_commission' => round($adjustedCommission, 2),
                'shop_earnings' => round($orderSubtotal - $adjustedCommission, 2),
                'platform_earnings' => round($adjustedCommission, 2)
            ];

        } catch (Exception $e) {
            throw new Exception("Commission calculation failed: " . $e->getMessage());
        }
    }

    /**
     * Apply commission rules and bonuses
     * 
     * @param int $shopId Shop ID
     * @param float $baseCommission Base commission amount
     * @param float $orderSubtotal Order subtotal
     * @param array $orderItems Order items
     * @return float Adjusted commission amount
     */
    private function applyCommissionRules($shopId, $baseCommission, $orderSubtotal, $orderItems) {
        $adjustedCommission = $baseCommission;

        // Rule 1: Volume discount for high-value orders
        if ($orderSubtotal >= 1000) {
            $adjustedCommission *= 0.9; // 10% discount on commission
        } elseif ($orderSubtotal >= 500) {
            $adjustedCommission *= 0.95; // 5% discount on commission
        }

        // Rule 2: New shop incentive (first 30 days)
        if ($this->isNewShop($shopId)) {
            $adjustedCommission *= 0.8; // 20% discount for new shops
        }

        // Rule 3: High-performing shop bonus
        if ($this->isHighPerformingShop($shopId)) {
            $adjustedCommission *= 0.9; // 10% discount for high performers
        }

        // Rule 4: Minimum commission threshold
        $minCommission = 5.0; // Minimum ৳5 commission
        if ($adjustedCommission < $minCommission) {
            $adjustedCommission = $minCommission;
        }

        return $adjustedCommission;
    }

    /**
     * Check if shop is new (within first 30 days)
     */
    private function isNewShop($shopId) {
        $stmt = $this->pdo->prepare("
            SELECT DATEDIFF(NOW(), created_at) as days_since_creation 
            FROM laundry_shops 
            WHERE id = ?
        ");
        $stmt->execute([$shopId]);
        $result = $stmt->fetch();
        
        return $result && $result['days_since_creation'] <= 30;
    }

    /**
     * Check if shop is high-performing (rating >= 4.5 and >= 50 reviews)
     */
    private function isHighPerformingShop($shopId) {
        $stmt = $this->pdo->prepare("
            SELECT rating, total_reviews 
            FROM laundry_shops 
            WHERE id = ?
        ");
        $stmt->execute([$shopId]);
        $result = $stmt->fetch();
        
        return $result && $result['rating'] >= 4.5 && $result['total_reviews'] >= 50;
    }

    /**
     * Record commission transaction
     * 
     * @param int $orderId Order ID
     * @param array $commissionData Commission calculation data
     * @return bool Success status
     */
    public function recordCommission($orderId, $commissionData) {
        try {
            $this->pdo->beginTransaction();

            // Insert commission record
            $stmt = $this->pdo->prepare("
                INSERT INTO commission_transactions (
                    order_id, shop_id, order_subtotal, commission_percentage,
                    base_commission, adjusted_commission, shop_earnings,
                    platform_earnings, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");

            $stmt->execute([
                $orderId,
                $commissionData['shop_id'],
                $commissionData['order_subtotal'],
                $commissionData['commission_percentage'],
                $commissionData['base_commission'],
                $commissionData['adjusted_commission'],
                $commissionData['shop_earnings'],
                $commissionData['platform_earnings']
            ]);

            // Update order with commission amount
            $updateOrderStmt = $this->pdo->prepare("
                UPDATE orders 
                SET shop_commission = ? 
                WHERE id = ?
            ");
            $updateOrderStmt->execute([
                $commissionData['adjusted_commission'],
                $orderId
            ]);

            $this->pdo->commit();
            return true;

        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw new Exception("Failed to record commission: " . $e->getMessage());
        }
    }

    /**
     * Get commission summary for a shop
     * 
     * @param int $shopId Shop ID
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return array Commission summary
     */
    public function getShopCommissionSummary($shopId, $startDate = null, $endDate = null) {
        $whereClause = "WHERE shop_id = ?";
        $params = [$shopId];

        if ($startDate && $endDate) {
            $whereClause .= " AND DATE(created_at) BETWEEN ? AND ?";
            $params[] = $startDate;
            $params[] = $endDate;
        }

        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(*) as total_orders,
                SUM(order_subtotal) as total_revenue,
                SUM(shop_earnings) as total_shop_earnings,
                SUM(platform_earnings) as total_platform_earnings,
                AVG(commission_percentage) as avg_commission_rate
            FROM commission_transactions 
            $whereClause
        ");
        $stmt->execute($params);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get platform commission summary
     * 
     * @param string $startDate Start date (Y-m-d format)
     * @param string $endDate End date (Y-m-d format)
     * @return array Platform commission summary
     */
    public function getPlatformCommissionSummary($startDate = null, $endDate = null) {
        $whereClause = "";
        $params = [];

        if ($startDate && $endDate) {
            $whereClause = "WHERE DATE(created_at) BETWEEN ? AND ?";
            $params = [$startDate, $endDate];
        }

        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(*) as total_orders,
                COUNT(DISTINCT shop_id) as total_shops,
                SUM(order_subtotal) as total_revenue,
                SUM(shop_earnings) as total_shop_earnings,
                SUM(platform_earnings) as total_platform_earnings,
                AVG(commission_percentage) as avg_commission_rate
            FROM commission_transactions 
            $whereClause
        ");
        $stmt->execute($params);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get top earning shops
     * 
     * @param int $limit Number of shops to return
     * @param string $period Period ('today', 'week', 'month', 'year')
     * @return array Top earning shops
     */
    public function getTopEarningShops($limit = 10, $period = 'month') {
        $whereClause = "";
        switch ($period) {
            case 'today':
                $whereClause = "WHERE DATE(ct.created_at) = CURDATE()";
                break;
            case 'week':
                $whereClause = "WHERE ct.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
                break;
            case 'month':
                $whereClause = "WHERE ct.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                break;
            case 'year':
                $whereClause = "WHERE ct.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
                break;
        }

        $stmt = $this->pdo->prepare("
            SELECT 
                ls.id,
                ls.name,
                ls.rating,
                COUNT(ct.id) as total_orders,
                SUM(ct.order_subtotal) as total_revenue,
                SUM(ct.shop_earnings) as total_earnings,
                SUM(ct.platform_earnings) as total_commission_paid
            FROM laundry_shops ls
            INNER JOIN commission_transactions ct ON ls.id = ct.shop_id
            $whereClause
            GROUP BY ls.id, ls.name, ls.rating
            ORDER BY total_earnings DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
