package com.mdsadrulhasan.gogolaundry.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Order;

import java.util.List;

/**
 * Adapter for displaying orders in a GridView
 */
public class OrderGridAdapter extends BaseAdapter {
    
    private final Context context;
    private List<Order> orders;
    private final OrderActionListener listener;
    
    /**
     * Interface for handling order actions
     */
    public interface OrderActionListener {
        void onOrderClicked(Order order);
        void onTrackOrderClicked(Order order);
        void onReorderClicked(Order order);
        void onCancelOrderClicked(Order order);
    }
    
    /**
     * Constructor
     * 
     * @param context Context
     * @param orders List of orders
     * @param listener Action listener
     */
    public OrderGridAdapter(Context context, List<Order> orders, OrderActionListener listener) {
        this.context = context;
        this.orders = orders;
        this.listener = listener;
    }
    
    /**
     * Update orders list and refresh adapter
     * 
     * @param orders New list of orders
     */
    public void updateOrders(List<Order> orders) {
        this.orders = orders;
        notifyDataSetChanged();
    }
    
    @Override
    public int getCount() {
        return orders.size();
    }
    
    @Override
    public Order getItem(int position) {
        return orders.get(position);
    }
    
    @Override
    public long getItemId(int position) {
        return position;
    }
    
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.item_order_grid, parent, false);
            
            holder = new ViewHolder();
            holder.orderId = convertView.findViewById(R.id.order_id);
            holder.orderDate = convertView.findViewById(R.id.order_date);
            holder.orderStatus = convertView.findViewById(R.id.order_status);
            holder.orderTotal = convertView.findViewById(R.id.order_total);
            holder.btnTrackOrder = convertView.findViewById(R.id.btn_track_order);
            holder.btnReorder = convertView.findViewById(R.id.btn_reorder);
            
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        
        // Get order at position
        Order order = getItem(position);
        
        // Bind data to views
        holder.orderId.setText(context.getString(R.string.order_id, order.getOrderNumber()));
        holder.orderDate.setText(context.getString(R.string.order_date, order.getCreatedAt()));
        holder.orderStatus.setText(getFormattedStatus(order.getStatus()));
        holder.orderTotal.setText(context.getString(R.string.order_total, String.valueOf(order.getTotalAmount())));
        
        // Set status color
        setStatusColor(holder.orderStatus, order.getStatus());
        
        // Set button visibility based on order status
        setButtonVisibility(holder, order.getStatus());
        
        // Set click listeners
        convertView.setOnClickListener(v -> listener.onOrderClicked(order));
        holder.btnTrackOrder.setOnClickListener(v -> listener.onTrackOrderClicked(order));
        holder.btnReorder.setOnClickListener(v -> listener.onReorderClicked(order));
        
        // Add long press listener for cancel option
        convertView.setOnLongClickListener(v -> {
            if (canCancel(order.getStatus())) {
                listener.onCancelOrderClicked(order);
                return true;
            }
            return false;
        });
        
        return convertView;
    }
    
    /**
     * ViewHolder pattern for grid items
     */
    private static class ViewHolder {
        TextView orderId;
        TextView orderDate;
        TextView orderStatus;
        TextView orderTotal;
        MaterialButton btnTrackOrder;
        MaterialButton btnReorder;
    }
    
    /**
     * Set status text color based on order status
     * 
     * @param statusTextView Status TextView
     * @param status Order status
     */
    private void setStatusColor(TextView statusTextView, String status) {
        int colorResId;
        
        switch (status.toLowerCase()) {
            case "placed":
            case "confirmed":
                colorResId = R.color.info;
                break;
            case "picked_up":
            case "processing":
                colorResId = R.color.warning;
                break;
            case "ready":
                colorResId = R.color.primary;
                break;
            case "delivered":
                colorResId = R.color.success;
                break;
            case "cancelled":
                colorResId = R.color.error;
                break;
            default:
                colorResId = R.color.text_secondary;
                break;
        }
        
        statusTextView.setTextColor(context.getResources().getColor(colorResId));
    }
    
    /**
     * Set button visibility based on order status
     * 
     * @param holder ViewHolder
     * @param status Order status
     */
    private void setButtonVisibility(ViewHolder holder, String status) {
        boolean canTrack = !status.equalsIgnoreCase("cancelled") && 
                          !status.equalsIgnoreCase("delivered");
        
        holder.btnTrackOrder.setVisibility(canTrack ? View.VISIBLE : View.GONE);
        holder.btnReorder.setVisibility(View.VISIBLE); // Always show reorder option
    }
    
    /**
     * Check if order can be cancelled
     * 
     * @param status Order status
     * @return True if order can be cancelled
     */
    private boolean canCancel(String status) {
        return status.equalsIgnoreCase("placed") || 
               status.equalsIgnoreCase("confirmed");
    }
    
    /**
     * Get formatted status text
     * 
     * @param status Raw status
     * @return Formatted status
     */
    private String getFormattedStatus(String status) {
        switch (status.toLowerCase()) {
            case "placed":
                return context.getString(R.string.order_placed);
            case "confirmed":
                return context.getString(R.string.order_confirmed);
            case "picked_up":
                return context.getString(R.string.order_picked_up);
            case "processing":
                return context.getString(R.string.order_processing);
            case "ready":
                return context.getString(R.string.order_ready);
            case "delivered":
                return context.getString(R.string.order_delivered);
            case "cancelled":
                return context.getString(R.string.order_cancelled);
            default:
                return status;
        }
    }
}
