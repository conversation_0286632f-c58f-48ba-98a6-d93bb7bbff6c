<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow effect -->
    <item android:top="4dp" android:left="4dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/search_shadow" />
            <corners 
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="20dp"
                android:bottomRightRadius="20dp" />
        </shape>
    </item>
    
    <!-- Main background -->
    <item android:bottom="4dp" android:right="4dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/search_background_primary"
                android:endColor="@color/search_background_secondary"
                android:angle="135" />
            <corners 
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="16dp"
                android:bottomRightRadius="16dp" />
        </shape>
    </item>
    
    <!-- Border -->
    <item android:bottom="4dp" android:right="4dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="1dp" 
                android:color="@color/search_border_primary" />
            <corners 
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="16dp"
                android:bottomRightRadius="16dp" />
        </shape>
    </item>
    
    <!-- Inner highlight -->
    <item android:top="1dp" android:left="1dp" android:right="5dp" android:bottom="5dp">
        <shape android:shape="rectangle">
            <stroke 
                android:width="0.5dp" 
                android:color="@color/search_highlight" />
            <corners 
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="15dp"
                android:bottomRightRadius="15dp" />
        </shape>
    </item>
    
</layer-list>
