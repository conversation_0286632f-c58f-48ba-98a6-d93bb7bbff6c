<?php
/**
 * Reset Password API Endpoint
 *
 * This endpoint handles password reset
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OtpManager.php';
require_once '../includes/UserManager.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Get input data
$inputData = json_decode(file_get_contents('php://input'), true);
if (!$inputData) {
    $inputData = $_POST;
}

// Log the input data for debugging
error_log('Reset password attempt with data: ' . json_encode($inputData));

// Validate input
if (empty($inputData['phone'])) {
    jsonResponse(false, 'Phone number is required', [], 400);
}

if (empty($inputData['otp'])) {
    jsonResponse(false, 'OTP is required', [], 400);
}

if (empty($inputData['new_password'])) {
    jsonResponse(false, 'New password is required', [], 400);
}

$phone = sanitize($inputData['phone']);
$otp = sanitize($inputData['otp']);
$newPassword = $inputData['new_password']; // Don't sanitize password

// Log the raw phone number
error_log('Raw phone number: ' . $phone);

// Format phone number
$formattedPhone = formatPhone($phone);
error_log('Formatted phone number: ' . $formattedPhone);

// Validate phone number
if (!validatePhone($phone)) {
    error_log('Phone validation failed for: ' . $phone);
    jsonResponse(false, 'Invalid phone number format', [], 400);
}

// Use the formatted phone number for all operations
$phone = $formattedPhone;

// Get minimum password length from settings
$minPasswordLength = 6; // Default to 6 characters
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'min_password_length'");
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
if ($result && !empty($result['setting_value'])) {
    $minPasswordLength = (int)$result['setting_value'];
}

// Validate password strength
if (strlen($newPassword) < $minPasswordLength) {
    jsonResponse(false, "Password must be at least {$minPasswordLength} characters long", [], 400);
}

// Initialize managers
$otpManager = new OtpManager($pdo);
$userManager = new UserManager($pdo);

// Check if user exists
if (!$userManager->userExistsByPhone($phone)) {
    jsonResponse(false, 'Phone number not registered', [], 404);
}

// Log OTP details
error_log('Attempting to verify OTP: ' . $otp . ' for phone: ' . $phone . ' with purpose: reset_password');

// Check if there's a valid OTP in the database
$stmt = $pdo->prepare("
    SELECT * FROM otps
    WHERE phone = ?
    AND purpose = ?
    AND attempt_count < ?
    AND expires_at > NOW()
    ORDER BY created_at DESC
    LIMIT 1
");
$stmt->execute([$phone, 'reset_password', OTP_MAX_ATTEMPTS]);
$otpRecord = $stmt->fetch();

if (!$otpRecord) {
    error_log('No valid OTP found for phone: ' . $phone);

    // Check if there's any OTP record regardless of validity
    $stmt = $pdo->prepare("
        SELECT * FROM otps
        WHERE phone = ?
        AND purpose = ?
        ORDER BY created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$phone, 'reset_password']);
    $anyOtpRecord = $stmt->fetch();

    if ($anyOtpRecord) {
        error_log('Found expired/used OTP record: ' . json_encode($anyOtpRecord));
        if ($anyOtpRecord['is_used'] == 1) {
            error_log('OTP has already been used');
        }
        if ($anyOtpRecord['attempt_count'] >= OTP_MAX_ATTEMPTS) {
            error_log('OTP max attempts exceeded');
        }
        if (strtotime($anyOtpRecord['expires_at']) < time()) {
            error_log('OTP has expired. Expiry time: ' . $anyOtpRecord['expires_at'] . ', Current time: ' . date('Y-m-d H:i:s'));
        }
    } else {
        error_log('No OTP record found at all for this phone and purpose');
    }

    jsonResponse(false, 'Invalid or expired OTP', [], 400);
}

error_log('Found OTP record: ' . json_encode($otpRecord));
error_log('Stored OTP: ' . $otpRecord['otp'] . ', Provided OTP: ' . $otp);

// Check if OTP matches
if ($otpRecord['otp'] !== $otp) {
    error_log('OTP does not match. Stored: ' . $otpRecord['otp'] . ', Provided: ' . $otp);

    // Increment attempt count
    $stmt = $pdo->prepare("
        UPDATE otps
        SET attempt_count = attempt_count + 1
        WHERE id = ?
    ");
    $stmt->execute([$otpRecord['id']]);
    error_log('Incremented attempt count for OTP ID: ' . $otpRecord['id']);

    jsonResponse(false, 'Invalid OTP', [], 400);
}

// Mark OTP as used
$stmt = $pdo->prepare("
    UPDATE otps
    SET is_used = 1
    WHERE id = ?
");
$stmt->execute([$otpRecord['id']]);
error_log('OTP verified and marked as used');

// Log successful verification
logOtpActivity($phone, getClientIp(), 'verified', $pdo);

// Update password
$result = $userManager->updatePassword($phone, $newPassword);

if (!$result) {
    jsonResponse(false, 'Failed to update password. Please try again later.', [], 500);
}

// Log success
error_log('Password updated successfully for phone: ' . $phone);

// Return success response
jsonResponse(true, 'Password updated successfully. You can now login with your new password.');
