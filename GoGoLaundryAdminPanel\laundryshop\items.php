<?php
// Include authentication middleware
require_once 'auth.php';

$pageTitle = 'My Items';
$currentPage = 'items';

// Handle item actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'toggle_item':
                $itemId = intval($_POST['item_id']);
                $isAvailable = intval($_POST['is_available']);
                $customPrice = !empty($_POST['custom_price']) ? floatval($_POST['custom_price']) : null;
                
                // Check if item is already in shop_items
                $checkStmt = $pdo->prepare("SELECT id FROM shop_items WHERE shop_id = ? AND item_id = ?");
                $checkStmt->execute([$shopOwnerData['shop_id'], $itemId]);
                
                if ($checkStmt->fetch()) {
                    // Update existing
                    $stmt = $pdo->prepare("UPDATE shop_items SET is_available = ?, custom_price = ? WHERE shop_id = ? AND item_id = ?");
                    $stmt->execute([$isAvailable, $customPrice, $shopOwnerData['shop_id'], $itemId]);
                } else {
                    // Insert new
                    $stmt = $pdo->prepare("INSERT INTO shop_items (shop_id, item_id, is_available, custom_price, estimated_hours) VALUES (?, ?, ?, ?, 24)");
                    $stmt->execute([$shopOwnerData['shop_id'], $itemId, $isAvailable, $customPrice]);
                }

                // Trigger cache invalidation for mobile app
                try {
                    $updateType = $customPrice ? 'pricing_update' : 'item_update';
                    $cacheInvalidateStmt = $pdo->prepare("
                        INSERT INTO shop_update_notifications (shop_id, update_type, created_at)
                        VALUES (?, ?, NOW())
                        ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0
                    ");
                    $cacheInvalidateStmt->execute([$shopOwnerData['shop_id'], $updateType]);
                } catch (PDOException $e) {
                    error_log('Cache invalidation error: ' . $e->getMessage());
                }

                $_SESSION['shop_success_message'] = 'Item availability updated successfully!';
                break;
        }
    }
}

// Get all items with shop availability status
try {
    $itemsStmt = $pdo->prepare("
        SELECT i.*, s.name as service_name,
               si.is_available as shop_is_available,
               si.custom_price,
               si.estimated_hours
        FROM items i
        LEFT JOIN services s ON i.service_id = s.id
        LEFT JOIN shop_items si ON i.id = si.item_id AND si.shop_id = ?
        WHERE i.is_active = 1 AND s.is_active = 1
        ORDER BY s.name ASC, i.name ASC
    ");
    $itemsStmt->execute([$shopOwnerData['shop_id']]);
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log('Items fetch error: ' . $e->getMessage());
    $items = [];
}

// Group items by service
$itemsByService = [];
foreach ($items as $item) {
    $itemsByService[$item['service_name']][] = $item;
}

include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">My Items</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <span class="badge bg-info fs-6">
            <i class="fas fa-store me-1"></i><?php echo htmlspecialchars($shopOwnerData['shop_name']); ?>
        </span>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <?php if (empty($itemsByService)): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-tshirt fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No items available.</p>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($itemsByService as $serviceName => $serviceItems): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-concierge-bell me-2"></i><?php echo htmlspecialchars($serviceName); ?>
                        </h5>
                        <small class="text-muted">Manage items for this service</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($serviceItems as $item): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 item-card">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div class="item-info flex-grow-1">
                                                    <?php if ($item['image_url']): ?>
                                                        <img src="<?php echo htmlspecialchars($item['image_url']); ?>" 
                                                             class="item-image mb-2" alt="Item Image">
                                                    <?php endif; ?>
                                                    <h6 class="card-title"><?php echo htmlspecialchars($item['name']); ?></h6>
                                                    <?php if ($item['bn_name']): ?>
                                                        <small class="text-muted d-block"><?php echo htmlspecialchars($item['bn_name']); ?></small>
                                                    <?php endif; ?>
                                                    <div class="price-info mt-2">
                                                        <span class="badge bg-secondary">
                                                            Default: ৳<?php echo number_format($item['price'], 2); ?>
                                                        </span>
                                                        <?php if ($item['custom_price']): ?>
                                                            <span class="badge bg-primary">
                                                                Your Price: ৳<?php echo number_format($item['custom_price'], 2); ?>
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="item-toggle">
                                                    <div class="form-check form-switch">
                                                        <input class="form-check-input item-switch" 
                                                               type="checkbox" 
                                                               id="item_<?php echo $item['id']; ?>"
                                                               data-item-id="<?php echo $item['id']; ?>"
                                                               <?php echo ($item['shop_is_available'] == 1) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" for="item_<?php echo $item['id']; ?>">
                                                            <small><?php echo ($item['shop_is_available'] == 1) ? 'Available' : 'Disabled'; ?></small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <?php if ($item['shop_is_available'] == 1): ?>
                                                <div class="item-details">
                                                    <small class="text-success d-block mb-2">
                                                        <i class="fas fa-check-circle me-1"></i>
                                                        Active in your shop
                                                    </small>
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="editItemPrice(<?php echo $item['id']; ?>, '<?php echo htmlspecialchars($item['name']); ?>', <?php echo $item['price']; ?>, <?php echo $item['custom_price'] ?: 'null'; ?>)">
                                                        <i class="fas fa-edit me-1"></i>Edit Price
                                                    </button>
                                                </div>
                                            <?php else: ?>
                                                <div class="item-details">
                                                    <small class="text-muted">
                                                        <i class="fas fa-times-circle me-1"></i>
                                                        Not available in your shop
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Edit Price Modal -->
<div class="modal fade" id="editPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Item Price</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="toggle_item">
                    <input type="hidden" name="item_id" id="edit_item_id">
                    <input type="hidden" name="is_available" value="1">
                    
                    <div class="alert alert-info">
                        <strong id="edit_item_name"></strong>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Price</label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="text" class="form-control" id="edit_default_price" readonly>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_custom_price" class="form-label">Your Custom Price (Optional)</label>
                        <div class="input-group">
                            <span class="input-group-text">৳</span>
                            <input type="number" class="form-control" id="edit_custom_price" name="custom_price" 
                                   step="0.01" min="0" placeholder="Leave empty to use default price">
                        </div>
                        <div class="form-text">Set a custom price for this item in your shop, or leave empty to use the default price.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Price</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.item-card {
    transition: all 0.3s ease;
    border: 1px solid #e3e6f0;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.15);
}

.item-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
}

.item-switch {
    transform: scale(1.2);
}

.form-check-input:checked {
    background-color: var(--shop-primary);
    border-color: var(--shop-primary);
}

.item-toggle {
    min-width: 80px;
    text-align: center;
}

.item-info {
    padding-right: 15px;
}

.price-info .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}
</style>

<script>
// Handle item toggle
document.addEventListener('DOMContentLoaded', function() {
    const itemSwitches = document.querySelectorAll('.item-switch');
    
    itemSwitches.forEach(function(switchElement) {
        switchElement.addEventListener('change', function() {
            const itemId = this.dataset.itemId;
            const isAvailable = this.checked ? 1 : 0;
            const label = this.nextElementSibling.querySelector('small');
            
            // Update label immediately
            label.textContent = isAvailable ? 'Available' : 'Disabled';
            
            // Send AJAX request
            const formData = new FormData();
            formData.append('action', 'toggle_item');
            formData.append('item_id', itemId);
            formData.append('is_available', isAvailable);
            
            fetch('items.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Reload page to show updated status
                location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                // Revert switch on error
                this.checked = !this.checked;
                label.textContent = this.checked ? 'Available' : 'Disabled';
                alert('Failed to update item. Please try again.');
            });
        });
    });
});

function editItemPrice(itemId, itemName, defaultPrice, customPrice) {
    document.getElementById('edit_item_id').value = itemId;
    document.getElementById('edit_item_name').textContent = itemName;
    document.getElementById('edit_default_price').value = defaultPrice.toFixed(2);
    document.getElementById('edit_custom_price').value = customPrice ? customPrice.toFixed(2) : '';
    
    new bootstrap.Modal(document.getElementById('editPriceModal')).show();
}
</script>

<?php include 'includes/footer.php'; ?>
