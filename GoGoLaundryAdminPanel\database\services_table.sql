-- Services Table
CREATE TABLE IF NOT EXISTS `services` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Sample Services Data (Optional)
INSERT INTO `services` (`name`, `description`, `price`, `is_active`) VALUES
('Wash & Fold', 'Regular washing and folding service for everyday clothes', 50.00, 1),
('Dry Cleaning', 'Professional dry cleaning for delicate fabrics', 120.00, 1),
('Express Service', 'Same-day service with priority handling', 80.00, 1),
('Ironing Only', 'Professional ironing service for pre-washed clothes', 30.00, 1),
('Stain Removal', 'Specialized stain removal for tough stains', 70.00, 1);
