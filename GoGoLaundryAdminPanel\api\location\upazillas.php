<?php
/**
 * Get Upazillas API Endpoint
 *
 * This endpoint returns upazillas for a given district
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get district ID from query parameters
$districtId = isset($_GET['district_id']) ? (int)$_GET['district_id'] : 0;

// Validate district ID
if ($districtId <= 0) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['success' => false, 'message' => 'Invalid district ID']);
    exit;
}

// Get upazillas for the given district
$stmt = $pdo->prepare("SELECT * FROM upazillas WHERE district_id = ? ORDER BY name");
$stmt->execute([$districtId]);
$upazillas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Return success response
header('Content-Type: application/json');
echo json_encode($upazillas);
