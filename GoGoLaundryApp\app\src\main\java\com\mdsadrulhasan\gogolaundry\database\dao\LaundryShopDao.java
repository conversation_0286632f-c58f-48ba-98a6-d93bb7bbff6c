package com.mdsadrulhasan.gogolaundry.database.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;

import java.util.List;

/**
 * DAO for LaundryShop entity
 */
@Dao
public interface LaundryShopDao {

    /**
     * Insert a laundry shop
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(LaundryShopEntity shop);

    /**
     * Insert multiple laundry shops
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAll(List<LaundryShopEntity> shops);

    /**
     * Update a laundry shop
     */
    @Update
    void update(LaundryShopEntity shop);

    /**
     * Delete a laundry shop
     */
    @Delete
    void delete(LaundryShopEntity shop);

    /**
     * Get all laundry shops
     */
    @Query("SELECT * FROM laundry_shops WHERE is_active = 1 ORDER BY rating DESC, total_reviews DESC")
    LiveData<List<LaundryShopEntity>> getAllShops();

    /**
     * Get laundry shop by ID
     */
    @Query("SELECT * FROM laundry_shops WHERE id = :shopId")
    LiveData<LaundryShopEntity> getShopById(int shopId);

    /**
     * Get laundry shop by ID (synchronous)
     */
    @Query("SELECT * FROM laundry_shops WHERE id = :shopId")
    LaundryShopEntity getShopByIdSync(int shopId);

    /**
     * Get shops by location (division, district, upazilla)
     */
    @Query("SELECT * FROM laundry_shops WHERE is_active = 1 AND " +
           "(:divisionId IS NULL OR division_id = :divisionId) AND " +
           "(:districtId IS NULL OR district_id = :districtId) AND " +
           "(:upazillaId IS NULL OR upazilla_id = :upazillaId) " +
           "ORDER BY rating DESC, total_reviews DESC")
    LiveData<List<LaundryShopEntity>> getShopsByLocation(Integer divisionId, Integer districtId, Integer upazillaId);

    /**
     * Search shops by name (include both active and inactive shops for better search results)
     */
    @Query("SELECT * FROM laundry_shops WHERE " +
           "(name LIKE '%' || :query || '%' OR bn_name LIKE '%' || :query || '%' OR address LIKE '%' || :query || '%') " +
           "ORDER BY is_active DESC, rating DESC, total_reviews DESC")
    LiveData<List<LaundryShopEntity>> searchShops(String query);

    /**
     * Get shops within a bounding box (for map display)
     */
    @Query("SELECT * FROM laundry_shops WHERE is_active = 1 AND " +
           "latitude BETWEEN :minLat AND :maxLat AND " +
           "longitude BETWEEN :minLng AND :maxLng " +
           "ORDER BY rating DESC")
    LiveData<List<LaundryShopEntity>> getShopsInBounds(double minLat, double maxLat, double minLng, double maxLng);

    /**
     * Get shops within a bounding box (synchronous for map display)
     */
    @Query("SELECT * FROM laundry_shops WHERE is_active = 1 AND " +
           "latitude BETWEEN :minLat AND :maxLat AND " +
           "longitude BETWEEN :minLng AND :maxLng " +
           "ORDER BY rating DESC")
    List<LaundryShopEntity> getShopsInBoundsSync(double minLat, double maxLat, double minLng, double maxLng);



    /**
     * Get top rated shops
     */
    @Query("SELECT * FROM laundry_shops WHERE is_active = 1 AND total_reviews >= 5 " +
           "ORDER BY rating DESC, total_reviews DESC LIMIT :limit")
    LiveData<List<LaundryShopEntity>> getTopRatedShops(int limit);

    /**
     * Get nearby shops (this will be used with calculated distance)
     */
    @Query("SELECT * FROM laundry_shops WHERE is_active = 1 ORDER BY rating DESC")
    List<LaundryShopEntity> getAllShopsSync();

    /**
     * Clear all shops
     */
    @Query("DELETE FROM laundry_shops")
    void clearAll();

    /**
     * Get all shops without any filters (for debugging)
     */
    @Query("SELECT * FROM laundry_shops ORDER BY id")
    LiveData<List<LaundryShopEntity>> getAllShopsDebug();

    /**
     * Get count of all shops in database (for debugging)
     */
    @Query("SELECT COUNT(*) FROM laundry_shops")
    int getAllShopCount();

    /**
     * Get active shop count
     */
    @Query("SELECT COUNT(*) FROM laundry_shops WHERE is_active = 1")
    LiveData<Integer> getActiveShopCount();
}
