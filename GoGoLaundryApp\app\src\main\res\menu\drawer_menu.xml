<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        <item
            android:id="@+id/nav_home"
            android:icon="@drawable/ic_dashboard"
            android:title="@string/home" />
        <item
            android:id="@+id/nav_services"
            android:icon="@drawable/ic_washing_machine"
            android:title="@string/services" />
        <item
            android:id="@+id/nav_nearest_zone"
            android:icon="@drawable/ic_map"
            android:title="@string/nearest_zone" />
        <item
            android:id="@+id/nav_orders"
            android:icon="@drawable/ic_receipt"
            android:title="@string/orders" />
        <item
            android:id="@+id/nav_profile"
            android:icon="@drawable/ic_person"
            android:title="@string/profile" />
    </group>

    <item android:title="Account">
        <menu>
            <item
                android:id="@+id/nav_settings"
                android:icon="@drawable/ic_settings"
                android:title="Settings" />
            <item
                android:id="@+id/nav_logout"
                android:icon="@drawable/ic_logout"
                android:title="Logout" />
        </menu>
    </item>


</menu>
