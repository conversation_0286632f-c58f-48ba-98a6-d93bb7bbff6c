<?php
/**
 * Get nearby laundry shops based on user location
 *
 * Parameters:
 * - latitude (required): User's latitude
 * - longitude (required): User's longitude
 * - radius (optional): Search radius in kilometers (default: 10)
 * - limit (optional): Maximum number of shops to return (default: 20)
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../../includes/functions.php';

try {
    // Get parameters
    $latitude = isset($_GET['latitude']) ? floatval($_GET['latitude']) : null;
    $longitude = isset($_GET['longitude']) ? floatval($_GET['longitude']) : null;
    $radius = isset($_GET['radius']) ? floatval($_GET['radius']) : 10.0; // Default 10km
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20; // Default 20 shops

    // Validate required parameters
    if ($latitude === null || $longitude === null) {
        throw new Exception('Latitude and longitude are required');
    }

    // Validate coordinates
    if ($latitude < -90 || $latitude > 90 || $longitude < -180 || $longitude > 180) {
        throw new Exception('Invalid coordinates');
    }

    // Calculate bounding box for efficient database query
    $latDelta = $radius / 111.0; // Approximate: 1 degree ≈ 111 km
    $lngDelta = $radius / (111.0 * cos(deg2rad($latitude)));

    $minLat = $latitude - $latDelta;
    $maxLat = $latitude + $latDelta;
    $minLng = $longitude - $lngDelta;
    $maxLng = $longitude + $lngDelta;

    // SQL query to get nearby shops
    $sql = "
        SELECT
            ls.*,
            d.name as division_name,
            dist.name as district_name,
            up.name as upazilla_name,
            (
                6371 * acos(
                    cos(radians(?)) *
                    cos(radians(ls.latitude)) *
                    cos(radians(ls.longitude) - radians(?)) +
                    sin(radians(?)) *
                    sin(radians(ls.latitude))
                )
            ) AS distance
        FROM laundry_shops ls
        LEFT JOIN divisions d ON ls.division_id = d.id
        LEFT JOIN districts dist ON ls.district_id = dist.id
        LEFT JOIN upazillas up ON ls.upazilla_id = up.id
        WHERE
            ls.is_active = 1
            AND ls.is_verified = 1
            AND ls.latitude BETWEEN ? AND ?
            AND ls.longitude BETWEEN ? AND ?
        HAVING distance <= ?
        ORDER BY distance ASC, ls.rating DESC
        LIMIT ?
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        $latitude, $longitude, $latitude, // For distance calculation
        $minLat, $maxLat, $minLng, $maxLng, // For bounding box
        $radius, // For distance filter
        $limit
    ]);

    $shops = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format the response
    $formattedShops = [];
    foreach ($shops as $shop) {
        $formattedShop = [
            'id' => intval($shop['id']),
            'name' => $shop['name'],
            'bn_name' => $shop['bn_name'],
            'description' => $shop['description'],
            'bn_description' => $shop['bn_description'],
            'owner_name' => $shop['owner_name'],
            'phone' => $shop['phone'],
            'email' => $shop['email'],
            'address' => $shop['address'],
            'division_id' => $shop['division_id'] ? intval($shop['division_id']) : null,
            'district_id' => $shop['district_id'] ? intval($shop['district_id']) : null,
            'upazilla_id' => $shop['upazilla_id'] ? intval($shop['upazilla_id']) : null,
            'division_name' => $shop['division_name'],
            'district_name' => $shop['district_name'],
            'upazilla_name' => $shop['upazilla_name'],
            'latitude' => floatval($shop['latitude']),
            'longitude' => floatval($shop['longitude']),
            'operating_hours' => $shop['operating_hours'] ? json_decode($shop['operating_hours'], true) : null,
            'rating' => floatval($shop['rating']),
            'total_reviews' => intval($shop['total_reviews']),
            'commission_percentage' => floatval($shop['commission_percentage']),
            'is_active' => boolval($shop['is_active']),
            'is_verified' => boolval($shop['is_verified']),
            'profile_image_url' => $shop['profile_image_url'],
            'cover_image_url' => $shop['cover_image_url'],
            'distance' => round(floatval($shop['distance']), 1),
            'created_at' => $shop['created_at'],
            'updated_at' => $shop['updated_at']
        ];

        // Get shop services
        $servicesSql = "
            SELECT s.id, s.name, s.bn_name, s.image_url, ss.estimated_hours
            FROM shop_services ss
            INNER JOIN services s ON ss.service_id = s.id
            WHERE ss.shop_id = ? AND ss.is_available = 1 AND s.is_active = 1
            ORDER BY s.sort_order ASC
        ";
        $servicesStmt = $pdo->prepare($servicesSql);
        $servicesStmt->execute([$shop['id']]);
        $formattedShop['services'] = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);

        // Check if shop is currently open
        $formattedShop['is_open'] = isShopOpen($shop['operating_hours']);

        $formattedShops[] = $formattedShop;
    }

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Nearby shops retrieved successfully',
        'data' => $formattedShops,
        'meta' => [
            'total_count' => count($formattedShops),
            'search_center' => [
                'latitude' => $latitude,
                'longitude' => $longitude
            ],
            'search_radius' => $radius,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    // Return error response
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}


