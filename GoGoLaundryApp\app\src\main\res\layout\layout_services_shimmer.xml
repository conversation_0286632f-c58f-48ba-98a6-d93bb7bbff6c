<?xml version="1.0" encoding="utf-8"?>
<com.facebook.shimmer.ShimmerFrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/shimmer_services"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:shimmer_auto_start="true"
    app:shimmer_base_color="@color/shimmer_placeholder"
    app:shimmer_highlight_color="@color/shimmer_highlight"
    app:shimmer_duration="1500"
    app:shimmer_direction="left_to_right">

    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:columnCount="4"
        android:rowCount="1"
        android:alignmentMode="alignBounds"
        android:useDefaultMargins="true">

        <!-- Service shimmer items -->
        <include layout="@layout/item_service_shimmer" />
        <include layout="@layout/item_service_shimmer" />
        <include layout="@layout/item_service_shimmer" />
        <include layout="@layout/item_service_shimmer" />

    </GridLayout>

</com.facebook.shimmer.ShimmerFrameLayout>
