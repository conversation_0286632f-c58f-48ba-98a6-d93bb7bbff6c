package com.mdsadrulhasan.gogolaundry;

import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.core.content.res.ResourcesCompat;

import androidx.appcompat.app.AppCompatActivity;

import org.json.JSONException;
import org.json.JSONObject;
import cn.pedant.SweetAlert.SweetAlertDialog;
import com.google.gson.Gson;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.AppConfig;
import com.mdsadrulhasan.gogolaundry.api.OtpResponse;
import com.mdsadrulhasan.gogolaundry.api.UserResponse;
import com.mdsadrulhasan.gogolaundry.utils.AccountVerifier;
import com.mdsadrulhasan.gogolaundry.utils.ConfigUpdateManager;
import com.mdsadrulhasan.gogolaundry.utils.DialogUtils;
import com.mdsadrulhasan.gogolaundry.utils.LanguageManager;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ValidationUtils;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class LoginActivity extends AppCompatActivity implements ConfigUpdateManager.ConfigUpdateListener {
    private static final String TAG = "LoginActivity";
    private TabLayout tabLayout;
    private View passwordLoginView, otpLoginView;

    // Password login views
    private TextInputLayout phoneLayout, passwordLayout;
    private TextInputEditText phoneEditText, passwordEditText;
    private Button loginButton;
    private TextView forgotPasswordText;

    // OTP login views
    private TextInputLayout otpPhoneLayout, otpLayout;
    private TextInputEditText otpPhoneEditText, otpEditText;
    private Button sendOtpButton, verifyOtpButton;
    private TextView otpTimerText;

    private TextView signupText;
    // Removed refreshConfigButton

    private ApiService apiService;
    private SessionManager sessionManager;
    private LanguageManager languageManager;
    private CountDownTimer otpTimer;
    private SweetAlertDialog loadingDialog;
    private ConfigUpdateManager configUpdateManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Initialize language manager before setting content view
        languageManager = new LanguageManager(this);

        // Apply language settings
        Context context = languageManager.applyLanguage(this);

        super.onCreate(savedInstanceState);

        // Initialize API service and session manager
        apiService = ApiClient.getApiService(this);
        sessionManager = new SessionManager(this);

        // Initialize config update manager
        configUpdateManager = ConfigUpdateManager.getInstance(this);

        // Only add the listener if it's not already registered
        if (!configUpdateManager.hasListener(this)) {
            configUpdateManager.addListener(this);
            Log.d(TAG, "Added config update listener");
        } else {
            Log.d(TAG, "Config update listener already registered");
        }

        // Set a shorter update interval for the login screen (30 seconds)
        configUpdateManager.setUpdateInterval(30);

        // Force refresh the API client
        ApiClient.forceRefreshApiClient();

        // Clear cached configuration and fetch from server
        sessionManager.clearConfig();

        // Show loading dialog
        SweetAlertDialog loadingDialog = DialogUtils.showLoadingDialog(this, "Loading...");

        // Check if there's a logout message from account verification
        String logoutMessage = getIntent().getStringExtra("logout_message");
        if (logoutMessage != null && !logoutMessage.isEmpty()) {
            // Show the logout message
            DialogUtils.showWarningDialog(this, "Session Invalidated", logoutMessage);
        }

        // Fetch configuration from server
        sessionManager.fetchConfigFromServer(new SessionManager.Callback() {
            @Override
            public void onSuccess() {
                // Dismiss loading dialog
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }

                // Log OTP status for debugging
                Log.d(TAG, "OTP verification enabled: " + sessionManager.isOtpEnabled());

                // Set the appropriate layout based on OTP status
                if (sessionManager.isOtpEnabled()) {
                    setContentView(R.layout.activity_login);
                    Log.d(TAG, "Using standard login layout with OTP tab");
                } else {
                    setContentView(R.layout.activity_login_no_otp);
                    Log.d(TAG, "Using login layout without OTP tab");
                }

                // Continue with initialization
                continueInitialization();

                // Start the config update manager
                configUpdateManager.start();
            }

            @Override
            public void onError(String errorMessage) {
                // Dismiss loading dialog
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }

                // Log error
                Log.e(TAG, "Error fetching configuration: " + errorMessage);

                // Use default configuration
                Log.d(TAG, "Using default configuration");
                Log.d(TAG, "OTP verification enabled: " + sessionManager.isOtpEnabled());

                // Set the appropriate layout based on OTP status
                if (sessionManager.isOtpEnabled()) {
                    setContentView(R.layout.activity_login);
                    Log.d(TAG, "Using standard login layout with OTP tab");
                } else {
                    setContentView(R.layout.activity_login_no_otp);
                    Log.d(TAG, "Using login layout without OTP tab");
                }

                // Continue with initialization
                continueInitialization();

                // Start the config update manager even if there was an error
                configUpdateManager.start();
            }
        });

        // Check if user is already logged in
        if (sessionManager.isLoggedIn()) {
            // Verify account status before proceeding to MainActivity
            verifyAccountStatus();
        }
    }

    /**
     * Verify account status before proceeding to MainActivity
     */
    private void verifyAccountStatus() {
        // Ensure any previous dialog is dismissed if this method is called again somehow
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismissWithAnimation();
        }

        // Show SweetAlert loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(this, "Checking account status...");

        // Verify account status (your existing logic)
        AccountVerifier.verifyAccountStatus(this, sessionManager, isValid -> {
            // Handle the result on the main thread
            runOnUiThread(() -> {
                if (isValid) {
                    // Account is valid, proceed to MainActivity
                    // Keep the loading dialog visible during transition
                    Intent intent = new Intent(this, MainActivity.class);
                    startActivity(intent);
                    finish(); // Finish the current activity (LoginActivity)

                    // The loading dialog will be automatically dismissed in onDestroy()
                } else {
                    // If account is not valid, dismiss the dialog
                    if (loadingDialog != null && loadingDialog.isShowing()) {
                        loadingDialog.dismissWithAnimation();
                    }

                    // If account is not valid, AccountVerifier *should* handle logout/redirection.
                    // If it doesn't, you might need to show an error message here or
                    // ensure the user is redirected back to a login/signup screen.
                    // Example:
                    // DialogUtils.showErrorDialog(this, "Account Issue", "Your account is not active. Please contact support.");
                    // Or ensure AccountVerifier correctly sends them back to LoginActivity.
                }
            }); // runOnUiThread ensures dialog dismissal and navigation happen on the UI thread
        });
    }

    // Add this to your Activity's onDestroy to prevent window leaks if the activity is destroyed
// before the callback finishes.

    /**
     * Continue initialization after configuration is loaded
     */
    private void continueInitialization() {
        // Initialize views
        initViews();

        // Only set up tab layout if OTP is enabled
        if (sessionManager.isOtpEnabled()) {
            setupTabLayout();
        }

        setupClickListeners();

        // Check if there are saved credentials and auto-login
        String savedPhone = sessionManager.getSavedPhone();
        String savedPassword = sessionManager.getSavedPassword();

        if (savedPhone != null && savedPassword != null) {
            // Set the saved credentials
            phoneEditText.setText(savedPhone);
            passwordEditText.setText(savedPassword);

            // Auto login
            loginWithPassword(savedPhone, savedPassword);
        }
    }

    private void initViews() {
        // Initialize toolbar
        androidx.appcompat.widget.Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(R.string.login_title);
        }

        // Password login views (common to both layouts)
        passwordLoginView = findViewById(R.id.passwordLoginView);
        phoneLayout = findViewById(R.id.phoneLayout);
        phoneEditText = findViewById(R.id.phoneEditText);
        passwordLayout = findViewById(R.id.passwordLayout);
        passwordEditText = findViewById(R.id.passwordEditText);
        loginButton = findViewById(R.id.loginButton);
        forgotPasswordText = findViewById(R.id.forgotPasswordText);
        signupText = findViewById(R.id.signupText);
        // Removed refreshConfigButton initialization

        // Check if OTP is enabled and views exist
        if (sessionManager.isOtpEnabled()) {
            // Try to initialize TabLayout and OTP views only if OTP is enabled
            // Use getIdentifier to check if resources exist
            int tabLayoutId = getResources().getIdentifier("tabLayout", "id", getPackageName());
            int otpLoginViewId = getResources().getIdentifier("otpLoginView", "id", getPackageName());

            if (tabLayoutId != 0 && otpLoginViewId != 0) {
                tabLayout = findViewById(tabLayoutId);
                otpLoginView = findViewById(otpLoginViewId);

                // Only initialize OTP-specific views if they exist in the layout
                if (tabLayout != null && otpLoginView != null) {
                    int otpPhoneLayoutId = getResources().getIdentifier("otpPhoneLayout", "id", getPackageName());
                    int otpPhoneEditTextId = getResources().getIdentifier("otpPhoneEditText", "id", getPackageName());
                    int otpLayoutId = getResources().getIdentifier("otpLayout", "id", getPackageName());
                    int otpEditTextId = getResources().getIdentifier("otpEditText", "id", getPackageName());
                    int sendOtpButtonId = getResources().getIdentifier("sendOtpButton", "id", getPackageName());
                    int verifyOtpButtonId = getResources().getIdentifier("verifyOtpButton", "id", getPackageName());
                    int otpTimerTextId = getResources().getIdentifier("otpTimerText", "id", getPackageName());

                    if (otpPhoneLayoutId != 0) otpPhoneLayout = findViewById(otpPhoneLayoutId);
                    if (otpPhoneEditTextId != 0) otpPhoneEditText = findViewById(otpPhoneEditTextId);
                    if (otpLayoutId != 0) otpLayout = findViewById(otpLayoutId);
                    if (otpEditTextId != 0) otpEditText = findViewById(otpEditTextId);
                    if (sendOtpButtonId != 0) sendOtpButton = findViewById(sendOtpButtonId);
                    if (verifyOtpButtonId != 0) verifyOtpButton = findViewById(verifyOtpButtonId);
                    if (otpTimerTextId != 0) otpTimerText = findViewById(otpTimerTextId);
                }
            } else {
                Log.d(TAG, "OTP views not found in layout, using password-only login");
                tabLayout = null;
                otpLoginView = null;
            }

            // Initially hide OTP input
            if (otpLayout != null) otpLayout.setVisibility(View.GONE);
            if (verifyOtpButton != null) verifyOtpButton.setVisibility(View.GONE);
            if (otpTimerText != null) otpTimerText.setVisibility(View.GONE);
        } else {
            // If OTP is disabled, we're using the no-OTP layout
            // Just get a reference to the otpLoginView for compatibility
            otpLoginView = findViewById(R.id.otpLoginView);

            // Log that we're using the no-OTP layout
            Log.d(TAG, "Using no-OTP layout, OTP views not initialized");
        }
    }

    private void setupTabLayout() {
        // If OTP is disabled or tabLayout is null, we don't need to set up the tab layout
        if (!sessionManager.isOtpEnabled() || tabLayout == null) {
            return;
        }

        // Only set up tab selection listener if OTP is enabled and tabLayout exists
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                int position = tab.getPosition();
                if (position == 0) {
                    passwordLoginView.setVisibility(View.VISIBLE);
                    otpLoginView.setVisibility(View.GONE);
                } else {
                    passwordLoginView.setVisibility(View.GONE);
                    otpLoginView.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {
            }
        });
    }

    private void setupClickListeners() {
        // Password login (common to both layouts)
        loginButton.setOnClickListener(v -> loginWithPassword());

        // Forgot password (common to both layouts)
        forgotPasswordText.setOnClickListener(v -> {
            Intent intent = new Intent(LoginActivity.this, ForgotPasswordActivity.class);
            startActivity(intent);
        });

        // Sign up (common to both layouts)
        signupText.setOnClickListener(v -> {
            // Stop the config update manager before navigating to SignupActivity
            if (configUpdateManager != null) {
                configUpdateManager.removeListener(this);
                configUpdateManager.stop();
            }

            Intent intent = new Intent(LoginActivity.this, SignupActivity.class);
            startActivity(intent);
            // Don't finish the activity here to avoid the recreation issue
        });

        // OTP login (only if OTP is enabled)
        if (sessionManager.isOtpEnabled()) {
            if (sendOtpButton != null) {
                sendOtpButton.setOnClickListener(v -> sendOtp());
            }
            if (verifyOtpButton != null) {
                verifyOtpButton.setOnClickListener(v -> verifyOtp());
            }
        }

        // Removed refreshConfigButton click listener as we now use automatic updates
    }

    private void loginWithPassword() {
        // Get input values
        String phone = phoneEditText.getText().toString().trim();
        String password = passwordEditText.getText().toString().trim();

        Log.d(TAG, "Login attempt with phone: " + phone);

        // Validate input
        if (!validatePasswordLoginInput(phone, password)) {
            Log.d(TAG, "Input validation failed");
            return;
        }

        // Show loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(this, getString(R.string.logging_in));
        loginButton.setEnabled(false);

        // Format phone number
        String formattedPhone = ValidationUtils.formatPhone(phone);
        Log.d(TAG, "Formatted phone for login: " + formattedPhone);

        // Make API call
        loginWithPassword(formattedPhone, password);
    }

    private void loginWithPassword(String phone, String password) {
        Log.d(TAG, "Making login API call with phone: " + phone);
        apiService.login(phone, password).enqueue(new Callback<ApiResponse<UserResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserResponse>> call, Response<ApiResponse<UserResponse>> response) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                loginButton.setEnabled(true);

                Log.d(TAG, "Login API response code: " + response.code());

                if (response.isSuccessful()) {
                    Log.d(TAG, "Login API call successful");

                    if (response.body() != null) {
                        ApiResponse<UserResponse> apiResponse = response.body();
                        Log.d(TAG, "Login response: success=" + apiResponse.isSuccess() + ", message=" + apiResponse.getMessage());

                        if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                            UserResponse userResponse = apiResponse.getData();

                            // Check if OTP verification is required and enabled
                            if (userResponse.isOtpRequired() && sessionManager.isOtpEnabled()) {
                                Log.d(TAG, "OTP verification required and enabled");

                                // Save the phone number for OTP verification
                                String formattedPhone = userResponse.getUser().getPhone();
                                otpPhoneEditText.setText(formattedPhone);

                                // Switch to OTP tab
                                tabLayout.getTabAt(1).select();

                                // Send OTP automatically
                                sendOtp();

                                // Show message
                                DialogUtils.showWarningDialog(LoginActivity.this,
                                        getString(R.string.otp_required),
                                        apiResponse.getMessage());
                            } else {
                                // If OTP is required but disabled in settings, log this information
                                if (userResponse.isOtpRequired() && !sessionManager.isOtpEnabled()) {
                                    Log.d(TAG, "OTP verification required but disabled in settings, skipping OTP verification");
                                }
                                Log.d(TAG, "Login successful, saving user data");
                                // Save user data and set logged in
                                sessionManager.saveUser(userResponse.getUser());
                                sessionManager.setLoggedIn(true);

                                // Log the session status
                                Log.d(TAG, "User logged in successfully, session established");
                                Log.d(TAG, "Session cookies: " + sessionManager.getCookies());

                                // Always save credentials
                                sessionManager.setStayLoggedIn(true, phone, password);

                                // Show success message with action
                                DialogUtils.showSuccessDialog(
                                        LoginActivity.this,
                                        getString(R.string.success),
                                        apiResponse.getMessage(),
                                        getString(R.string.continue_text),
                                        sweetAlertDialog -> {
                                            // Navigate to main activity
                                            Intent intent = new Intent(LoginActivity.this, MainActivity.class);
                                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                            startActivity(intent);
                                            finish();
                                            sweetAlertDialog.dismissWithAnimation();
                                        });
                            }
                        } else {
                            // Show error message
                            Log.d(TAG, "Login failed: " + apiResponse.getMessage());

                            // Check for specific error messages
                            String errorMessage = apiResponse.getMessage();
                            if (errorMessage.contains("not registered")) {
                                DialogUtils.showWarningDialog(LoginActivity.this,
                                        getString(R.string.error),
                                        getString(R.string.phone_not_registered));
                            } else if (errorMessage.contains("Invalid credentials")) {
                                DialogUtils.showErrorDialog(LoginActivity.this,
                                        getString(R.string.error),
                                        getString(R.string.invalid_credentials));
                            } else {
                                DialogUtils.showErrorDialog(LoginActivity.this, errorMessage);
                            }
                        }
                    } else {
                        Log.e(TAG, "Login response body is null");
                        DialogUtils.showErrorDialog(LoginActivity.this, getString(R.string.login_failed));
                    }
                } else {
                    // Show error message
                    try {
                        if (response.errorBody() != null) {
                            String errorBody = response.errorBody().string();
                            Log.e(TAG, "Login failed with code " + response.code() + ": " + errorBody);

                            try {
                                JSONObject errorJson = new JSONObject(errorBody);
                                String errorMessage = errorJson.optString("message", getString(R.string.login_failed));

                                // Check for specific error messages
                                if (errorMessage.contains("not registered")) {
                                    DialogUtils.showWarningDialog(LoginActivity.this,
                                            getString(R.string.error),
                                            getString(R.string.phone_not_registered));
                                } else {
                                    DialogUtils.showErrorDialog(LoginActivity.this, errorMessage);
                                }
                            } catch (JSONException jsonEx) {
                                DialogUtils.showErrorDialog(LoginActivity.this, "Error " + response.code() + ": " + errorBody);
                            }
                        } else {
                            DialogUtils.showErrorDialog(LoginActivity.this, getString(R.string.login_failed));
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error parsing error response", e);
                        DialogUtils.showErrorDialog(LoginActivity.this, getString(R.string.login_failed));
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserResponse>> call, Throwable t) {
                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    loadingDialog.dismissWithAnimation();
                }
                loginButton.setEnabled(true);

                // Show error message
                Log.e(TAG, "Login API call failed", t);
                String errorMessage = t.getMessage();
                if (errorMessage != null && errorMessage.contains("Unable to resolve host")) {
                    errorMessage = getString(R.string.network_error);
                }
                DialogUtils.showErrorDialog(LoginActivity.this, errorMessage);
            }
        });
    }

    private boolean validatePasswordLoginInput(String phone, String password) {
        boolean isValid = true;

        // Validate phone
        if (!ValidationUtils.isValidPhone(phone)) {
            phoneLayout.setError(getString(R.string.invalid_phone));
            isValid = false;
        } else {
            phoneLayout.setError(null);
        }

        // Validate password
        if (password.isEmpty()) {
            passwordLayout.setError(getString(R.string.password_required));
            isValid = false;
        } else {
            passwordLayout.setError(null);
        }

        return isValid;
    }

    private void sendOtp() {
        // This method should only be called if OTP is enabled
        if (!sessionManager.isOtpEnabled()) {
            Log.e(TAG, "sendOtp() called when OTP is disabled!");
            return;
        }

        // Make sure OTP views are initialized
        if (otpPhoneEditText == null || otpPhoneLayout == null) {
            Log.e(TAG, "OTP views not initialized!");
            return;
        }

        // Get input values
        String phone = otpPhoneEditText.getText().toString().trim();

        // Validate input
        if (!ValidationUtils.isValidPhone(phone)) {
            otpPhoneLayout.setError(getString(R.string.invalid_phone));
            return;
        } else {
            otpPhoneLayout.setError(null);
        }

        // Show loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(this, getString(R.string.sending_otp));
        sendOtpButton.setEnabled(false);

        // Format phone number
        phone = ValidationUtils.formatPhone(phone);

        // Make API call
        apiService.sendRegistrationOtp(phone, "", "login").enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                // Check if activity is still active
                if (!isActivityActive) {
                    Log.w(TAG, "Ignoring OTP response callback - activity is no longer active");
                    return;
                }

                if (response.isSuccessful() && response.body() != null) {
                    try {
                        // Parse response manually
                        Gson gson = new Gson();
                        String jsonString = gson.toJson(response.body());
                        JSONObject jsonResponse = new JSONObject(jsonString);

                        boolean success = jsonResponse.optBoolean("success", false);
                        String message = jsonResponse.optString("message", "");

                        if (success) {
                            // Show OTP input
                            otpLayout.setVisibility(View.VISIBLE);
                            verifyOtpButton.setVisibility(View.VISIBLE);
                            otpTimerText.setVisibility(View.VISIBLE);

                            // Get expires_in from data object
                            JSONObject dataObject = jsonResponse.optJSONObject("data");
                            int expiresIn = 600; // Default 10 minutes
                            if (dataObject != null) {
                                expiresIn = dataObject.optInt("expires_in", 600);
                            }

                            // Start OTP timer
                            startOtpTimer(expiresIn);

                            // Dismiss loading dialog
                            if (loadingDialog != null) {
                                try {
                                    loadingDialog.dismissWithAnimation();
                                } catch (Exception e) {
                                    Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                                }
                            }

                            // Show success message
                            DialogUtils.showSuccessDialog(LoginActivity.this,
                                    getString(R.string.success),
                                    message);
                        } else {
                            // Dismiss loading dialog and reset button
                            if (loadingDialog != null) {
                                try {
                                    loadingDialog.dismissWithAnimation();
                                } catch (Exception e) {
                                    Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                                }
                            }
                            sendOtpButton.setEnabled(true);
                            sendOtpButton.setText(R.string.send_otp);

                            // Show error message
                            DialogUtils.showErrorDialog(LoginActivity.this, message);
                        }
                    } catch (Exception e) {
                        Log.e("LoginActivity", "Error parsing OTP response", e);

                        // Dismiss loading dialog and reset button
                        if (loadingDialog != null) {
                            try {
                                loadingDialog.dismissWithAnimation();
                            } catch (Exception ex) {
                                Log.e(TAG, "Error dismissing dialog: " + ex.getMessage());
                            }
                        }
                        sendOtpButton.setEnabled(true);
                        sendOtpButton.setText(R.string.send_otp);

                        DialogUtils.showErrorDialog(LoginActivity.this, getString(R.string.otp_send_failed));
                    }
                } else {
                    // Dismiss loading dialog and reset button
                    if (loadingDialog != null) {
                        try {
                            loadingDialog.dismissWithAnimation();
                        } catch (Exception e) {
                            Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                        }
                    }
                    sendOtpButton.setEnabled(true);
                    sendOtpButton.setText(R.string.send_otp);

                    // Show error message
                    DialogUtils.showErrorDialog(LoginActivity.this, getString(R.string.otp_send_failed));
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                // Check if activity is still active
                if (!isActivityActive) {
                    Log.w(TAG, "Ignoring OTP failure callback - activity is no longer active");
                    return;
                }

                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    try {
                        loadingDialog.dismissWithAnimation();
                    } catch (Exception e) {
                        Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                    }
                }
                sendOtpButton.setEnabled(true);
                sendOtpButton.setText(R.string.send_otp);

                // Show error message
                String errorMessage = t.getMessage();
                if (errorMessage != null && errorMessage.contains("Unable to resolve host")) {
                    errorMessage = getString(R.string.network_error);
                }
                DialogUtils.showErrorDialog(LoginActivity.this, errorMessage);
            }
        });
    }

    private void verifyOtp() {
        // This method should only be called if OTP is enabled
        if (!sessionManager.isOtpEnabled()) {
            Log.e(TAG, "verifyOtp() called when OTP is disabled!");
            return;
        }

        // Make sure OTP views are initialized
        if (otpPhoneEditText == null || otpPhoneLayout == null ||
                otpEditText == null || otpLayout == null) {
            Log.e(TAG, "OTP views not initialized!");
            return;
        }

        // Get input values
        String phone = otpPhoneEditText.getText().toString().trim();
        String otp = otpEditText.getText().toString().trim();

        // Validate input
        if (!ValidationUtils.isValidPhone(phone)) {
            otpPhoneLayout.setError(getString(R.string.invalid_phone));
            return;
        } else {
            otpPhoneLayout.setError(null);
        }

        // Get OTP length with default fallback
        int otpLength = 6; // Default OTP length
        if (sessionManager.getConfig() != null) {
            otpLength = sessionManager.getConfig().getOtpLength();
        }

        if (!ValidationUtils.isValidOtp(otp, otpLength)) {
            otpLayout.setError(getString(R.string.invalid_otp));
            return;
        } else {
            otpLayout.setError(null);
        }

        // Show loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(this, getString(R.string.verifying_otp));
        verifyOtpButton.setEnabled(false);

        // Format phone number
        phone = ValidationUtils.formatPhone(phone);

        // Make API call
        apiService.verifyOtp(phone, otp, "login").enqueue(new Callback<Object>() {
            @Override
            public void onResponse(Call<Object> call, Response<Object> response) {
                // Check if activity is still active
                if (!isActivityActive) {
                    Log.w(TAG, "Ignoring OTP verification response callback - activity is no longer active");
                    return;
                }

                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    try {
                        loadingDialog.dismissWithAnimation();
                    } catch (Exception e) {
                        Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                    }
                }
                verifyOtpButton.setEnabled(true);

                if (response.isSuccessful() && response.body() != null) {
                    try {
                        // Parse response manually
                        Gson gson = new Gson();
                        String jsonString = gson.toJson(response.body());
                        JSONObject jsonResponse = new JSONObject(jsonString);

                        boolean success = jsonResponse.optBoolean("success", false);
                        String message = jsonResponse.optString("message", "");

                        if (success) {
                            // Check if we have auto_login field in the response
                            boolean autoLogin = false;
                            String userId = null;
                            String userPhone = null;
                            String userName = null;

                            JSONObject dataObject = jsonResponse.optJSONObject("data");
                            if (dataObject != null) {
                                autoLogin = dataObject.optBoolean("auto_login", false);
                                userId = dataObject.optString("user_id", null);
                                userPhone = dataObject.optString("phone", null);
                                userName = dataObject.optString("full_name", null);
                            }

                        if (autoLogin && userId != null && userPhone != null) {
                            // Create a minimal user object
                            com.mdsadrulhasan.gogolaundry.model.User user = new com.mdsadrulhasan.gogolaundry.model.User();
                            try {
                                // Remove any decimal part if present
                                if (userId.contains(".")) {
                                    userId = userId.substring(0, userId.indexOf("."));
                                }
                                user.setId(Integer.parseInt(userId));
                            } catch (NumberFormatException e) {
                                Log.e(TAG, "Error parsing user ID: " + userId, e);
                                // Use a default ID as fallback
                                user.setId(0);
                            }
                            user.setPhone(userPhone);
                            if (userName != null) {
                                user.setFullName(userName);
                            }

                            // Save user data and set logged in
                            sessionManager.saveUser(user);
                            sessionManager.setLoggedIn(true);

                            // Log the session status
                            Log.d(TAG, "User logged in automatically, session established");

                            // Check if activity is still active before navigating
                            if (isActivityActive) {
                                // Navigate to main activity directly
                                Intent intent = new Intent(LoginActivity.this, MainActivity.class);
                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                startActivity(intent);
                                finish();
                            } else {
                                Log.w(TAG, "Not navigating - activity is no longer active");
                            }
                        } else {
                            // Show success message with action
                            DialogUtils.showSuccessDialog(
                                    LoginActivity.this,
                                    getString(R.string.success),
                                    message,
                                    getString(R.string.continue_text),
                                    sweetAlertDialog -> {
                                        // Check if activity is still active before navigating
                                        if (isActivityActive) {
                                            // Navigate to main activity
                                            Intent intent = new Intent(LoginActivity.this, MainActivity.class);
                                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                            startActivity(intent);
                                            finish();
                                            try {
                                                sweetAlertDialog.dismissWithAnimation();
                                            } catch (Exception e) {
                                                Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                                            }
                                        } else {
                                            Log.w(TAG, "Not navigating - activity is no longer active");
                                        }
                                    });
                            }
                        } else {
                            // Show error message
                            DialogUtils.showErrorDialog(LoginActivity.this, message);
                        }
                    } catch (Exception e) {
                        Log.e("LoginActivity", "Error parsing verify OTP response", e);
                        DialogUtils.showErrorDialog(LoginActivity.this, getString(R.string.otp_verification_failed));
                    }
                } else {
                    // Show error message
                    DialogUtils.showErrorDialog(LoginActivity.this, getString(R.string.otp_verification_failed));
                }
            }

            @Override
            public void onFailure(Call<Object> call, Throwable t) {
                // Check if activity is still active
                if (!isActivityActive) {
                    Log.w(TAG, "Ignoring OTP verification failure callback - activity is no longer active");
                    return;
                }

                // Dismiss loading dialog and reset button
                if (loadingDialog != null) {
                    try {
                        loadingDialog.dismissWithAnimation();
                    } catch (Exception e) {
                        Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
                    }
                }
                verifyOtpButton.setEnabled(true);

                // Show error message
                String errorMessage = t.getMessage();
                if (errorMessage != null && errorMessage.contains("Unable to resolve host")) {
                    errorMessage = getString(R.string.network_error);
                }
                DialogUtils.showErrorDialog(LoginActivity.this, errorMessage);
            }
        });
    }

    private void startOtpTimer(int seconds) {
        // Cancel existing timer if any
        if (otpTimer != null) {
            otpTimer.cancel();
        }

        // Create new timer
        otpTimer = new CountDownTimer(seconds * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                long minutes = millisUntilFinished / 60000;
                long seconds = (millisUntilFinished % 60000) / 1000;
                otpTimerText.setText(getString(R.string.otp_timer, minutes, seconds));
            }

            @Override
            public void onFinish() {
                otpTimerText.setText(R.string.otp_expired);
                sendOtpButton.setEnabled(true);
                sendOtpButton.setText(R.string.resend_otp);
            }
        }.start();
    }

    // Flag to track if activity is still active
    private boolean isActivityActive = true;

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Mark activity as inactive
        isActivityActive = false;

        // Cancel timer to prevent memory leaks
        if (otpTimer != null) {
            otpTimer.cancel();
        }

        // Dismiss any active loading dialog
        if (loadingDialog != null && loadingDialog.isShowing()) {
            try {
                loadingDialog.dismissWithAnimation();
            } catch (Exception e) {
                Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
            }
        }

        // Stop the config update manager and remove this listener
        if (configUpdateManager != null) {
            configUpdateManager.removeListener(this);
            configUpdateManager.stop();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        isActivityActive = true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        isActivityActive = false;
    }

    /**
     * Handle configuration updates
     * This method is called when the configuration is updated in the background
     *
     * @param oldConfig Previous configuration
     * @param newConfig New configuration
     */
    @Override
    public void onConfigUpdated(AppConfig oldConfig, AppConfig newConfig) {
        // Check if activity is still active
        if (!isActivityActive) {
            Log.w(TAG, "Ignoring config update callback - activity is no longer active");
            return;
        }

        // Check if OTP status has changed
        if (oldConfig.isOtpEnabled() != newConfig.isOtpEnabled()) {
            Log.d(TAG, "OTP status changed from " + oldConfig.isOtpEnabled() + " to " + newConfig.isOtpEnabled());

            // Show a notification to the user
            String message = "OTP verification is now " + (newConfig.isOtpEnabled() ? "enabled" : "disabled") +
                    ". The app will update automatically.";

            try {
                // Show a toast notification
                Toast.makeText(this, message, Toast.LENGTH_LONG).show();

                // Show a dialog with more information
                DialogUtils.showInfoDialog(
                        LoginActivity.this,
                        "Configuration Updated",
                        message);

                // Apply the new configuration by recreating the activity
                // This is done with a slight delay to allow the user to see the notification
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    // Check again if activity is still active before recreating
                    if (isActivityActive) {
                        Intent intent = getIntent();
                        finish();
                        startActivity(intent);
                    } else {
                        Log.w(TAG, "Not recreating activity - activity is no longer active");
                    }
                }, 3000); // 3 second delay
            } catch (Exception e) {
                Log.e(TAG, "Error handling config update: " + e.getMessage());
            }
        }
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        // Apply language settings to base context
        LanguageManager languageManager = new LanguageManager(newBase);
        super.attachBaseContext(languageManager.applyLanguage(newBase));
    }
}
