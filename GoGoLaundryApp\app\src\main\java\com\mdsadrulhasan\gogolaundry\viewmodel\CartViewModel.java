package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.model.CartItem;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.utils.CartManager;

import java.util.List;

/**
 * ViewModel for cart screen
 */
public class CartViewModel extends AndroidViewModel {

    private static final String TAG = "CartViewModel";

    private final CartManager cartManager;
    private final MutableLiveData<List<CartItem>> cartItems = new MutableLiveData<>();
    private final MutableLiveData<Double> cartTotal = new MutableLiveData<>(0.0);
    private final MutableLiveData<Integer> itemCount = new MutableLiveData<>(0);

    /**
     * Constructor
     *
     * @param application Application context
     */
    public CartViewModel(@NonNull Application application) {
        super(application);
        cartManager = CartManager.getInstance(application);
        refreshCart();
    }

    /**
     * Refresh cart data
     */
    public void refreshCart() {
        List<CartItem> items = cartManager.getCartItems();
        cartItems.setValue(items);
        cartTotal.setValue(cartManager.getCartTotal());
        itemCount.setValue(cartManager.getItemCount());
    }

    /**
     * Get cart items LiveData
     *
     * @return LiveData of cart items
     */
    public LiveData<List<CartItem>> getCartItems() {
        return cartItems;
    }

    /**
     * Get cart total LiveData
     *
     * @return LiveData of cart total
     */
    public LiveData<Double> getCartTotal() {
        return cartTotal;
    }

    /**
     * Get item count LiveData
     *
     * @return LiveData of item count
     */
    public LiveData<Integer> getItemCount() {
        return itemCount;
    }

    /**
     * Get cart item count LiveData
     *
     * @return LiveData of cart item count
     */
    public LiveData<Integer> getCartItemCount() {
        return itemCount;
    }

    /**
     * Add item to cart
     *
     * @param item Item to add
     * @param quantity Quantity
     * @return True if added successfully
     */
    public boolean addToCart(Item item, double quantity) {
        boolean result = cartManager.addToCart(item, quantity);
        refreshCart();
        return result;
    }

    /**
     * Update item quantity in cart
     *
     * @param itemId Item ID
     * @param quantity New quantity
     * @return True if updated successfully
     */
    public boolean updateQuantity(int itemId, double quantity) {
        boolean result = cartManager.updateQuantity(itemId, quantity);
        refreshCart();
        return result;
    }

    /**
     * Remove item from cart
     *
     * @param itemId Item ID
     * @return True if removed successfully
     */
    public boolean removeFromCart(int itemId) {
        boolean result = cartManager.removeFromCart(itemId);
        refreshCart();
        return result;
    }

    /**
     * Increase item quantity in cart
     *
     * @param itemId Item ID
     * @return True if updated successfully
     */
    public boolean increaseQuantity(int itemId) {
        // Find current quantity
        List<CartItem> items = cartItems.getValue();
        if (items != null) {
            for (CartItem item : items) {
                if (item.getItemId() == itemId) {
                    return updateQuantity(itemId, item.getQuantity() + 1);
                }
            }
        }
        return false;
    }

    /**
     * Decrease item quantity in cart
     *
     * @param itemId Item ID
     * @return True if updated successfully
     */
    public boolean decreaseQuantity(int itemId) {
        // Find current quantity
        List<CartItem> items = cartItems.getValue();
        if (items != null) {
            for (CartItem item : items) {
                if (item.getItemId() == itemId) {
                    double newQuantity = item.getQuantity() - 1;
                    if (newQuantity <= 0) {
                        return removeFromCart(itemId);
                    } else {
                        return updateQuantity(itemId, newQuantity);
                    }
                }
            }
        }
        return false;
    }

    /**
     * Clear cart
     */
    public void clearCart() {
        cartManager.clearCart();
        refreshCart();
    }
}
