package com.mdsadrulhasan.gogolaundry.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Order;

import java.util.List;

/**
 * Adapter for displaying orders in a RecyclerView
 */
public class OrderAdapter extends RecyclerView.Adapter<OrderAdapter.OrderViewHolder> {
    
    private List<Order> orders;
    private final OrderActionListener listener;
    
    /**
     * Interface for handling order actions
     */
    public interface OrderActionListener {
        void onOrderClicked(Order order);
        void onTrackOrderClicked(Order order);
        void onReorderClicked(Order order);
        void onCancelOrderClicked(Order order);
    }
    
    /**
     * Constructor
     * 
     * @param orders List of orders
     * @param listener Action listener
     */
    public OrderAdapter(List<Order> orders, OrderActionListener listener) {
        this.orders = orders;
        this.listener = listener;
    }
    
    /**
     * Update orders list and refresh adapter
     * 
     * @param orders New list of orders
     */
    public void updateOrders(List<Order> orders) {
        this.orders = orders;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public OrderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_order, parent, false);
        return new OrderViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull OrderViewHolder holder, int position) {
        Order order = orders.get(position);
        holder.bind(order, listener);
    }
    
    @Override
    public int getItemCount() {
        return orders.size();
    }
    
    /**
     * ViewHolder for order items
     */
    static class OrderViewHolder extends RecyclerView.ViewHolder {
        
        private final TextView orderId;
        private final TextView orderDate;
        private final TextView orderStatus;
        private final TextView orderTotal;
        private final MaterialButton btnTrackOrder;
        private final MaterialButton btnReorder;
        
        public OrderViewHolder(@NonNull View itemView) {
            super(itemView);
            orderId = itemView.findViewById(R.id.order_id);
            orderDate = itemView.findViewById(R.id.order_date);
            orderStatus = itemView.findViewById(R.id.order_status);
            orderTotal = itemView.findViewById(R.id.order_total);
            btnTrackOrder = itemView.findViewById(R.id.btn_track_order);
            btnReorder = itemView.findViewById(R.id.btn_reorder);
        }
        
        /**
         * Bind order data to views
         * 
         * @param order Order to display
         * @param listener Action listener
         */
        public void bind(Order order, OrderActionListener listener) {
            // Set order details
            orderId.setText(itemView.getContext().getString(R.string.order_id, order.getOrderNumber()));
            orderDate.setText(itemView.getContext().getString(R.string.order_date, order.getCreatedAt()));
            orderStatus.setText(itemView.getContext().getString(R.string.order_status, getFormattedStatus(order.getStatus())));
            orderTotal.setText(itemView.getContext().getString(R.string.order_total, String.valueOf(order.getTotalAmount())));
            
            // Set status color
            setStatusColor(order.getStatus());
            
            // Set button visibility based on order status
            setButtonVisibility(order.getStatus());
            
            // Set click listeners
            itemView.setOnClickListener(v -> listener.onOrderClicked(order));
            btnTrackOrder.setOnClickListener(v -> listener.onTrackOrderClicked(order));
            btnReorder.setOnClickListener(v -> listener.onReorderClicked(order));
            
            // Add long press listener for cancel option
            itemView.setOnLongClickListener(v -> {
                if (canCancel(order.getStatus())) {
                    listener.onCancelOrderClicked(order);
                    return true;
                }
                return false;
            });
        }
        
        /**
         * Set status text color based on order status
         * 
         * @param status Order status
         */
        private void setStatusColor(String status) {
            int colorResId;
            
            switch (status.toLowerCase()) {
                case "placed":
                case "confirmed":
                    colorResId = R.color.info;
                    break;
                case "picked_up":
                case "processing":
                    colorResId = R.color.warning;
                    break;
                case "ready":
                    colorResId = R.color.primary;
                    break;
                case "delivered":
                    colorResId = R.color.success;
                    break;
                case "cancelled":
                    colorResId = R.color.error;
                    break;
                default:
                    colorResId = R.color.text_secondary;
                    break;
            }
            
            orderStatus.setTextColor(itemView.getContext().getResources().getColor(colorResId));
        }
        
        /**
         * Set button visibility based on order status
         * 
         * @param status Order status
         */
        private void setButtonVisibility(String status) {
            boolean canTrack = !status.equalsIgnoreCase("cancelled") && 
                              !status.equalsIgnoreCase("delivered");
            
            btnTrackOrder.setVisibility(canTrack ? View.VISIBLE : View.GONE);
            btnReorder.setVisibility(View.VISIBLE); // Always show reorder option
        }
        
        /**
         * Check if order can be cancelled
         * 
         * @param status Order status
         * @return True if order can be cancelled
         */
        private boolean canCancel(String status) {
            return status.equalsIgnoreCase("placed") || 
                   status.equalsIgnoreCase("confirmed");
        }
        
        /**
         * Get formatted status text
         * 
         * @param status Raw status
         * @return Formatted status
         */
        private String getFormattedStatus(String status) {
            switch (status.toLowerCase()) {
                case "placed":
                    return itemView.getContext().getString(R.string.order_placed);
                case "confirmed":
                    return itemView.getContext().getString(R.string.order_confirmed);
                case "picked_up":
                    return itemView.getContext().getString(R.string.order_picked_up);
                case "processing":
                    return itemView.getContext().getString(R.string.order_processing);
                case "ready":
                    return itemView.getContext().getString(R.string.order_ready);
                case "delivered":
                    return itemView.getContext().getString(R.string.order_delivered);
                case "cancelled":
                    return itemView.getContext().getString(R.string.order_cancelled);
                default:
                    return status;
            }
        }
    }
}
