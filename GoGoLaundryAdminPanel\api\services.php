<?php
/**
 * Services API Endpoint
 *
 * This endpoint returns all services or active services (Original for Android app)
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ServiceManager.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Initialize service manager
$serviceManager = new ServiceManager($pdo);

// Check if only active services are requested
$activeOnly = isset($_GET['active_only']) && $_GET['active_only'] === '1';

// Get services
if ($activeOnly) {
    $services = $serviceManager->getActiveServices();
} else {
    $result = $serviceManager->getAllServices(1, 100, ''); // Get all services with a high limit
    $services = $result['services'];
}

// Process services to include image URLs
foreach ($services as &$service) {
    // If image URL is relative, convert to absolute URL
    if (!empty($service['image_url']) && strpos($service['image_url'], 'http') !== 0) {
        $service['image_url'] = APP_URL . '/uploads/services/' . $service['image_url'];
    }
}

// Return success response
jsonResponse(true, 'Services retrieved successfully', $services);
