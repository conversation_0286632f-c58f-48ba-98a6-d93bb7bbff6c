<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - GoGoLaundry</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>FCM API Test</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h5>Test FCM Token Registration</h5>
                            <button id="testRegister" class="btn btn-primary">Test Token Registration</button>
                        </div>

                        <div class="mb-3">
                            <h5>Test FCM Notification Send</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" id="notifTitle" class="form-control mb-2" placeholder="Title" value="Test Notification">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" id="notifMessage" class="form-control mb-2" placeholder="Message" value="This is a test notification">
                                </div>
                            </div>
                            <button id="testSend" class="btn btn-success">Test Send Notification</button>
                        </div>

                        <div id="results" class="mt-4">
                            <h5>Results:</h5>
                            <div id="resultContainer" class="border p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                                <!-- Results will appear here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const resultContainer = document.getElementById('resultContainer');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `text-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'dark'}`;
            logEntry.innerHTML = `<small>[${timestamp}]</small> ${message}`;
            resultContainer.appendChild(logEntry);
            resultContainer.scrollTop = resultContainer.scrollHeight;
        }

        // Test token registration
        async function testTokenRegistration() {
            try {
                log('Testing FCM token registration...');

                const response = await fetch('api/fcm/register_token.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 1,
                        token: 'test_token_' + Date.now(),
                        device_id: 'test_device_' + Date.now(),
                        device_type: 'web'
                    })
                });

                log('Response status: ' + response.status);
                log('Response headers: ' + JSON.stringify([...response.headers.entries()]));

                const responseText = await response.text();
                log('Raw response: ' + responseText);

                try {
                    const result = JSON.parse(responseText);

                    if (result.success) {
                        log('✓ Token registration successful: ' + result.message, 'success');
                    } else {
                        log('✗ Token registration failed: ' + result.message, 'error');
                    }

                    log('Full response: ' + JSON.stringify(result, null, 2));
                } catch (parseError) {
                    log('✗ Failed to parse JSON response: ' + parseError.message, 'error');
                    log('Response was not valid JSON. Raw response: ' + responseText, 'error');
                }

            } catch (error) {
                log('✗ Error testing token registration: ' + error.message, 'error');
            }
        }

        // Test notification send
        async function testNotificationSend() {
            try {
                const title = document.getElementById('notifTitle').value;
                const message = document.getElementById('notifMessage').value;

                log('Testing FCM notification send...');

                const response = await fetch('api/fcm/send_notification.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: title,
                        message: message,
                        type: 'system',
                        data: {
                            test: true,
                            timestamp: Date.now()
                        }
                    })
                });

                log('Response status: ' + response.status);

                const responseText = await response.text();
                log('Raw response: ' + responseText);

                try {
                    const result = JSON.parse(responseText);

                    if (result.success) {
                        log('✓ Notification send successful: ' + result.message, 'success');
                    } else {
                        log('✗ Notification send failed: ' + result.message, 'error');
                    }

                    log('Full response: ' + JSON.stringify(result, null, 2));
                } catch (parseError) {
                    log('✗ Failed to parse JSON response: ' + parseError.message, 'error');
                    log('Response was not valid JSON. Raw response: ' + responseText, 'error');
                }

            } catch (error) {
                log('✗ Error testing notification send: ' + error.message, 'error');
            }
        }

        // Event listeners
        document.getElementById('testRegister').addEventListener('click', testTokenRegistration);
        document.getElementById('testSend').addEventListener('click', testNotificationSend);

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('API Test page loaded');
        });
    </script>
</body>
</html>
