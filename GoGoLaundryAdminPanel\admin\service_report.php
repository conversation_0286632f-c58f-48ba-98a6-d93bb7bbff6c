<?php
/**
 * Service Report Page
 *
 * This page displays service usage reports and analytics
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/ServiceManager.php';
require_once '../includes/OrderManager.php';

// Include authentication middleware
require_once 'auth.php';

// Initialize managers
$serviceManager = new ServiceManager($pdo);
$orderManager = new OrderManager($pdo);

// Get filter parameters
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$serviceId = isset($_GET['service_id']) ? (int)$_GET['service_id'] : 0;

// Validate dates
if (strtotime($startDate) > strtotime($endDate)) {
    $_SESSION['error_message'] = 'Start date cannot be after end date';
    $startDate = date('Y-m-d', strtotime('-30 days'));
    $endDate = date('Y-m-d');
}

// Get all services for filter dropdown
$allServices = $serviceManager->getActiveServices();

// Get service usage data
$serviceUsageData = getServiceUsageData($pdo, $startDate, $endDate, $serviceId);

// Get summary statistics
$totalServiceOrders = getTotalServiceOrders($pdo, $startDate, $endDate, $serviceId);
$totalServiceRevenue = getTotalServiceRevenue($pdo, $startDate, $endDate, $serviceId);
$topItems = getTopItemsByService($pdo, $startDate, $endDate, $serviceId, 5);
$servicePerformance = getServicePerformance($pdo, $startDate, $endDate);

// Page title and breadcrumbs
$pageTitle = 'Service Analytics';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Reports', 'link' => ''],
    ['text' => 'Service Analytics', 'link' => '']
];

// Add Chart.js to page scripts
$pageScripts = [
    '../assets/js/chart.min.js'
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Service Analytics</h1>

    <!-- Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Options</h6>
        </div>
        <div class="card-body">
            <form method="get" action="service_report.php" class="row g-3">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>">
                </div>
                <div class="col-md-4">
                    <label for="service_id" class="form-label">Service</label>
                    <select class="form-control" id="service_id" name="service_id">
                        <option value="0">All Services</option>
                        <?php foreach ($allServices as $service): ?>
                            <option value="<?= $service['id'] ?>" <?= $serviceId == $service['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($service['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalServiceOrders) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalServiceRevenue, 2) ?> BDT</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Average Order Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= $totalServiceOrders > 0 ? number_format($totalServiceRevenue / $totalServiceOrders, 2) : '0.00' ?> BDT
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calculator fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Active Services</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= count($allServices) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-concierge-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Usage Chart -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Service Usage Over Time</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Export Options:</div>
                            <a class="dropdown-item" href="export_service_report.php?format=csv&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>&service_id=<?= $serviceId ?>">Export to CSV</a>
                            <a class="dropdown-item" href="export_service_report.php?format=excel&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>&service_id=<?= $serviceId ?>">Export to Excel</a>
                            <a class="dropdown-item" href="export_service_report.php?format=pdf&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>&service_id=<?= $serviceId ?>">Export to PDF</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="serviceUsageChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Performance Pie Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Service Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4">
                        <canvas id="serviceDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Items Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Top Items</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="topItemsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Service</th>
                            <th>Orders</th>
                            <th>Revenue</th>
                            <th>Percentage</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($topItems as $item): ?>
                            <tr>
                                <td><?= htmlspecialchars($item['item_name']) ?></td>
                                <td><?= htmlspecialchars($item['service_name']) ?></td>
                                <td><?= number_format($item['orders']) ?></td>
                                <td><?= number_format($item['revenue'], 2) ?> BDT</td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: <?= $item['percentage'] ?>%;"
                                             aria-valuenow="<?= $item['percentage'] ?>" aria-valuemin="0" aria-valuemax="100">
                                            <?= number_format($item['percentage'], 1) ?>%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Service Usage Data Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Service Usage Data</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="serviceUsageTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Service</th>
                            <th>Orders</th>
                            <th>Revenue</th>
                            <th>Items Processed</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($serviceUsageData as $data): ?>
                            <tr>
                                <td><?= htmlspecialchars($data['date']) ?></td>
                                <td><?= htmlspecialchars($data['service_name']) ?></td>
                                <td><?= number_format($data['orders']) ?></td>
                                <td><?= number_format($data['revenue'], 2) ?> BDT</td>
                                <td><?= number_format($data['items']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
    // Pass data to charts
    const serviceUsageData = <?= json_encode(array_values($serviceUsageData)) ?>;
    const serviceUsageLabels = <?= json_encode(array_map(function($data) { return $data['date']; }, $serviceUsageData)) ?>;
    const servicePerformanceData = <?= json_encode(array_map(function($service) { return $service['revenue']; }, $servicePerformance)) ?>;
    const servicePerformanceLabels = <?= json_encode(array_map(function($service) { return $service['service_name']; }, $servicePerformance)) ?>;

    // Initialize charts when the page is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initServiceUsageChart();
        initServiceDistributionChart();
        initDataTables();
    });

    // Initialize service usage chart
    function initServiceUsageChart() {
        const ctx = document.getElementById('serviceUsageChart').getContext('2d');

        // Extract revenue and orders data
        const revenueData = serviceUsageData.map(item => item.revenue);
        const ordersData = serviceUsageData.map(item => item.orders);

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: serviceUsageLabels,
                datasets: [
                    {
                        label: "Revenue",
                        backgroundColor: "rgba(78, 115, 223, 0.2)",
                        borderColor: "rgba(78, 115, 223, 1)",
                        borderWidth: 1,
                        data: revenueData,
                        yAxisID: 'y-axis-1',
                    },
                    {
                        label: "Orders",
                        backgroundColor: "rgba(28, 200, 138, 0.2)",
                        borderColor: "rgba(28, 200, 138, 1)",
                        borderWidth: 1,
                        data: ordersData,
                        yAxisID: 'y-axis-2',
                        type: 'line',
                    }
                ],
            },
            options: {
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        left: 10,
                        right: 25,
                        top: 25,
                        bottom: 0
                    }
                },
                scales: {
                    xAxes: [{
                        time: {
                            unit: 'date'
                        },
                        gridLines: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            maxTicksLimit: 7
                        }
                    }],
                    yAxes: [
                        {
                            id: 'y-axis-1',
                            type: 'linear',
                            position: 'left',
                            ticks: {
                                maxTicksLimit: 5,
                                padding: 10,
                                callback: function(value) {
                                    return value.toLocaleString() + ' BDT';
                                }
                            },
                            gridLines: {
                                color: "rgb(234, 236, 244)",
                                zeroLineColor: "rgb(234, 236, 244)",
                                drawBorder: false,
                                borderDash: [2],
                                zeroLineBorderDash: [2]
                            }
                        },
                        {
                            id: 'y-axis-2',
                            type: 'linear',
                            position: 'right',
                            ticks: {
                                maxTicksLimit: 5,
                                padding: 10,
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            },
                            gridLines: {
                                display: false
                            }
                        }
                    ],
                },
                legend: {
                    display: true
                },
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    titleMarginBottom: 10,
                    titleFontColor: '#6e707e',
                    titleFontSize: 14,
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    intersect: false,
                    mode: 'index',
                    caretPadding: 10,
                    callbacks: {
                        label: function(tooltipItem, chart) {
                            var datasetLabel = chart.datasets[tooltipItem.datasetIndex].label || '';
                            if (datasetLabel === "Revenue") {
                                return datasetLabel + ': ' + tooltipItem.yLabel.toLocaleString() + ' BDT';
                            } else {
                                return datasetLabel + ': ' + tooltipItem.yLabel.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    // Initialize service distribution chart
    function initServiceDistributionChart() {
        const ctx = document.getElementById('serviceDistributionChart').getContext('2d');

        // Generate random colors for each service
        const backgroundColors = servicePerformanceLabels.map(() => {
            const r = Math.floor(Math.random() * 200);
            const g = Math.floor(Math.random() * 200);
            const b = Math.floor(Math.random() * 200);
            return `rgba(${r}, ${g}, ${b}, 0.8)`;
        });

        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: servicePerformanceLabels,
                datasets: [{
                    data: servicePerformanceData,
                    backgroundColor: backgroundColors,
                    hoverBackgroundColor: backgroundColors.map(color => color.replace('0.8', '1')),
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const value = data.datasets[0].data[tooltipItem.index];
                            const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return data.labels[tooltipItem.index] + ': ' + value.toLocaleString() + ' BDT (' + percentage + '%)';
                        }
                    }
                },
                legend: {
                    display: true,
                    position: 'bottom'
                },
                cutoutPercentage: 0,
            },
        });
    }

    // Initialize DataTables
    function initDataTables() {
        $('#serviceUsageTable').DataTable({
            order: [[0, 'desc']],
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ]
        });

        $('#topItemsTable').DataTable({
            order: [[3, 'desc']],
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ]
        });
    }
</script>

<?php
/**
 * Get service usage data
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param int $serviceId Service ID (0 for all services)
 * @return array Service usage data
 */
function getServiceUsageData($pdo, $startDate, $endDate, $serviceId = 0) {
    $query = "
        SELECT
            DATE(o.created_at) AS date,
            s.id AS service_id,
            s.name AS service_name,
            COUNT(DISTINCT o.id) AS orders,
            SUM(oi.price * oi.quantity) AS revenue,
            SUM(oi.quantity) AS items
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN items i ON oi.item_id = i.id
        JOIN services s ON i.service_id = s.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
    ";

    $params = [$startDate, $endDate];

    if ($serviceId > 0) {
        $query .= " AND s.id = ?";
        $params[] = $serviceId;
    }

    $query .= "
        GROUP BY date, s.id, s.name
        ORDER BY date DESC, revenue DESC
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get total service orders
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param int $serviceId Service ID (0 for all services)
 * @return int Total orders
 */
function getTotalServiceOrders($pdo, $startDate, $endDate, $serviceId = 0) {
    $query = "
        SELECT COUNT(DISTINCT o.id) AS total
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN items i ON oi.item_id = i.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
    ";

    $params = [$startDate, $endDate];

    if ($serviceId > 0) {
        $query .= " AND i.service_id = ?";
        $params[] = $serviceId;
    }

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    return (int)$stmt->fetchColumn();
}

/**
 * Get total service revenue
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param int $serviceId Service ID (0 for all services)
 * @return float Total revenue
 */
function getTotalServiceRevenue($pdo, $startDate, $endDate, $serviceId = 0) {
    $query = "
        SELECT SUM(oi.price * oi.quantity) AS total
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN items i ON oi.item_id = i.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
    ";

    $params = [$startDate, $endDate];

    if ($serviceId > 0) {
        $query .= " AND i.service_id = ?";
        $params[] = $serviceId;
    }

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    return (float)$stmt->fetchColumn();
}

/**
 * Get top items by service
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param int $serviceId Service ID (0 for all services)
 * @param int $limit Number of items to return
 * @return array Top items
 */
function getTopItemsByService($pdo, $startDate, $endDate, $serviceId = 0, $limit = 5) {
    $query = "
        SELECT
            i.name AS item_name,
            s.name AS service_name,
            COUNT(DISTINCT o.id) AS orders,
            SUM(oi.price * oi.quantity) AS revenue,
            SUM(oi.quantity) AS quantity
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN items i ON oi.item_id = i.id
        JOIN services s ON i.service_id = s.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
    ";

    $params = [$startDate, $endDate];

    if ($serviceId > 0) {
        $query .= " AND s.id = ?";
        $params[] = $serviceId;
    }

    $query .= "
        GROUP BY i.id, i.name, s.id, s.name
        ORDER BY revenue DESC
        LIMIT ?
    ";

    $params[] = $limit;

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate total revenue for percentage
    $totalRevenue = array_reduce($items, function($carry, $item) {
        return $carry + $item['revenue'];
    }, 0);

    // Add percentage to each item
    foreach ($items as &$item) {
        $item['percentage'] = $totalRevenue > 0 ? ($item['revenue'] / $totalRevenue) * 100 : 0;
    }

    return $items;
}

/**
 * Get service performance
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return array Service performance data
 */
function getServicePerformance($pdo, $startDate, $endDate) {
    $query = "
        SELECT
            s.id AS service_id,
            s.name AS service_name,
            COUNT(DISTINCT o.id) AS orders,
            SUM(oi.price * oi.quantity) AS revenue,
            SUM(oi.quantity) AS items
        FROM orders o
        JOIN order_items oi ON o.id = oi.order_id
        JOIN items i ON oi.item_id = i.id
        JOIN services s ON i.service_id = s.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
        GROUP BY s.id, s.name
        ORDER BY revenue DESC
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$startDate, $endDate]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
