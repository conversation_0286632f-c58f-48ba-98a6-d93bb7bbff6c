<?php
/**
 * Customer Report Page
 *
 * This page displays customer analytics and reports
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/UserManager.php';
require_once '../includes/OrderManager.php';

// Include authentication middleware
require_once 'auth.php';

// Initialize managers
$userManager = new UserManager($pdo);
$orderManager = new OrderManager($pdo);

// Get filter parameters
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Validate dates
if (strtotime($startDate) > strtotime($endDate)) {
    $_SESSION['error_message'] = 'Start date cannot be after end date';
    $startDate = date('Y-m-d', strtotime('-30 days'));
    $endDate = date('Y-m-d');
}

// Get customer analytics data
$newCustomers = getNewCustomers($pdo, $startDate, $endDate);
$topCustomers = getTopCustomers($pdo, $startDate, $endDate, 10);
$customerRetention = getCustomerRetention($pdo, $startDate, $endDate);
$orderFrequency = getOrderFrequency($pdo, $startDate, $endDate);
$customerDemographics = getCustomerDemographics($pdo);

// Get summary statistics
$totalCustomers = getTotalCustomers($pdo);
$activeCustomers = getActiveCustomers($pdo, $startDate, $endDate);
$newCustomersCount = count($newCustomers);
$averageOrdersPerCustomer = getAverageOrdersPerCustomer($pdo, $startDate, $endDate);

// Page title and breadcrumbs
$pageTitle = 'Customer Analytics';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Reports', 'link' => ''],
    ['text' => 'Customer Analytics', 'link' => '']
];

// Add Chart.js to page scripts
$pageScripts = [
    '../assets/js/chart.min.js'
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Customer Analytics</h1>

    <!-- Filter Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Options</h6>
        </div>
        <div class="card-body">
            <form method="get" action="customer_report.php" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($startDate) ?>">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($endDate) ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($totalCustomers) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($activeCustomers) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                New Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($newCustomersCount) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Avg. Orders per Customer</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?= number_format($averageOrdersPerCustomer, 1) ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-bag fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Customers Chart -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">New Customer Registrations</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">Export Options:</div>
                            <a class="dropdown-item" href="export_customer_report.php?format=csv&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">Export to CSV</a>
                            <a class="dropdown-item" href="export_customer_report.php?format=excel&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">Export to Excel</a>
                            <a class="dropdown-item" href="export_customer_report.php?format=pdf&start_date=<?= urlencode($startDate) ?>&end_date=<?= urlencode($endDate) ?>">Export to PDF</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="newCustomersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Retention Pie Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Retention</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4">
                        <canvas id="customerRetentionChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> One-time Customers
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> Returning Customers
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-info"></i> Loyal Customers
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Frequency Chart -->
    <div class="row">
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Frequency</h6>
                </div>
                <div class="card-body">
                    <div class="chart-bar">
                        <canvas id="orderFrequencyChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Demographics Chart -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Demographics</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie">
                        <canvas id="customerDemographicsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Customers Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Top Customers</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="topCustomersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Orders</th>
                            <th>Total Spent</th>
                            <th>Avg. Order Value</th>
                            <th>Last Order</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($topCustomers as $customer): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if (!empty($customer['profile_picture_url'])): ?>
                                            <img src="<?= htmlspecialchars($customer['profile_picture_url']) ?>" class="rounded-circle mr-2" width="32" height="32" alt="Profile">
                                        <?php else: ?>
                                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mr-2" style="width: 32px; height: 32px;">
                                                <?= strtoupper(substr($customer['full_name'], 0, 1)) ?>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="font-weight-bold"><?= htmlspecialchars($customer['full_name']) ?></div>
                                            <div class="small text-muted"><?= htmlspecialchars($customer['phone']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?= number_format($customer['order_count']) ?></td>
                                <td><?= number_format($customer['total_spent'], 2) ?> BDT</td>
                                <td><?= number_format($customer['avg_order_value'], 2) ?> BDT</td>
                                <td><?= date('M d, Y', strtotime($customer['last_order'])) ?></td>
                                <td>
                                    <a href="user_details.php?id=<?= $customer['id'] ?>" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
    // Pass data to charts
    const newCustomersData = <?= json_encode(array_values($newCustomers)) ?>;
    const newCustomersLabels = <?= json_encode(array_map(function($data) { return $data['date']; }, $newCustomers)) ?>;
    const customerRetentionData = [
        <?= $customerRetention['one_time'] ?>,
        <?= $customerRetention['returning'] ?>,
        <?= $customerRetention['loyal'] ?>
    ];
    const orderFrequencyLabels = <?= json_encode(array_map(function($data) { return $data['frequency']; }, $orderFrequency)) ?>;
    const orderFrequencyData = <?= json_encode(array_map(function($data) { return $data['count']; }, $orderFrequency)) ?>;
    const demographicsLabels = <?= json_encode(array_map(function($data) { return $data['region']; }, $customerDemographics)) ?>;
    const demographicsData = <?= json_encode(array_map(function($data) { return $data['count']; }, $customerDemographics)) ?>;

    // Initialize charts when the page is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initNewCustomersChart();
        initCustomerRetentionChart();
        initOrderFrequencyChart();
        initCustomerDemographicsChart();
        initDataTables();
    });

    // Initialize new customers chart
    function initNewCustomersChart() {
        const ctx = document.getElementById('newCustomersChart').getContext('2d');

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: newCustomersLabels,
                datasets: [{
                    label: 'New Customers',
                    backgroundColor: "rgba(78, 115, 223, 0.05)",
                    borderColor: "rgba(78, 115, 223, 1)",
                    pointRadius: 3,
                    pointBackgroundColor: "rgba(78, 115, 223, 1)",
                    pointBorderColor: "rgba(78, 115, 223, 1)",
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: "rgba(78, 115, 223, 1)",
                    pointHoverBorderColor: "rgba(78, 115, 223, 1)",
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    data: newCustomersData.map(item => item.count),
                    fill: true
                }]
            },
            options: {
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        left: 10,
                        right: 25,
                        top: 25,
                        bottom: 0
                    }
                },
                scales: {
                    xAxes: [{
                        time: {
                            unit: 'date'
                        },
                        gridLines: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            maxTicksLimit: 7
                        }
                    }],
                    yAxes: [{
                        ticks: {
                            maxTicksLimit: 5,
                            padding: 10,
                            beginAtZero: true,
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        },
                        gridLines: {
                            color: "rgb(234, 236, 244)",
                            zeroLineColor: "rgb(234, 236, 244)",
                            drawBorder: false,
                            borderDash: [2],
                            zeroLineBorderDash: [2]
                        }
                    }]
                },
                legend: {
                    display: false
                },
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    titleMarginBottom: 10,
                    titleFontColor: '#6e707e',
                    titleFontSize: 14,
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    intersect: false,
                    mode: 'index',
                    caretPadding: 10,
                    callbacks: {
                        label: function(tooltipItem, chart) {
                            return 'New Customers: ' + tooltipItem.yLabel.toLocaleString();
                        }
                    }
                }
            }
        });
    }

    // Initialize customer retention chart
    function initCustomerRetentionChart() {
        const ctx = document.getElementById('customerRetentionChart').getContext('2d');

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['One-time Customers', 'Returning Customers', 'Loyal Customers'],
                datasets: [{
                    data: customerRetentionData,
                    backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc'],
                    hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf'],
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const value = data.datasets[0].data[tooltipItem.index];
                            const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return data.labels[tooltipItem.index] + ': ' + value.toLocaleString() + ' (' + percentage + '%)';
                        }
                    }
                },
                legend: {
                    display: false
                },
                cutoutPercentage: 70,
            },
        });
    }

    // Initialize order frequency chart
    function initOrderFrequencyChart() {
        const ctx = document.getElementById('orderFrequencyChart').getContext('2d');

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: orderFrequencyLabels,
                datasets: [{
                    label: 'Number of Customers',
                    backgroundColor: "#4e73df",
                    hoverBackgroundColor: "#2e59d9",
                    borderColor: "#4e73df",
                    data: orderFrequencyData,
                }],
            },
            options: {
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        left: 10,
                        right: 25,
                        top: 25,
                        bottom: 0
                    }
                },
                scales: {
                    xAxes: [{
                        gridLines: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            maxTicksLimit: 6
                        }
                    }],
                    yAxes: [{
                        ticks: {
                            min: 0,
                            maxTicksLimit: 5,
                            padding: 10,
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        },
                        gridLines: {
                            color: "rgb(234, 236, 244)",
                            zeroLineColor: "rgb(234, 236, 244)",
                            drawBorder: false,
                            borderDash: [2],
                            zeroLineBorderDash: [2]
                        }
                    }],
                },
                legend: {
                    display: false
                },
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    titleMarginBottom: 10,
                    titleFontColor: '#6e707e',
                    titleFontSize: 14,
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                    callbacks: {
                        label: function(tooltipItem, chart) {
                            return 'Customers: ' + tooltipItem.yLabel.toLocaleString();
                        }
                    }
                }
            }
        });
    }

    // Initialize customer demographics chart
    function initCustomerDemographicsChart() {
        const ctx = document.getElementById('customerDemographicsChart').getContext('2d');

        // Generate random colors for each region
        const backgroundColors = demographicsLabels.map(() => {
            const r = Math.floor(Math.random() * 200);
            const g = Math.floor(Math.random() * 200);
            const b = Math.floor(Math.random() * 200);
            return `rgba(${r}, ${g}, ${b}, 0.8)`;
        });

        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: demographicsLabels,
                datasets: [{
                    data: demographicsData,
                    backgroundColor: backgroundColors,
                    hoverBackgroundColor: backgroundColors.map(color => color.replace('0.8', '1')),
                    hoverBorderColor: "rgba(234, 236, 244, 1)",
                }],
            },
            options: {
                maintainAspectRatio: false,
                tooltips: {
                    backgroundColor: "rgb(255,255,255)",
                    bodyFontColor: "#858796",
                    borderColor: '#dddfeb',
                    borderWidth: 1,
                    xPadding: 15,
                    yPadding: 15,
                    displayColors: false,
                    caretPadding: 10,
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const value = data.datasets[0].data[tooltipItem.index];
                            const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return data.labels[tooltipItem.index] + ': ' + value.toLocaleString() + ' (' + percentage + '%)';
                        }
                    }
                },
                legend: {
                    display: true,
                    position: 'bottom'
                },
                cutoutPercentage: 0,
            },
        });
    }

    // Initialize DataTables
    function initDataTables() {
        $('#topCustomersTable').DataTable({
            order: [[1, 'desc']],
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
            dom: 'Bfrtip',
            buttons: [
                'copy', 'csv', 'excel', 'pdf', 'print'
            ]
        });
    }
</script>

<?php
/**
 * Get total number of customers
 *
 * @param PDO $pdo PDO database connection
 * @return int Total number of customers
 */
function getTotalCustomers($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    return (int)$stmt->fetchColumn();
}

/**
 * Get number of active customers in the given period
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return int Number of active customers
 */
function getActiveCustomers($pdo, $startDate, $endDate) {
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT user_id)
        FROM orders
        WHERE DATE(created_at) BETWEEN ? AND ?
    ");
    $stmt->execute([$startDate, $endDate]);
    return (int)$stmt->fetchColumn();
}

/**
 * Get average number of orders per customer
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return float Average orders per customer
 */
function getAverageOrdersPerCustomer($pdo, $startDate, $endDate) {
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) / COUNT(DISTINCT user_id) as avg_orders
        FROM orders
        WHERE DATE(created_at) BETWEEN ? AND ?
    ");
    $stmt->execute([$startDate, $endDate]);
    return (float)$stmt->fetchColumn();
}

/**
 * Get new customer registrations by date
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return array New customer data by date
 */
function getNewCustomers($pdo, $startDate, $endDate) {
    $stmt = $pdo->prepare("
        SELECT
            DATE(created_at) as date,
            COUNT(*) as count
        FROM users
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY date
    ");
    $stmt->execute([$startDate, $endDate]);

    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fill in missing dates with zero values
    $filledData = [];
    $currentDate = new DateTime($startDate);
    $endDateTime = new DateTime($endDate);
    $interval = new DateInterval('P1D');

    // Create a map of dates to counts
    $dateMap = [];
    foreach ($result as $row) {
        $dateMap[$row['date']] = $row['count'];
    }

    // Fill in all dates in the range
    while ($currentDate <= $endDateTime) {
        $dateStr = $currentDate->format('Y-m-d');
        $filledData[] = [
            'date' => $dateStr,
            'count' => isset($dateMap[$dateStr]) ? (int)$dateMap[$dateStr] : 0
        ];
        $currentDate->add($interval);
    }

    return $filledData;
}

/**
 * Get top customers by order count and revenue
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @param int $limit Number of customers to return
 * @return array Top customers data
 */
function getTopCustomers($pdo, $startDate, $endDate, $limit = 10) {
    $stmt = $pdo->prepare("
        SELECT
            u.id,
            u.full_name,
            u.phone,
            u.email,
            u.profile_picture_url,
            COUNT(o.id) as order_count,
            SUM(o.total) as total_spent,
            AVG(o.total) as avg_order_value,
            MAX(o.created_at) as last_order
        FROM users u
        JOIN orders o ON u.id = o.user_id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        GROUP BY u.id, u.full_name, u.phone, u.email, u.profile_picture_url
        ORDER BY order_count DESC, total_spent DESC
        LIMIT ?
    ");
    $stmt->execute([$startDate, $endDate, $limit]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get customer retention metrics
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return array Customer retention data
 */
function getCustomerRetention($pdo, $startDate, $endDate) {
    // Get order counts per customer
    $stmt = $pdo->prepare("
        SELECT
            user_id,
            COUNT(*) as order_count
        FROM orders
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY user_id
    ");
    $stmt->execute([$startDate, $endDate]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Count customers by order frequency
    $oneTime = 0;
    $returning = 0;
    $loyal = 0;

    foreach ($results as $row) {
        if ($row['order_count'] == 1) {
            $oneTime++;
        } else if ($row['order_count'] >= 2 && $row['order_count'] <= 3) {
            $returning++;
        } else {
            $loyal++;
        }
    }

    return [
        'one_time' => $oneTime,
        'returning' => $returning,
        'loyal' => $loyal
    ];
}

/**
 * Get order frequency distribution
 *
 * @param PDO $pdo PDO database connection
 * @param string $startDate Start date in Y-m-d format
 * @param string $endDate End date in Y-m-d format
 * @return array Order frequency data
 */
function getOrderFrequency($pdo, $startDate, $endDate) {
    // Get order counts per customer
    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as order_count,
            COUNT(DISTINCT user_id) as customer_count
        FROM (
            SELECT
                user_id,
                COUNT(*) as orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY user_id
            HAVING COUNT(*) = 1
        ) as one_time
    ");
    $stmt->execute([$startDate, $endDate]);
    $oneTime = $stmt->fetch(PDO::FETCH_ASSOC);

    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as order_count,
            COUNT(DISTINCT user_id) as customer_count
        FROM (
            SELECT
                user_id,
                COUNT(*) as orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY user_id
            HAVING COUNT(*) = 2
        ) as two_times
    ");
    $stmt->execute([$startDate, $endDate]);
    $twoTimes = $stmt->fetch(PDO::FETCH_ASSOC);

    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as order_count,
            COUNT(DISTINCT user_id) as customer_count
        FROM (
            SELECT
                user_id,
                COUNT(*) as orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY user_id
            HAVING COUNT(*) = 3
        ) as three_times
    ");
    $stmt->execute([$startDate, $endDate]);
    $threeTimes = $stmt->fetch(PDO::FETCH_ASSOC);

    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as order_count,
            COUNT(DISTINCT user_id) as customer_count
        FROM (
            SELECT
                user_id,
                COUNT(*) as orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY user_id
            HAVING COUNT(*) >= 4 AND COUNT(*) <= 6
        ) as four_to_six
    ");
    $stmt->execute([$startDate, $endDate]);
    $fourToSix = $stmt->fetch(PDO::FETCH_ASSOC);

    $stmt = $pdo->prepare("
        SELECT
            COUNT(*) as order_count,
            COUNT(DISTINCT user_id) as customer_count
        FROM (
            SELECT
                user_id,
                COUNT(*) as orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY user_id
            HAVING COUNT(*) > 6
        ) as more_than_six
    ");
    $stmt->execute([$startDate, $endDate]);
    $moreThanSix = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        ['frequency' => '1 Order', 'count' => (int)$oneTime['customer_count']],
        ['frequency' => '2 Orders', 'count' => (int)$twoTimes['customer_count']],
        ['frequency' => '3 Orders', 'count' => (int)$threeTimes['customer_count']],
        ['frequency' => '4-6 Orders', 'count' => (int)$fourToSix['customer_count']],
        ['frequency' => '7+ Orders', 'count' => (int)$moreThanSix['customer_count']]
    ];
}

/**
 * Get customer demographics by region
 *
 * @param PDO $pdo PDO database connection
 * @return array Customer demographics data
 */
function getCustomerDemographics($pdo) {
    $stmt = $pdo->prepare("
        SELECT
            d.name as region,
            COUNT(u.id) as count
        FROM users u
        JOIN divisions d ON u.division_id = d.id
        GROUP BY d.id, d.name
        ORDER BY count DESC
    ");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // If no results (possibly because division data is missing), return placeholder data
    if (empty($results)) {
        return [
            ['region' => 'Dhaka', 'count' => 0],
            ['region' => 'Chittagong', 'count' => 0],
            ['region' => 'Rajshahi', 'count' => 0],
            ['region' => 'Khulna', 'count' => 0],
            ['region' => 'Other', 'count' => 0]
        ];
    }

    return $results;
}
?>
