<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="160dp"
    android:layout_height="200dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="12dp">

        <!-- Item Image Shimmer -->
        <View
            android:id="@+id/item_image_shimmer"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:background="@drawable/shimmer_background"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Item Name Shimmer -->
        <View
            android:id="@+id/item_name_shimmer"
            android:layout_width="0dp"
            android:layout_height="14dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/shimmer_background"
            app:layout_constraintTop_toBottomOf="@id/item_image_shimmer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Item Price Shimmer -->
        <View
            android:id="@+id/item_price_shimmer"
            android:layout_width="60dp"
            android:layout_height="12dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/shimmer_background"
            app:layout_constraintTop_toBottomOf="@id/item_name_shimmer"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Add to Cart Button Shimmer -->
        <View
            android:id="@+id/add_cart_button_shimmer"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/shimmer_button_background"
            app:layout_constraintTop_toBottomOf="@id/item_price_shimmer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
