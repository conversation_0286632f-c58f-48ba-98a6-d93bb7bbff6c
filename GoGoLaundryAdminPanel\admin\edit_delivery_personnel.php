<?php
/**
 * Edit Delivery Personnel Page
 * 
 * This page handles the editing of delivery personnel
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';

// Include authentication middleware
require_once 'auth.php';

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_personnel'])) {
    $personnelId = (int)$_POST['personnel_id'];
    $fullName = $_POST['full_name'];
    $phone = $_POST['phone'];
    $email = $_POST['email'] ?? null;
    $address = $_POST['address'] ?? null;
    $isActive = isset($_POST['is_active']) ? 1 : 0;
    
    // Validate input
    $errors = [];
    
    if (empty($fullName)) {
        $errors[] = 'Full name is required';
    }
    
    if (empty($phone)) {
        $errors[] = 'Phone number is required';
    } elseif (!preg_match('/^\+?[0-9]{10,15}$/', $phone)) {
        $errors[] = 'Invalid phone number format';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format';
    }
    
    // Check if phone number already exists for other personnel
    if (!empty($phone)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM delivery_personnel WHERE phone = ? AND id != ?");
        $stmt->execute([$phone, $personnelId]);
        if ($stmt->fetchColumn() > 0) {
            $errors[] = 'Phone number already exists';
        }
    }
    
    // Check if email already exists for other personnel
    if (!empty($email)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM delivery_personnel WHERE email = ? AND id != ?");
        $stmt->execute([$email, $personnelId]);
        if ($stmt->fetchColumn() > 0) {
            $errors[] = 'Email already exists';
        }
    }
    
    if (empty($errors)) {
        // Update delivery personnel
        $stmt = $pdo->prepare("
            UPDATE delivery_personnel
            SET full_name = ?, phone = ?, email = ?, address = ?, is_active = ?
            WHERE id = ?
        ");
        
        $result = $stmt->execute([$fullName, $phone, $email, $address, $isActive, $personnelId]);
        
        if ($result) {
            $_SESSION['success_message'] = 'Delivery personnel updated successfully';
        } else {
            $_SESSION['error_message'] = 'Failed to update delivery personnel';
        }
        
        // Redirect to delivery personnel page
        header("Location: delivery_personnel.php");
        exit;
    } else {
        $_SESSION['error_message'] = implode('<br>', $errors);
        
        // Redirect back to the form with error message
        header("Location: edit_delivery_personnel.php?id=$personnelId");
        exit;
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['id'])) {
    // Get delivery personnel ID from URL
    $personnelId = (int)$_GET['id'];
    
    // Get delivery personnel details
    $stmt = $pdo->prepare("SELECT * FROM delivery_personnel WHERE id = ?");
    $stmt->execute([$personnelId]);
    $personnel = $stmt->fetch();
    
    if (!$personnel) {
        $_SESSION['error_message'] = 'Delivery personnel not found';
        header("Location: delivery_personnel.php");
        exit;
    }
} else {
    // Redirect to delivery personnel page if no ID is provided
    header("Location: delivery_personnel.php");
    exit;
}

// Page title and breadcrumbs
$pageTitle = 'Edit Delivery Personnel';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Delivery Personnel', 'link' => 'delivery_personnel.php'],
    ['text' => 'Edit', 'link' => '']
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Edit Delivery Personnel</h1>
    
    <?php include 'includes/alerts.php'; ?>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Personnel Information</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="edit_delivery_personnel.php">
                        <input type="hidden" name="personnel_id" value="<?= $personnel['id'] ?>">
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" value="<?= htmlspecialchars($personnel['full_name']) ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone</label>
                            <input type="text" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars($personnel['phone']) ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email (Optional)</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?= htmlspecialchars($personnel['email'] ?? '') ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address (Optional)</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($personnel['address'] ?? '') ?></textarea>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?= $personnel['is_active'] ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_active">Active</label>
                        </div>
                        
                        <div class="mb-3">
                            <button type="submit" name="update_personnel" class="btn btn-primary">Update Personnel</button>
                            <a href="delivery_personnel.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Current Location</h6>
                </div>
                <div class="card-body">
                    <?php if ($personnel['current_location_lat'] && $personnel['current_location_lng']): ?>
                        <div id="map" style="height: 300px;"></div>
                        <div class="mt-3">
                            <p><strong>Last Updated:</strong> <?= date('M d, Y H:i', strtotime($personnel['last_location_update'])) ?></p>
                            <p><strong>Latitude:</strong> <?= $personnel['current_location_lat'] ?></p>
                            <p><strong>Longitude:</strong> <?= $personnel['current_location_lng'] ?></p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i> No location data available.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Assigned Orders</h6>
                </div>
                <div class="card-body">
                    <?php
                    // Get assigned orders
                    $stmt = $pdo->prepare("
                        SELECT id, order_number, status, created_at
                        FROM orders
                        WHERE delivery_personnel_id = ?
                        ORDER BY created_at DESC
                        LIMIT 5
                    ");
                    $stmt->execute([$personnelId]);
                    $assignedOrders = $stmt->fetchAll();
                    ?>
                    
                    <?php if (empty($assignedOrders)): ?>
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i> No orders assigned to this personnel.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($assignedOrders as $order): ?>
                                        <tr>
                                            <td><a href="order_details.php?id=<?= $order['id'] ?>"><?= $order['order_number'] ?></a></td>
                                            <td>
                                                <span class="badge bg-<?= getStatusBadgeClass($order['status']) ?>">
                                                    <?= formatStatus($order['status']) ?>
                                                </span>
                                            </td>
                                            <td><?= date('M d, Y', strtotime($order['created_at'])) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="orders.php?delivery_personnel_id=<?= $personnelId ?>" class="btn btn-sm btn-primary">View All Orders</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<?php if ($personnel['current_location_lat'] && $personnel['current_location_lng']): ?>
<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script>
<script>
    // Initialize map
    function initMap() {
        const position = {
            lat: <?= $personnel['current_location_lat'] ?>,
            lng: <?= $personnel['current_location_lng'] ?>
        };
        
        // Create map
        const map = new google.maps.Map(document.getElementById("map"), {
            zoom: 15,
            center: position,
        });
        
        // Create marker
        const marker = new google.maps.Marker({
            position: position,
            map: map,
            title: "<?= htmlspecialchars($personnel['full_name']) ?>'s Location"
        });
    }
</script>
<?php endif; ?>

<?php
/**
 * Get status badge class
 * 
 * @param string $status Order status
 * @return string Badge class
 */
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'placed': return 'secondary';
        case 'confirmed': return 'info';
        case 'pickup_scheduled': return 'primary';
        case 'picked_up': return 'warning';
        case 'processing': return 'warning';
        case 'ready_for_delivery': return 'info';
        case 'out_for_delivery': return 'primary';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

/**
 * Format status
 * 
 * @param string $status Order status
 * @return string Formatted status
 */
function formatStatus($status) {
    return ucwords(str_replace('_', ' ', $status));
}
?>
