package com.mdsadrulhasan.gogolaundry.api;

import com.mdsadrulhasan.gogolaundry.data.model.PromoDialogResponse;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;

/**
 * API service for promotional dialog operations
 */
public interface PromoDialogApiService {
    
    /**
     * Get active promotional dialog
     * @return Call with PromoDialogResponse containing dialog data or null if no active dialog
     */
    @GET("promotional_dialog.php")
    Call<PromoDialogResponse> getActivePromoDialog();
    
    /**
     * Log promotional dialog view
     * @param request Request body containing dialog_id, user_id, and action
     * @return Call with response status
     */
    @POST("promotional_dialog.php")
    Call<PromoDialogResponse> logPromoDialogInteraction(@Body PromoDialogInteractionRequest request);
    
    /**
     * Request class for logging promotional dialog interactions
     */
    class PromoDialogInteractionRequest {
        private String action;
        private int dialog_id;
        private Integer user_id;
        
        public PromoDialogInteractionRequest(String action, int dialogId, Integer userId) {
            this.action = action;
            this.dialog_id = dialogId;
            this.user_id = userId;
        }
        
        // Getters and setters
        public String getAction() {
            return action;
        }
        
        public void setAction(String action) {
            this.action = action;
        }
        
        public int getDialog_id() {
            return dialog_id;
        }
        
        public void setDialog_id(int dialog_id) {
            this.dialog_id = dialog_id;
        }
        
        public Integer getUser_id() {
            return user_id;
        }
        
        public void setUser_id(Integer user_id) {
            this.user_id = user_id;
        }
    }
}
