package com.mdsadrulhasan.gogolaundry.adapter;

import android.content.Context;
import android.text.format.DateUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.bumptech.glide.request.RequestOptions;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Notification;

import java.util.ArrayList;
import java.util.List;

/**
 * Adapter for notifications RecyclerView
 */
public class NotificationAdapter extends RecyclerView.Adapter<NotificationAdapter.NotificationViewHolder> {
    private final Context context;
    private List<Notification> notifications;
    private final NotificationClickListener listener;

    /**
     * Interface for notification click events
     */
    public interface NotificationClickListener {
        void onNotificationClicked(Notification notification);
        void onMarkAsReadClicked(Notification notification);
        void onNotificationImageClicked(Notification notification);
    }

    /**
     * Constructor
     *
     * @param context Context
     * @param listener Click listener
     */
    public NotificationAdapter(Context context, NotificationClickListener listener) {
        this.context = context;
        this.notifications = new ArrayList<>();
        this.listener = listener;
    }

    @NonNull
    @Override
    public NotificationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_notification, parent, false);
        return new NotificationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull NotificationViewHolder holder, int position) {
        Notification notification = notifications.get(position);
        holder.bind(notification);
    }

    @Override
    public int getItemCount() {
        return notifications.size();
    }

    /**
     * Update notifications list
     *
     * @param notifications New notifications list
     */
    public void updateNotifications(List<Notification> notifications) {
        this.notifications = notifications;
        notifyDataSetChanged();
    }

    /**
     * Update notifications list with filtering options
     *
     * @param notifications New notifications list
     * @param showReadNotifications Whether to show read notifications (ignored - always filters out read notifications)
     */
    public void updateNotifications(List<Notification> notifications, boolean showReadNotifications) {
        // Always filter out read notifications regardless of preference
        this.notifications = notifications.stream()
                .filter(notification -> !notification.isRead())
                .collect(java.util.stream.Collectors.toList());

        Log.d("NotificationAdapter", "Updated notifications: " + this.notifications.size() +
              " unread out of " + notifications.size() + " total");
        notifyDataSetChanged();
    }

    /**
     * Get notification at position
     *
     * @param position Position in the adapter
     * @return Notification at position or null if invalid position
     */
    public Notification getNotificationAt(int position) {
        if (position >= 0 && position < notifications.size()) {
            return notifications.get(position);
        }
        return null;
    }

    /**
     * ViewHolder for notification items
     */
    class NotificationViewHolder extends RecyclerView.ViewHolder {
        private final CardView cardView;
        private final TextView titleTextView;
        private final TextView messageTextView;
        private final TextView timeTextView;
        private final TextView typeTextView;
        private final ImageView iconImageView;
        private final ImageView notificationImageView;
        private final ImageView readStatusImageView;

        NotificationViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.notification_card);
            titleTextView = itemView.findViewById(R.id.notification_title);
            messageTextView = itemView.findViewById(R.id.notification_message);
            timeTextView = itemView.findViewById(R.id.notification_time);
            typeTextView = itemView.findViewById(R.id.notification_type);
            iconImageView = itemView.findViewById(R.id.notification_icon);
            notificationImageView = itemView.findViewById(R.id.notification_image);
            readStatusImageView = itemView.findViewById(R.id.notification_read_status);
        }

        void bind(Notification notification) {
            Log.d("NotificationAdapter", "Binding notification: " + notification.toString());
            titleTextView.setText(notification.getTitle());
            messageTextView.setText(notification.getMessage());

            // Format time as relative time span (e.g., "2 hours ago")
            if (notification.getCreatedAt() != null) {
                long timeMillis = notification.getCreatedAt().getTime();
                String relativeTime = DateUtils.getRelativeTimeSpanString(
                        timeMillis,
                        System.currentTimeMillis(),
                        DateUtils.MINUTE_IN_MILLIS,
                        DateUtils.FORMAT_ABBREV_RELATIVE
                ).toString();
                timeTextView.setText(relativeTime);
            } else {
                timeTextView.setText("");
            }

            // Set notification type
            typeTextView.setText(notification.getTypeDisplayName());

            // Handle notification image
            String imageUrl = notification.getImageUrl();
            Log.d("NotificationAdapter", "Image URL from notification: '" + imageUrl + "'");

            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                Log.d("NotificationAdapter", "Loading image: " + imageUrl);
                notificationImageView.setVisibility(View.VISIBLE);

                // Load image with Glide
                Glide.with(context)
                        .load(imageUrl)
                        .apply(new RequestOptions()
                                .placeholder(R.drawable.ic_notification)
                                .error(R.drawable.ic_notification)
                                .transform(new RoundedCorners(16)))
                        .listener(new com.bumptech.glide.request.RequestListener<android.graphics.drawable.Drawable>() {
                            @Override
                            public boolean onLoadFailed(@androidx.annotation.Nullable com.bumptech.glide.load.engine.GlideException e, Object model, com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target, boolean isFirstResource) {
                                Log.e("NotificationAdapter", "Failed to load image: " + imageUrl + " - " + (e != null ? e.getMessage() : "Unknown error"));
                                return false;
                            }

                            @Override
                            public boolean onResourceReady(android.graphics.drawable.Drawable resource, Object model, com.bumptech.glide.request.target.Target<android.graphics.drawable.Drawable> target, com.bumptech.glide.load.DataSource dataSource, boolean isFirstResource) {
                                Log.d("NotificationAdapter", "Image loaded successfully: " + imageUrl);
                                return false;
                            }
                        })
                        .into(notificationImageView);
            } else {
                Log.d("NotificationAdapter", "No image URL, hiding image view");
                notificationImageView.setVisibility(View.GONE);
            }

            // Set icon based on notification type
            switch (notification.getType()) {
                case "order_status":
                    iconImageView.setImageResource(R.drawable.ic_receipt);
                    break;
                case "promo":
                    iconImageView.setImageResource(R.drawable.ic_add_shopping_cart);
                    break;
                case "system":
                    iconImageView.setImageResource(R.drawable.ic_info);
                    break;
                default:
                    iconImageView.setImageResource(R.drawable.ic_notification);
                    break;
            }

            // Set read status
            if (notification.isRead()) {
                readStatusImageView.setVisibility(View.GONE);
                cardView.setCardBackgroundColor(ContextCompat.getColor(context, R.color.card_background));
            } else {
                readStatusImageView.setVisibility(View.VISIBLE);
                cardView.setCardBackgroundColor(ContextCompat.getColor(context, R.color.primary_light));
            }

            // Set click listeners
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onNotificationClicked(notification);
                }
            });

            readStatusImageView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onMarkAsReadClicked(notification);
                }
            });

            // Set image click listener for full screen view
            notificationImageView.setOnClickListener(v -> {
                if (listener != null && imageUrl != null && !imageUrl.trim().isEmpty()) {
                    listener.onNotificationImageClicked(notification);
                }
            });
        }
    }
}
