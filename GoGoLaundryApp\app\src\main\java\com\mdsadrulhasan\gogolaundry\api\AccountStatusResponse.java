package com.mdsadrulhasan.gogolaundry.api;

import com.google.gson.annotations.SerializedName;

/**
 * Response model for account status verification
 */
public class AccountStatusResponse {
    @SerializedName("exists")
    private boolean exists;

    @SerializedName("is_verified")
    private boolean isVerified;

    @SerializedName("is_deleted")
    private boolean isDeleted;

    public boolean exists() {
        return exists;
    }

    public void setExists(boolean exists) {
        this.exists = exists;
    }

    public boolean isVerified() {
        return isVerified;
    }

    public void setVerified(boolean verified) {
        isVerified = verified;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }
}
