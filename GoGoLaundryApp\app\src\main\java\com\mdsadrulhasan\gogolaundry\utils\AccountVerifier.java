package com.mdsadrulhasan.gogolaundry.utils;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;


import com.mdsadrulhasan.gogolaundry.LoginActivity;
import com.mdsadrulhasan.gogolaundry.api.AccountStatusResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.model.User;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Utility class for verifying user account status
 */
public class AccountVerifier {
    private static final String TAG = "AccountVerifier";

    /**
     * Verify user account status
     *
     * @param context Context
     * @param sessionManager Session manager
     * @param callback Callback to be called when verification is complete
     */
    public static void verifyAccountStatus(Context context, SessionManager sessionManager, VerificationCallback callback) {
        // Check if user is logged in
        if (!sessionManager.isLoggedIn()) {
            Log.d(TAG, "User is not logged in, skipping verification");
            if (callback != null) {
                callback.onVerificationComplete(true);
            }
            return;
        }

        // Get current user
        User user = sessionManager.getUser();
        if (user == null) {
            Log.e(TAG, "User data is null, logging out");
            logoutUser(context, sessionManager, "Your session has expired. Please log in again.");
            if (callback != null) {
                callback.onVerificationComplete(false);
            }
            return;
        }

        // Get API service
        ApiService apiService = ApiClient.getApiService(context);

        // Make API call to verify account status
        apiService.verifyAccountStatus(user.getId(), user.getPhone()).enqueue(new Callback<ApiResponse<AccountStatusResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<AccountStatusResponse>> call, Response<ApiResponse<AccountStatusResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<AccountStatusResponse> apiResponse = response.body();
                    
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        AccountStatusResponse statusResponse = apiResponse.getData();
                        
                        // Check if account exists
                        if (!statusResponse.exists()) {
                            Log.e(TAG, "Account does not exist, logging out");
                            logoutUser(context, sessionManager, "Your account no longer exists. Please contact support.");
                            if (callback != null) {
                                callback.onVerificationComplete(false);
                            }
                            return;
                        }
                        
                        // Check if account is deleted
                        if (statusResponse.isDeleted()) {
                            Log.e(TAG, "Account is deleted, logging out");
                            logoutUser(context, sessionManager, "Your account has been deleted. Please contact support if this was not intended.");
                            if (callback != null) {
                                callback.onVerificationComplete(false);
                            }
                            return;
                        }
                        
                        // Check if account is verified
                        if (!statusResponse.isVerified()) {
                            Log.e(TAG, "Account is not verified, logging out");
                            logoutUser(context, sessionManager, "Your account has been marked as unverified. Please log in again to verify your account.");
                            if (callback != null) {
                                callback.onVerificationComplete(false);
                            }
                            return;
                        }
                        
                        // Account is valid
                        Log.d(TAG, "Account verification successful");
                        if (callback != null) {
                            callback.onVerificationComplete(true);
                        }
                    } else {
                        // API returned an error
                        Log.e(TAG, "API error: " + apiResponse.getMessage());
                        // Don't log out for API errors, just notify the callback
                        if (callback != null) {
                            callback.onVerificationComplete(true);
                        }
                    }
                } else {
                    // Network error
                    Log.e(TAG, "Network error: " + (response.errorBody() != null ? response.errorBody().toString() : "Unknown error"));
                    // Don't log out for network errors, just notify the callback
                    if (callback != null) {
                        callback.onVerificationComplete(true);
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<AccountStatusResponse>> call, Throwable t) {
                // Network failure
                Log.e(TAG, "Network failure: " + t.getMessage());
                // Don't log out for network failures, just notify the callback
                if (callback != null) {
                    callback.onVerificationComplete(true);
                }
            }
        });
    }

    /**
     * Log out user and redirect to login screen
     *
     * @param context Context
     * @param sessionManager Session manager
     * @param message Message to display to user
     */
    private static void logoutUser(Context context, SessionManager sessionManager, String message) {
        // Clear session data
        sessionManager.logout();
        
        // Reset API client
        ApiClient.resetApiClient();
        
        // Show message to user
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
        
        // Redirect to login activity
        Intent intent = new Intent(context, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        intent.putExtra("logout_message", message);
        context.startActivity(intent);
    }

    /**
     * Callback interface for account verification
     */
    public interface VerificationCallback {
        /**
         * Called when verification is complete
         *
         * @param isValid True if account is valid, false otherwise
         */
        void onVerificationComplete(boolean isValid);
    }
}
