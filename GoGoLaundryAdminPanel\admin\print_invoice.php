<?php
/**
 * Print Invoice Page
 *
 * This page generates a printable invoice for an order
 */

// Include authentication middleware
require_once 'auth.php';

// Include required files
require_once '../includes/OrderManager.php';
require_once '../includes/SettingsManager.php';

// Initialize managers
$orderManager = new OrderManager($pdo);
$settingsManager = new SettingsManager($pdo);

// Check if order ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error_message'] = 'Invalid order ID provided.';
    header('Location: orders.php');
    exit;
}

$orderId = (int)$_GET['id'];

// Get order details
$order = $orderManager->getOrderById($orderId);

if (!$order) {
    $_SESSION['error_message'] = 'Order not found.';
    header('Location: orders.php');
    exit;
}

// Get order items
$orderItems = $orderManager->getOrderItems($orderId);

// Get company settings
$companyName = $settingsManager->getSetting('company_name', 'GoGoLaundry');
$companyAddress = $settingsManager->getSetting('company_address', 'Dhaka, Bangladesh');
$companyPhone = $settingsManager->getSetting('company_phone', '+880 1234567890');
$companyEmail = $settingsManager->getSetting('company_email', '<EMAIL>');

// Helper functions
function formatStatus($status) {
    return ucwords(str_replace('_', ' ', $status));
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - Order #<?php echo htmlspecialchars($order['order_number']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
        }
        
        .invoice-body {
            padding: 30px;
        }
        
        .company-logo {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            font-size: 1.5rem;
            margin-bottom: 0;
        }
        
        .invoice-number {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .section-title {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .info-table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .info-table td {
            border: none;
            border-bottom: 1px solid #dee2e6;
        }
        
        .items-table th {
            background-color: #007bff;
            color: white;
            border: none;
        }
        
        .items-table td {
            border: none;
            border-bottom: 1px solid #dee2e6;
        }
        
        .total-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .total-row {
            font-size: 1.1rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        @media print {
            body {
                background-color: white;
            }
            
            .invoice-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
            
            .print-button {
                display: none;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
        }
        
        .status-delivered { background-color: #d4edda; color: #155724; }
        .status-processing { background-color: #fff3cd; color: #856404; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        .status-default { background-color: #e2e3e5; color: #383d41; }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="btn btn-primary print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> Print Invoice
    </button>
    
    <div class="invoice-container">
        <!-- Invoice Header -->
        <div class="invoice-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="company-logo"><?php echo htmlspecialchars($companyName); ?></div>
                    <div><?php echo htmlspecialchars($companyAddress); ?></div>
                    <div><?php echo htmlspecialchars($companyPhone); ?></div>
                    <div><?php echo htmlspecialchars($companyEmail); ?></div>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="invoice-title">INVOICE</div>
                    <div class="invoice-number">#<?php echo htmlspecialchars($order['order_number']); ?></div>
                    <div class="mt-2">
                        <span class="status-badge status-<?php echo $order['status']; ?>">
                            <?php echo formatStatus($order['status']); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Invoice Body -->
        <div class="invoice-body">
            <!-- Order Information -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h5 class="section-title">Bill To</h5>
                    <table class="table info-table">
                        <tr>
                            <th width="40%">Customer:</th>
                            <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                        </tr>
                        <tr>
                            <th>Phone:</th>
                            <td><?php echo htmlspecialchars($order['customer_phone']); ?></td>
                        </tr>
                        <tr>
                            <th>Pickup Address:</th>
                            <td><?php echo htmlspecialchars($order['pickup_address']); ?></td>
                        </tr>
                        <tr>
                            <th>Delivery Address:</th>
                            <td><?php echo htmlspecialchars($order['delivery_address']); ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h5 class="section-title">Order Details</h5>
                    <table class="table info-table">
                        <tr>
                            <th width="40%">Order Number:</th>
                            <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                        </tr>
                        <tr>
                            <th>Tracking Number:</th>
                            <td><?php echo htmlspecialchars($order['tracking_number']); ?></td>
                        </tr>
                        <tr>
                            <th>Order Date:</th>
                            <td><?php echo date('F j, Y', strtotime($order['created_at'])); ?></td>
                        </tr>
                        <tr>
                            <th>Pickup Date:</th>
                            <td><?php echo date('F j, Y', strtotime($order['pickup_date'])); ?></td>
                        </tr>
                        <tr>
                            <th>Payment Method:</th>
                            <td><?php echo ucfirst($order['payment_method']); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Order Items -->
            <h5 class="section-title">Order Items</h5>
            <div class="table-responsive">
                <table class="table items-table">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Service</th>
                            <th class="text-center">Quantity</th>
                            <th class="text-end">Unit Price</th>
                            <th class="text-end">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($orderItems)): ?>
                            <tr>
                                <td colspan="5" class="text-center">No items found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($orderItems as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                    <td><?php echo htmlspecialchars($item['service_name']); ?></td>
                                    <td class="text-center"><?php echo $item['quantity']; ?></td>
                                    <td class="text-end">৳<?php echo number_format($item['price'], 2); ?></td>
                                    <td class="text-end">৳<?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Total Section -->
            <div class="row">
                <div class="col-md-6 offset-md-6">
                    <div class="total-section">
                        <table class="table table-borderless">
                            <tr>
                                <th>Subtotal:</th>
                                <td class="text-end">৳<?php echo number_format($order['subtotal'], 2); ?></td>
                            </tr>
                            <?php if ($order['discount'] > 0): ?>
                            <tr>
                                <th>Discount:</th>
                                <td class="text-end text-success">-৳<?php echo number_format($order['discount'], 2); ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <th>Delivery Fee:</th>
                                <td class="text-end">৳<?php echo number_format($order['delivery_fee'], 2); ?></td>
                            </tr>
                            <tr class="total-row">
                                <th>Total Amount:</th>
                                <td class="text-end">৳<?php echo number_format($order['total'], 2); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <p class="text-muted">Thank you for choosing <?php echo htmlspecialchars($companyName); ?>!</p>
                    <p class="text-muted small">This is a computer-generated invoice and does not require a signature.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Close window after printing
        window.onafterprint = function() {
            // Uncomment the line below if you want to close the window after printing
            // window.close();
        }
    </script>
</body>
</html>
