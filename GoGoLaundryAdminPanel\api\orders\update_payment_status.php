<?php
/**
 * Update Payment Status API Endpoint
 *
 * This endpoint allows admins to update payment status for orders
 */

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/OrderManager.php';
require_once '../../includes/AdminManager.php';

// Set content type
header('Content-Type: application/json');

// Handle CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Method not allowed', [], 405);
}

// Initialize managers
$orderManager = new OrderManager($pdo);
$adminManager = new AdminManager($pdo);

try {
    // Get input data
    $inputData = json_decode(file_get_contents('php://input'), true);
    
    // Validate required parameters
    if (!isset($inputData['order_id']) || !isset($inputData['payment_status'])) {
        jsonResponse(false, 'Missing required parameters: order_id and payment_status', [], 400);
    }

    $orderId = (int)$inputData['order_id'];
    $paymentStatus = $inputData['payment_status'];
    $adminNotes = isset($inputData['admin_notes']) ? $inputData['admin_notes'] : '';
    $updatedBy = isset($inputData['updated_by']) ? (int)$inputData['updated_by'] : null;

    // Validate payment status
    $validStatuses = ['pending', 'paid', 'failed', 'refunded'];
    if (!in_array($paymentStatus, $validStatuses)) {
        jsonResponse(false, 'Invalid payment status. Valid statuses: ' . implode(', ', $validStatuses), [], 400);
    }

    // Verify order exists
    $order = $orderManager->getOrderById($orderId);
    if (!$order) {
        jsonResponse(false, 'Order not found', [], 404);
    }

    // Log the payment status update attempt
    error_log("Attempting to update payment status for Order ID: $orderId");
    error_log("Current payment status: " . $order['payment_status']);
    error_log("New payment status: $paymentStatus");
    error_log("Admin notes: $adminNotes");

    // Start transaction
    $pdo->beginTransaction();

    // Update payment status
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET payment_status = ?, updated_at = NOW() 
        WHERE id = ?
    ");
    
    $result = $stmt->execute([$paymentStatus, $orderId]);

    if (!$result) {
        $pdo->rollBack();
        error_log("Failed to update payment status for order ID: $orderId");
        jsonResponse(false, 'Failed to update payment status', [], 500);
    }

    // Insert payment status history record
    $stmt = $pdo->prepare("
        INSERT INTO order_status_history (
            order_id, status, notes, updated_by, updated_by_type, created_at
        ) VALUES (?, ?, ?, ?, ?, NOW())
    ");
    
    $historyNotes = "Payment status updated to: $paymentStatus";
    if (!empty($adminNotes)) {
        $historyNotes .= " - Admin notes: $adminNotes";
    }
    
    $stmt->execute([
        $orderId,
        'payment_' . $paymentStatus, // Prefix to distinguish from order status
        $historyNotes,
        $updatedBy,
        'admin'
    ]);

    // Commit transaction
    $pdo->commit();

    // Log successful update
    error_log("Payment status updated successfully for Order ID: $orderId to $paymentStatus");

    // Get updated order data
    $updatedOrder = $orderManager->getOrderById($orderId);

    // Prepare response
    $responseData = [
        'order_id' => $orderId,
        'old_payment_status' => $order['payment_status'],
        'new_payment_status' => $paymentStatus,
        'updated_at' => date('Y-m-d H:i:s'),
        'order' => $updatedOrder
    ];

    jsonResponse(true, 'Payment status updated successfully', $responseData);

} catch (PDOException $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log('Database Error in update_payment_status.php: ' . $e->getMessage());
    error_log('Stack Trace: ' . $e->getTraceAsString());
    
    jsonResponse(false, 'Database error occurred', [], 500);
} catch (Exception $e) {
    // Rollback transaction if active
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log('General Error in update_payment_status.php: ' . $e->getMessage());
    error_log('Stack Trace: ' . $e->getTraceAsString());
    
    jsonResponse(false, 'An error occurred while updating payment status', [], 500);
}
?>
