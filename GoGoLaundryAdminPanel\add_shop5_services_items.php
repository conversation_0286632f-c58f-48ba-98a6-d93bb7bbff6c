<?php
/**
 * Add Services and Items for Test Shop 2 (Shop ID 5)
 * This script will add services and items to shop_id = 5 (Test Shop 2)
 */

require_once 'config/config.php';
require_once 'config/db.php';

echo "<h2>🏪 Adding Services and Items for Test Shop 2 (Shop ID 5)</h2>";

try {
    // First, check if Test Shop 2 (ID 5) exists
    $shopStmt = $pdo->prepare("SELECT id, name FROM laundry_shops WHERE id = 5");
    $shopStmt->execute();
    $shop = $shopStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$shop) {
        echo "<p style='color: red;'>❌ Test Shop 2 (ID 5) not found!</p>";
        exit;
    }
    
    echo "<p>✅ Found shop: <strong>{$shop['name']}</strong> (ID: {$shop['id']})</p>";
    
    // Check existing services
    $servicesStmt = $pdo->query("SELECT id, name FROM services WHERE is_active = 1");
    $services = $servicesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📋 Available Services:</h3>";
    foreach ($services as $service) {
        echo "<p>- {$service['name']} (ID: {$service['id']})</p>";
    }
    
    // Check existing items
    $itemsStmt = $pdo->query("SELECT id, name, service_id FROM items WHERE is_active = 1 LIMIT 10");
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>🛍️ Available Items (first 10):</h3>";
    foreach ($items as $item) {
        echo "<p>- {$item['name']} (ID: {$item['id']}, Service: {$item['service_id']})</p>";
    }
    
    // Add services to shop
    echo "<h3>🔧 Adding Services to Test Shop 2 (ID 5):</h3>";
    
    $shopServices = [
        ['service_id' => 7, 'estimated_hours' => 24], // Wash & Fold
        ['service_id' => 8, 'estimated_hours' => 36], // Wash & Iron
        ['service_id' => 9, 'estimated_hours' => 48], // Dry Cleaning  
        ['service_id' => 10, 'estimated_hours' => 12], // Iron Only
        ['service_id' => 11, 'estimated_hours' => 6], // Express Service
    ];
    
    $shopServiceStmt = $pdo->prepare("
        INSERT IGNORE INTO shop_services (shop_id, service_id, is_available, estimated_hours)
        VALUES (5, ?, 1, ?)
    ");
    
    foreach ($shopServices as $shopService) {
        $result = $shopServiceStmt->execute([$shopService['service_id'], $shopService['estimated_hours']]);
        if ($result) {
            $serviceName = '';
            foreach ($services as $service) {
                if ($service['id'] == $shopService['service_id']) {
                    $serviceName = $service['name'];
                    break;
                }
            }
            echo "<p>✅ Added service: <strong>$serviceName</strong> ({$shopService['estimated_hours']} hours)</p>";
        }
    }
    
    // Add items to shop
    echo "<h3>🛍️ Adding Items to Test Shop 2 (ID 5):</h3>";
    
    $shopItems = [
        ['item_id' => 3, 'custom_price' => 40.00, 'estimated_hours' => 24], // T-Shirt
        ['item_id' => 4, 'custom_price' => 50.00, 'estimated_hours' => 24], // Jeans
        ['item_id' => 5, 'custom_price' => 70.00, 'estimated_hours' => 24], // Bed Sheet
        ['item_id' => 6, 'custom_price' => 300.00, 'estimated_hours' => 48], // Pillow Cover
        ['item_id' => 7, 'custom_price' => 30.00, 'estimated_hours' => 12], // Towel
    ];
    
    $shopItemStmt = $pdo->prepare("
        INSERT IGNORE INTO shop_items (shop_id, item_id, custom_price, is_available, estimated_hours)
        VALUES (5, ?, ?, 1, ?)
    ");
    
    foreach ($shopItems as $shopItem) {
        $result = $shopItemStmt->execute([
            $shopItem['item_id'], 
            $shopItem['custom_price'], 
            $shopItem['estimated_hours']
        ]);
        
        if ($result) {
            $itemName = '';
            foreach ($items as $item) {
                if ($item['id'] == $shopItem['item_id']) {
                    $itemName = $item['name'];
                    break;
                }
            }
            echo "<p>✅ Added item: <strong>$itemName</strong> (৳{$shopItem['custom_price']}, {$shopItem['estimated_hours']} hours)</p>";
        }
    }
    
    echo "<h3>🎉 Success!</h3>";
    echo "<p>✅ Services and items have been added to Test Shop 2 (ID 5)!</p>";
    echo "<p>📱 Now test the mobile app by clicking on Test Shop 2 marker.</p>";
    
    // Show final counts
    $serviceCountStmt = $pdo->prepare("SELECT COUNT(*) FROM shop_services WHERE shop_id = 5 AND is_available = 1");
    $serviceCountStmt->execute();
    $serviceCount = $serviceCountStmt->fetchColumn();
    
    $itemCountStmt = $pdo->prepare("SELECT COUNT(*) FROM shop_items WHERE shop_id = 5 AND is_available = 1");
    $itemCountStmt->execute();
    $itemCount = $itemCountStmt->fetchColumn();
    
    echo "<p><strong>📊 Final Count:</strong></p>";
    echo "<p>- Services: $serviceCount</p>";
    echo "<p>- Items: $itemCount</p>";
    
    // Test API call
    echo "<h3>🔍 Testing API Call:</h3>";
    $apiUrl = "http://192.168.0.106/GoGoLaundry/GoGoLaundryAdminPanel/api/shops/details.php?shop_id=5&include_services=true&include_items=true";
    echo "<p><a href='$apiUrl' target='_blank'>📡 Test API Call for Shop ID 5</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #2c3e50; }
h3 { color: #34495e; margin-top: 20px; }
p { margin: 5px 0; }
a { color: #3498db; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
