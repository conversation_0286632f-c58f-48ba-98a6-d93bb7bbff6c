<?php
/**
 * API endpoint to create a notification if it doesn't exist
 *
 * Required parameters:
 * - notification_id: ID of the notification to create
 * - user_id: ID of the user
 * - title: Notification title
 * - message: Notification message
 * - type: Notification type (order_status, promo, system, custom)
 * - is_read: Whether the notification is read (0 or 1)
 *
 * Returns:
 * - success: true/false
 * - message: Success/error message
 * - data: Object with notification details
 */

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Include required files
require_once '../../config/config.php';
require_once '../../config/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/NotificationManager.php';
require_once '../../includes/UserManager.php';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit();
}

// Check if required parameters are provided
if (!isset($_POST['notification_id']) || empty($_POST['notification_id']) ||
    !isset($_POST['user_id']) || empty($_POST['user_id']) ||
    !isset($_POST['title']) || empty($_POST['title']) ||
    !isset($_POST['message']) || empty($_POST['message']) ||
    !isset($_POST['type']) || empty($_POST['type'])) {
    $response['message'] = 'Required parameters are missing';
    echo json_encode($response);
    exit();
}

// Get parameters
$notification_id = intval($_POST['notification_id']);
$user_id = intval($_POST['user_id']);
$title = $_POST['title'];
$message = $_POST['message'];
$type = $_POST['type'];
$is_read = isset($_POST['is_read']) ? intval($_POST['is_read']) : 0;
$order_id = isset($_POST['order_id']) && !empty(trim($_POST['order_id'])) ? intval($_POST['order_id']) : null;
$image_url = isset($_POST['image_url']) ? $_POST['image_url'] : null;

try {
    // Initialize managers
    $userManager = new UserManager($pdo);
    $notificationManager = new NotificationManager($pdo);

    // Validate user exists
    $user = $userManager->getUserById($user_id);
    if (!$user) {
        $response['message'] = 'User not found';
        echo json_encode($response);
        exit();
    }

    // Check if notification exists
    $stmt = $pdo->prepare("
        SELECT id
        FROM notifications
        WHERE id = ?
    ");
    $stmt->execute([$notification_id]);

    if ($stmt->rowCount() > 0) {
        // Notification already exists, update it
        $stmt = $pdo->prepare("
            UPDATE notifications
            SET user_id = ?, order_id = ?, title = ?, message = ?, type = ?, image_url = ?, is_read = ?, fcm_sent = ?, sms_sent = ?
            WHERE id = ?
        ");
        $stmt->execute([$user_id, $order_id, $title, $message, $type, $image_url, $is_read, 0, 0, $notification_id]);

        $response['success'] = true;
        $response['message'] = 'Notification updated successfully';
    } else {
        // Notification doesn't exist, create it
        $stmt = $pdo->prepare("
            INSERT INTO notifications (id, user_id, order_id, title, message, type, image_url, is_read, fcm_sent, sms_sent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$notification_id, $user_id, $order_id, $title, $message, $type, $image_url, $is_read, 0, 0]);

        $response['success'] = true;
        $response['message'] = 'Notification created successfully';
    }

    // Get the notification details
    $stmt = $pdo->prepare("
        SELECT id, user_id, order_id, title, message, type, image_url, is_read, created_at
        FROM notifications
        WHERE id = ?
    ");
    $stmt->execute([$notification_id]);

    if ($stmt->rowCount() > 0) {
        $notification = $stmt->fetch(PDO::FETCH_ASSOC);
        $response['data'] = $notification;
    }
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    // Log the error for server-side debugging
    error_log('Notification API Error: ' . $e->getMessage());
}

// Return response
echo json_encode($response);
