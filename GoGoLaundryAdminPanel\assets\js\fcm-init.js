/**
 * FCM Initialization for GoGoLaundry Admin Panel
 */

// Check if the browser supports service workers and notifications
if ('serviceWorker' in navigator && 'Notification' in window) {
    // Register service worker
    navigator.serviceWorker.register('/GoGoLaundry/GoGoLaundryAdminPanel/firebase-messaging-sw.js')
        .then((registration) => {
            console.log('Service Worker registered successfully:', registration);

            // Initialize FCM after service worker is ready
            initializeFCM();
        })
        .catch((error) => {
            console.error('Service Worker registration failed:', error);
        });
} else {
    console.warn('This browser does not support service workers or notifications');
}

/**
 * Initialize Firebase Cloud Messaging
 */
async function initializeFCM() {
    try {
        // Check if Firebase is loaded
        if (typeof firebase === 'undefined') {
            console.error('Firebase is not loaded');
            return;
        }

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyASl3UlvsWsfylrHNHLWUOxc2Lcln6PI0g",
            authDomain: "gogolaundry-c4dd1.firebaseapp.com",
            projectId: "gogolaundry-c4dd1",
            storageBucket: "gogolaundry-c4dd1.firebasestorage.app",
            messagingSenderId: "523301621504",
            appId: "1:523301621504:android:59b7b8816b0e2b7604ede5"
        };

        // Initialize Firebase if not already initialized
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        // Initialize Firebase Messaging
        const messaging = firebase.messaging();

        // VAPID key for web push
        const vapidKey = "BKxvxhk6f0JTzuykemBWTpCe4kFBmHiMmwOKONgHuOhpeZmZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ";

        // Request notification permission and get token
        await requestNotificationPermission(messaging, vapidKey);

        // Handle foreground messages
        messaging.onMessage((payload) => {
            console.log('Message received in foreground:', payload);
            handleForegroundMessage(payload);
        });

        // Listen for service worker messages
        navigator.serviceWorker.addEventListener('message', (event) => {
            console.log('Message from service worker:', event.data);
            handleServiceWorkerMessage(event.data);
        });

        console.log('FCM initialized successfully');

    } catch (error) {
        console.error('Error initializing FCM:', error);
    }
}

/**
 * Request notification permission and get FCM token
 */
async function requestNotificationPermission(messaging, vapidKey) {
    try {
        console.log('Requesting notification permission...');

        // Check current permission status
        let permission = Notification.permission;

        if (permission === 'default') {
            // Request permission
            permission = await Notification.requestPermission();
        }

        if (permission === 'granted') {
            console.log('Notification permission granted');

            // Get FCM token
            const token = await messaging.getToken({ vapidKey: vapidKey });

            if (token) {
                console.log('FCM Token:', token);

                // Store token in localStorage
                localStorage.setItem('fcm_token', token);

                // Register token with server
                await registerTokenWithServer(token);

                // Show success message
                showNotificationStatus('Notifications enabled successfully!', 'success');

                return token;
            } else {
                console.log('No registration token available');
                showNotificationStatus('Failed to get notification token', 'error');
                return null;
            }
        } else {
            console.log('Notification permission denied');
            showNotificationStatus('Notification permission denied', 'warning');
            return null;
        }
    } catch (error) {
        console.error('Error requesting notification permission:', error);
        showNotificationStatus('Error setting up notifications: ' + error.message, 'error');
        return null;
    }
}

/**
 * Register FCM token with server
 */
async function registerTokenWithServer(token) {
    try {
        const deviceId = getDeviceId();
        const adminId = await getAdminId();

        if (!adminId) {
            console.log('Admin not logged in, skipping token registration');
            return;
        }

        const response = await fetch('api/fcm/register_token.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: adminId,
                token: token,
                device_id: deviceId,
                device_type: 'web'
            })
        });

        const result = await response.json();

        if (result.success) {
            console.log('FCM token registered successfully with server');
        } else {
            console.error('Failed to register FCM token with server:', result.message);
        }
    } catch (error) {
        console.error('Error registering FCM token with server:', error);
    }
}

/**
 * Handle foreground messages
 */
function handleForegroundMessage(payload) {
    const { notification, data } = payload;

    const title = notification?.title || data?.title || 'New Notification';
    const body = notification?.body || data?.message || 'You have a new notification';

    // Show browser notification
    showBrowserNotification(title, body, data);

    // Play notification sound
    playNotificationSound();

    // Handle specific notification types
    handleNotificationData(data || {});

    // Show in-app notification
    showInAppNotification(title, body, data);
}

/**
 * Show browser notification
 */
function showBrowserNotification(title, body, data = {}) {
    if (Notification.permission === 'granted') {
        const notification = new Notification(title, {
            body: body,
            icon: '/GoGoLaundry/GoGoLaundryAdminPanel/assets/images/logo.png',
            badge: '/GoGoLaundry/GoGoLaundryAdminPanel/assets/images/badge.png',
            data: data,
            requireInteraction: true,
            tag: 'gogolaundry-foreground'
        });

        notification.onclick = function() {
            window.focus();
            handleNotificationClick(data);
            notification.close();
        };

        // Auto close after 10 seconds
        setTimeout(() => {
            notification.close();
        }, 10000);
    }
}

/**
 * Show in-app notification
 */
function showInAppNotification(title, body, data = {}) {
    // Create notification element
    const notificationHtml = `
        <div class="alert alert-info alert-dismissible fade show notification-popup" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
            <strong>${title}</strong><br>
            ${body}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;

    // Add to page
    document.body.insertAdjacentHTML('beforeend', notificationHtml);

    // Auto remove after 8 seconds
    setTimeout(() => {
        const popup = document.querySelector('.notification-popup');
        if (popup) {
            popup.remove();
        }
    }, 8000);
}

/**
 * Play notification sound
 */
function playNotificationSound() {
    try {
        const audio = new Audio('/GoGoLaundry/GoGoLaundryAdminPanel/assets/sounds/notification.mp3');
        audio.volume = 0.5;
        audio.play().catch(e => {
            console.log('Could not play notification sound:', e);
        });
    } catch (error) {
        console.log('Error playing notification sound:', error);
    }
}

/**
 * Handle notification data based on type
 */
function handleNotificationData(data) {
    const type = data.type;

    switch (type) {
        case 'new_order':
            handleNewOrderNotification(data);
            break;
        case 'order_status':
            handleOrderStatusNotification(data);
            break;
        case 'user_registration':
            handleUserRegistrationNotification(data);
            break;
        default:
            console.log('Unknown notification type:', type);
    }
}

/**
 * Handle new order notification
 */
function handleNewOrderNotification(data) {
    console.log('New order received:', data);

    // Update order count if on dashboard
    updateDashboardCounts();

    // Refresh page if on orders page
    if (window.location.pathname.includes('orders') || window.location.pathname.includes('index')) {
        setTimeout(() => {
            location.reload();
        }, 2000);
    }
}

/**
 * Handle order status notification
 */
function handleOrderStatusNotification(data) {
    console.log('Order status updated:', data);

    // Refresh page if on orders page
    if (window.location.pathname.includes('orders')) {
        setTimeout(() => {
            location.reload();
        }, 2000);
    }
}

/**
 * Handle user registration notification
 */
function handleUserRegistrationNotification(data) {
    console.log('New user registered:', data);

    // Update user count if on dashboard
    updateDashboardCounts();

    // Refresh page if on users page
    if (window.location.pathname.includes('users')) {
        setTimeout(() => {
            location.reload();
        }, 2000);
    }
}

/**
 * Handle notification click
 */
function handleNotificationClick(data) {
    const type = data.type;
    const orderId = data.order_id;
    const userId = data.user_id;

    switch (type) {
        case 'new_order':
        case 'order_status':
            if (orderId) {
                window.location.href = `order_details.php?id=${orderId}`;
            } else {
                window.location.href = 'orders.php';
            }
            break;
        case 'user_registration':
            if (userId) {
                window.location.href = `users.php?user_id=${userId}`;
            } else {
                window.location.href = 'users.php';
            }
            break;
        default:
            window.location.href = 'notifications.php';
    }
}

/**
 * Handle service worker messages
 */
function handleServiceWorkerMessage(data) {
    if (data.type === 'NOTIFICATION_CLICK') {
        handleNotificationClick(data.data);
    }
}

/**
 * Update dashboard counts
 */
function updateDashboardCounts() {
    // This would update dashboard statistics
    // Implement based on your dashboard structure
    console.log('Updating dashboard counts...');
}

/**
 * Show notification status message
 */
function showNotificationStatus(message, type = 'info') {
    // You can implement this based on your UI framework
    console.log(`[${type.toUpperCase()}] ${message}`);
}

/**
 * Get or generate device ID
 */
function getDeviceId() {
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
        deviceId = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
}

/**
 * Get admin ID from session
 */
async function getAdminId() {
    try {
        const response = await fetch('api/admin/current_user.php');
        const result = await response.json();

        if (result.success && result.data) {
            return result.data.id;
        } else {
            console.log('Admin not logged in or error getting admin info:', result.message);
            return null;
        }
    } catch (error) {
        console.error('Error getting admin ID:', error);
        return null;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing FCM...');

    // Add notification permission button if needed
    addNotificationPermissionButton();
});

/**
 * Add notification permission button to UI
 */
function addNotificationPermissionButton() {
    if (Notification.permission === 'default') {
        // You can add a button to request permission
        console.log('Notification permission not granted yet');
    }
}
