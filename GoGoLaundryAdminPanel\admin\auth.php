<?php
/**
 * Admin Authentication Middleware
 *
 * This file checks if the admin is logged in and redirects to the login page if not
 */

// Include required files
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/AdminManager.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // Store the requested URL for redirection after login
    $_SESSION['admin_redirect'] = $_SERVER['REQUEST_URI'];

    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Initialize admin manager
$adminManager = new AdminManager($pdo);

// Get admin data
$admin = $adminManager->getAdminById($_SESSION['admin_id']);

// Check if admin exists and is active
if (!$admin || !$admin['is_active']) {
    // Clear session
    session_unset();
    session_destroy();

    // Redirect to login page
    header('Location: login.php?error=account_inactive');
    exit;
}

// Create CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = $adminManager->createCsrfToken();
}

// Set admin data for use in templates
$adminData = [
    'id' => $admin['id'],
    'username' => $admin['username'],
    'email' => $admin['email'],
    'full_name' => $admin['full_name'],
    'role' => $admin['role'],
    'is_active' => $admin['is_active'],
    'last_login' => $admin['last_login']
];
