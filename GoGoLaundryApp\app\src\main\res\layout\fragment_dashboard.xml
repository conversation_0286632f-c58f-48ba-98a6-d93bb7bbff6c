<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/welcome_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardUseCompatPadding="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/welcome_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Welcome"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/welcome_message"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Welcome to the OTP Management System. You are now logged in."
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/welcome_title" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/user_info_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardUseCompatPadding="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/welcome_card">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/user_info_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Your Information"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/user_name_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Name:"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/user_info_title" />

                <TextView
                    android:id="@+id/user_name_value"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/user_name_label"
                    app:layout_constraintTop_toTopOf="@id/user_name_label"
                    tools:text="John Doe" />

                <TextView
                    android:id="@+id/user_phone_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Phone:"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/user_name_value" />

                <TextView
                    android:id="@+id/user_phone_value"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/user_phone_label"
                    app:layout_constraintTop_toTopOf="@id/user_phone_label"
                    tools:text="+8801712345678" />

                <TextView
                    android:id="@+id/user_email_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Email:"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/user_phone_value" />

                <TextView
                    android:id="@+id/user_email_value"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/user_email_label"
                    app:layout_constraintTop_toTopOf="@id/user_email_label"
                    tools:text="<EMAIL>" />

                <TextView
                    android:id="@+id/user_address_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Address:"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/user_email_value" />

                <TextView
                    android:id="@+id/user_address_value"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textAppearance="@style/TextAppearance.MaterialComponents.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/user_address_label"
                    app:layout_constraintTop_toTopOf="@id/user_address_label"
                    tools:text="123 Main St, Dhaka" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
